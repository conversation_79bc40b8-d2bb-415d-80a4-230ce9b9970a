package com.xt.hsk.module.game.api.dto;

import java.util.List;
import lombok.Data;

/**
 * 专项练习 resp dto
 *
 * <AUTHOR>
 * @since 2025/06/30
 */
@Data
public class SpecialExercisePageRespDTO {
    /**
     * 专项练习ID
     */
    private Long id;

    /**
     * 专项练习名称-中文
     */
    private String nameCn;

    /**
     * 练习类型
     *
     */
    private Integer type;
    /**
     * 练习类型名称
     */
    private String typeDesc;


    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 专项练习ID
     */
    private Long specialExerciseId;

    /**
     * 题目列表
     */
    private List<String> questionList;
    /**
     * 所属互动课
     */
    private String interactiveCourseName;
    /**
     * 课程ID
     */
    private String courseIds;
    /**
     * 引用的越南语
     */
    private String translationOt;
}
