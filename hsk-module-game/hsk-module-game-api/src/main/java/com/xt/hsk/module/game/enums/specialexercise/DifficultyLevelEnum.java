package com.xt.hsk.module.game.enums.specialexercise;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 专项练习难度等级 1 简单 2中等 3 困难
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Getter
@AllArgsConstructor
public enum DifficultyLevelEnum implements BasicEnum<Integer>, VO {
    /**
     * 难度等级
     */
    EASY(1, "简单"),
    MEDIUM(2, "中等"),
    HARD(3, "困难"),
    ;

    private final Integer code;
    private final String desc;


    /**
     * 根据专项练习难度等级码获取对应的描述信息
     *
     * @param code 专项练习难度等级码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (DifficultyLevelEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据专项练习难度等级码获取对应的枚举实例
     *
     * @param code 专项练习难度等级码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static DifficultyLevelEnum getByCode(Integer code) {
        for (DifficultyLevelEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据专项练习难度等级描述获取对应的枚举实例
     *
     * @param desc 专项练习难度等级描述
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static DifficultyLevelEnum getByDesc(String desc) {
        for (DifficultyLevelEnum value : values()) {
            if (value.desc.equals(desc)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据专项练习难度等级描述获取对应的枚举实例
     *
     * @param desc 专项练习难度等级描述
     * @return 对应的专项练习难度等级码，如果找不到则返回null
     */
    public static Integer getCodeByDesc(String desc) {
        for (DifficultyLevelEnum value : values()) {
            if (value.desc.equals(desc)) {
                return value.code;
            }
        }
        return null;
    }
}
