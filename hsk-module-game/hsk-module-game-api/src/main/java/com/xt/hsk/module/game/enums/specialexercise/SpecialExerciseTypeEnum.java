package com.xt.hsk.module.game.enums.specialexercise;

import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.Getter;

/**
 * 专项练习类型枚举
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Getter
public enum SpecialExerciseTypeEnum implements
    ArrayValuable<Integer> , BasicEnum<Integer> {
    /**
     * 专项练习类型枚举
     */
    WORD_MATCHING(1, "单词连连看"),
    STROKE_WRITING(2, "笔画书写"),
    WORD_TO_SENTENCE(3, "连词成句"),
    KARAOKE(4, "卡拉OK"),
    ;


    private final Integer code;
    private final String desc;

    SpecialExerciseTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据专项练习类型码获取对应的描述信息
     *
     * @param code 专项练习类型码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (SpecialExerciseTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据专项练习类型码获取对应的枚举实例
     *
     * @param code 专项练习类型码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static SpecialExerciseTypeEnum getByCode(Integer code) {
        for (SpecialExerciseTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    private static final Integer[] ARRAYS = Arrays.stream(values())
        .map(SpecialExerciseTypeEnum::getCode)
        .toArray(Integer[]::new);
    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}
