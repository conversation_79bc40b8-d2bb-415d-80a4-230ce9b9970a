package com.xt.hsk.module.game.enums;

import com.xt.hsk.framework.common.exception.ErrorCode;
import com.xt.hsk.framework.common.exception.enums.ServiceErrorCodeRange;

/**
 * 错误代码常量
 * 模块 game 游戏模块错误码区间 [1-026-001-000 ~ 1-027-000-000)]
 * 游戏系统使用 026 模块编码
 * 单词连连看，错误码从 001 开始
 * 笔画书写，错误码从 002 开始
 * 连词成句，错误码从 003 开始
 * 卡拉OK，错误码从 004 开始
 * 专项类型，错误码从 005 开始
 * 题库，错误码从 006 开始
 * 练习组，错误码从 007 开始
 * 练习记录，错误码从 008 开始
 *
 * @see ServiceErrorCodeRange yudao设计说明
 * <AUTHOR>
 * @since 2025-06-12
 */
public interface ErrorCodeConstants {

    // ========== 单词连连看 1-026-001-000 ==========
    ErrorCode WORD_MATCHING_ROUND_DUPLICATE = new ErrorCode(1_026_001_001, "第【{}】轮的轮次重复，请检查");
    ErrorCode WORD_MATCHING_ROUND_EMPTY = new ErrorCode(1_026_001_002, "第【{}】轮次题目为空，请检查");
    ErrorCode WORD_MATCHING_ROUND_EXCEED_LIMIT = new ErrorCode(1_026_001_003, "第【{}】轮次题目数量超过{}组，请检查");
    ErrorCode WORD_MATCHING_ROUNDS_EMPTY = new ErrorCode(1_026_001_004, "练习组题目不能为空");

    // ========== 笔画书写 1-026-002-000 ==========
    ErrorCode STROKE_WRITING_EXERCISE_QUESTION_EMPTY = new ErrorCode(1_026_002_001, "笔画书写的练习组题目不能为空");
    ErrorCode STROKE_WRITING_SORT_EMPTY = new ErrorCode(1_026_002_002, "练习组中存在题目序号为空的情况");
    ErrorCode STROKE_WRITING_SORT_DUPLICATE = new ErrorCode(1_026_002_003, "题目序号【{}】重复");

    // ========== 卡拉OK 1-026-003-000 ==========
    ErrorCode KARAOKE_QUESTION_NOT_EXISTS = new ErrorCode(1_026_003_001, "卡拉OK题目不存在");
    ErrorCode KARAOKE_EXERCISE_QUESTION_EMPTY = new ErrorCode(1_026_002_001, "笔画书写的练习组题目不能为空");
    ErrorCode KARAOKE_SORT_EMPTY = new ErrorCode(1_026_002_002, "练习组中存在题目序号为空的情况");
    ErrorCode KARAOKE_SORT_DUPLICATE = new ErrorCode(1_026_002_003, "题目序号【{}】重复");
    ErrorCode KARAOKE_AI_CORRECTION_COUNT_LIMIT = new ErrorCode(1_026_003_004, "今天练得够多啦 明天再来吧~");

    // ========== 专项类型 1-026-005-000 ==========
    ErrorCode SPECIAL_EXERCISE_UNKNOWN_EXERCISE_TYPE = new ErrorCode(1_026_005_001, "未找到专项练习类型");
    ErrorCode SPECIAL_EXERCISE_NOT_EXISTS = new ErrorCode(1_026_005_002, "此专项练习不存在", "Business.special_exercise_not_exists");
    ErrorCode SPECIAL_EXERCISE_IN_USE = new ErrorCode(1_026_005_003, "专项练习已被引用，不可删除！");

    // ========== 游戏题库 1-026-006-000 ==========
    ErrorCode GAME_QUESTION_NOT_EXISTS = new ErrorCode(1_026_006_001, "题目不存在", "Business.game_question_not_exists");
    ErrorCode GAME_QUESTION_TYPE_NOT_EXISTS = new ErrorCode(1_026_006_002, "题目类型不存在");
    ErrorCode GAME_QUESTION_IN_USE = new ErrorCode(1_026_006_003, "题目已被引用，不可删除！");
    ErrorCode GAME_QUESTION_IN_USE_CANNOT_HIDE = new ErrorCode(1_026_006_004, "题目已被引用，不可隐藏！");
    ErrorCode GAME_QUESTION_GENERATE_PINYIN_FAIL = new ErrorCode(1_026_006_005, "拼音生成失败");
    ErrorCode GAME_QUESTION_NOT_FINISHED = new ErrorCode(1_026_006_006, "题目未完成，请继续作答", "Business.game_question_not_finished");

    // ========== 练习组 1-026-007-000 ==========
    ErrorCode EXERCISE_QUESTION_EMPTY = new ErrorCode(1_026_007_001, "练习组题目不能为空");
    ErrorCode EXERCISE_QUESTION_SORT_EMPTY = new ErrorCode(1_026_007_002,
        "练习组中存在题目序号为空的情况");
    ErrorCode EXERCISE_QUESTION_SORT_DUPLICATE = new ErrorCode(1_026_007_003, "题目序号【{}】重复");
    ErrorCode EXERCISE_QUESTION_DUPLICATE_GROUP_CONTENT = new ErrorCode(1_026_007_004, "第{}组有重复内容，请检查！");

    // ========== 练习记录 1-026-008-000 ==========
    ErrorCode GAME_RECORD_NOT_EXISTS = new ErrorCode(1_026_008_001, "此练习记录不存在", "Business.game_record_not_exists");
    ErrorCode GAME_RECORD_COMPLETED = new ErrorCode(1_026_008_002, "本次练习已完成，请重新作答", "Business.game_record_completed");
    ErrorCode GAME_RECORD_AUDIO_FILE_EMPTY = new ErrorCode(1_026_008_003, "语音为空");
    ErrorCode GAME_RECORD_PARAM_INVALID = new ErrorCode(1_026_008_004, "请求参数不合法");
    ErrorCode GAME_RECORD_AUDIO_PROCESSING_ERROR = new ErrorCode(1_026_008_006, "处理语音出错");


}