package com.xt.hsk.module.game.enums.specialexercise;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 专项练习记录来源枚举
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Getter
@AllArgsConstructor
public enum SpecialExerciseRecordSourceEnum implements BasicEnum<Integer>, VO,
        ArrayValuable<Integer> {
    /**
     * 专项练习记录来源枚举
     */
    SPECIAL_EXERCISE(1, "专项练习"),
    INTERACTIVE_COURSE(2, "互动课"),
    ;

    private final Integer code;
    private final String desc;

    private static final Integer[] ARRAYS = Arrays.stream(values())
            .map(SpecialExerciseRecordSourceEnum::getCode)
            .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}
