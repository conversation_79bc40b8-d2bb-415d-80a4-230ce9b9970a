package com.xt.hsk.module.game.enums.question;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 题目类型枚举
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Getter
@AllArgsConstructor
public enum GameQuestionTypeEnum implements BasicEnum<Integer> {

    /**
     * 题目类型枚举
     */
    WORD_MATCHING(1, "单词连连看"),
    STROKE_WRITING(2, "笔画书写"),
    WORD_TO_SENTENCE(3, "连词成句"),
    KARAOKE(4, "卡拉OK"),
    ;

    private final Integer code;
    private final String desc;

    /**
     * 根据题目类型码获取对应的描述信息
     *
     * @param code 题目类型码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (GameQuestionTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据题目类型码获取对应的枚举实例
     *
     * @param code 题目类型码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static GameQuestionTypeEnum getByCode(Integer code) {
        for (GameQuestionTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}