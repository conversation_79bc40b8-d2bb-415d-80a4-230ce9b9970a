package com.xt.hsk.module.game.enums.specialexercise;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 专项练习答题模式枚举
 *
 * <AUTHOR>
 * @since 2025-06-29
 */
@Getter
@AllArgsConstructor
public enum SpecialExerciseAnswerModeEnum implements BasicEnum<Integer>, VO,
    ArrayValuable<Integer> {
    /**
     * 答题模式
     */
    REDO(1, "重新作答"),
    CONTINUE(2, "继续作答"),
    NEW(3, "新练习"),
    ;

    private final Integer code;
    private final String desc;


    /**
     * 根据专项练习答题模式码获取对应的描述信息
     *
     * @param code 专项练习答题模式码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (SpecialExerciseAnswerModeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据专项练习答题模式码获取对应的枚举实例
     *
     * @param code 专项练习答题模式码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static SpecialExerciseAnswerModeEnum getByCode(Integer code) {
        for (SpecialExerciseAnswerModeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据专项练习答题模式描述获取对应的枚举实例
     *
     * @param desc 专项练习答题模式描述
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static SpecialExerciseAnswerModeEnum getByDesc(String desc) {
        for (SpecialExerciseAnswerModeEnum value : values()) {
            if (value.desc.equals(desc)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据专项练习答题模式描述获取对应的枚举实例
     *
     * @param desc 专项练习答题模式描述
     * @return 对应的专项练习答题模式码，如果找不到则返回null
     */
    public static Integer getCodeByDesc(String desc) {
        for (SpecialExerciseAnswerModeEnum value : values()) {
            if (value.desc.equals(desc)) {
                return value.code;
            }
        }
        return null;
    }


    private static final Integer[] ARRAYS = Arrays.stream(values())
        .map(SpecialExerciseAnswerModeEnum::getCode)
        .toArray(Integer[]::new);
    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}
