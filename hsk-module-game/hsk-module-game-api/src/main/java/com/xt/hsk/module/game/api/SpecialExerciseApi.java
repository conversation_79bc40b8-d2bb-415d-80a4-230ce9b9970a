package com.xt.hsk.module.game.api;

import com.xt.hsk.module.game.api.dto.SpecialExerciseOtQuoteDTO;
import com.xt.hsk.module.game.api.dto.SpecialExerciseOtQuoteReqDTO;
import com.xt.hsk.module.game.api.dto.SpecialExercisePageRespDTO;
import com.xt.hsk.module.game.api.dto.WordQuoteCountDTO;
import java.util.List;
import java.util.Map;

/**
 * 专项练习API
 */
public interface SpecialExerciseApi {

    /**
     * 获取单词是否被专项练习引用
     */
    boolean isQuoteWord(Long wordId);

    /**
     * 根据专项练习IDS获取信息
     */
    List<SpecialExercisePageRespDTO> getQuestionInfoList(List<Long> specialExerciseIdList);

    /**
     * 获取专项练习信息
     */
    SpecialExercisePageRespDTO getQuestionInfo(Long id);

    /**
     * 根据字词库ID获取专项练习信息
     */
    List<SpecialExercisePageRespDTO> getQuestionInfoListByWordId(Long wordId);

    List<WordQuoteCountDTO> countQuoteByWordIds(List<Long> wordIds);

    /**
     * 获取字词库中翻译在专项练习中的引用信息
     *
     * @param req
     * @return
     */
    SpecialExerciseOtQuoteDTO getSpecialExerciseQuoteInfo(SpecialExerciseOtQuoteReqDTO req);

    /**
     * 批量查询指定HSK等级下各类型专项练习的可用性
     *
     * @param hskLevel HSK等级
     * @return 类型到是否有题目的映射 key: 专项练习类型, value: 是否有题目
     */
    Map<String, Boolean> getSpecialExerciseAvailabilityByHskLevel(Integer hskLevel);
}
