package com.xt.hsk.module.game.service.question;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.game.controller.admin.question.vo.GameQuestionExerciseReferenceVO;
import com.xt.hsk.module.game.controller.admin.question.vo.GameQuestionPageReqVO;
import com.xt.hsk.module.game.dal.dataobject.question.GameQuestionDO;
import com.xt.hsk.module.game.dal.mysql.question.GameQuestionMapper;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.stereotype.Service;


/**
 * 游戏-题库 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Service
public class GameQuestionServiceImpl extends ServiceImpl<GameQuestionMapper, GameQuestionDO> implements GameQuestionService {

    @Resource
    private GameQuestionMapper gameQuestionMapper;

    /**
     * 分页获取题目
     *
     * @param pageReqVO 查询条件请求对象
     * @return 题目习分页
     */
    @Override
    public PageResult<GameQuestionDO> getQuestionPage(GameQuestionPageReqVO pageReqVO) {
        LambdaQueryWrapper<GameQuestionDO> queryWrapper = buildQuestionQuery(pageReqVO);
        return gameQuestionMapper.selectPage(pageReqVO, queryWrapper);
    }

    /**
     * 题目总数
     *
     * @param pageReqVO 查询条件请求对象
     * @return 题目总数
     */
    @Override
    public Long countQuestion(GameQuestionPageReqVO pageReqVO) {
        LambdaQueryWrapper<GameQuestionDO> queryWrapper = buildQuestionQuery(pageReqVO);
        return gameQuestionMapper.selectCount(queryWrapper);
    }

    /**
     * 构建用于查询题目的条件构造器
     *
     * @param reqVO 查询条件请求对象
     * @return 构建好的条件对象
     */
    private LambdaQueryWrapper<GameQuestionDO> buildQuestionQuery(GameQuestionPageReqVO reqVO) {
        LambdaQueryWrapperX<GameQuestionDO> queryWrapper = new LambdaQueryWrapperX<>();
        return queryWrapper.likeIfPresent(GameQuestionDO::getQuestionContent, reqVO.getQuestionContent())
                .eq(GameQuestionDO::getType, reqVO.getType())
                .eqIfPresent(GameQuestionDO::getHskLevel, reqVO.getHskLevel())
                .eqIfPresent(GameQuestionDO::getIsShow, reqVO.getIsShow())
                .orderByAsc(GameQuestionDO::getHskLevel)
                .orderByDesc(GameQuestionDO::getId);
    }

    /**
     * 获取引用题目的专项练习组信息
     *
     * @param questionId 题目ID
     * @param type       类型
     * @return 专项练习组引用信息列表
     */
    @Override
    public List<GameQuestionExerciseReferenceVO> getExerciseReferences(Long questionId, Integer type) {
        return gameQuestionMapper.getExerciseReferences(questionId, type);
    }

}