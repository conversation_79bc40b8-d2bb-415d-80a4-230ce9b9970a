package com.xt.hsk.module.game.service.specialexercise;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.xt.hsk.framework.common.constants.RedisKeyPrefix;
import com.xt.hsk.framework.common.enums.IsShowEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.api.interactivecourse.dto.InteractiveCourseApi;
import com.xt.hsk.module.edu.api.interactivecourse.dto.InteractiveCourseBaseInfoRespDTO;
import com.xt.hsk.module.game.api.SpecialExerciseApi;
import com.xt.hsk.module.game.api.dto.SpecialExerciseOtQuoteDTO;
import com.xt.hsk.module.game.api.dto.SpecialExerciseOtQuoteReqDTO;
import com.xt.hsk.module.game.api.dto.SpecialExercisePageRespDTO;
import com.xt.hsk.module.game.api.dto.WordQuoteCountDTO;
import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionRespVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.ExerciseQuestionInfoRespVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExercisePageReqVO;
import com.xt.hsk.module.game.controller.app.specialexercise.vo.SpecialExerciseAppPageReqVO;
import com.xt.hsk.module.game.controller.app.specialexercise.vo.SpecialExerciseRecordRespVO;
import com.xt.hsk.module.game.convert.specialexercise.SpecialExerciseConvert;
import com.xt.hsk.module.game.dal.dataobject.specialexercise.SpecialExerciseDO;
import com.xt.hsk.module.game.dal.mysql.specialexercise.SpecialExerciseMapper;
import com.xt.hsk.module.game.enums.question.GameQuestionTypeEnum;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import com.xt.hsk.module.game.service.exercisequestion.ExerciseQuestionService;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * 游戏-专项练习-练习组 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Service
public class SpecialExerciseServiceImpl extends ServiceImpl<SpecialExerciseMapper, SpecialExerciseDO> implements SpecialExerciseService, SpecialExerciseApi {

    @Resource
    private SpecialExerciseMapper specialExerciseMapper;
    @Autowired
    private ExerciseQuestionService exerciseQuestionService;

    @Resource
    private InteractiveCourseApi interactiveCourseApi;

    @Resource
    private SpecialExerciseService specialExerciseService;

    /**
     * 分页获取专项练习
     *
     * @param reqVO 查询条件请求对象
     * @return 专项练习分页
     */
    @Override
    public PageResult<SpecialExerciseDO> getSpecialExercisePage(SpecialExercisePageReqVO reqVO) {

        // 如果根据互动课名称查询
        if (CharSequenceUtil.isNotBlank(reqVO.getInteractiveCourseName())) {
            List<Long> idList = interactiveCourseApi.listResourceIdByCourseName(reqVO.getInteractiveCourseName());

            // 如果没有找到对应的互动课，则返回空
            if (CollUtil.isEmpty(idList)) {
                return PageResult.empty();
            }

            reqVO.setIds(idList);
        }

        // 如果不是根据题目查询，则使用Mybatis-Plus自带的分页查询
        if (CharSequenceUtil.isBlank(reqVO.getQuestion())) {
            LambdaQueryWrapper<SpecialExerciseDO> queryWrapper = buildSpecialExerciseQuery(reqVO);
            return specialExerciseMapper.selectPage(reqVO, queryWrapper);
        }

        // 如果是根据题目查询，则使用自定义的分页查询
        IPage<SpecialExerciseDO> page = specialExerciseMapper.getSpecialExercisePage(new Page<>(reqVO.getPageNo(), reqVO.getPageSize()), reqVO);
        return new PageResult<>(page.getRecords(), page.getTotal());

    }

    /**
     * 专项练习总数
     *
     * @param reqVO 查询条件请求对象
     * @return 专项练习总数
     */
    @Override
    public Long countSpecialExercise(SpecialExercisePageReqVO reqVO) {

        // 如果不是根据名称查询，则使用Mybatis-Plus自带的分页查询
        if (CharSequenceUtil.isBlank(reqVO.getNameCn())) {
            LambdaQueryWrapper<SpecialExerciseDO> queryWrapper = buildSpecialExerciseQuery(reqVO);
            return specialExerciseMapper.selectCount(queryWrapper);
        }

        // 如果是根据名称查询，则使用自定义的分页查询
        return specialExerciseMapper.countSpecialExercisePage(reqVO);

    }

    /**
     * 构建用于查询专项练习的条件构造器
     *
     * @param reqVO 查询条件请求对象
     * @return 构建好的条件对象
     */
    private LambdaQueryWrapper<SpecialExerciseDO> buildSpecialExerciseQuery(SpecialExercisePageReqVO reqVO) {
        LambdaQueryWrapperX<SpecialExerciseDO> queryWrapper = new LambdaQueryWrapperX<>();
        return queryWrapper.likeIfPresent(SpecialExerciseDO::getNameCn, reqVO.getNameCn())
                .likeIfPresent(SpecialExerciseDO::getNameEn, reqVO.getNameEn())
                .likeIfPresent(SpecialExerciseDO::getNameOt, reqVO.getNameOt())
                .inIfPresent(SpecialExerciseDO::getId, reqVO.getIds())
                .eqIfPresent(SpecialExerciseDO::getType, reqVO.getType())
                .eqIfPresent(SpecialExerciseDO::getHskLevel, reqVO.getHskLevel())
                .eqIfPresent(SpecialExerciseDO::getDifficultyLevel, reqVO.getDifficultyLevel())
                .eqIfPresent(SpecialExerciseDO::getIsShow, reqVO.getIsShow())
                .notIn(CollUtil.isNotEmpty(reqVO.getExcludeIds()), SpecialExerciseDO::getId, reqVO.getExcludeIds())
                .like(Objects.nonNull(reqVO.getSpecialExerciseId()), SpecialExerciseDO::getId, reqVO.getSpecialExerciseId())
                .orderByAsc(SpecialExerciseDO::getHskLevel, SpecialExerciseDO::getSort);
    }

    @Override
    public boolean isQuoteWord(Long wordId) {
        return exerciseQuestionService.isQuoteWord(wordId) > 0;
    }

    @Override
    public List<SpecialExercisePageRespDTO> getQuestionInfoList(List<Long> specialExerciseIdList) {
        if (CollUtil.isEmpty(specialExerciseIdList)) {
            return Collections.emptyList();
        }
        // 获取专项练习的分页数据
        List<SpecialExerciseDO> specialExerciseDOList = specialExerciseService.lambdaQuery()
                .in(SpecialExerciseDO::getId, specialExerciseIdList)
                .list();

        List<SpecialExercisePageRespDTO> dtoList = SpecialExerciseConvert.INSTANCE.doListToPageRespDtoList(specialExerciseDOList);

        setQuestionList(dtoList);
        return dtoList;
    }

    @Override
    public SpecialExercisePageRespDTO getQuestionInfo(Long id) {
        SpecialExerciseDO exerciseDO = specialExerciseService.getById(id);
        return SpecialExerciseConvert.INSTANCE.doListToPageRespDto(exerciseDO);
    }

    @Override
    public List<SpecialExercisePageRespDTO> getQuestionInfoListByWordId(Long wordId) {
        // 获取专项练习的分页数据
        List<SpecialExercisePageRespDTO> exerciseDOS = specialExerciseMapper.getQuestionInfoListByWordId(wordId);
        // 获取所属的互动课
        List<InteractiveCourseBaseInfoRespDTO> infoRespDTOS = interactiveCourseApi.listByResourceIdList(exerciseDOS.stream().map(SpecialExercisePageRespDTO::getId).toList());
        Map<Long, List<InteractiveCourseBaseInfoRespDTO>> courseMap = infoRespDTOS.stream().collect(Collectors.groupingBy(InteractiveCourseBaseInfoRespDTO::getResourceId));
        for (SpecialExercisePageRespDTO respDTO : exerciseDOS) {
            List<InteractiveCourseBaseInfoRespDTO> dtoList = courseMap.get(respDTO.getId());
            if (dtoList != null) {
                respDTO.setInteractiveCourseName(dtoList.stream().map(InteractiveCourseBaseInfoRespDTO::getCourseNameCn).collect(Collectors.joining(",")));
                respDTO.setCourseIds(dtoList.stream().map(InteractiveCourseBaseInfoRespDTO::getId).map(String::valueOf).collect(Collectors.joining(",")));
            }
            respDTO.setTypeDesc(SpecialExerciseTypeEnum.getDescByCode(respDTO.getType()));
        }
        return exerciseDOS;
    }

    @Override
    public List<WordQuoteCountDTO> countQuoteByWordIds(List<Long> wordIds) {
        if (wordIds == null || wordIds.isEmpty()) {
            return Collections.emptyList();
        }

        return specialExerciseMapper.countQuoteByWordIds(wordIds);
    }

    @Override
    public SpecialExerciseOtQuoteDTO getSpecialExerciseQuoteInfo(SpecialExerciseOtQuoteReqDTO req) {
        List<SpecialExercisePageRespDTO> dtoList = specialExerciseMapper.getSpecialExerciseQuoteInfo(req);
        if (dtoList != null && !dtoList.isEmpty()) {
            Set<Integer> types = dtoList.stream().map(SpecialExercisePageRespDTO::getType).collect(Collectors.toSet());
            StringBuilder typeDesc = new StringBuilder();
            for (Integer type : types) {
                typeDesc.append(SpecialExerciseTypeEnum.getDescByCode(type)).append(",");
            }
            typeDesc.deleteCharAt(typeDesc.length() - 1);
            SpecialExerciseOtQuoteDTO dto = new SpecialExerciseOtQuoteDTO();
            dto.setTypes(Lists.newArrayList(types));
            dto.setTypeDesc(typeDesc.toString());
            dto.setData(dtoList);
            return dto;
        }

        return new SpecialExerciseOtQuoteDTO();
    }

    /**
     * 获取每个专项练习的题目列表
     *
     * @param voList 专项练习vo列表
     */
    private void setQuestionList(List<SpecialExercisePageRespDTO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        List<Long> group1 = new ArrayList<>();
        List<Long> group2 = new ArrayList<>();

        for (SpecialExercisePageRespDTO vo : voList) {
            if (SpecialExerciseTypeEnum.WORD_MATCHING.getCode().equals(vo.getType())
                    || SpecialExerciseTypeEnum.STROKE_WRITING.getCode().equals(vo.getType())) {
                group1.add(vo.getSpecialExerciseId());
            }
            if (SpecialExerciseTypeEnum.WORD_TO_SENTENCE.getCode().equals(vo.getType())
                    || SpecialExerciseTypeEnum.KARAOKE.getCode().equals(vo.getType())) {

                group2.add(vo.getSpecialExerciseId());
            }
        }

        if (CollUtil.isEmpty(group1) && CollUtil.isEmpty(group2)) {
            return;
        }

        List<ExerciseQuestionInfoRespVO> questionInfoList1 =
                specialExerciseService.getQuestionInfoList(group1, SpecialExerciseTypeEnum.WORD_MATCHING.getCode());

        List<ExerciseQuestionInfoRespVO> questionInfoList2 =
                specialExerciseService.getQuestionInfoList(group2, SpecialExerciseTypeEnum.WORD_TO_SENTENCE.getCode());

        List<ExerciseQuestionInfoRespVO> allQuestionInfoList = new ArrayList<>(questionInfoList1);
        allQuestionInfoList.addAll(questionInfoList2);

        Map<Long, List<String>> map = allQuestionInfoList.stream()
                .collect(Collectors.groupingBy(
                        ExerciseQuestionInfoRespVO::getSpecialExerciseId,
                        Collectors.mapping(
                                ExerciseQuestionInfoRespVO::getQuestionContent,
                                Collectors.toList()
                        )
                ));

        // 设置每个专项练习对应的题目内容列表
        voList.forEach(vo ->
                vo.setQuestionList(map.getOrDefault(
                        vo.getSpecialExerciseId(),
                        Collections.emptyList()
                ))
        );
    }

    /**
     * 获取专项练习的题目信息
     */
    @Override
    public List<ExerciseQuestionInfoRespVO> getQuestionInfoList(List<Long> specialExerciseIdList,
                                                                Integer type) {
        if (CollUtil.isEmpty(specialExerciseIdList)
                || GameQuestionTypeEnum.getByCode(type) == null) {
            return Collections.emptyList();
        }
        return specialExerciseMapper.getQuestionInfoList(specialExerciseIdList, type);
    }

    /**
     * 获取专项练习组的题目
     *
     * @param specialExerciseId 专项练习id
     * @param type              类型
     * @return 练习组的题目列表
     */
    @Override
    public List<ExerciseQuestionRespVO> getExerciseQuestion(Long specialExerciseId, Integer type) {
        if (specialExerciseId == null || GameQuestionTypeEnum.getByCode(type) == null) {
            return Collections.emptyList();
        }
        return specialExerciseMapper.getExerciseQuestion(specialExerciseId, type);
    }

    /**
     * 构建用于查询专项练习app的条件构造器
     *
     * @param reqVO 查询条件请求对象
     * @return 构建好的条件对象
     */
    private LambdaQueryWrapper<SpecialExerciseDO> buildAppSpecialExerciseQuery(SpecialExerciseAppPageReqVO reqVO) {
        LambdaQueryWrapperX<SpecialExerciseDO> queryWrapper = new LambdaQueryWrapperX<>();
        return queryWrapper
                .eq(SpecialExerciseDO::getType, reqVO.getType())
                .eq(SpecialExerciseDO::getHskLevel, reqVO.getHskLevel())
                .eqIfPresent(SpecialExerciseDO::getDifficultyLevel, reqVO.getDifficultyLevel())
                .inIfPresent(SpecialExerciseDO::getDifficultyLevel, reqVO.getDifficultyLevelList())
                .eq(SpecialExerciseDO::getIsShow, IsShowEnum.SHOW.getCode())
                .and(CharSequenceUtil.isNotBlank(reqVO.getName()), wrapper ->
                        wrapper.like(SpecialExerciseDO::getNameCn, reqVO.getName())
                                .or()
                                .like(SpecialExerciseDO::getNameEn, reqVO.getName())
                                .or()
                                .like(SpecialExerciseDO::getNameOt, reqVO.getName())
                )
                .orderByAsc(SpecialExerciseDO::getSort);
    }

    /**
     * 分页获取专项练习app
     */
    @Override
    public PageResult<SpecialExerciseRecordRespVO> getAppSpecialExercisePage(SpecialExerciseAppPageReqVO reqVO) {
        // 如果用户ID为空，直接查询专项练习
        if (reqVO.getUserId() == null) {
            LambdaQueryWrapper<SpecialExerciseDO> queryWrapper = buildAppSpecialExerciseQuery(reqVO);
            PageResult<SpecialExerciseDO> specialExercisePage = specialExerciseMapper.selectPage(reqVO, queryWrapper);

            // 转换
            List<SpecialExerciseRecordRespVO> recordRespVOList = SpecialExerciseConvert.INSTANCE.doListToRecordRespVOList(specialExercisePage.getList());

            return new PageResult<>(recordRespVOList, specialExercisePage.getTotal());
        }

        // 如果用户ID不为空，需要列表查记录
        IPage<SpecialExerciseRecordRespVO> page = specialExerciseMapper.getAppSpecialExercisePage(
                new Page<>(reqVO.getPageNo(), reqVO.getPageSize()), reqVO);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 按HSK级别获取专项练习可用性
     * key: 专项练习类型, value: 是否有题目
     * 因为使用了redis缓存,系统的序列化器将key转换为了字符串,所以这里使用字符串作为key
     *
     * @param hskLevel HSK级别
     * @return {@code Map<String, Boolean> }
     */
    @Override
    @Cacheable(value = RedisKeyPrefix.SPECIAL_EXERCISE_AVAILABILITY,
        key = "#hskLevel",
        unless = "#hskLevel == null")
    public Map<String, Boolean> getSpecialExerciseAvailabilityByHskLevel(Integer hskLevel) {
        Map<String, Boolean> result = new HashMap<>();

        if (hskLevel == null) {
            // 如果HSK等级为空，所有类型都返回false
            for (SpecialExerciseTypeEnum typeEnum : SpecialExerciseTypeEnum.values()) {
                result.put(String.valueOf(typeEnum.getCode()), false);
            }
            return result;
        }

        // 查询指定HSK等级下所有展示的专项练习
        List<SpecialExerciseDO> specialExercises = this.lambdaQuery()
            .eq(SpecialExerciseDO::getHskLevel, hskLevel)
            .eq(SpecialExerciseDO::getIsShow, IsShowEnum.SHOW.getCode())
            .select(SpecialExerciseDO::getType)
            .list();

        // 获取存在的类型集合
        Set<Integer> existingTypes = specialExercises.stream()
            .map(SpecialExerciseDO::getType)
            .collect(Collectors.toSet());

        // 为所有类型设置可用性
        for (SpecialExerciseTypeEnum typeEnum : SpecialExerciseTypeEnum.values()) {
            result.put(String.valueOf(typeEnum.getCode()), existingTypes.contains(typeEnum.getCode()));
        }

        return result;
    }

}