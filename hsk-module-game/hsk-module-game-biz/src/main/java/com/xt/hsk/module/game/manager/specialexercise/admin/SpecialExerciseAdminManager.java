package com.xt.hsk.module.game.manager.specialexercise.admin;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.game.enums.ErrorCodeConstants.SPECIAL_EXERCISE_IN_USE;
import static com.xt.hsk.module.game.enums.ErrorCodeConstants.SPECIAL_EXERCISE_NOT_EXISTS;
import static com.xt.hsk.module.game.enums.ErrorCodeConstants.SPECIAL_EXERCISE_UNKNOWN_EXERCISE_TYPE;

import cn.hutool.core.collection.CollUtil;
import cn.idev.excel.EasyExcel;
import cn.idev.excel.exception.ExcelAnalysisException;
import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.constants.RedisKeyPrefix;
import com.xt.hsk.framework.common.enums.IsShowEnum;
import com.xt.hsk.framework.common.pojo.ImportResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.BasicEnumUtil;
import com.xt.hsk.framework.web.core.util.WebFrameworkUtils;
import com.xt.hsk.module.edu.api.interactivecourse.dto.InteractiveCourseApi;
import com.xt.hsk.module.edu.api.interactivecourse.dto.InteractiveCourseBaseInfoRespDTO;
import com.xt.hsk.module.edu.api.word.WordApi;
import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionRespVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.ExerciseQuestionInfoRespVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.ExerciseRoundRespVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.KaraokeImportExcelVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExerciseDeleteCheckRespVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExerciseInfoRespVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExercisePageReqVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExercisePageRespVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExerciseSaveReqVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.StrokeWritingImportExcelVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.WordMatchingImportExcelVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.WordToSentenceImportExcelVO;
import com.xt.hsk.module.game.convert.specialexercise.SpecialExerciseConvert;
import com.xt.hsk.module.game.dal.dataobject.specialexercise.SpecialExerciseDO;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import com.xt.hsk.module.game.listener.importtask.KaraokeImportListener;
import com.xt.hsk.module.game.listener.importtask.StrokeWritingImportListener;
import com.xt.hsk.module.game.listener.importtask.WordMatchingImportListener;
import com.xt.hsk.module.game.listener.importtask.WordToSentenceImportListener;
import com.xt.hsk.module.game.manager.exercisequestion.admin.ExerciseQuestionAdminManager;
import com.xt.hsk.module.game.service.exercisequestion.ExerciseQuestionService;
import com.xt.hsk.module.game.service.exercisequestion.ExerciseQuestionVersionService;
import com.xt.hsk.module.game.service.question.GameQuestionService;
import com.xt.hsk.module.game.service.specialexercise.SpecialExerciseService;
import com.xt.hsk.module.infra.listener.CompositeRowLimitListener;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * 游戏-专项练习-练习组 后台管理 Manager
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Slf4j
@Component
public class SpecialExerciseAdminManager {

    @Resource
    private SpecialExerciseService specialExerciseService;

    @Resource
    private ExerciseQuestionAdminManager exerciseQuestionAdminManager;

    @Resource
    private WordApi wordApi;

    @Resource
    private ExerciseQuestionService exerciseQuestionService;

    @Resource
    private ExerciseQuestionVersionService exerciseQuestionVersionService;

    @Resource
    private GameQuestionService gameQuestionService;

    @Resource
    private InteractiveCourseApi interactiveCourseApi;

    /**
     * 创建专项练习-练习组
     */
    @Transactional(rollbackFor = Exception.class)
    public void createSpecialExercise(SpecialExerciseSaveReqVO createReqVO) {

        // 获取对应的枚举
        SpecialExerciseTypeEnum specialExerciseTypeEnum = SpecialExerciseTypeEnum.getByCode(
            createReqVO.getType());

        if (specialExerciseTypeEnum == null) {
            throw exception(SPECIAL_EXERCISE_UNKNOWN_EXERCISE_TYPE);
        }

        // 设置序号
        incrementSort(createReqVO.getHskLevel(), createReqVO.getType());
        createReqVO.setSort(1);

        // 设置是否展示
        createReqVO.setIsShow(IsShowEnum.validCodeOrDefault(createReqVO.getIsShow(), IsShowEnum.SHOW));

        // 将请求参数对象转换为数据库实体对象
        SpecialExerciseDO specialExerciseDO = SpecialExerciseConvert.INSTANCE.saveReqVoToDo(createReqVO);

        // 将实体对象保存到数据库中
        specialExerciseDO.setRelatedVersion(1);
        specialExerciseService.save(specialExerciseDO);

        // 设置专项练习ID
        createReqVO.setId(specialExerciseDO.getId());

        exerciseQuestionAdminManager.create(createReqVO, specialExerciseTypeEnum);

        // 设置日志上下文变量
        LogRecordContext.putVariable("exerciseId", specialExerciseDO.getId());
        LogRecordContext.putVariable("exercise", specialExerciseDO);
        LogRecordContext.putVariable("typeDesc", BasicEnumUtil.getDescByCode(SpecialExerciseTypeEnum.class,
            createReqVO.getType()));

    }

    /**
     * 根据指定的HSK等级和练习类型，将所有对应的专项练习的排序值（sort）加1
     *
     * @param hskLevel 指定的HSK等级，用于筛选需要更新的记录
     */
    private void incrementSort(Integer hskLevel, Integer type) {
        specialExerciseService.lambdaUpdate()
                .eq(SpecialExerciseDO::getHskLevel, hskLevel)
                .eq(SpecialExerciseDO::getType, type)
                .setSql("sort = sort + 1")
                .set(SpecialExerciseDO::getUpdateTime, LocalDateTime.now())
                .set(SpecialExerciseDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                .update();
    }


    /**
     * 更新专项练习-练习组
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSpecialExercise(SpecialExerciseSaveReqVO updateReqVO) {
        // 校验存在
        SpecialExerciseDO specialExercise = validateSpecialExerciseExists(updateReqVO.getId());
        updateReqVO.setType(specialExercise.getType());

        // 获取对应的枚举
        SpecialExerciseTypeEnum specialExerciseTypeEnum = SpecialExerciseTypeEnum.getByCode(
            updateReqVO.getType());

        // 如果没有对应的处理器
        if (specialExerciseTypeEnum == null) {
            throw exception(SPECIAL_EXERCISE_UNKNOWN_EXERCISE_TYPE);
        }

        // 设置专项练习ID
        updateReqVO.setId(specialExercise.getId());

        // 更新练习组
        Integer relatedVersion = exerciseQuestionAdminManager.update(updateReqVO,
            specialExerciseTypeEnum);


        // 设置关联版本
        updateReqVO.setRelatedVersion(relatedVersion);

        // 将请求参数对象转换为数据库实体对象
        SpecialExerciseDO newSpecialExercise = SpecialExerciseConvert.INSTANCE.saveReqVoToDo(updateReqVO);

        newSpecialExercise.setUpdateTime(LocalDateTime.now());
        newSpecialExercise.setUpdater(String.valueOf(WebFrameworkUtils.getLoginUserId()));
        specialExerciseService.updateById(newSpecialExercise);

        // 设置日志上下文变量
        LogRecordContext.putVariable("exercise", newSpecialExercise);
        LogRecordContext.putVariable("typeDesc", BasicEnumUtil.getDescByCode(SpecialExerciseTypeEnum.class,
            newSpecialExercise.getType()));
    }

    /**
     * 根据id列表删除
     *
     * @param ids id列表
     */
    @CacheEvict(value = RedisKeyPrefix.SPECIAL_EXERCISE_AVAILABILITY, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Long> ids) {
        if (CollUtil.isNotEmpty(ids)) {
            // 检查专项练习是否被引用
            boolean isReferenced = checkSpecialExerciseReferenced(ids);
            if (isReferenced) {
                throw exception(SPECIAL_EXERCISE_IN_USE);
            }
            
            // 设置日志上下文变量
            LogRecordContext.putVariable("deleteCount", ids.size());
            LogRecordContext.putVariable("batchBizNo", ids.get(0).toString());

            // 删除
            specialExerciseService.removeByIds(ids);
        }
    }

    /**
     * 验证专项练习是否存在
     *
     * @param id 专项练习ID
     * @return 专项练习
     */
    private SpecialExerciseDO validateSpecialExerciseExists(Long id) {
        SpecialExerciseDO specialExerciseDO = specialExerciseService.getById(id);
        if (specialExerciseDO == null) {
            throw exception(SPECIAL_EXERCISE_NOT_EXISTS);
        }
        return specialExerciseDO;
    }

    /**
     * 根据id获取专项练习-练习组
     */
    public SpecialExerciseInfoRespVO getSpecialExercise(Long id) {
        SpecialExerciseDO specialExercise = validateSpecialExerciseExists(id);

        SpecialExerciseInfoRespVO infoRespVO = SpecialExerciseConvert.INSTANCE.doToInfoRespVO(
            specialExercise);

        List<ExerciseQuestionRespVO> questionRespVOList = specialExerciseService.getExerciseQuestion(
            infoRespVO.getId(), specialExercise.getType());

        if (CollUtil.isEmpty(questionRespVOList)) {
            return infoRespVO;
        }

        if (SpecialExerciseTypeEnum.WORD_MATCHING.getCode().equals(specialExercise.getType())) {
            Map<Integer, List<ExerciseQuestionRespVO>> voMap = questionRespVOList
                .stream()
                .sorted(Comparator.comparingInt(ExerciseQuestionRespVO::getRoundNumber))
                .collect(Collectors.groupingBy(ExerciseQuestionRespVO::getRoundNumber));

            Map<Integer, Integer> showRoundMap = questionRespVOList
                .stream()
                .collect(Collectors.toMap(
                    ExerciseQuestionRespVO::getRoundNumber,
                    ExerciseQuestionRespVO::getShowRound,
                    (oldValue, newValue) -> oldValue)
                );

            List<ExerciseRoundRespVO> roundList = new ArrayList<>();
            for (Integer roundNumber : voMap.keySet()) {
                ExerciseRoundRespVO respVO = new ExerciseRoundRespVO();
                respVO.setShowRound(showRoundMap.getOrDefault(roundNumber, 1));
                respVO.setRoundNumber(roundNumber);
                respVO.setExerciseQuestionList(
                    voMap.getOrDefault(roundNumber, Collections.emptyList()));
                roundList.add(respVO);
            }
            roundList.sort(
                Comparator.comparing(
                    ExerciseQuestionRespVO::getRoundNumber,
                    Comparator.nullsLast(Comparator.naturalOrder())
                )
            );
            infoRespVO.setRoundList(roundList);

            return infoRespVO;

        }

        questionRespVOList.sort(
            Comparator.comparing(
                ExerciseQuestionRespVO::getSort,
                Comparator.nullsLast(Comparator.naturalOrder())
            )
        );

        infoRespVO.setExerciseQuestionList(questionRespVOList);

        return infoRespVO;
    }

    /**
     * 分页获取专项练习-练习组
     *
     * @param pageReqVO 查询条件请求对象
     * @return 返回分页结果对象
     */
    public PageResult<SpecialExercisePageRespVO> getSpecialExercisePage(SpecialExercisePageReqVO pageReqVO) {
        // 获取专项练习的分页数据
        PageResult<SpecialExerciseDO> specialExercisePage = specialExerciseService.getSpecialExercisePage(pageReqVO);
        List<SpecialExerciseDO> doList = specialExercisePage.getList();

        // 如果分页数据为空，则直接返回空结果
        if (CollUtil.isEmpty(doList)) {
            return PageResult.empty();
        }

        // 将数据对象列表转换为响应 VO 列表
        List<SpecialExercisePageRespVO> voList = SpecialExerciseConvert.INSTANCE.doListToPageRespVoList(doList);

        // 获取每个专项练习的题目列表
        setQuestionList(voList);

        // 获取每个专项练习的引用互动课字段
        setInteractiveCourseNameList(voList);

        return new PageResult<>(voList, specialExercisePage.getTotal());
    }

    /**
     * 获取每个专项练习的引用互动课字段
     *
     * @param voList VO 列表
     */
    private void setInteractiveCourseNameList(List<SpecialExercisePageRespVO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        List<Long> specialExerciseIdList = voList.stream().map(SpecialExercisePageRespVO::getSpecialExerciseId).toList();
        Map<Long, List<String>> nameMap = interactiveCourseApi.listByResourceIdList(specialExerciseIdList)
                .stream()
                .collect(Collectors.groupingBy(
                        InteractiveCourseBaseInfoRespDTO::getResourceId,
                        Collectors.mapping(InteractiveCourseBaseInfoRespDTO::getCourseNameCn, Collectors.toList())
                ));

        voList.forEach(vo -> vo.setReferenceInteractiveCourseNameList(
                nameMap.getOrDefault(vo.getSpecialExerciseId(), Collections.emptyList())));
    }

    /**
     * 获取每个专项练习的题目列表
     *
     * @param voList 专项练习vo列表
     */
    private void setQuestionList(List<SpecialExercisePageRespVO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        List<Long> group1 = new ArrayList<>();
        List<Long> group2 = new ArrayList<>();

        for (SpecialExercisePageRespVO vo : voList) {
            if (SpecialExerciseTypeEnum.WORD_MATCHING.getCode().equals(vo.getType())
                    || SpecialExerciseTypeEnum.STROKE_WRITING.getCode().equals(vo.getType())) {
                group1.add(vo.getSpecialExerciseId());
            }
            if (SpecialExerciseTypeEnum.WORD_TO_SENTENCE.getCode().equals(vo.getType())
                    || SpecialExerciseTypeEnum.KARAOKE.getCode().equals(vo.getType())) {

                group2.add(vo.getSpecialExerciseId());
            }
        }

        if (CollUtil.isEmpty(group1) && CollUtil.isEmpty(group2)) {
            return;
        }

        List<ExerciseQuestionInfoRespVO> questionInfoList1 =
                specialExerciseService.getQuestionInfoList(group1, SpecialExerciseTypeEnum.WORD_MATCHING.getCode());

        List<ExerciseQuestionInfoRespVO> questionInfoList2 =
                specialExerciseService.getQuestionInfoList(group2, SpecialExerciseTypeEnum.WORD_TO_SENTENCE.getCode());

        List<ExerciseQuestionInfoRespVO> allQuestionInfoList = new ArrayList<>(questionInfoList1);
        allQuestionInfoList.addAll(questionInfoList2);

        Map<Long, List<String>> map = allQuestionInfoList.stream()
            .collect(Collectors.groupingBy(
                ExerciseQuestionInfoRespVO::getSpecialExerciseId,
                Collectors.mapping(
                    ExerciseQuestionInfoRespVO::getQuestionContent,
                    Collectors.toList()
                )
            ));

        // 设置每个专项练习对应的题目内容列表
        voList.forEach(vo ->
            vo.setQuestionList(map.getOrDefault(
                vo.getSpecialExerciseId(),
                Collections.emptyList()
            ))
        );
    }


    /**
     * 批量显示/隐藏专项练习
     */
    @CacheEvict(value = RedisKeyPrefix.SPECIAL_EXERCISE_AVAILABILITY, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateStatus(List<Long> ids, Integer isShow) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }

        isShow = IsShowEnum.validCodeOrDefault(isShow, IsShowEnum.SHOW);

        // 设置日志上下文变量
        String statusText = IsShowEnum.SHOW.getCode().equals(isShow) ? "显示" : "隐藏";
        LogRecordContext.putVariable("statusText", statusText);
        LogRecordContext.putVariable("updateCount", ids.size());
        LogRecordContext.putVariable("batchBizNo", ids.get(0).toString());

        specialExerciseService.lambdaUpdate()
                .in(SpecialExerciseDO::getId, ids)
                .set(SpecialExerciseDO::getIsShow, isShow)
                .set(SpecialExerciseDO::getUpdateTime, LocalDateTime.now())
                .set(SpecialExerciseDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                .update();
    }

    /**
     * 修改专项练习排序
     * <p>
     * 校验专项练习存在性后，依据新旧序号调整同等级专项练习顺序，并更新当前专项练习的序号。
     *
     * @param id      专项练习id
     * @param newSort 新排序
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSort(Long id, Integer newSort) {
        // 校验专项练习是否存在
        SpecialExerciseDO originalSpecialExercise = validateSpecialExerciseExists(id);

        Integer oldSort = originalSpecialExercise.getSort();
        Integer hskLevel = originalSpecialExercise.getHskLevel();

        // 设置日志上下文变量
        LogRecordContext.putVariable("oldSort", oldSort);
        LogRecordContext.putVariable("exercise", originalSpecialExercise);

        // 如果新旧序号相同，直接返回
        if (oldSort.equals(newSort)) {
            return;
        }

        // 调整同HSK等级下其他专项练习的序号
        if (oldSort < newSort) {
            // 旧序号小于新序号：将(旧序号+1)到新序号范围内的专项练习序号-1
            specialExerciseService.lambdaUpdate()
                    .eq(SpecialExerciseDO::getHskLevel, hskLevel)
                    .between(SpecialExerciseDO::getSort, oldSort + 1, newSort)
                    .setSql("sort = sort - 1")
                    .set(SpecialExerciseDO::getUpdateTime, LocalDateTime.now())
                    .set(SpecialExerciseDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                    .update();
        } else {
            // 旧序号大于新序号：将新序号到(旧序号-1)范围内的专项练习序号+1
            specialExerciseService.lambdaUpdate()
                    .eq(SpecialExerciseDO::getHskLevel, hskLevel)
                    .between(SpecialExerciseDO::getSort, newSort, oldSort - 1)
                    .setSql("sort = sort + 1")
                    .set(SpecialExerciseDO::getUpdateTime, LocalDateTime.now())
                    .set(SpecialExerciseDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                    .update();
        }

        // 更新当前专项练习的序号
        specialExerciseService.lambdaUpdate()
                .eq(SpecialExerciseDO::getId, id)
                .set(SpecialExerciseDO::getSort, newSort)
                .set(SpecialExerciseDO::getUpdateTime, LocalDateTime.now())
                .set(SpecialExerciseDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                .update();
    }

    /**
     * 导入专项练习
     *
     * @param file Excel文件
     * @param type 类型
     * @return 导入结果
     */
    public ImportResult importSpecialExercise(MultipartFile file, Integer type) {

        ImportResult result = new ImportResult();

        SpecialExerciseTypeEnum specialExerciseTypeEnum = SpecialExerciseTypeEnum.getByCode(type);
        if (specialExerciseTypeEnum == null) {
            result.setSuccess(false);
            result.setMsg(Collections.singletonList("导入失败：" + SPECIAL_EXERCISE_UNKNOWN_EXERCISE_TYPE.getMsg()));
            result.setInvalidCount(0);
            result.setValidCount(0);

            return result;
        }


        try {

            if (SpecialExerciseTypeEnum.WORD_MATCHING.equals(specialExerciseTypeEnum)) {
                // 创建数据处理监听器
                WordMatchingImportListener dataListener = new WordMatchingImportListener(specialExerciseService, wordApi, exerciseQuestionService, exerciseQuestionVersionService);

                // 创建组合监听器，限制最多导入500条数据
                CompositeRowLimitListener<WordMatchingImportExcelVO> compositeListener =
                        new CompositeRowLimitListener<>(500, dataListener,
                                "一次最多导入500条数据，请分批导入");

                // 执行导入，设置headRowNumber=1表示从第二行开始读取数据（第一行是表头）
                EasyExcel.read(file.getInputStream(), WordMatchingImportExcelVO.class, compositeListener)
                        .headRowNumber(2)
                        .sheet()
                        .doRead();

                result.setSuccess(dataListener.SUCCESS);
                result.setMsg(dataListener.msg);
                result.setInvalidCount(Math.max(dataListener.INVALID_COUNT, 0));
                result.setValidCount(dataListener.VALID_COUNT);

            } else if (SpecialExerciseTypeEnum.STROKE_WRITING.equals(specialExerciseTypeEnum)) {
                // 创建数据处理监听器
                StrokeWritingImportListener dataListener = new StrokeWritingImportListener(specialExerciseService, wordApi, exerciseQuestionService, exerciseQuestionVersionService);

                // 创建组合监听器，限制最多导入500条数据
                CompositeRowLimitListener<StrokeWritingImportExcelVO> compositeListener =
                        new CompositeRowLimitListener<>(500, dataListener,
                                "一次最多导入500条数据，请分批导入");

                // 执行导入，设置headRowNumber=1表示从第二行开始读取数据（第一行是表头）
                EasyExcel.read(file.getInputStream(), StrokeWritingImportExcelVO.class, compositeListener)
                        .headRowNumber(2)
                        .sheet()
                        .doRead();

                result.setSuccess(dataListener.SUCCESS);
                result.setMsg(dataListener.msg);
                result.setInvalidCount(Math.max(dataListener.INVALID_COUNT, 0));
                result.setValidCount(dataListener.VALID_COUNT);
            } else if (SpecialExerciseTypeEnum.WORD_TO_SENTENCE.equals(specialExerciseTypeEnum)) {
                // 创建数据处理监听器
                WordToSentenceImportListener dataListener = new WordToSentenceImportListener(specialExerciseService, gameQuestionService, exerciseQuestionService, exerciseQuestionVersionService);

                // 创建组合监听器，限制最多导入500条数据
                CompositeRowLimitListener<WordToSentenceImportExcelVO> compositeListener =
                        new CompositeRowLimitListener<>(500, dataListener,
                                "一次最多导入500条数据，请分批导入");

                // 执行导入，设置headRowNumber=1表示从第二行开始读取数据（第一行是表头）
                EasyExcel.read(file.getInputStream(), WordToSentenceImportExcelVO.class, compositeListener)
                        .headRowNumber(2)
                        .sheet()
                        .doRead();

                result.setSuccess(dataListener.SUCCESS);
                result.setMsg(dataListener.msg);
                result.setInvalidCount(Math.max(dataListener.INVALID_COUNT, 0));
                result.setValidCount(dataListener.VALID_COUNT);
            } else if (SpecialExerciseTypeEnum.KARAOKE.equals(specialExerciseTypeEnum)) {
                // 创建数据处理监听器
                KaraokeImportListener dataListener = new KaraokeImportListener(specialExerciseService, gameQuestionService, exerciseQuestionService, exerciseQuestionVersionService);

                // 创建组合监听器，限制最多导入500条数据
                CompositeRowLimitListener<KaraokeImportExcelVO> compositeListener =
                        new CompositeRowLimitListener<>(500, dataListener,
                                "一次最多导入500条数据，请分批导入");

                // 执行导入，设置headRowNumber=1表示从第二行开始读取数据（第一行是表头）
                EasyExcel.read(file.getInputStream(), KaraokeImportExcelVO.class, compositeListener)
                        .headRowNumber(2)
                        .sheet()
                        .doRead();

                result.setSuccess(dataListener.SUCCESS);
                result.setMsg(dataListener.msg);
                result.setInvalidCount(Math.max(dataListener.INVALID_COUNT, 0));
                result.setValidCount(dataListener.VALID_COUNT);
            } else {
                result.setSuccess(false);
                result.setMsg(Collections.singletonList("导入失败：未找到导入监听器"));
                result.setInvalidCount(0);
                result.setValidCount(0);

            }

        } catch (ExcelAnalysisException e) {
            log.warn("导入专项练习受限: {}", e.getMessage());
            result.setSuccess(false);
            result.setMsg(Collections.singletonList(e.getMessage()));
            result.setInvalidCount(0);
            result.setValidCount(0);
        } catch (Exception e) {
            log.error("导入专项练习失败: {}", e.getMessage(), e);
            result.setSuccess(false);
            result.setMsg(Collections.singletonList("导入失败：" + e.getMessage()));
            result.setInvalidCount(0);
            result.setValidCount(0);
        }

        return result;

    }

    /**
     * 检查专项练习是否被引用
     *
     * @param ids 专项练习ID列表
     * @return 是否被引用
     */
    private boolean checkSpecialExerciseReferenced(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }

        try {
            List<InteractiveCourseBaseInfoRespDTO> referenceMap = interactiveCourseApi.listByResourceIdList(ids);
            return CollUtil.isNotEmpty(referenceMap);
        } catch (Exception e) {
            log.error("检查专项练习引用关系失败: {}", e.getMessage(), e);
            // 如果检查失败，为了安全起见，认为被引用了
            return true;
        }
    }

    /**
     * 检查专项练习是否可以删除
     */
    public SpecialExerciseDeleteCheckRespVO checkSpecialExerciseCanDelete(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new SpecialExerciseDeleteCheckRespVO(true);
        }

        boolean isReferenced = checkSpecialExerciseReferenced(ids);
        return new SpecialExerciseDeleteCheckRespVO(!isReferenced);
    }
}