package com.xt.hsk.module.game.listener.importtask;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.idev.excel.context.AnalysisContext;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.enums.IsShowEnum;
import com.xt.hsk.module.game.controller.admin.question.vo.KaraokeQuestionImportExcelVO;
import com.xt.hsk.module.game.convert.question.GameQuestionConvert;
import com.xt.hsk.module.game.dal.dataobject.question.GameQuestionDO;
import com.xt.hsk.module.game.dal.dataobject.question.GameQuestionVersionDO;
import com.xt.hsk.module.game.enums.question.GameQuestionTypeEnum;
import com.xt.hsk.module.game.service.question.GameQuestionService;
import com.xt.hsk.module.game.service.question.GameQuestionVersionService;
import com.xt.hsk.module.game.util.PinyinUtil;
import com.xt.hsk.module.infra.listener.BaseAnalysisEventListener;
import com.xt.hsk.module.thirdparty.api.YouDaoApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 卡拉OK题目导入监听器
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Slf4j
public class KaraokeQuestionImportListener extends
    BaseAnalysisEventListener<KaraokeQuestionImportExcelVO> {

    private final List<KaraokeQuestionImportExcelVO> voList = new ArrayList<>();

    private final GameQuestionService gameQuestionService;

    private final GameQuestionVersionService gameQuestionVersionService;

    private final YouDaoApi youDaoApi;

    public KaraokeQuestionImportListener(GameQuestionService gameQuestionService,
                                         GameQuestionVersionService gameQuestionVersionService,
                                         YouDaoApi youDaoApi) {
        this.gameQuestionService = gameQuestionService;
        this.gameQuestionVersionService = gameQuestionVersionService;
        this.youDaoApi = youDaoApi;
    }

    @Override
    public void invoke(KaraokeQuestionImportExcelVO data, AnalysisContext context) {
        SUCCESS = false;

        // 行号 + 1是因为索引从0开始
        int rowIndex = context.readRowHolder().getRowIndex() + 1;

        try {
            if (Objects.isNull(data.getHskLevel())) {
                msg.add(String.format("第%d行：HSK等级不能为空", rowIndex));
            }

            Integer hskLevel = getHskCodeByLevel(data.getHskLevel());
            if (hskLevel == null) {
                msg.add(String.format("第%d行：HSK等级不存在", rowIndex));
            }

            if (CharSequenceUtil.isBlank(data.getQuestionContent())) {
                msg.add(String.format("第%d行：题干不能为空", rowIndex));
            }

            voList.add(data);

        } catch (Exception e) {
            // 记录未预期的异常
            log.error("处理第{}行数据时发生异常", rowIndex, e);
            msg.add(String.format("第%d行：处理失败，原因：%s", rowIndex, e.getMessage()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (CollUtil.isEmpty(voList)) {
            msg.add("未读取到数据");
            return;
        }

        if (CollUtil.isNotEmpty(msg)){
            return;
        }

        List<GameQuestionDO> allDoList = new ArrayList<>();

        // 将voList分为50条数据一组
        List<List<KaraokeQuestionImportExcelVO>> split = CollUtil.split(voList, 50);
        for (List<KaraokeQuestionImportExcelVO> importVOList : split) {
            List<GameQuestionDO> doList = importVOList.stream().map(vo -> {
                        GameQuestionDO question = new GameQuestionDO();
                        question.setHskLevel(getHskCodeByLevel(vo.getHskLevel()));
                        question.setType(GameQuestionTypeEnum.KARAOKE.getCode());
                        question.setQuestionContent(vo.getQuestionContent());
                        question.setPinyin(PinyinUtil.generatePinyin(vo.getQuestionContent(), GameQuestionTypeEnum.KARAOKE));
                        question.setIsShow(IsShowEnum.SHOW.getCode());
                        question.setLatestVersion(1);
                        return question;
                    })
                    .toList();
            gameQuestionService.saveBatch(doList);
            allDoList.addAll(doList);

            List<GameQuestionVersionDO> questionVersionList = doList.stream().map(question -> {
                        GameQuestionVersionDO questionVersion = GameQuestionConvert.INSTANCE.doToVersionDo(question);
                        questionVersion.setId(null);
                        return questionVersion;
                    })
                    .toList();

            gameQuestionVersionService.saveBatch(questionVersionList);

        }

        VALID_COUNT = voList.size();
        SUCCESS = true;

        // 异步生成音频
        CompletableFuture.runAsync(() -> {
            try {
                generateAudioUrl(allDoList);
            } catch (Exception e) {
                log.error("导入卡拉OK题目,异步生成音频失败,", e);
            }
        });
    }

    /**
     * 生成音频
     */
    private void generateAudioUrl(List<GameQuestionDO> allDoList) {
        if (CollUtil.isEmpty(allDoList)) {
            return;
        }

        log.info("导入卡拉OK题目,开始生成音频");
        for (GameQuestionDO question : allDoList) {

            try {
                String audioUrl = getAudioUrl(question.getQuestionContent());
                if (CharSequenceUtil.isNotBlank(audioUrl)) {
                    gameQuestionService.lambdaUpdate()
                            .set(GameQuestionDO::getAudioUrl, audioUrl)
                            .eq(GameQuestionDO::getId, question.getId())
                            .update();

                    gameQuestionVersionService.lambdaUpdate()
                            .set(GameQuestionVersionDO::getAudioUrl, audioUrl)
                            .eq(GameQuestionVersionDO::getQuestionId, question.getId())
                            .eq(GameQuestionVersionDO::getType, question.getType())
                            .eq(GameQuestionVersionDO::getVersion, question.getLatestVersion())
                            .update();
                }
            } catch (Exception e) {
                log.error("导入卡拉OK题目,更新音频失败,题目id:{}", question.getId(), e);
            }
        }

        log.info("导入卡拉OK题目,生成音频完成");

    }

    /**
     * 获取音频 URL
     *
     * @param questionContent 题目内容
     * @return 字符串
     */
    private String getAudioUrl(String questionContent) {

        if (CharSequenceUtil.isBlank(questionContent)) {
            return null;
        }

        try {
            return youDaoApi.getAudioUrl(questionContent, IdUtil.simpleUUID());
        } catch (Exception e) {
            log.error("导入卡拉OK题目,获取音频失败", e);
        }

        return null;
    }

    private Integer getHskCodeByLevel(Integer hskLevel) {
        if (hskLevel == 0) {
            return HskEnum.HSK_0.getCode();
        }
        if (hskLevel == 1) {
            return HskEnum.HSK_1.getCode();
        }
        if (hskLevel == 2) {
            return HskEnum.HSK_2.getCode();
        }
        if (hskLevel == 3) {
            return HskEnum.HSK_3.getCode();
        }
        if (hskLevel == 4) {
            return HskEnum.HSK_4.getCode();
        }
        if (hskLevel == 5) {
            return HskEnum.HSK_5.getCode();
        }
        if (hskLevel == 6) {
            return HskEnum.HSK_6.getCode();
        }
        return null;
    }
} 