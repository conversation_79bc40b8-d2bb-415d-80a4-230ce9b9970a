
package com.xt.hsk.module.game.manager.favorite.app;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.xt.hsk.module.edu.api.word.WordApi;
import com.xt.hsk.module.edu.api.word.dto.WordRespDTO;
import com.xt.hsk.module.game.controller.app.favorite.vo.GameFavoriteWordStatusReqVO;
import com.xt.hsk.module.game.controller.app.favorite.vo.GameFavoriteWordStatusRespVO;
import com.xt.hsk.module.user.favorite.UserFavoriteApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 游戏收藏 Manager
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Slf4j
@Component
public class GameFavoriteAppManager {

    @Resource
    private WordApi wordApi;

    @Resource
    private UserFavoriteApi userFavoriteApi;

    /**
     * 查询字词收藏状态
     *
     * @param reqVO 请求参数
     * @return 收藏状态
     */
    public GameFavoriteWordStatusRespVO checkWordFavoriteStatus(GameFavoriteWordStatusReqVO reqVO) {

        if (CharSequenceUtil.isEmpty(reqVO.getWord()) || CharSequenceUtil.isEmpty(reqVO.getPinyin())) {
            return GameFavoriteWordStatusRespVO.notExist();
        }

        // 获取当前登录用户ID
        Long userId = StpUtil.getLoginIdAsLong();

        // 通过字词和拼音查询ID
        WordRespDTO wordRespDTO = wordApi.getWordByName(reqVO.getWord(), reqVO.getPinyin());
        if (wordRespDTO == null) {
            return GameFavoriteWordStatusRespVO.notExist();
        }

        // 查询收藏状态
        Boolean isFavorited = userFavoriteApi.checkUserFavoriteStatus(userId, wordRespDTO.getId(), 1);

        return new GameFavoriteWordStatusRespVO(Boolean.TRUE.equals(isFavorited) ? 1 : 0);
    }

}