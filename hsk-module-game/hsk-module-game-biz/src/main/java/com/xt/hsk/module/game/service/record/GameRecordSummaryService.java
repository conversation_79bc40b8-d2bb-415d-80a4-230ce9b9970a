package com.xt.hsk.module.game.service.record;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.game.dal.dataobject.record.GameRecordSummaryDO;

import java.util.List;

/**
 * 专项练习记录汇总 Service 接口
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface GameRecordSummaryService extends IService<GameRecordSummaryDO> {

    /**
     * 批量查询用户在指定专项练习中的最新记录
     *
     * @param specialExerciseIdList 专项练习ID列表
     * @param userId                用户ID
     * @return 每个专项练习的最新记录列表
     */
    List<GameRecordSummaryDO> getLatestRecords(List<Long> specialExerciseIdList, Long userId);

    /**
     * 批量查询用户在指定专项练习中的最新记录
     *
     * @param userId         用户 ID
     * @param practiceStatus 练习状态 1 进行中 2 已完成 3未开始
     * @return 每个专项练习的最新记录列表
     */
    List<GameRecordSummaryDO> getLatestByUserIdAndPracticeStatus(Long userId, Integer practiceStatus);
}