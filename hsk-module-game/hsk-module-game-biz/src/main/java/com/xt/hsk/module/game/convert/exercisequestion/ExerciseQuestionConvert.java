package com.xt.hsk.module.game.convert.exercisequestion;

import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionAppRespVO;
import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 专项练习组转换类
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Mapper
public interface ExerciseQuestionConvert {

    ExerciseQuestionConvert INSTANCE = Mappers.getMapper(ExerciseQuestionConvert.class);

    /**
     * 响应类vo类转app响应vo类
     */
    ExerciseQuestionAppRespVO respVoToAppRespVo(ExerciseQuestionRespVO vo);

    /**
     * 响应类vo类列表转app响应vo类列表
     */
    List<ExerciseQuestionAppRespVO> respVoListToAppRespVoList(List<ExerciseQuestionRespVO> voList);
}
