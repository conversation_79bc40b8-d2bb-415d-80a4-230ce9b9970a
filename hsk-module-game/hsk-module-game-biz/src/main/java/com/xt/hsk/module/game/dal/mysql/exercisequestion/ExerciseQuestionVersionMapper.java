package com.xt.hsk.module.game.dal.mysql.exercisequestion;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionRespVO;
import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionVersionPageReqVO;
import com.xt.hsk.module.game.dal.dataobject.exercisequestion.ExerciseQuestionVersionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 游戏-练习组题目版本库 Mapper
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Mapper
public interface ExerciseQuestionVersionMapper extends BaseMapperX<ExerciseQuestionVersionDO> {

    default PageResult<ExerciseQuestionVersionDO> selectPage(ExerciseQuestionVersionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ExerciseQuestionVersionDO>()
            .eqIfPresent(ExerciseQuestionVersionDO::getSpecialExerciseId,
                reqVO.getSpecialExerciseId())
                .eqIfPresent(ExerciseQuestionVersionDO::getQuestionId, reqVO.getQuestionId())
                .eqIfPresent(ExerciseQuestionVersionDO::getTranslationOt, reqVO.getTranslationOt())
                .eqIfPresent(ExerciseQuestionVersionDO::getIsShow, reqVO.getIsShow())
                .eqIfPresent(ExerciseQuestionVersionDO::getSort, reqVO.getSort())
                .eqIfPresent(ExerciseQuestionVersionDO::getShowRound, reqVO.getShowRound())
                .eqIfPresent(ExerciseQuestionVersionDO::getRoundNumber, reqVO.getRoundNumber())
                .eqIfPresent(ExerciseQuestionVersionDO::getVersion, reqVO.getVersion())
                .eqIfPresent(ExerciseQuestionVersionDO::getType, reqVO.getType())
                .betweenIfPresent(ExerciseQuestionVersionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ExerciseQuestionVersionDO::getId));
    }

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<GameExerciseQuestionVersion> 实例对象列表
     * @return 影响行数
     */
    int saveBatch(@Param("entities") List<ExerciseQuestionVersionDO> entities);

    /**
     * 获取专项练习组题目列表
     *
     * @param specialExerciseId 专项练习 ID
     * @param type              类型
     * @param version           版本
     * @return 练习组的题目列表
     */
    List<ExerciseQuestionRespVO> getExerciseQuestion(@Param("specialExerciseId") Long specialExerciseId,
                                                     @Param("type") Integer type,
                                                     @Param("version") Integer version);

    /**
     * 根据题目版本ID列表和类型获取练习题目
     *
     * @param exerciseQuestionVersionId 练习题目版本ID
     * @param type                      题目类型
     * @return 练习题目
     */
    ExerciseQuestionRespVO getExerciseQuestionById(@Param("id") Long exerciseQuestionVersionId,
                                                   @Param("type") Integer type);

    /**
     * 根据题目版本ID列表和类型获取练习题目
     *
     * @param exerciseQuestionVersionIdList 练习题目版本ID列表
     * @param type                          题目类型
     * @return 练习题目列表
     */
    List<ExerciseQuestionRespVO> getExerciseQuestionByIdList(@Param("exerciseQuestionVersionIdList") List<Long> exerciseQuestionVersionIdList,
                                                             @Param("type") Integer type);
}