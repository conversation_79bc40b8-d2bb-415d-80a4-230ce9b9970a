package com.xt.hsk.module.game.enums.record;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 练习答案是否正确的枚举
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Getter
@AllArgsConstructor
public enum GameRecordCorrectTypeEnum implements BasicEnum<Integer>, VO {
    /**
     * 练习答案是否正确的枚举
     */
    ERROR(0, "错误"),
    CORRECT(1, "正确"),
    ;

    private final Integer code;
    private final String desc;

    /**
     * 根据练习答案是否正确码获取对应的描述信息
     *
     * @param code 练习答案是否正确码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (GameRecordCorrectTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据练习答案是否正确码获取对应的枚举实例
     *
     * @param code 练习答案是否正确码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static GameRecordCorrectTypeEnum getByCode(Integer code) {
        for (GameRecordCorrectTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
