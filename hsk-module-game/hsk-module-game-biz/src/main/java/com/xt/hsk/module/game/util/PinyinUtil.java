package com.xt.hsk.module.game.util;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ReUtil;
import com.rnkrsoft.bopomofo4j.Bopomofo4j;
import com.xt.hsk.module.game.enums.question.GameQuestionTypeEnum;

public class PinyinUtil {

    /**
     * 根据题目类型生成拼音
     *
     * @param content      内容
     * @param questionType 题目类型枚举
     * @return 拼音
     */
    public static String generatePinyin(String content, GameQuestionTypeEnum questionType) {

        return switch (questionType) {
            case WORD_TO_SENTENCE -> generatePinyinFromSplit(content);
            case KARAOKE -> generatePinyinFromContent(content);
            default -> null;
        };
    }

    /**
     * 从 questionSplit 字段中提取有效字符并生成拼音。
     *
     * @param questionSplit 拆分后的题干
     * @return 拼音
     */
    private static String generatePinyinFromSplit(String questionSplit) {
        if (CharSequenceUtil.isNotBlank(questionSplit)) {
            String filtered = extractCharacters(questionSplit, "[\\u4e00-\\u9fa5a-zA-Z0-9/]");
            if (CharSequenceUtil.isNotBlank(filtered)) {
                Bopomofo4j.local();
                String pinyin = Bopomofo4j.pinyin(filtered, 0, false, false, " ");
                return pinyin.replaceAll("/\\s+", "/");
            }
        }
        return null;
    }

    /**
     * 从 questionContent 字段中提取有效字符并生成拼音。
     *
     * @param questionContent 题目内容
     * @return 拼音
     */
    private static String generatePinyinFromContent(String questionContent) {
        if (CharSequenceUtil.isNotBlank(questionContent)) {
            String filtered = extractCharacters(questionContent, "[\\u4e00-\\u9fa5a-zA-Z0-9]");
            Bopomofo4j.local();
            return Bopomofo4j.pinyin(filtered, 0, false, false, "/");
        }
        return null;
    }

    /**
     * 提取文本中所有匹配的字符并拼接为一个新字符串。
     *
     * @param text  要处理的原始文本
     * @param regex 用于提取的正则表达式
     * @return 提取并拼接后的字符串
     */
    private static String extractCharacters(String text, String regex) {
        return ReUtil.findAll(regex, text, 0).stream().reduce("", String::concat);
    }
}
