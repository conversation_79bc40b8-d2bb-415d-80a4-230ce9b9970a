package com.xt.hsk.module.game.service.exercisequestion;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionPageReqVO;
import com.xt.hsk.module.game.dal.dataobject.exercisequestion.ExerciseQuestionDO;
import java.util.List;
import java.util.Map;

/**
 * 游戏-练习组题目 Service 接口
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
public interface ExerciseQuestionService extends IService<ExerciseQuestionDO> {
    PageResult<ExerciseQuestionDO> selectPage(ExerciseQuestionPageReqVO pageReqVO);

    /**
     * 批量新增数据
     *
     * @param doList 实例对象列表
     */
    void saveBatch(List<ExerciseQuestionDO> doList);

    /**
     * 题目引用次数列表map
     *
     * @param questionIdList 题目ID列表
     * @param type 类型
     * @return 题目引用次数列表map
     */
    Map<Long, Integer> referenceCountMap(List<Long> questionIdList, Integer type);

    int isQuoteWord(Long wordId);
}