package com.xt.hsk.module.game.dal.dataobject.question;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 游戏-题库版本库 DO
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@TableName("game_question_version")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GameQuestionVersionDO extends BaseDO {

    /**
     * 题库ID
     */
    @TableId
    private Long id;
    /**
     * HSK等级
     */
    private Integer hskLevel;
    /**
     * 题目类型
     */
    private Integer type;
    /**
     * 题目ID
     */
    private Long questionId;
    /**
     * 题干内容
     */
    private String questionContent;
    /**
     * 拆分后的题干
     */
    private String questionSplit;
    /**
     * 题干的拼音
     */
    private String pinyin;
    /**
     * 音频链接
     */
    private String audioUrl;
    /**
     * 考察点
     */
    private String knowledgePoint;
    /**
     * 考察点拼音
     */
    private String knowledgePointPinyin;
    /**
     * 参考答案
     */
    private String referenceAnswer;
    /**
     * 版本号
     */
    private Integer version;
    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;

}