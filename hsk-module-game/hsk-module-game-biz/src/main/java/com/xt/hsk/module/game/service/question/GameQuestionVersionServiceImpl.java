package com.xt.hsk.module.game.service.question;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.module.game.dal.dataobject.question.GameQuestionVersionDO;
import com.xt.hsk.module.game.dal.mysql.question.GameQuestionVersionMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;


/**
 * 游戏-题库版本库 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Service
public class GameQuestionVersionServiceImpl extends ServiceImpl<GameQuestionVersionMapper, GameQuestionVersionDO> implements GameQuestionVersionService {

    @Resource
    private GameQuestionVersionMapper gameQuestionVersionMapper;

}