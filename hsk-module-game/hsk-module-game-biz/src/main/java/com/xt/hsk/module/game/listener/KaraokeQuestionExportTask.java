package com.xt.hsk.module.game.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xt.hsk.module.infra.api.export.ExportTaskParams;
import com.xt.hsk.module.infra.listener.BaseExportTask;
import jakarta.annotation.Resource;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 卡拉OK导出任务
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Slf4j
@Component("karaokeQuestionExportTask")
public class KaraokeQuestionExportTask extends BaseExportTask<KaraokeQuestionExportTaskExport> {

    @Resource
    private KaraokeQuestionExportTaskExport karaokeQuestionExportTaskExport;

    @Override
    protected KaraokeQuestionExportTaskExport getExporter() {
        return karaokeQuestionExportTaskExport;
    }

    @Override
    protected String getFileName() {
        return "卡拉OK题目数据";
    }

    @Override
    protected Map<String, Object> buildQueryParams(String params) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            ExportTaskParams taskParams = objectMapper.readValue(params, ExportTaskParams.class);

            // 返回查询参数，任务名称已经在创建任务时使用，这里不需要处理
            return objectMapper.convertValue(taskParams.getQueryParams(),
                    new TypeReference<Map<String, Object>>() {
                    });
        } catch (Exception e) {
            log.error("解析导出参数失败: {}", e.getMessage(), e);
            return Map.of();
        }
    }
} 