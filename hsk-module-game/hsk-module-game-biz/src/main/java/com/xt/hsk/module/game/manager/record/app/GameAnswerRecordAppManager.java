package com.xt.hsk.module.game.manager.record.app;

import static com.xt.hsk.framework.common.constants.RedisKeyPrefix.SPECIAL_EXERCISE_KARAOKE_USAGE_COUNT;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.LOCKED;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.game.enums.ErrorCodeConstants.GAME_QUESTION_NOT_EXISTS;
import static com.xt.hsk.module.game.enums.ErrorCodeConstants.GAME_QUESTION_NOT_FINISHED;
import static com.xt.hsk.module.game.enums.ErrorCodeConstants.GAME_RECORD_AUDIO_PROCESSING_ERROR;
import static com.xt.hsk.module.game.enums.ErrorCodeConstants.GAME_RECORD_NOT_EXISTS;
import static com.xt.hsk.module.game.enums.ErrorCodeConstants.SPECIAL_EXERCISE_NOT_EXISTS;
import static com.xt.hsk.module.game.enums.ErrorCodeConstants.SPECIAL_EXERCISE_UNKNOWN_EXERCISE_TYPE;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.xt.hsk.framework.common.enums.IsEnum;
import com.xt.hsk.framework.redis.utils.RedisUtil;
import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionRespVO;
import com.xt.hsk.module.game.controller.app.record.vo.GameAnswerRecordAppReqVO;
import com.xt.hsk.module.game.controller.app.record.vo.GameAnswerRecordAppRespVO;
import com.xt.hsk.module.game.controller.app.record.vo.GameAnswerRecordDetailsAppReqVO;
import com.xt.hsk.module.game.controller.app.record.vo.GameKaraokeAnswerRecordAppRespVO;
import com.xt.hsk.module.game.convert.record.GameRecordDetailsConvert;
import com.xt.hsk.module.game.dal.dataobject.record.GameRecordDetailsDO;
import com.xt.hsk.module.game.dal.dataobject.record.GameRecordSummaryDO;
import com.xt.hsk.module.game.dal.dataobject.specialexercise.SpecialExerciseDO;
import com.xt.hsk.module.game.enums.record.GameAnswerStatusEnum;
import com.xt.hsk.module.game.enums.record.GameRecordCorrectTypeEnum;
import com.xt.hsk.module.game.enums.record.GameRecordPracticeStatusEnum;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import com.xt.hsk.module.game.manager.correction.app.GameCorrectionAppManager;
import com.xt.hsk.module.game.producer.game.GameProducer;
import com.xt.hsk.module.game.service.exercisequestion.ExerciseQuestionService;
import com.xt.hsk.module.game.service.exercisequestion.ExerciseQuestionVersionService;
import com.xt.hsk.module.game.service.record.GameRecordDetailsService;
import com.xt.hsk.module.game.service.record.GameRecordSummaryService;
import com.xt.hsk.module.game.service.specialexercise.SpecialExerciseService;
import com.xt.hsk.module.infra.api.file.FileApi;
import com.xt.hsk.module.thirdparty.api.ShengTongApi;
import com.xt.hsk.module.thirdparty.api.aicorrectiontimes.AiCorrectionTimesApi;
import com.xt.hsk.module.thirdparty.dto.ShengTong.ShengTongCallDto;
import com.xt.hsk.module.thirdparty.dto.ShengTong.ShengTongRespDto;
import com.xt.hsk.module.thirdparty.dto.aiCorrection.AiCorrectionTimesSaveReqDto;
import com.xt.hsk.module.thirdparty.enums.AiCorrectBizTypeEnum;
import jakarta.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * 游戏作答记录 app manager
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Component
public class GameAnswerRecordAppManager {


    @Resource
    private GameRecordSummaryService gameRecordSummaryService;

    @Resource
    private GameRecordDetailsService gameRecordDetailsService;

    @Resource
    private ExerciseQuestionVersionService exerciseQuestionVersionService;

    @Resource
    private SpecialExerciseService specialExerciseService;

    @Resource
    private ShengTongApi shengTongApi;

    @Resource
    private FileApi fileApi;

    @Resource
    private ExerciseQuestionService exerciseQuestionService;

    @Resource
    private GameProducer gameProducer;

    @Resource
    private GameCorrectionAppManager gameCorrectionAppManager;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private AiCorrectionTimesApi aiCorrectionTimesApi;

    /**
     * 提交答案
     *
     * @param reqVO 请求参数
     * @return 提交结果
     */
    @Transactional(rollbackFor = Exception.class)
    public GameAnswerRecordAppRespVO submitAnswer(GameAnswerRecordAppReqVO reqVO) {
        Long userId = StpUtil.getLoginIdAsLong();

        // 基础验证
        validateSubmitRequest(reqVO);

        // 获取并验证相关数据
        SpecialExerciseDO specialExercise = validateAndGetSpecialExercise(reqVO);
        GameRecordSummaryDO recordSummary = validateAndGetRecordSummary(reqVO.getRecordSummaryId());

        // 处理答题详情
        if (CollUtil.isNotEmpty(reqVO.getAnswerDetailsList())) {
            processAnswerDetails(reqVO.getAnswerDetailsList(), reqVO.getCurrentRound(), specialExercise, recordSummary, userId);
        }

        // 处理完成状态
        if (IsEnum.YES.getCode().equals(reqVO.getIsCompleted()) &&
            !GameRecordPracticeStatusEnum.COMPLETED.getCode().equals(recordSummary.getPracticeStatus())) {
            handleCompletedStatus(recordSummary.getId(), reqVO.getInteractiveCourseUnitId());
        }

        return new GameAnswerRecordAppRespVO().setRecordSummaryId(recordSummary.getId());
    }

    /**
     * 验证提交请求的基础参数
     */
    private void validateSubmitRequest(GameAnswerRecordAppReqVO reqVO) {
        List<GameAnswerRecordDetailsAppReqVO> answerDetailsList = reqVO.getAnswerDetailsList();
        if (CollUtil.isEmpty(answerDetailsList) && Objects.equals(reqVO.getIsCompleted(), 0)) {
            throw exception(GAME_QUESTION_NOT_FINISHED);
        }
    }

    /**
     * 验证并获取专项练习信息
     */
    private SpecialExerciseDO validateAndGetSpecialExercise(GameAnswerRecordAppReqVO reqVO) {
        SpecialExerciseTypeEnum specialExerciseTypeEnum = SpecialExerciseTypeEnum.getByCode(reqVO.getType());
        if (specialExerciseTypeEnum == null) {
            throw exception(SPECIAL_EXERCISE_UNKNOWN_EXERCISE_TYPE);
        }

        Long specialExerciseId = reqVO.getSpecialExerciseId();
        SpecialExerciseDO specialExercise = specialExerciseService.getById(specialExerciseId);
        if (specialExercise == null) {
            throw exception(SPECIAL_EXERCISE_NOT_EXISTS);
        }

        return specialExercise;
    }

    /**
     * 验证并获取记录汇总信息
     */
    private GameRecordSummaryDO validateAndGetRecordSummary(Long recordSummaryId) {
        GameRecordSummaryDO recordSummary = gameRecordSummaryService.getById(recordSummaryId);
        if (recordSummary == null) {
            throw exception(GAME_RECORD_NOT_EXISTS);
        }
        return recordSummary;
    }

    /**
     * 处理答题详情
     */
    private void processAnswerDetails(List<GameAnswerRecordDetailsAppReqVO> answerDetailsList,
                                      Integer currentRound,
                                      SpecialExerciseDO specialExercise,
                                      GameRecordSummaryDO recordSummary,
                                      Long userId) {
        // 如果答题列表为空，直接返回
        if (CollUtil.isEmpty(answerDetailsList)) {
            return;
        }

        // 按题目版本ID分组
        Map<Long, List<GameAnswerRecordDetailsAppReqVO>> reqMap = answerDetailsList.stream()
                .collect(Collectors.groupingBy(GameAnswerRecordDetailsAppReqVO::getExerciseQuestionVersionId));

        Integer exerciseType = recordSummary.getType();

        // 处理单词连连看类型的逻辑
        if (SpecialExerciseTypeEnum.WORD_MATCHING.getCode().equals(exerciseType)) {
            processWordMatchingExercise(reqMap, currentRound, specialExercise, recordSummary, userId);
            return;
        }

        // 处理笔画书写类型的逻辑
        if (SpecialExerciseTypeEnum.STROKE_WRITING.getCode().equals(exerciseType)) {
            processStrokeWritingExercise(reqMap, currentRound, specialExercise, recordSummary, userId);
            return;
        }

        // 处理其他类型的逻辑
        processOtherExercise(reqMap, currentRound, specialExercise, recordSummary, userId);
    }

    /**
     * 处理单词连连看类型的逻辑
     */
    private void processWordMatchingExercise(Map<Long, List<GameAnswerRecordDetailsAppReqVO>> reqMap,
                                             Integer currentRound,
                                             SpecialExerciseDO specialExercise,
                                             GameRecordSummaryDO recordSummary,
                                             Long userId) {
        int correctQuestions = 0;
        int wrongQuestions = 0;

        for (Map.Entry<Long, List<GameAnswerRecordDetailsAppReqVO>> entry : reqMap.entrySet()) {

            boolean isCorrect = entry.getValue().stream()
                    .filter(vo -> Objects.equals(vo.getAnswerSequence(), 1))
                    .min(Comparator.comparing(GameAnswerRecordDetailsAppReqVO::getAnswerTime))
                    .map(vo -> GameRecordCorrectTypeEnum.CORRECT.getCode().equals(vo.getIsCorrect()))
                    .orElse(false);

            if (isCorrect) {
                correctQuestions++;
            } else {
                wrongQuestions++;
            }
        }

        // 批量保存答题记录
        List<GameRecordDetailsDO> recordDetailsList = createRecordDetailsList(
                reqMap, specialExercise.getId(), specialExercise.getType(), userId, recordSummary.getId());
        gameRecordDetailsService.saveBatch(recordDetailsList);

        // 更新统计信息
        updateRecordSummaryStatistics(recordSummary, correctQuestions, wrongQuestions, currentRound);
    }

    /**
     * 处理笔画书写类型的练习
     */
    private void processStrokeWritingExercise(Map<Long, List<GameAnswerRecordDetailsAppReqVO>> reqMap,
                                              Integer currentRound,
                                         SpecialExerciseDO specialExercise,
                                         GameRecordSummaryDO recordSummary,
                                         Long userId) {

        // 批量保存答题记录
        List<GameRecordDetailsDO> newRecords = createRecordDetailsList(
                reqMap, specialExercise.getId(), specialExercise.getType(), userId, recordSummary.getId());
        gameRecordDetailsService.saveBatch(newRecords);

        // 查询已存在的记录
        List<GameRecordDetailsDO> existingRecords = gameRecordDetailsService.lambdaQuery()
                .eq(GameRecordDetailsDO::getRecordSummaryId, recordSummary.getId())
                .list();

        long count = existingRecords.stream()
                .map(GameRecordDetailsDO::getExerciseQuestionVersionId)
                .filter(Objects::nonNull)
                .distinct()
                .count();

        Integer correctQuestions = Math.toIntExact(count);

        // 更新统计信息
        int totalUnansweredQuestions = recordSummary.getTotalQuestions() - correctQuestions;

        BigDecimal progress = calculateProgressPercentage(correctQuestions, recordSummary.getTotalQuestions());

        gameRecordSummaryService.lambdaUpdate()
                .eq(GameRecordSummaryDO::getId, recordSummary.getId())
                .set(GameRecordSummaryDO::getAnsweredQuestions, correctQuestions)
                .set(GameRecordSummaryDO::getCorrectQuestions, correctQuestions)
                .set(GameRecordSummaryDO::getWrongQuestions, 0)
                .set(GameRecordSummaryDO::getUnansweredQuestions, totalUnansweredQuestions)
                .set(GameRecordSummaryDO::getProgress, progress)
                .set(GameRecordSummaryDO::getCurrentRound, currentRound)
                .update();
    }

    /**
     * 处理其他类型的逻辑
     */
    private void processOtherExercise(Map<Long, List<GameAnswerRecordDetailsAppReqVO>> reqMap,
                                      Integer currentRound,
                                      SpecialExerciseDO specialExercise,
                                      GameRecordSummaryDO recordSummary,
                                      Long userId) {

        // 批量保存答题记录
        List<GameRecordDetailsDO> newRecords = createRecordDetailsList(
                reqMap, specialExercise.getId(), specialExercise.getType(), userId, recordSummary.getId());
        gameRecordDetailsService.saveBatch(newRecords);

        // 查询已存在的记录
        Map<Long, List<GameRecordDetailsDO>> recordDetailsMap = gameRecordDetailsService.lambdaQuery()
                .eq(GameRecordDetailsDO::getRecordSummaryId, recordSummary.getId())
                .list()
                .stream()
                .collect(Collectors.groupingBy(GameRecordDetailsDO::getExerciseQuestionVersionId));

        int correctQuestions = 0;
        int wrongQuestions = 0;

        for (Map.Entry<Long, List<GameRecordDetailsDO>> entry : recordDetailsMap.entrySet()) {
            boolean isCorrect = entry.getValue().stream()
                    .max(Comparator.comparing(GameRecordDetailsDO::getAnswerTime))
                    .map(vo -> GameRecordCorrectTypeEnum.CORRECT.getCode().equals(vo.getIsCorrect()))
                    .orElse(false);

            if (isCorrect) {
                correctQuestions++;
            } else {
                wrongQuestions++;
            }
        }

        // 更新统计信息
        int totalAnsweredQuestions = correctQuestions + wrongQuestions;
        int totalUnansweredQuestions = recordSummary.getTotalQuestions() - totalAnsweredQuestions;

        BigDecimal progress = calculateProgressPercentage(totalAnsweredQuestions, recordSummary.getTotalQuestions());

        gameRecordSummaryService.lambdaUpdate()
                .eq(GameRecordSummaryDO::getId, recordSummary.getId())
                .set(GameRecordSummaryDO::getAnsweredQuestions, totalAnsweredQuestions)
                .set(GameRecordSummaryDO::getCorrectQuestions, correctQuestions)
                .set(GameRecordSummaryDO::getWrongQuestions, wrongQuestions)
                .set(GameRecordSummaryDO::getUnansweredQuestions, totalUnansweredQuestions)
                .set(GameRecordSummaryDO::getProgress, progress)
                .set(GameRecordSummaryDO::getCurrentRound, currentRound)
                .update();
    }

    /**
     * 计算正确和错误题目数量
     * @return int数组，第一个元素为正确题目数，第二个元素为错误题目数
     */
    private int[] calculateCorrectAndWrongCounts(Map<Long, List<GameAnswerRecordDetailsAppReqVO>> reqMap, Integer exerciseType) {
        int correctQuestions = 0;
        int wrongQuestions = 0;

        for (List<GameAnswerRecordDetailsAppReqVO> appReqVOList : reqMap.values()) {
            boolean isCorrect;

            if (SpecialExerciseTypeEnum.WORD_MATCHING.getCode().equals(exerciseType)) {
                // 单词连连看：取第一次答题且答题时间最早的记录
                isCorrect = appReqVOList.stream()
                        .filter(vo -> Objects.equals(vo.getAnswerSequence(), 1))
                        .min(Comparator.comparing(GameAnswerRecordDetailsAppReqVO::getAnswerTime))
                        .map(vo -> GameRecordCorrectTypeEnum.CORRECT.getCode().equals(vo.getIsCorrect()))
                        .orElse(false);
            } else {
                // 其他类型：取最后一次答题的记录
                isCorrect = appReqVOList.stream()
                        .filter(vo -> Objects.nonNull(vo.getAnswerTime()))
                        .max(Comparator.comparing(GameAnswerRecordDetailsAppReqVO::getAnswerTime))
                        .map(vo -> GameRecordCorrectTypeEnum.CORRECT.getCode().equals(vo.getIsCorrect()))
                        .orElse(false);
            }

            if (isCorrect) {
                correctQuestions++;
            } else {
                wrongQuestions++;
            }
        }

        return new int[]{correctQuestions, wrongQuestions};
    }

    /**
     * 创建答题记录详情列表
     */
    private List<GameRecordDetailsDO> createRecordDetailsList(Map<Long, List<GameAnswerRecordDetailsAppReqVO>> reqMap,
                                                              Long specialExerciseId,
                                                              Integer type,
                                                              Long userId,
                                                              Long recordSummaryId) {
        List<GameRecordDetailsDO> addRecordDetailsDOList = new ArrayList<>();

        for (List<GameAnswerRecordDetailsAppReqVO> appReqVOList : reqMap.values()) {
            List<GameRecordDetailsDO> addRecordDetailsList = appReqVOList.stream()
                    .map(vo -> createGameRecordDetailsDO(vo, specialExerciseId, type, userId, recordSummaryId))
                    .toList();

            addRecordDetailsDOList.addAll(addRecordDetailsList);
        }

        return addRecordDetailsDOList;
    }

    /**
     * 创建游戏记录详情对象
     */
    private GameRecordDetailsDO createGameRecordDetailsDO(GameAnswerRecordDetailsAppReqVO vo,
                                                          Long specialExerciseId,
                                                          Integer type,
                                                          Long userId,
                                                          Long recordSummaryId) {
        GameRecordDetailsDO gameRecordDetailsDO = GameRecordDetailsConvert.INSTANCE.toDo(vo, specialExerciseId, type, userId, recordSummaryId);
        gameRecordDetailsDO.setAnswerDate(LocalDate.now());
        gameRecordDetailsDO.setAnswerStatus(1);
        return gameRecordDetailsDO;
    }

    /**
     * 更新记录汇总的统计信息
     */
    private void updateRecordSummaryStatistics(GameRecordSummaryDO recordSummary, int correctQuestions, int wrongQuestions, Integer currentRound) {
        int totalCorrectQuestions = recordSummary.getCorrectQuestions() + correctQuestions;
        int totalWrongQuestions = recordSummary.getWrongQuestions() + wrongQuestions;
        int totalAnsweredQuestions = totalCorrectQuestions + totalWrongQuestions;
        int totalUnansweredQuestions = recordSummary.getTotalQuestions() - totalAnsweredQuestions;

        BigDecimal progress = calculateProgressPercentage(totalAnsweredQuestions, recordSummary.getTotalQuestions());

        gameRecordSummaryService.lambdaUpdate()
                .eq(GameRecordSummaryDO::getId, recordSummary.getId())
                .set(GameRecordSummaryDO::getAnsweredQuestions, totalAnsweredQuestions)
                .set(GameRecordSummaryDO::getCorrectQuestions, totalCorrectQuestions)
                .set(GameRecordSummaryDO::getWrongQuestions, totalWrongQuestions)
                .set(GameRecordSummaryDO::getUnansweredQuestions, totalUnansweredQuestions)
                .set(GameRecordSummaryDO::getProgress, progress)
                .set(GameRecordSummaryDO::getCurrentRound, currentRound)
                .update();
    }

    /**
     * 计算进度百分比
     */
    private BigDecimal calculateProgressPercentage(int answeredQuestions, int totalQuestions) {
        if (totalQuestions <= 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal hundred = BigDecimal.valueOf(100);

        BigDecimal decimal = BigDecimal.valueOf(answeredQuestions)
                .multiply(hundred)
                .divide(BigDecimal.valueOf(totalQuestions), 4, RoundingMode.HALF_UP);

        // 限制范围在0~100
        return NumberUtil.max(BigDecimal.ZERO, NumberUtil.min(decimal, hundred));
    }


    /**
     * 处理完成状态
     */
    private void handleCompletedStatus(Long recordSummaryId, Long interactiveCourseUnitId) {
        completeRecordDetailsForSpecialExercises(recordSummaryId);

        // 更新状态为已完成
        gameRecordSummaryService.lambdaUpdate()
                .set(GameRecordSummaryDO::getPracticeStatus, GameRecordPracticeStatusEnum.COMPLETED.getCode())
                .set(GameRecordSummaryDO::getAnswerEndTime, LocalDateTime.now())
                .eq(GameRecordSummaryDO::getId, recordSummaryId)
                .update();

        CompletableFuture.runAsync(() -> {
            try {
                updateCompletedRecordStatistics(recordSummaryId, interactiveCourseUnitId);
            } catch (Exception e) {
                log.error("更新完成记录统计信息异常，recordSummaryId: {}", recordSummaryId, e);
            }
        });
    }

    /**
     * 为特定类型的练习补全缺失的答题记录
     * 针对书写练习和卡拉OK练习，当用户完成时自动补全未答的题目记录
     *
     * @param recordSummaryId 记录汇总ID
     */
    private void completeRecordDetailsForSpecialExercises(Long recordSummaryId) {

        try {
            // 获取记录汇总信息
            GameRecordSummaryDO recordSummary = gameRecordSummaryService.getById(recordSummaryId);
            if (recordSummary == null) {
                return;
            }

            // 只处理卡拉OK练习
            if (!SpecialExerciseTypeEnum.KARAOKE.getCode().equals(recordSummary.getType())) {
                return;
            }

            // 获取专项练习信息
            SpecialExerciseDO specialExercise = specialExerciseService.getById(recordSummary.getSpecialExerciseId());
            if (specialExercise == null) {
                return;
            }

            String exerciseQuestionVersionIds = recordSummary.getExerciseQuestionVersionIds();
            List<Long> exerciseQuestionVersionIdList = JSONUtil.toList(exerciseQuestionVersionIds, Long.class);

            // 获取所有题目版本信息
            List<ExerciseQuestionRespVO> allQuestions = exerciseQuestionVersionService
                    .getExerciseQuestionByIdList(exerciseQuestionVersionIdList, specialExercise.getType());

            if (CollUtil.isEmpty(allQuestions)) {
                return;
            }

            // 获取已答题目ID集合
            Set<Long> answeredQuestionIds = gameRecordDetailsService.lambdaQuery()
                    .eq(GameRecordDetailsDO::getRecordSummaryId, recordSummaryId)
                    .eq(GameRecordDetailsDO::getAnswerSequence, 1)
                    .select(GameRecordDetailsDO::getExerciseQuestionId)
                    .list()
                    .stream()
                    .map(GameRecordDetailsDO::getExerciseQuestionId)
                    .collect(Collectors.toSet());

            // 批量创建缺失的答题记录
            List<GameRecordDetailsDO> missingRecords = createMissingRecordDetails(
                    allQuestions, answeredQuestionIds, recordSummary);

            // 批量保存
            if (CollUtil.isNotEmpty(missingRecords)) {
                gameRecordDetailsService.saveBatch(missingRecords);
            }

        } catch (Exception e) {
            log.error("补全答题记录失败,recordSummaryId: {}", recordSummaryId, e);
        }
    }

    /**
     * 创建缺失的答题记录详情
     *
     * @param allQuestions        所有题目
     * @param answeredQuestionIds 已答题目ID集合
     * @param recordSummary       记录汇总
     * @return 缺失的答题记录列表
     */
    private List<GameRecordDetailsDO> createMissingRecordDetails(List<ExerciseQuestionRespVO> allQuestions,
                                                                 Set<Long> answeredQuestionIds,
                                                                 GameRecordSummaryDO recordSummary) {
        LocalDateTime now = LocalDateTime.now();
        LocalDate nowDate = LocalDate.now();

        return allQuestions.stream()
                .filter(question -> !answeredQuestionIds.contains(question.getExerciseQuestionId()))
                .map(question -> createDefaultRecordDetail(question, recordSummary, now, nowDate))
                .toList();
    }

    /**
     * 创建默认的答题记录详情
     *
     * @param question      题目信息
     * @param recordSummary 记录汇总
     * @param answerTime    答题时间
     * @param answerDate    答题日期
     * @return 答题记录详情
     */
    private GameRecordDetailsDO createDefaultRecordDetail(ExerciseQuestionRespVO question,
                                                          GameRecordSummaryDO recordSummary,
                                                          LocalDateTime answerTime,
                                                          LocalDate answerDate) {
        GameRecordDetailsDO record = new GameRecordDetailsDO();
        record.setUserId(recordSummary.getUserId());
        record.setSpecialExerciseId(recordSummary.getSpecialExerciseId());
        record.setQuestionId(question.getQuestionId());
        record.setExerciseQuestionId(question.getExerciseQuestionId());
        record.setExerciseQuestionVersionId(question.getExerciseQuestionVersionId());
        record.setRecordSummaryId(recordSummary.getId());
        record.setType(recordSummary.getType());
        record.setQuestionContent(question.getQuestionContent());
        record.setReferenceAnswer(question.getReferenceAnswer());
        record.setUserAnswer("");
        record.setIsCorrect(GameRecordCorrectTypeEnum.ERROR.getCode());
        record.setAnswerTime(answerTime);
        record.setAnswerDate(answerDate);
        record.setVersion(question.getVersion());
        record.setAnswerSequence(1);
        record.setAnswerStatus(0);
        record.setPinyin(question.getPinyin());
        return record;
    }

    /**
     * 更新已完成记录的统计信息
     * 当练习完成时，重新计算并更新记录汇总的统计数据
     *
     * @param recordSummaryId 记录汇总ID
     * @param interactiveCourseUnitId 互动课单元ID（可选）
     */
    private void updateCompletedRecordStatistics(Long recordSummaryId, Long interactiveCourseUnitId) {
        // 获取记录汇总信息
        GameRecordSummaryDO recordSummary = gameRecordSummaryService.getById(recordSummaryId);
        if (recordSummary == null) {
            log.warn("记录汇总不存在,recordSummaryId: {}", recordSummaryId);
            return;
        }

        // 获取答题详情记录
        List<GameRecordDetailsDO> recordDetailsList = gameRecordDetailsService.lambdaQuery()
                .eq(GameRecordDetailsDO::getRecordSummaryId, recordSummaryId)
                .list();

        if (CollUtil.isEmpty(recordDetailsList)) {
            log.info("答题详情记录为空,recordSummaryId: {}", recordSummaryId);
            gameRecordSummaryService.lambdaUpdate()
                    .eq(GameRecordSummaryDO::getId, recordSummaryId)
                    .set(GameRecordSummaryDO::getAnsweredQuestions, 0)
                    .set(GameRecordSummaryDO::getCorrectQuestions, 0)
                    .set(GameRecordSummaryDO::getWrongQuestions, 0)
                    .set(GameRecordSummaryDO::getUnansweredQuestions, recordSummary.getTotalQuestions())
                    .set(GameRecordSummaryDO::getProgress, BigDecimal.ZERO)
                    .set(GameRecordSummaryDO::getAccuracy, BigDecimal.ZERO)
                    .set(GameRecordSummaryDO::getAverageScore, BigDecimal.ZERO)
                    .set(GameRecordSummaryDO::getPracticeStatus, GameRecordPracticeStatusEnum.COMPLETED.getCode())
                    .update();
            // 发送互动课练习完成事件 提前结束 答题数量0
            sendPracticeCompletedEventIfNeeded(recordSummary, interactiveCourseUnitId, 0, 0, false);
            return;
        }

        // 根据练习类型获取答题记录映射
        Map<Long, GameRecordDetailsDO> recordDetailsMap = getRecordDetailsMap(recordSummary.getType(), recordDetailsList);

        // 计算基础统计数据
        int totalQuestions = recordSummary.getTotalQuestions();
        int correctQuestions = 0;

        // 根据练习类型执行不同的统计逻辑
        if (SpecialExerciseTypeEnum.KARAOKE.getCode().equals(recordSummary.getType())) {
            correctQuestions = updateKaraokeExerciseStatistics(recordDetailsMap, totalQuestions, recordSummaryId);
        } else {
            correctQuestions = updateStandardExerciseStatistics(recordDetailsMap, totalQuestions, recordSummaryId);
        }

        // 发送互动课练习完成事件
        sendPracticeCompletedEventIfNeeded(recordSummary, interactiveCourseUnitId, correctQuestions, totalQuestions, false);
    }

    /**
     * 更新卡拉OK练习的统计数据
     *
     * @return 正确题目数量
     */
    private int updateKaraokeExerciseStatistics(Map<Long, GameRecordDetailsDO> recordDetailsMap,
                                                int totalQuestions,
                                                Long recordSummaryId) {
        // 计算总分和已答题数量
        int totalScore = recordDetailsMap.values().stream()
                .filter(e -> Objects.nonNull(e.getScore()))
                .mapToInt(GameRecordDetailsDO::getScore)
                .sum();

        int correctQuestions = (int) recordDetailsMap.values()
                .stream()
                .filter(e -> Objects.equals(e.getAnswerStatus(), 1))
                .count();

        // 计算其他统计数据
        int answeredQuestions = correctQuestions;
        int unansweredQuestions = totalQuestions - correctQuestions;
        BigDecimal progress = calculateProgressPercentage(answeredQuestions, totalQuestions);

        // 计算平均分
        BigDecimal averageScore = totalQuestions > 0
                ? BigDecimal.valueOf(totalScore)
                .divide(BigDecimal.valueOf(totalQuestions), 4, RoundingMode.HALF_UP)
                : BigDecimal.ZERO;

        // 更新记录汇总
        gameRecordSummaryService.lambdaUpdate()
                .eq(GameRecordSummaryDO::getId, recordSummaryId)
                .set(GameRecordSummaryDO::getAnsweredQuestions, answeredQuestions)
                .set(GameRecordSummaryDO::getCorrectQuestions, correctQuestions)
                .set(GameRecordSummaryDO::getWrongQuestions, 0)
                .set(GameRecordSummaryDO::getUnansweredQuestions, unansweredQuestions)
                .set(GameRecordSummaryDO::getProgress, progress)
                .set(GameRecordSummaryDO::getAverageScore, averageScore)
                .set(GameRecordSummaryDO::getPracticeStatus, GameRecordPracticeStatusEnum.COMPLETED.getCode())
                .update();

        return correctQuestions;
    }

    /**
     * 更新标准练习的统计数据
     *
     * @return 正确题目数量
     */
    private int updateStandardExerciseStatistics(Map<Long, GameRecordDetailsDO> recordDetailsMap,
                                                 int totalQuestions,
                                                 Long recordSummaryId) {
        // 统计正确和错误题目数
        int correctQuestions = 0;
        int wrongQuestions = 0;

        for (GameRecordDetailsDO recordDetails : recordDetailsMap.values()) {
            if (Objects.equals(recordDetails.getAnswerStatus(), 1)) {
                if (GameRecordCorrectTypeEnum.CORRECT.getCode().equals(recordDetails.getIsCorrect())) {
                    correctQuestions++;
                } else if (GameRecordCorrectTypeEnum.ERROR.getCode().equals(recordDetails.getIsCorrect())) {
                    wrongQuestions++;
                }
            }
        }

        // 计算其他统计数据
        int answeredQuestions = correctQuestions + wrongQuestions;
        int unansweredQuestions = totalQuestions - answeredQuestions;
        BigDecimal progress = calculateProgressPercentage(answeredQuestions, totalQuestions);

        // 计算正确率
        BigDecimal accuracy = totalQuestions > 0
                ? BigDecimal.valueOf(correctQuestions)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(totalQuestions), 4, RoundingMode.HALF_UP)
                : BigDecimal.ZERO;

        // 更新记录汇总
        gameRecordSummaryService.lambdaUpdate()
                .eq(GameRecordSummaryDO::getId, recordSummaryId)
                .set(GameRecordSummaryDO::getAnsweredQuestions, answeredQuestions)
                .set(GameRecordSummaryDO::getCorrectQuestions, correctQuestions)
                .set(GameRecordSummaryDO::getWrongQuestions, wrongQuestions)
                .set(GameRecordSummaryDO::getUnansweredQuestions, unansweredQuestions)
                .set(GameRecordSummaryDO::getProgress, progress)
                .set(GameRecordSummaryDO::getAccuracy, accuracy)
                .set(GameRecordSummaryDO::getPracticeStatus, GameRecordPracticeStatusEnum.COMPLETED.getCode())
                .update();

        return correctQuestions;
    }

    /**
     * 根据练习类型获取答题记录映射
     */
    private Map<Long, GameRecordDetailsDO> getRecordDetailsMap(Integer exerciseType, List<GameRecordDetailsDO> recordDetailsList) {
        // 对于特定类型的练习，只考虑第一次答题记录
        if (SpecialExerciseTypeEnum.STROKE_WRITING.getCode().equals(exerciseType)) {
            return recordDetailsList.stream()
                    .collect(Collectors.toMap(
                            GameRecordDetailsDO::getExerciseQuestionId,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));
        } else if (SpecialExerciseTypeEnum.WORD_MATCHING.getCode().equals(exerciseType)) {
            return recordDetailsList.stream()
                    .filter(e -> Objects.equals(e.getAnswerSequence(), 1))
                    .collect(Collectors.toMap(
                            GameRecordDetailsDO::getExerciseQuestionId,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));
        } else {
            return recordDetailsList.stream()
                    .collect(Collectors.toMap(
                            GameRecordDetailsDO::getExerciseQuestionId,
                            Function.identity(),
                            (e1, e2) -> e1.getAnswerTime().isAfter(e2.getAnswerTime()) ? e1 : e2
                    ));
        }
    }

    /**
     * 如果需要，发送练习完成事件
     *
     * @param recordSummary 记录汇总
     * @param interactiveCourseUnitId 互动课单元ID
     * @param correctQuestions 正确题数
     * @param totalQuestions 总题数
     */
    private void sendPracticeCompletedEventIfNeeded(GameRecordSummaryDO recordSummary, Long interactiveCourseUnitId,
                                                  int correctQuestions, int totalQuestions,boolean hasAICorrection) {
        // 如果没有互动课单元ID，则不发送事件
        if (interactiveCourseUnitId == null) {
            log.warn("未找到互动课单元ID，跳过发送练习完成事件: recordSummaryId={}", recordSummary.getId());
            return;
        }

        // 计算正确率
        BigDecimal accuracy = BigDecimal.ZERO;
        if (totalQuestions > 0) {
            accuracy = BigDecimal.valueOf(correctQuestions)
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(totalQuestions), 4, RoundingMode.HALF_UP);
        }

        // 发送练习完成事件
        gameProducer.sendPracticeCompletedEvent(
            recordSummary.getUserId(),
            interactiveCourseUnitId,
            recordSummary.getId(),
            // 正确率取整
            BigDecimal.valueOf(accuracy.intValue()),
            hasAICorrection
        );
    }


    /**
     * 获取答题报告
     *
     * @param audioFile                    音频文件
     * @param recordSummaryIdStr           记录汇总ID
     * @param specialExerciseIdStr         专项练习ID
     * @param exerciseQuestionVersionIdStr 练习题版本ID
     * @param questionContent              题目内容
     * @param currentRoundStr              当前轮次
     * @return 答题报告
     */
    @Transactional(rollbackFor = Exception.class)
    public GameKaraokeAnswerRecordAppRespVO getAnswerReport(MultipartFile audioFile,
                                                            String recordSummaryIdStr,
                                                            String specialExerciseIdStr,
                                                            String exerciseQuestionVersionIdStr,
                                                            String questionContent,
                                                            String currentRoundStr) {

        Long userId = StpUtil.getLoginIdAsLong();

        // 参数验证
        validateParameters(userId, recordSummaryIdStr, specialExerciseIdStr, exerciseQuestionVersionIdStr, currentRoundStr);

        // 参数转换
        Long recordSummaryId = Long.parseLong(recordSummaryIdStr);
        Long specialExerciseId = Long.parseLong(specialExerciseIdStr);
        Long exerciseQuestionVersionId = Long.parseLong(exerciseQuestionVersionIdStr);
        Integer currentRound = Integer.parseInt(currentRoundStr);

        // 验证记录汇总是否存在
        GameRecordSummaryDO recordSummary = validateRecordSummary(recordSummaryId);
        gameRecordSummaryService.lambdaUpdate()
                .set(GameRecordSummaryDO::getCurrentRound, currentRound)
                .set(GameRecordSummaryDO::getUpdateTime, LocalDateTime.now())
                .update();

        // 验证用户剩余批改次数
        gameCorrectionAppManager.validateCorrectionCount(recordSummary.getType());

        Integer answerStatus = GameAnswerStatusEnum.UNANSWERED.getCode();

        // 检查音频文件
        if (audioFile == null || audioFile.isEmpty()) {
            log.warn("音频文件为空,用户ID:{},记录ID：{}", userId, recordSummaryId);

            // 查询或创建答题记录
            GameRecordDetailsDO recordDetails = createOrUpdateRecordDetails(
                    recordSummaryId, specialExerciseId, exerciseQuestionVersionId, recordSummary.getType(), userId, answerStatus);

            // 保存答题记录
            gameRecordDetailsService.save(recordDetails);

            GameKaraokeAnswerRecordAppRespVO respVO = new GameKaraokeAnswerRecordAppRespVO();
            respVO.setRecordSummaryId(recordSummaryId);
            respVO.setSpecialExerciseId(specialExerciseId);
            respVO.setType(recordSummary.getType());
            respVO.setTotalScore(0);
            respVO.setAnswerStatus(answerStatus);
            return respVO;
        }

        log.info("开始处理卡拉OK语音评估请求,用户ID:{},记录汇总ID:{},专项练习ID:{},题目版本ID:{}",
                userId, recordSummaryId, specialExerciseId, exerciseQuestionVersionId);

        // 查询或创建答题记录
        GameRecordDetailsDO recordDetails = createOrUpdateRecordDetails(
                recordSummaryId, specialExerciseId, exerciseQuestionVersionId, recordSummary.getType(), userId, answerStatus);

        // 保存答题记录
        gameRecordDetailsService.save(recordDetails);

        // 处理音频文件和调用第三方API
        GameKaraokeAnswerRecordAppRespVO appRespVO = processAudioAndEvaluate(audioFile, recordDetails, recordSummaryId, specialExerciseId, userId, questionContent);


        if (GameAnswerStatusEnum.UNANSWERED.getCode().equals(appRespVO.getAnswerStatus())) {
            gameRecordDetailsService.lambdaUpdate()
                    .eq(GameRecordDetailsDO::getId, recordDetails.getId())
                    .set(GameRecordDetailsDO::getAnswerStatus, answerStatus)
                    .set(GameRecordDetailsDO::getUpdateTime, LocalDateTime.now())
                    .update();

            GameKaraokeAnswerRecordAppRespVO respVO = new GameKaraokeAnswerRecordAppRespVO();
            respVO.setRecordSummaryId(recordSummaryId);
            respVO.setSpecialExerciseId(specialExerciseId);
            respVO.setType(recordSummary.getType());
            respVO.setTotalScore(0);
            respVO.setAnswerStatus(answerStatus);
            return respVO;
        } else if (GameAnswerStatusEnum.FAILED.getCode().equals(appRespVO.getAnswerStatus())) {
            answerStatus = GameAnswerStatusEnum.FAILED.getCode();
            // 更新答题记录
            gameRecordDetailsService.lambdaUpdate()
                    .eq(GameRecordDetailsDO::getId, recordDetails.getId())
                    .set(GameRecordDetailsDO::getAnswerStatus, answerStatus)
                    .set(GameRecordDetailsDO::getUpdateTime, LocalDateTime.now())
                    .update();

            appRespVO = new GameKaraokeAnswerRecordAppRespVO();
            appRespVO.setRecordSummaryId(recordSummaryId);
            appRespVO.setSpecialExerciseId(specialExerciseId);
            appRespVO.setType(recordSummary.getType());
            appRespVO.setTotalScore(0);
            appRespVO.setAnswerStatus(answerStatus);
            return appRespVO;

        }

        // 异步更新
        CompletableFuture.runAsync(() -> {
            try {
                updateCorrectionCount(userId, recordDetails.getId(), recordSummaryId);
                updateKaraokeAnswerSummary(userId, recordSummaryId, recordSummary.getTotalQuestions());
            } catch (Exception e) {
                log.error("异步更新批改次数失败,记录ID:{},错误信息:{}", recordDetails.getId(), e.getMessage(), e);
            }
        });


        return appRespVO;
    }

    /**
     * 更新卡拉OK作答汇总
     *
     * @param userId          用户 ID
     * @param recordSummaryId 作答汇总 ID
     * @param totalQuestions  题数
     */
    private void updateKaraokeAnswerSummary(Long userId, Long recordSummaryId, Integer totalQuestions) {
        List<GameRecordDetailsDO> recordDetailsList = gameRecordDetailsService.lambdaQuery()
                .eq(GameRecordDetailsDO::getRecordSummaryId, recordSummaryId)
                .eq(GameRecordDetailsDO::getUserId, userId)
                .ne(GameRecordDetailsDO::getAnswerStatus, GameAnswerStatusEnum.UNANSWERED.getCode())
                .list();
        long correctQuestions = recordDetailsList.stream()
                .filter(recordDetails -> recordDetails.getAnswerStatus() == 1)
                .map(GameRecordDetailsDO::getExerciseQuestionVersionId)
                .distinct()
                .count();

        long unansweredQuestions = recordDetailsList.size() - correctQuestions;

        int totalScore = recordDetailsList.stream()
                .filter(e -> Objects.nonNull(e.getScore()))
                .mapToInt(GameRecordDetailsDO::getScore)
                .sum();

        // 计算平均分
        BigDecimal averageScore = totalQuestions > 0
                ? BigDecimal.valueOf(totalScore)
                .divide(BigDecimal.valueOf(totalQuestions), 4, RoundingMode.HALF_UP)
                : BigDecimal.ZERO;

        gameRecordSummaryService.lambdaUpdate()
                .eq(GameRecordSummaryDO::getId, recordSummaryId)
                .set(GameRecordSummaryDO::getAnsweredQuestions, recordDetailsList.size())
                .set(GameRecordSummaryDO::getCorrectQuestions, correctQuestions)
                .set(GameRecordSummaryDO::getWrongQuestions, 0)
                .set(GameRecordSummaryDO::getUnansweredQuestions, unansweredQuestions)
                .set(GameRecordSummaryDO::getProgress, calculateProgressPercentage(recordDetailsList.size(), totalQuestions))
                .set(GameRecordSummaryDO::getAverageScore, averageScore)
                .update();

    }

    /**
     * 更新批改次数
     *
     * @param userId          用户 ID
     * @param recordDetailsId 记录详情 ID
     * @param recordSummaryId 记录汇总 ID
     */
    private void updateCorrectionCount(Long userId, Long recordDetailsId, Long recordSummaryId) {
        AiCorrectionTimesSaveReqDto reqDto = new AiCorrectionTimesSaveReqDto();
        reqDto.setUserId(userId);
        reqDto.setBizType(AiCorrectBizTypeEnum.SPECIAL_EXERCISE.getCode());
        reqDto.setBizId(recordDetailsId);
        reqDto.setCallTime(DateUtil.date());
        reqDto.setCallCount(1);
        reqDto.setRecordId(recordSummaryId);

        aiCorrectionTimesApi.createAiCorrectionTimes(reqDto);

        String key = String.format(SPECIAL_EXERCISE_KARAOKE_USAGE_COUNT, DateUtil.format(DateUtil.date(), "yyyyMMdd"), userId);
        // 增加次数
        Long count = redisUtil.increment(key);
        // 第一次时，设置过期时间
        if (count != null && count == 1) {
            redisUtil.expire(key, 1, TimeUnit.DAYS);
        }
    }

    /**
     * 验证请求参数
     */
    private void validateParameters(Long userId,
                                    String recordSummaryIdStr,
                                    String specialExerciseIdStr,
                                    String exerciseQuestionVersionIdStr,
                                    String currentRoundStr) {

        // 检查必需参数
        if (CharSequenceUtil.isBlank(recordSummaryIdStr) ||
                CharSequenceUtil.isBlank(specialExerciseIdStr) ||
                CharSequenceUtil.isBlank(exerciseQuestionVersionIdStr) ||
                CharSequenceUtil.isBlank(currentRoundStr)
        ) {

            log.error("卡拉ok参数有空值:用户id:{},recordSummaryId:{},specialExerciseId:{},exerciseQuestionVersionId:{}",
                    userId, recordSummaryIdStr, specialExerciseIdStr, exerciseQuestionVersionIdStr);
            throw exception(LOCKED);
        }

        // 验证参数格式
        try {
            Long.parseLong(recordSummaryIdStr);
            Long.parseLong(specialExerciseIdStr);
            Long.parseLong(exerciseQuestionVersionIdStr);
            Integer.parseInt(currentRoundStr);
        } catch (NumberFormatException e) {
            log.error("卡拉ok参数格式错误:用户id:{},recordSummaryId:{},specialExerciseId:{},exerciseQuestionVersionId:{}",
                    userId, recordSummaryIdStr, specialExerciseIdStr, exerciseQuestionVersionIdStr);
            throw exception(LOCKED);
        }
    }

    /**
     * 验证记录汇总是否存在
     */
    private GameRecordSummaryDO validateRecordSummary(Long recordSummaryId) {
        GameRecordSummaryDO recordSummary = gameRecordSummaryService.getById(recordSummaryId);
        if (recordSummary == null) {
            throw exception(GAME_RECORD_NOT_EXISTS);
        }
        return recordSummary;
    }

    /**
     * 创建或更新答题记录详情
     */
    private GameRecordDetailsDO createOrUpdateRecordDetails(Long recordSummaryId,
                                                            Long specialExerciseId,
                                                            Long exerciseQuestionVersionId,
                                                            Integer type,
                                                            Long userId, Integer answerStatus) {

        // 查询是否存在记录
        GameRecordDetailsDO existingRecord = gameRecordDetailsService.lambdaQuery()
                .eq(GameRecordDetailsDO::getSpecialExerciseId, specialExerciseId)
                .eq(GameRecordDetailsDO::getRecordSummaryId, recordSummaryId)
                .eq(GameRecordDetailsDO::getExerciseQuestionVersionId, exerciseQuestionVersionId)
                .eq(GameRecordDetailsDO::getUserId, userId)
                .orderByDesc(GameRecordDetailsDO::getId)
                .last("limit 1")
                .one();

        // 创建新的答题记录
        GameRecordDetailsDO newRecord = new GameRecordDetailsDO();
        newRecord.setUserId(userId);
        newRecord.setSpecialExerciseId(specialExerciseId);
        newRecord.setRecordSummaryId(recordSummaryId);
        newRecord.setAnswerTime(LocalDateTime.now());
        newRecord.setAnswerDate(LocalDate.now());
        newRecord.setIsCorrect(GameRecordCorrectTypeEnum.ERROR.getCode());
        newRecord.setAnswerReport("");
        newRecord.setAnswerStatus(answerStatus);
        newRecord.setScore(0);

        if (existingRecord == null) {
            // 首次作答,查询题目信息
            ExerciseQuestionRespVO respVO =
                    exerciseQuestionVersionService.getExerciseQuestionById(exerciseQuestionVersionId, type);

            if (respVO == null) {
                throw exception(GAME_QUESTION_NOT_EXISTS);
            }

            newRecord.setQuestionContent(respVO.getQuestionContent());
            newRecord.setPinyin(respVO.getPinyin());
            newRecord.setQuestionId(respVO.getQuestionId());
            newRecord.setExerciseQuestionId(respVO.getExerciseQuestionId());
            newRecord.setExerciseQuestionVersionId(respVO.getExerciseQuestionVersionId());
            newRecord.setType(type);
            newRecord.setVersion(respVO.getVersion());
            newRecord.setAnswerSequence(1);

        } else {
            // 重新作答,使用已有信息
            newRecord.setQuestionContent(existingRecord.getQuestionContent());
            newRecord.setPinyin(existingRecord.getPinyin());
            newRecord.setQuestionId(existingRecord.getQuestionId());
            newRecord.setExerciseQuestionId(existingRecord.getExerciseQuestionId());
            newRecord.setExerciseQuestionVersionId(existingRecord.getExerciseQuestionVersionId());
            newRecord.setType(existingRecord.getType());
            newRecord.setVersion(existingRecord.getVersion());
            newRecord.setAnswerSequence(existingRecord.getAnswerSequence() + 1);
        }

        return newRecord;
    }

    /**
     * 处理音频文件并调用第三方API进行评估
     */
    private GameKaraokeAnswerRecordAppRespVO processAudioAndEvaluate(MultipartFile audioFile,
                                                                     GameRecordDetailsDO recordDetails,
                                                                     Long recordSummaryId,
                                                                     Long specialExerciseId,
                                                                     Long userId,
                                                                     String questionContent) {
        File tempFile = null;
        try {

            // 创建临时文件
            String originalFilename = audioFile.getOriginalFilename();
            String suffix = originalFilename != null && originalFilename.contains(".")
                    ? originalFilename.substring(originalFilename.lastIndexOf("."))
                    : ".tmp";

            tempFile = File.createTempFile("audio_", suffix);
            audioFile.transferTo(tempFile);

            // 上传音频文件
            String userAudioUrl = fileApi.createFile(Files.readAllBytes(tempFile.toPath()), IdUtil.simpleUUID());

            return getGameKaraokeAnswerRecordAppRespVO(recordDetails, recordSummaryId, specialExerciseId, userId, questionContent, tempFile, userAudioUrl);

        } catch (Exception e) {
            log.error("卡拉OK语音评估失败,用户ID:{},记录ID:{},错误信息:{}", userId, recordDetails.getId(), e.getMessage(), e);
            return new GameKaraokeAnswerRecordAppRespVO().setAnswerStatus(GameAnswerStatusEnum.UNANSWERED.getCode());
        } finally {
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }

    }

    private GameKaraokeAnswerRecordAppRespVO getGameKaraokeAnswerRecordAppRespVO(GameRecordDetailsDO recordDetails, Long recordSummaryId, Long specialExerciseId, Long userId, String questionContent, File tempFile, String userAudioUrl) {
        try {

            // 调用第三方语音评估API
            ShengTongRespDto evaluationResult = callVoiceEvaluationAPI(tempFile, recordDetails, userId, questionContent);

            // 构造响应对象
            GameKaraokeAnswerRecordAppRespVO response = new GameKaraokeAnswerRecordAppRespVO();
            response.setRecordSummaryId(recordSummaryId);
            response.setSpecialExerciseId(specialExerciseId);
            response.setType(recordDetails.getType());
            response.setResult(evaluationResult.getResult());
            response.setUserAudio(userAudioUrl);
            response.setTotalScore(evaluationResult.getTotalScore());
            response.setAnswerStatus(GameAnswerStatusEnum.ANSWERED.getCode());

            // 异步更新记录详情
            CompletableFuture.runAsync(() -> {
                try {
                    updateRecordDetails(recordDetails, evaluationResult.getTotalScore(), userAudioUrl);
                } catch (Exception e) {
                    log.error("异步更新记录详情失败,用户ID:{},记录ID:{},错误信息:{}", userId, recordDetails.getId(), e.getMessage(), e);
                }
            });

            log.info("卡拉OK语音评估完成,用户ID:{},记录ID:{},得分:{}",
                    userId, recordDetails.getId(), evaluationResult.getTotalScore());

            return response;
        } catch (Exception e) {
            log.error("卡拉OK语音评估失败,用户ID: {},记录ID:{}", userId, recordSummaryId, e);
            return new GameKaraokeAnswerRecordAppRespVO().setAnswerStatus(GameAnswerStatusEnum.FAILED.getCode());
        }
    }

    /**
     * 调用第三方语音评估API
     */
    private ShengTongRespDto callVoiceEvaluationAPI(File tempFile,
                                                    GameRecordDetailsDO recordDetails,
                                                    Long userId,
                                                    String refText) {

        // 构建请求 DTO
        ShengTongCallDto callDto = new ShengTongCallDto();
        callDto.setFile(tempFile);
        callDto.setRefText(refText);
        callDto.setRecordId(recordDetails.getId());
        callDto.setUserId(userId);
        callDto.setQuestionId(recordDetails.getQuestionId());
        callDto.setQuestionVersion(recordDetails.getVersion());

        // 调用 API
        ShengTongRespDto response = shengTongApi.oralEvaluation(callDto);

        // 验证响应
        if (response == null || response.getErrId() != null || CharSequenceUtil.isBlank(response.getResult())) {
            log.error("卡拉OK调用语音评估API失败,用户ID:{},记录ID:{},错误ID:{}",
                    userId, recordDetails.getId(), response != null ? response.getErrId() : "null");
            throw exception(GAME_RECORD_AUDIO_PROCESSING_ERROR);
        }

        return response;

    }


    /**
     * 更新记录详情
     */
    private void updateRecordDetails(GameRecordDetailsDO recordDetails, Integer totalScore, String userAudioUrl) {
        if (totalScore != null) {
            recordDetails.setIsCorrect(GameRecordCorrectTypeEnum.CORRECT.getCode());
            recordDetails.setScore(totalScore);
            recordDetails.setUserAnswer(userAudioUrl);
            recordDetails.setAnswerStatus(1);
            gameRecordDetailsService.updateById(recordDetails);
            log.info("记录详情更新成功,记录ID:{},用户ID:{},得分:{}",
                    recordDetails.getId(), recordDetails.getUserId(), totalScore);
        }
    }
}
