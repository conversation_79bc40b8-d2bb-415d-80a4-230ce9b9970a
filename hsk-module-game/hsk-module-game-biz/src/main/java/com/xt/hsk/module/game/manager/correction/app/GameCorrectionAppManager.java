package com.xt.hsk.module.game.manager.correction.app;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.DateUtil;
import com.xt.hsk.module.game.controller.app.correction.vo.UserCorrectionRemainingCountRespVO;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import com.xt.hsk.module.thirdparty.api.aicorrectiontimes.AiCorrectionTimesApi;
import com.xt.hsk.module.thirdparty.enums.AiCorrectBizTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.xt.hsk.framework.common.constants.RedisKeyPrefix.SPECIAL_EXERCISE_KARAOKE_USAGE_COUNT;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.game.enums.ErrorCodeConstants.KARAOKE_AI_CORRECTION_COUNT_LIMIT;

/**
 * 专项练习批改 app manager
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Slf4j
@Component
public class GameCorrectionAppManager {

    /**
     * 专项练习卡拉OK配置额度KEY
     */
    private static final String SPECIAL_EXERCISE_KARAOKE_CONFIG_COUNT = "ai:correction:karaoke:max";

    @Resource
    private AiCorrectionTimesApi aiCorrectionTimesApi;

    /**
     * 获取用户剩余批改次数
     */
    public UserCorrectionRemainingCountRespVO getUserRemainingCount(Integer type) {
        if (!SpecialExerciseTypeEnum.KARAOKE.getCode().equals(type)) {
            return new UserCorrectionRemainingCountRespVO().setRemainingCount(0);
        }

        long userId = StpUtil.getLoginIdAsLong();

        int remainingCount = aiCorrectionTimesApi.getUserRemainingCount(
                userId, SPECIAL_EXERCISE_KARAOKE_CONFIG_COUNT, SPECIAL_EXERCISE_KARAOKE_USAGE_COUNT, DateUtil.date(), AiCorrectBizTypeEnum.SPECIAL_EXERCISE);

        return new UserCorrectionRemainingCountRespVO().setRemainingCount(remainingCount);
    }

    /**
     * 验证批改次数
     *
     * @param type 专项练习类型
     */
    public void validateCorrectionCount(Integer type) {
        if (SpecialExerciseTypeEnum.KARAOKE.getCode().equals(type)) {
            UserCorrectionRemainingCountRespVO userRemainingCount = this.getUserRemainingCount(type);

            if (userRemainingCount.getRemainingCount() <= 0) {
                throw exception(KARAOKE_AI_CORRECTION_COUNT_LIMIT);
            }
        }
    }
}
