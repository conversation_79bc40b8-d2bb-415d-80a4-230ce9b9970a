package com.xt.hsk.module.game.service.record;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.module.game.dal.dataobject.record.GameRecordSummaryDO;
import com.xt.hsk.module.game.dal.mysql.record.GameRecordSummaryMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;


/**
 * 专项练习记录汇总 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
public class GameRecordSummaryServiceImpl extends ServiceImpl<GameRecordSummaryMapper, GameRecordSummaryDO> implements GameRecordSummaryService {

    @Resource
    private GameRecordSummaryMapper gameRecordSummaryMapper;

    /**
     * 批量查询用户在指定专项练习中的最新记录
     *
     * @param specialExerciseIdList 专项练习ID列表
     * @param userId                用户ID
     * @return 每个专项练习的最新记录列表
     */
    @Override
    public List<GameRecordSummaryDO> getLatestRecords(List<Long> specialExerciseIdList, Long userId) {
        if (CollUtil.isEmpty(specialExerciseIdList) || userId == null) {
            return Collections.emptyList();
        }
        return gameRecordSummaryMapper.getLatestRecords(specialExerciseIdList, userId);
    }

    /**
     * 批量查询用户在指定专项练习中的最新记录
     *
     * @param userId         用户 ID
     * @param practiceStatus 练习状态 1 进行中 2 已完成 3未开始
     * @return 每个专项练习的最新记录列表
     */
    @Override
    public List<GameRecordSummaryDO> getLatestByUserIdAndPracticeStatus(Long userId, Integer practiceStatus) {
        return gameRecordSummaryMapper.getLatestByUserIdAndPracticeStatus(userId, practiceStatus);
    }
}