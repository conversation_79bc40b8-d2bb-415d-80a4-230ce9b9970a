package com.xt.hsk.module.game.dal.dataobject.record;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.AppBaseDO;
import com.xt.hsk.module.game.enums.record.GameRecordCorrectTypeEnum;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 专项练习记录详情 DO
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@TableName("game_record_details")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GameRecordDetailsDO extends AppBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 专项练习的id
     */
    private Long specialExerciseId;
    /**
     * 题目ID
     */
    private Long questionId;
    /**
     * 练习组ID
     */
    private Long exerciseQuestionId;
    /**
     * 练习组题目版本库ID
     */
    private Long exerciseQuestionVersionId;
    /**
     * 记录总表ID
     */
    private Long recordSummaryId;
    /**
     * 类型 1单词连连看 2笔画书写 3连词成句 4卡拉OK
     *
     * @see SpecialExerciseTypeEnum
     */
    private Integer type;
    /**
     * 题目内容
     */
    private String questionContent;
    /**
     * 参考答案
     */
    private String referenceAnswer;
    /**
     * 用户答案
     */
    private String userAnswer;

    /**
     * 拼音
     */
    private String pinyin;

    /**
     * 是否正确 0错误 1正确
     *
     * @see GameRecordCorrectTypeEnum
     */
    private Integer isCorrect;
    /**
     * 答题时间
     */
    private LocalDateTime answerTime;
    /**
     * 答题日期
     */
    private LocalDate answerDate;
    /**
     * 答题报告
     */
    private String answerReport;
    /**
     * 版本号
     */
    private Integer version;

    /**
     * 第几次答题
     */
    private Integer answerSequence;

    /**
     * 分数
     */
    private Integer score;

    /**
     * 答题状态 0-未答 1-已答
     */
    private Integer answerStatus;

}