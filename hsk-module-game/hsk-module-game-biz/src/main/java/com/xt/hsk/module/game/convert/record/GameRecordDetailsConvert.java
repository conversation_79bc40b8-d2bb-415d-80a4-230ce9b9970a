package com.xt.hsk.module.game.convert.record;

import com.xt.hsk.module.game.controller.app.record.vo.GameAnswerRecordDetailsAppReqVO;
import com.xt.hsk.module.game.dal.dataobject.record.GameRecordDetailsDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 游戏记录详细信息转换
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Mapper
public interface GameRecordDetailsConvert {

    GameRecordDetailsConvert INSTANCE = Mappers.getMapper(GameRecordDetailsConvert.class);

    @Mapping(source = "reqVO.questionContent", target = "questionContent")
    @Mapping(source = "reqVO.referenceAnswer", target = "referenceAnswer")
    @Mapping(source = "reqVO.userAnswer", target = "userAnswer")
    @Mapping(source = "reqVO.isCorrect", target = "isCorrect")
    @Mapping(source = "reqVO.answerTime", target = "answerTime")
    @Mapping(source = "reqVO.answerSequence", target = "answerSequence")
    @Mapping(source = "reqVO.exerciseQuestionVersionId", target = "exerciseQuestionVersionId")
    @Mapping(source = "reqVO.exerciseQuestionId", target = "exerciseQuestionId")
    @Mapping(source = "reqVO.version", target = "version")
    @Mapping(source = "reqVO.questionId", target = "questionId")

    @Mapping(source = "specialExerciseId", target = "specialExerciseId")
    @Mapping(source = "type", target = "type")
    @Mapping(source = "userId", target = "userId")
    @Mapping(source = "recordSummaryId", target = "recordSummaryId")
    GameRecordDetailsDO toDo(GameAnswerRecordDetailsAppReqVO reqVO, Long specialExerciseId, Integer type, Long userId, Long recordSummaryId);
}
