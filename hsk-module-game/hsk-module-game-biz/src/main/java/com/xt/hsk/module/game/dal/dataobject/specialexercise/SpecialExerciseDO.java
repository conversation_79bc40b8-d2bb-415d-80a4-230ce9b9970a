package com.xt.hsk.module.game.dal.dataobject.specialexercise;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import lombok.*;

/**
 * 游戏-专项练习-练习组 DO
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@TableName("game_special_exercise")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpecialExerciseDO extends BaseDO {

    /**
     * 专项练习ID
     */
    @TableId
    private Long id;
    /**
     * 专项练习名称-中文
     */
    private String nameCn;
    /**
     * 专项练习名称-英文
     */
    private String nameEn;
    /**
     * 专项练习名称-其他
     */
    private String nameOt;
    /**
     * 练习类型
     *
     * @see SpecialExerciseTypeEnum
     */
    private Integer type;
    /**
     * HSK等级
     */
    private Integer hskLevel;
    /**
     * 难度等级 1 简单 2中等 3 困难
     */
    private Integer difficultyLevel;
    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;
    /**
     * 排序序号
     */
    private Integer sort;
    /**
     * 练习题版本库的最新版本
     */
    private Integer relatedVersion;

}