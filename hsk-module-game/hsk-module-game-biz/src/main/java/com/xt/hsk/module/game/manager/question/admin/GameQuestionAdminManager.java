package com.xt.hsk.module.game.manager.question.admin;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.json.JSONUtil;
import cn.idev.excel.EasyExcel;
import cn.idev.excel.exception.ExcelAnalysisException;
import com.mzt.logapi.context.LogRecordContext;
import com.rnkrsoft.bopomofo4j.Bopomofo4j;
import com.xt.hsk.framework.common.enums.IsShowEnum;
import com.xt.hsk.framework.common.pojo.ImportResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.security.core.util.SecurityFrameworkUtils;
import com.xt.hsk.framework.web.core.util.WebFrameworkUtils;
import com.xt.hsk.module.game.controller.admin.question.vo.*;
import com.xt.hsk.module.game.convert.question.GameQuestionConvert;
import com.xt.hsk.module.game.dal.dataobject.exercisequestion.ExerciseQuestionDO;
import com.xt.hsk.module.game.dal.dataobject.question.GameQuestionDO;
import com.xt.hsk.module.game.dal.dataobject.question.GameQuestionVersionDO;
import com.xt.hsk.module.game.enums.question.GameQuestionTypeEnum;
import com.xt.hsk.module.game.listener.importtask.KaraokeQuestionImportListener;
import com.xt.hsk.module.game.listener.importtask.WordToSentenceQuestionImportListener;
import com.xt.hsk.module.game.service.exercisequestion.ExerciseQuestionService;
import com.xt.hsk.module.game.service.question.GameQuestionService;
import com.xt.hsk.module.game.service.question.GameQuestionVersionService;
import com.xt.hsk.module.game.util.PinyinUtil;
import com.xt.hsk.module.infra.listener.CompositeRowLimitListener;
import com.xt.hsk.module.thirdparty.api.YouDaoApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.game.enums.ErrorCodeConstants.*;


/**
 * 游戏-题库 后台管理 Manager
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@Component
public class GameQuestionAdminManager {

    @Resource
    private GameQuestionService gameQuestionService;

    @Resource
    private GameQuestionVersionService questionVersionService;

    @Resource
    private ExerciseQuestionService exerciseQuestionService;

    @Resource
    private GameQuestionVersionService gameQuestionVersionService;

    @Resource
    private YouDaoApi youDaoApi;

    /**
     * 创建题目
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createQuestion(GameQuestionSaveReqVO createReqVO) {

        // 获取题目类型枚举
        GameQuestionTypeEnum questionType = GameQuestionTypeEnum.getByCode(createReqVO.getType());
        if (questionType == null) {
            throw exception(GAME_QUESTION_TYPE_NOT_EXISTS);
        }

        // 生成音频
        generateAudioUrl(createReqVO, questionType);

        // 转换为实体对象
        GameQuestionDO question = GameQuestionConvert.INSTANCE.saveReqVoToDo(createReqVO);

        // 生成拼音
        generatePinyin(question, questionType);

        // 设置是否展示，默认展示
        question.setIsShow(IsShowEnum.validCodeOrDefault(createReqVO.getIsShow(), IsShowEnum.SHOW));
        question.setLatestVersion(1);

        // 保存题目
        gameQuestionService.save(question);

        // 保存题目版本
        GameQuestionVersionDO questionVersion = GameQuestionConvert.INSTANCE.doToVersionDo(question);
        questionVersion.setVersion(1);
        questionVersion.setId(null);
        questionVersionService.save(questionVersion);

        // 设置日志上下文变量
        LogRecordContext.putVariable("questionId", question.getId());
        LogRecordContext.putVariable("question", question);
        LogRecordContext.putVariable("questionTypeName", questionType.getDesc());

        return question.getId();
    }

    /**
     * 获取音频 URL
     *
     * @param questionContent 题目内容
     * @return 字符串
     */
    private String getAudioUrl(String questionContent) {

        if (CharSequenceUtil.isBlank(questionContent)) {
            return null;
        }

        try {
            return youDaoApi.getAudioUrl(questionContent, IdUtil.simpleUUID());
        } catch (Exception e) {
            log.error("创建专项练习题目,获取音频失败", e);
        }

        return null;
    }

    /**
     * 处理参考答案
     *
     * @param referenceAnswerList 参考答案列表
     * @return 参考答案字符串
     */
    private String handleReferenceAnswer(
        List<QuestionReferenceAnswerSaveReqVO> referenceAnswerList) {
        if (CollUtil.isEmpty(referenceAnswerList)) {
            return JSONUtil.toJsonStr(Collections.emptyList());
        }

        List<QuestionReferenceAnswerSaveReqVO> saveReqList = new ArrayList<>();
        for (QuestionReferenceAnswerSaveReqVO vo : referenceAnswerList) {
            if (CharSequenceUtil.isBlank(vo.getReferenceAnswerCn())) {
                continue;
            }

            if (CharSequenceUtil.isBlank(vo.getAudioUrl())) {
                vo.setAudioUrl(getAudioUrl(vo.getReferenceAnswerCn()));
            }
            saveReqList.add(vo);
        }

        return JSONUtil.toJsonStr(saveReqList);
    }

    /**
     * 根据题目类型生成拼音
     *
     * @param question     题目对象
     * @param questionType 题目类型枚举
     */
    private void generatePinyin(GameQuestionDO question, GameQuestionTypeEnum questionType) {

        switch (questionType) {
            case WORD_TO_SENTENCE:
                question.setPinyin(PinyinUtil.generatePinyin(question.getQuestionSplit(), questionType));
                question.setKnowledgePointPinyin(PinyinUtil.generatePinyin(question.getKnowledgePoint(), questionType));
                break;
            case KARAOKE:
                question.setPinyin(PinyinUtil.generatePinyin(question.getQuestionContent(), questionType));
                break;
            default:
                // 其他类型不处理拼音
                break;
        }
    }

    /**
     * 根据题目类型生成音频
     *
     * @param reqVO        请求对象
     * @param questionType 题目类型枚举
     */
    private void generateAudioUrl(GameQuestionSaveReqVO reqVO, GameQuestionTypeEnum questionType) {

        switch (questionType) {
            case WORD_TO_SENTENCE:
                reqVO.setReferenceAnswer(handleReferenceAnswer(reqVO.getReferenceAnswerList()));
                break;
            case KARAOKE:
                reqVO.setAudioUrl(getAudioUrl(reqVO.getQuestionContent()));
                break;
            default:
                // 其他类型不处理音频
                break;
        }
    }


    /**
     * 更新题目
     */
    public void updateQuestion(GameQuestionSaveReqVO updateReqVO) {
        // 校验存在
        GameQuestionDO gameQuestionDO = validateQuestionExists(updateReqVO.getId());

        // 获取题目类型枚举
        GameQuestionTypeEnum questionType = GameQuestionTypeEnum.getByCode(gameQuestionDO.getType());
        if (questionType == null) {
            throw exception(GAME_QUESTION_TYPE_NOT_EXISTS);
        }

        // 生成音频
        generateAudioUrl(updateReqVO, questionType);

        // 转换为实体对象
        GameQuestionDO updateObj = GameQuestionConvert.INSTANCE.saveReqVoToDo(updateReqVO);

        // 生成拼音
        generatePinyin(updateObj, questionType);

        // 设置是否展示，默认展示
        updateObj.setIsShow(IsShowEnum.validCodeOrDefault(updateReqVO.getIsShow(), IsShowEnum.SHOW));

        Integer maxVersion = questionVersionService.lambdaQuery()
                .eq(GameQuestionVersionDO::getQuestionId, updateObj.getId())
                .select(GameQuestionVersionDO::getVersion)
                .orderByDesc(GameQuestionVersionDO::getVersion)
                .last("LIMIT 1")
                .oneOpt()
                .map(GameQuestionVersionDO::getVersion)
                .orElse(0);

        // 保存题目版本
        GameQuestionVersionDO questionVersion = GameQuestionConvert.INSTANCE.doToVersionDo(updateObj);
        questionVersion.setId(null);
        questionVersion.setVersion(maxVersion + 1);
        questionVersionService.save(questionVersion);

        updateObj.setLatestVersion(questionVersion.getVersion());
        updateObj.setUpdateTime(LocalDateTime.now());
        updateObj.setUpdater(String.valueOf(SecurityFrameworkUtils.getLoginUserId()));
        gameQuestionService.updateById(updateObj);

        // 设置日志上下文变量
        LogRecordContext.putVariable("question", updateObj);
        LogRecordContext.putVariable("questionTypeName", questionType.getDesc());
    }

    /**
     * 删除题目
     */
    public void deleteQuestion(Long id) {
        // 校验存在并获取题目信息
        GameQuestionDO question = validateQuestionExists(id);

        boolean exists = gameQuestionService.lambdaQuery()
                .eq(GameQuestionDO::getId, id)
                .exists();

        if (exists) {
            throw exception(GAME_QUESTION_IN_USE);
        }

        // 获取题目类型名称
        GameQuestionTypeEnum questionType = GameQuestionTypeEnum.getByCode(question.getType());
        String questionTypeName = questionType != null ? questionType.getDesc() : "未知类型";

        // 设置日志上下文变量
        LogRecordContext.putVariable("question", question);
        LogRecordContext.putVariable("questionTypeName", questionTypeName);

        // 删除
        gameQuestionService.removeById(id);
    }

    /**
     * 验证题目是否存在
     *
     * @param id 题目id
     * @return 题目
     */
    private GameQuestionDO validateQuestionExists(Long id) {
        GameQuestionDO gameQuestion = gameQuestionService.getById(id);
        if (gameQuestion == null) {
            throw exception(GAME_QUESTION_NOT_EXISTS);
        }

        return gameQuestion;
    }

    /**
     * 根据id获得题目
     */
    public GameQuestionRespVO getQuestion(Long id) {
        GameQuestionDO question = gameQuestionService.getById(id);
        if (question == null) {
            return null;
        }

        // 转换为VO
        GameQuestionRespVO respVO = GameQuestionConvert.INSTANCE.doToRespVo(question);

        // 查询引用该题目的专项练习组列表
        List<GameQuestionExerciseReferenceVO> exerciseReferences = gameQuestionService.getExerciseReferences(id, question.getType());
        respVO.setExerciseReferenceList(exerciseReferences);

        return respVO;
    }

    /**
     * 分页获得题目
     */
    public PageResult<GameQuestionRespVO> getQuestionPage(GameQuestionPageReqVO pageReqVO) {
        PageResult<GameQuestionDO> questionPage = gameQuestionService.getQuestionPage(pageReqVO);

        List<GameQuestionDO> doList = questionPage.getList();

        List<GameQuestionRespVO> voList = GameQuestionConvert.INSTANCE.doListToRespVOList(doList);

        // 设置题目引用次数
        setReferenceCount(voList, pageReqVO.getType());

        return new PageResult<>(voList, questionPage.getTotal());
    }

    /**
     * 设置题目引用次数
     *
     * @param voList VO 列表
     * @param type
     */
    private void setReferenceCount(List<GameQuestionRespVO> voList, Integer type) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        List<Long> questionIdList = voList.stream().map(GameQuestionRespVO::getId).toList();

        Map<Long, Integer> countMap = exerciseQuestionService.referenceCountMap(questionIdList, type);

        voList.forEach(vo -> vo.setReferenceCount(countMap.getOrDefault(vo.getId(), 0)));
    }


    /**
     * 批量删除题目
     *
     * @param ids id列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }

        boolean exists = exerciseQuestionService.lambdaQuery()
                .in(ExerciseQuestionDO::getQuestionId, ids)
                .exists();

        if (exists) {
            throw exception(GAME_QUESTION_IN_USE);
        }

        // 设置日志上下文变量
        LogRecordContext.putVariable("deleteCount", ids.size());
        LogRecordContext.putVariable("batchBizNo", ids.get(0).toString()); // 使用第一个ID作为业务编号

        gameQuestionService.removeByIds(ids);
    }

    /**
     * 批量显示/隐藏题目
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateStatus(List<Long> ids, Integer isShow) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }

        isShow = IsShowEnum.validCodeOrDefault(isShow, IsShowEnum.SHOW);

        // 如果是隐藏操作，需要检查题目是否被引用
        if (IsShowEnum.DISPLAY.getCode().equals(isShow)) {
            boolean exists = exerciseQuestionService.lambdaQuery()
                    .in(ExerciseQuestionDO::getQuestionId, ids)
                    .exists();

            if (exists) {
                throw exception(GAME_QUESTION_IN_USE_CANNOT_HIDE);
            }
        }

        // 设置日志上下文变量
        String statusText = IsShowEnum.SHOW.getCode().equals(isShow) ? "显示" : "隐藏";
        String statusName = IsShowEnum.SHOW.getCode().equals(isShow) ? "显示" : "隐藏";
        LogRecordContext.putVariable("statusText", statusText);
        LogRecordContext.putVariable("statusName", statusName);
        LogRecordContext.putVariable("updateCount", ids.size());
        LogRecordContext.putVariable("batchBizNo", ids.get(0).toString()); // 使用第一个ID作为业务编号

        gameQuestionService.lambdaUpdate()
                .in(GameQuestionDO::getId, ids)
                .set(GameQuestionDO::getIsShow, isShow)
                .set(GameQuestionDO::getUpdateTime, LocalDateTime.now())
                .set(GameQuestionDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                .update();
    }

    public ImportResult importQuestion(MultipartFile file, Integer type) {

        ImportResult result = new ImportResult();

        GameQuestionTypeEnum questionTypeEnum = GameQuestionTypeEnum.getByCode(type);
        if (questionTypeEnum == null) {
            result.setSuccess(false);
            result.setMsg(Collections.singletonList("导入失败：" + GAME_QUESTION_TYPE_NOT_EXISTS.getMsg()));
            result.setInvalidCount(0);
            result.setValidCount(0);

            return result;
        }


        try {

            if (GameQuestionTypeEnum.WORD_TO_SENTENCE.equals(questionTypeEnum)) {
                // 创建数据处理监听器
                WordToSentenceQuestionImportListener dataListener = new WordToSentenceQuestionImportListener(
                        gameQuestionService, gameQuestionVersionService, youDaoApi);

                // 创建组合监听器，限制最多导入500条数据
                CompositeRowLimitListener<WordToSentenceQuestionImportExcelVO> compositeListener =
                        new CompositeRowLimitListener<>(500, dataListener,
                                "一次最多导入500条数据，请分批导入");

                // 执行导入，设置headRowNumber=1表示从第二行开始读取数据（第一行是表头）
                EasyExcel.read(file.getInputStream(), WordToSentenceQuestionImportExcelVO.class, compositeListener)
                        .headRowNumber(2)
                        .sheet()
                        .doRead();

                result.setSuccess(dataListener.SUCCESS);
                result.setMsg(dataListener.msg);
                result.setInvalidCount(Math.max(dataListener.INVALID_COUNT, 0));
                result.setValidCount(dataListener.VALID_COUNT);
            } else if (GameQuestionTypeEnum.KARAOKE.equals(questionTypeEnum)) {
                // 创建数据处理监听器
                KaraokeQuestionImportListener dataListener = new KaraokeQuestionImportListener(
                        gameQuestionService, gameQuestionVersionService, youDaoApi);

                // 创建组合监听器，限制最多导入500条数据
                CompositeRowLimitListener<KaraokeQuestionImportExcelVO> compositeListener =
                        new CompositeRowLimitListener<>(500, dataListener,
                                "一次最多导入500条数据，请分批导入");

                // 执行导入，设置headRowNumber=1表示从第二行开始读取数据（第一行是表头）
                EasyExcel.read(file.getInputStream(), KaraokeQuestionImportExcelVO.class, compositeListener)
                        .headRowNumber(2)
                        .sheet()
                        .doRead();

                result.setSuccess(dataListener.SUCCESS);
                result.setMsg(dataListener.msg);
                result.setInvalidCount(Math.max(dataListener.INVALID_COUNT, 0));
                result.setValidCount(dataListener.VALID_COUNT);
            } else {
                result.setSuccess(false);
                result.setMsg(Collections.singletonList("导入失败：未找到导入监听器"));
                result.setInvalidCount(0);
                result.setValidCount(0);

            }

        } catch (ExcelAnalysisException e) {
            log.warn("导入题目受限: {}", e.getMessage());
            result.setSuccess(false);
            result.setMsg(Collections.singletonList(e.getMessage()));
            result.setInvalidCount(0);
            result.setValidCount(0);
        } catch (Exception e) {
            log.error("导入题目失败: {}", e.getMessage(), e);
            result.setSuccess(false);
            result.setMsg(Collections.singletonList("导入失败：" + e.getMessage()));
            result.setInvalidCount(0);
            result.setValidCount(0);
        }

        // 设置日志上下文变量
        LogRecordContext.putVariable("importResult", result);
        LogRecordContext.putVariable("typeName", questionTypeEnum.getDesc());

        return result;

    }

    /**
     * 检查题目是否可以删除
     */
    public GameQuestionDeleteCheckRespVO checkQuestionCanDelete(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new GameQuestionDeleteCheckRespVO(true);
        }

        boolean exists = exerciseQuestionService.lambdaQuery()
                .in(ExerciseQuestionDO::getQuestionId, ids)
                .exists();

        return new GameQuestionDeleteCheckRespVO(!exists);
    }

    /**
     * 检查题目是否可以隐藏
     */
    public GameQuestionHideCheckRespVO checkQuestionCanHide(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new GameQuestionHideCheckRespVO(true);
        }

        boolean exists = exerciseQuestionService.lambdaQuery()
                .in(ExerciseQuestionDO::getQuestionId, ids)
                .exists();

        return new GameQuestionHideCheckRespVO(!exists);
    }

    /**
     * 生成拼音
     */
    public GameQuestionRespVO generatePinyin(String content, Integer type) {
        GameQuestionTypeEnum questionType = GameQuestionTypeEnum.getByCode(type);

        if (questionType == null) {
            throw exception(SPECIAL_EXERCISE_UNKNOWN_EXERCISE_TYPE);
        }

        String pinyin = PinyinUtil.generatePinyin(content, questionType);

        if (CharSequenceUtil.isEmpty(pinyin)) {
            throw exception(GAME_QUESTION_GENERATE_PINYIN_FAIL);
        }

        return new GameQuestionRespVO().setPinyin(pinyin);
    }
}