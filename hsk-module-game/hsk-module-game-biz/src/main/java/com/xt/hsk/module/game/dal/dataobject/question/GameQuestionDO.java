package com.xt.hsk.module.game.dal.dataobject.question;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import com.xt.hsk.module.game.enums.question.GameQuestionTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 游戏-题库 DO
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@TableName("game_question")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GameQuestionDO extends BaseDO {

    /**
     * 题库ID
     */
    @TableId
    private Long id;
    /**
     * HSK等级
     */
    private Integer hskLevel;
    /**
     * 题目类型
     *
     * @see GameQuestionTypeEnum
     */
    private Integer type;
    /**
     * 题干内容
     */
    private String questionContent;
    /**
     * 拆分后的题干
     */
    private String questionSplit;
    /**
     * 题干的拼音
     */
    private String pinyin;
    /**
     * 音频链接
     */
    private String audioUrl;
    /**
     * 考察点
     */
    private String knowledgePoint;
    /**
     * 考察点拼音
     */
    private String knowledgePointPinyin;
    /**
     * 参考答案
     */
    private String referenceAnswer;
    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;

    /**
     * 最新版本
     */
    private Integer latestVersion;

    /**
     * 被引用次数（非数据库字段，仅用于查询结果）
     */
    @TableField(exist = false)
    private Integer referenceCount;

}