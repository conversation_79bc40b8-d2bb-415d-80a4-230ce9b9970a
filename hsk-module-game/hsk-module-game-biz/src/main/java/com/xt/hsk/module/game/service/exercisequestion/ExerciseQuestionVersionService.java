package com.xt.hsk.module.game.service.exercisequestion;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionRespVO;
import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionVersionPageReqVO;
import com.xt.hsk.module.game.dal.dataobject.exercisequestion.ExerciseQuestionVersionDO;

import java.util.List;

/**
 * 游戏-练习组题目版本库 Service 接口
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
public interface ExerciseQuestionVersionService extends IService<ExerciseQuestionVersionDO> {
    PageResult<ExerciseQuestionVersionDO> selectPage(ExerciseQuestionVersionPageReqVO pageReqVO);

    /**
     * 批量新增数据
     *
     * @param doList 实例对象列表
     */
    void saveBatch(List<ExerciseQuestionVersionDO> doList);

    /**
     * 根据专项练习ID获取最大的版本
     *
     * @param specialExercisesId 专项练习ID
     * @return 最大的版本
     */
    Integer getMaxVersion(Long specialExercisesId);

    /**
     * 获取专项练习组题目列表
     *
     * @param specialExerciseId 专项练习 ID
     * @param type              类型
     * @param version           版本
     * @return 练习组的题目列表
     */
    List<ExerciseQuestionRespVO> getExerciseQuestion(Long specialExerciseId, Integer type, Integer version);

    /**
     * 根据题目版本ID列表和类型获取练习题目
     *
     * @param exerciseQuestionVersionId 练习题目版本ID
     * @param type                      题目类型
     * @return 练习题目
     */
    ExerciseQuestionRespVO getExerciseQuestionById(Long exerciseQuestionVersionId, Integer type);

    /**
     * 根据题目版本ID列表和类型获取练习题目
     *
     * @param exerciseQuestionVersionIdList 练习题目版本ID列表
     * @param type                          题目类型
     * @return 练习题目列表
     */
    List<ExerciseQuestionRespVO> getExerciseQuestionByIdList(List<Long> exerciseQuestionVersionIdList, Integer type);

}