package com.xt.hsk.module.game.convert.question;

import com.xt.hsk.module.game.controller.admin.question.vo.GameQuestionRespVO;
import com.xt.hsk.module.game.controller.admin.question.vo.GameQuestionSaveReqVO;
import com.xt.hsk.module.game.dal.dataobject.question.GameQuestionDO;
import com.xt.hsk.module.game.dal.dataobject.question.GameQuestionVersionDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 游戏题目 转换类
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Mapper
public interface GameQuestionConvert {

    GameQuestionConvert INSTANCE = Mappers.getMapper(GameQuestionConvert.class);

    /**
     * 保存 req vo类转do类
     */
    GameQuestionDO saveReqVoToDo(GameQuestionSaveReqVO createReqVO);

    /**
     * do列表转resp vo列表
     */
    List<GameQuestionRespVO> doListToRespVOList(List<GameQuestionDO> doList);

    /**
     * do类转版本do类
     */
    @Mapping(source = "id", target = "questionId")
    GameQuestionVersionDO doToVersionDo(GameQuestionDO question);

    /**
     * do类转版本resp类
     */
    GameQuestionRespVO doToRespVo(GameQuestionDO question);
}
