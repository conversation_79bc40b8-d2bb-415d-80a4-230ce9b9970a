package com.xt.hsk.module.game.enums.record;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 答题状态枚举
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Getter
@AllArgsConstructor
public enum GameAnswerStatusEnum implements BasicEnum<Integer>, VO {
    /**
     * 答题状态枚举
     */
    UNANSWERED(0, "未答"),
    ANSWERED(1, "已答"),
    FAILED(2, "失败"),
    ;

    private final Integer code;
    private final String desc;

    /**
     * 根据答题状态码获取对应的描述信息
     *
     * @param code 答题状态码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (GameAnswerStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据答题状态码获取对应的枚举实例
     *
     * @param code 答题状态码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static GameAnswerStatusEnum getByCode(Integer code) {
        for (GameAnswerStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
