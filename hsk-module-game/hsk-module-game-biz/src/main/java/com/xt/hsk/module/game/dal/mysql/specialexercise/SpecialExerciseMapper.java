package com.xt.hsk.module.game.dal.mysql.specialexercise;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.module.game.api.dto.SpecialExerciseOtQuoteReqDTO;
import com.xt.hsk.module.game.api.dto.SpecialExercisePageRespDTO;
import com.xt.hsk.module.game.api.dto.WordQuoteCountDTO;
import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionRespVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.ExerciseQuestionInfoRespVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExercisePageReqVO;
import com.xt.hsk.module.game.controller.app.specialexercise.vo.SpecialExerciseAppPageReqVO;
import com.xt.hsk.module.game.controller.app.specialexercise.vo.SpecialExerciseRecordRespVO;
import com.xt.hsk.module.game.dal.dataobject.specialexercise.SpecialExerciseDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 游戏-专项练习-练习组 Mapper
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Mapper
public interface SpecialExerciseMapper extends BaseMapperX<SpecialExerciseDO> {

    /**
     * 分页获取专项练习
     */
    IPage<SpecialExerciseDO> getSpecialExercisePage(Page<SpecialExerciseDO> page, @Param("req") SpecialExercisePageReqVO pageReqVO);

    /**
     * 专项练习总数
     */
    Long countSpecialExercisePage(@Param("req") SpecialExercisePageReqVO reqVO);

    /**
     * 获取专项练习的题目信息
     */
    List<ExerciseQuestionInfoRespVO> getQuestionInfoList(
        @Param("idList") List<Long> specialExerciseIdList, @Param("type") Integer type);

    /**
     * 获取专项练习组的题目
     *
     * @param specialExerciseId 专项练习id
     * @param type              类型
     * @return 练习组的题目列表
     */
    List<ExerciseQuestionRespVO> getExerciseQuestion(
        @Param("specialExerciseId") Long specialExerciseId, @Param("type") Integer type);

    List<SpecialExercisePageRespDTO> getQuestionInfoListByWordId(Long wordId);

    /**
     * 获取单词的引用数量
     *
     * @param wordIds
     * @return
     */
    List<WordQuoteCountDTO> countQuoteByWordIds(List<Long> wordIds);

    List<SpecialExercisePageRespDTO> getSpecialExerciseQuoteInfo(SpecialExerciseOtQuoteReqDTO req);

    /**
     * 分页获取专项练习app
     */
    IPage<SpecialExerciseRecordRespVO> getAppSpecialExercisePage(Page<SpecialExerciseRecordRespVO> page, @Param("req") SpecialExerciseAppPageReqVO reqVO);

}