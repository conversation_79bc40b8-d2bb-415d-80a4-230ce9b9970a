package com.xt.hsk.module.game.service.exercisequestion;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionPageReqVO;
import com.xt.hsk.module.game.controller.admin.question.vo.GameQuestionReferenceCountVO;
import com.xt.hsk.module.game.dal.dataobject.exercisequestion.ExerciseQuestionDO;
import com.xt.hsk.module.game.dal.mysql.exercisequestion.ExerciseQuestionMapper;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;


/**
 * 游戏-练习组题目 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Service
public class ExerciseQuestionServiceImpl extends ServiceImpl<ExerciseQuestionMapper, ExerciseQuestionDO> implements ExerciseQuestionService {

    @Resource
    private ExerciseQuestionMapper exerciseQuestionMapper;

    @Override
    public PageResult<ExerciseQuestionDO> selectPage(ExerciseQuestionPageReqVO pageReqVO) {

        return exerciseQuestionMapper.selectPage(pageReqVO);
    }

    /**
     * 批量新增数据
     *
     * @param doList 实例对象列表
     */
    @Override
    public void saveBatch(List<ExerciseQuestionDO> doList) {
        if (CollUtil.isNotEmpty(doList)) {
            exerciseQuestionMapper.saveBatch(doList);
        }
    }

    /**
     * 题目引用次数列表map
     *
     * @param questionIdList 题目ID列表
     * @param type 类型
     * @return 题目引用次数列表map
     */
    @Override
    public Map<Long, Integer> referenceCountMap(List<Long> questionIdList, Integer type) {
        if (CollUtil.isEmpty(questionIdList)) {
            return Collections.emptyMap();
        }

        return exerciseQuestionMapper.referenceCount(questionIdList, type)
                .stream()
                .collect(Collectors.toMap(
                        GameQuestionReferenceCountVO::getQuestionId,
                        GameQuestionReferenceCountVO::getReferenceCount,
                        (k1, k2) -> k1
                ));

    }

    @Override
    public int isQuoteWord(Long wordId) {
        return exerciseQuestionMapper.isQuoteWord(wordId);
    }

}