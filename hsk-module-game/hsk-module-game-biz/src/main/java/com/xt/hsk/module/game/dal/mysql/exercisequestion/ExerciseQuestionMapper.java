package com.xt.hsk.module.game.dal.mysql.exercisequestion;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionPageReqVO;
import com.xt.hsk.module.game.controller.admin.question.vo.GameQuestionReferenceCountVO;
import com.xt.hsk.module.game.dal.dataobject.exercisequestion.ExerciseQuestionDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 游戏-练习组题目 Mapper
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Mapper
public interface ExerciseQuestionMapper extends BaseMapperX<ExerciseQuestionDO> {

    default PageResult<ExerciseQuestionDO> selectPage(ExerciseQuestionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ExerciseQuestionDO>()
            .eqIfPresent(ExerciseQuestionDO::getSpecialExerciseId, reqVO.getSpecialExerciseId())
                .eqIfPresent(ExerciseQuestionDO::getQuestionId, reqVO.getQuestionId())
                .eqIfPresent(ExerciseQuestionDO::getTranslationOt, reqVO.getTranslationOt())
                .eqIfPresent(ExerciseQuestionDO::getIsShow, reqVO.getIsShow())
                .eqIfPresent(ExerciseQuestionDO::getSort, reqVO.getSort())
                .eqIfPresent(ExerciseQuestionDO::getShowRound, reqVO.getShowRound())
                .eqIfPresent(ExerciseQuestionDO::getRoundNumber, reqVO.getRoundNumber())
                .eqIfPresent(ExerciseQuestionDO::getVersion, reqVO.getVersion())
                .eqIfPresent(ExerciseQuestionDO::getType, reqVO.getType())
                .betweenIfPresent(ExerciseQuestionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ExerciseQuestionDO::getId));
    }


    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities 实例对象列表
     * @return 影响行数
     */
    int saveBatch(@Param("entities") List<ExerciseQuestionDO> entities);

    /**
     * 题目引用次数列表map
     *
     * @param questionIdList 题目ID列表
     * @param type 类型
     * @return 题目引用次数列表map
     */
    List<GameQuestionReferenceCountVO> referenceCount(@Param("questionIdList") List<Long> questionIdList, @Param("type") Integer type);

    /**
     * 查询单词的引用次数
     *
     * @param wordId
     * @return
     */
    int isQuoteWord(Long wordId);
}