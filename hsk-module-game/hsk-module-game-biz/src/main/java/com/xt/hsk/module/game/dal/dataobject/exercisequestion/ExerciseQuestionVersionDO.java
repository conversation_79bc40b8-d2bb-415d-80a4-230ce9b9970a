package com.xt.hsk.module.game.dal.dataobject.exercisequestion;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 游戏-练习组题目版本库 DO
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@TableName("game_exercise_question_version")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExerciseQuestionVersionDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 练习组题目ID
     */
    private Long exerciseQuestionId;
    /**
     * 专项练习ID
     */
    private Long specialExerciseId;
    /**
     * 题目ID
     */
    private Long questionId;
    /**
     * 翻译
     */
    private String translationOt;
    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 展示轮次（仅word_matching）
     */
    private Integer showRound;
    /**
     * 轮次数（仅word_matching）
     */
    private Integer roundNumber;
    /**
     * 版本号
     */
    private Integer version;
    /**
     * 类型
     *
     * @see SpecialExerciseTypeEnum
     */
    private Integer type;

}