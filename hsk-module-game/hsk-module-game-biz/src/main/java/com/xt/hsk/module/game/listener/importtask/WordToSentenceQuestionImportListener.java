package com.xt.hsk.module.game.listener.importtask;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import cn.idev.excel.context.AnalysisContext;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.enums.IsShowEnum;
import com.xt.hsk.module.game.controller.admin.question.vo.QuestionReferenceAnswerSaveReqVO;
import com.xt.hsk.module.game.controller.admin.question.vo.QuestionReferenceAnswerVO;
import com.xt.hsk.module.game.controller.admin.question.vo.WordToSentenceQuestionImportExcelVO;
import com.xt.hsk.module.game.convert.question.GameQuestionConvert;
import com.xt.hsk.module.game.dal.dataobject.question.GameQuestionDO;
import com.xt.hsk.module.game.dal.dataobject.question.GameQuestionVersionDO;
import com.xt.hsk.module.game.enums.question.GameQuestionTypeEnum;
import com.xt.hsk.module.game.service.question.GameQuestionService;
import com.xt.hsk.module.game.service.question.GameQuestionVersionService;
import com.xt.hsk.module.game.util.PinyinUtil;
import com.xt.hsk.module.infra.listener.BaseAnalysisEventListener;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import com.xt.hsk.module.thirdparty.api.YouDaoApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.transaction.annotation.Transactional;

/**
 * 连词成句题目导入监听器
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Slf4j
public class WordToSentenceQuestionImportListener extends
    BaseAnalysisEventListener<WordToSentenceQuestionImportExcelVO> {

    private final List<WordToSentenceQuestionImportExcelVO> voList = new ArrayList<>();

    private final GameQuestionService gameQuestionService;

    private final GameQuestionVersionService gameQuestionVersionService;

    private final YouDaoApi youDaoApi;

    public WordToSentenceQuestionImportListener(GameQuestionService gameQuestionService,
                                                GameQuestionVersionService gameQuestionVersionService,
                                                YouDaoApi youDaoApi) {
        this.gameQuestionService = gameQuestionService;
        this.gameQuestionVersionService = gameQuestionVersionService;
        this.youDaoApi = youDaoApi;
    }

    @Override
    public void invoke(WordToSentenceQuestionImportExcelVO data, AnalysisContext context) {
        SUCCESS = false;

        // 行号 + 1是因为索引从0开始
        int rowIndex = context.readRowHolder().getRowIndex() + 1;

        try {
            if (Objects.isNull(data.getHskLevel())) {
                msg.add(String.format("第%d行：HSK等级不能为空", rowIndex));
            }

            Integer hskLevel = getHskCodeByLevel(data.getHskLevel());
            if (hskLevel == null) {
                msg.add(String.format("第%d行：HSK等级不存在", rowIndex));
            }

            if (CharSequenceUtil.isBlank(data.getQuestionContent())) {
                msg.add(String.format("第%d行：题干不能为空", rowIndex));
            }

            if (CharSequenceUtil.isBlank(data.getQuestionSplit())) {
                msg.add(String.format("第%d行：拆分后的题干不能为空", rowIndex));
            }

            if (CharSequenceUtil.isBlank(data.getReferenceAnswerCn1())) {
                msg.add(String.format("第%d行：参考答案1不能为空", rowIndex));
            }

            voList.add(data);

        } catch (Exception e) {
            // 记录未预期的异常
            log.error("处理第{}行数据时发生异常", rowIndex, e);
            msg.add(String.format("第%d行：处理失败，原因：%s", rowIndex, e.getMessage()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (CollUtil.isEmpty(voList)) {
            msg.add("未读取到数据");
            return;
        }

        if (CollUtil.isNotEmpty(msg)) {
            return;
        }

        List<GameQuestionDO> allDoList = new ArrayList<>();

        // 将voList分为50条数据一组
        List<List<WordToSentenceQuestionImportExcelVO>> split = CollUtil.split(voList, 50);
        for (List<WordToSentenceQuestionImportExcelVO> importVOList : split) {
            List<GameQuestionDO> doList = importVOList.stream().map(vo -> {
                        GameQuestionDO question = new GameQuestionDO();
                        question.setHskLevel(getHskCodeByLevel(vo.getHskLevel()));
                        question.setType(GameQuestionTypeEnum.WORD_TO_SENTENCE.getCode());
                        question.setQuestionContent(vo.getQuestionContent());
                        question.setQuestionSplit(vo.getQuestionSplit());
                        question.setPinyin(PinyinUtil.generatePinyin(vo.getQuestionSplit(), GameQuestionTypeEnum.WORD_TO_SENTENCE));
                        question.setKnowledgePoint(vo.getKnowledgePoint());
                        question.setKnowledgePointPinyin(PinyinUtil.generatePinyin(vo.getKnowledgePoint(), GameQuestionTypeEnum.WORD_TO_SENTENCE));
                        question.setReferenceAnswer(handleReferenceAnswer(vo));
                        question.setIsShow(IsShowEnum.SHOW.getCode());
                        question.setLatestVersion(1);
                        return question;
                    })
                    .toList();
            gameQuestionService.saveBatch(doList);
            allDoList.addAll(doList);

            List<GameQuestionVersionDO> questionVersionList = doList.stream().map(question -> {
                        GameQuestionVersionDO questionVersion = GameQuestionConvert.INSTANCE.doToVersionDo(question);
                        questionVersion.setId(null);
                        return questionVersion;
                    })
                    .toList();

            gameQuestionVersionService.saveBatch(questionVersionList);

        }

        VALID_COUNT = voList.size();
        SUCCESS = true;

        // 异步生成音频
        CompletableFuture.runAsync(() -> {
            try {
                generateAudioUrl(allDoList);
            } catch (Exception e) {
                log.error("导入连词成句题目,异步生成音频失败,", e);
            }
        });

    }

    /**
     * 生成音频
     */
    private void generateAudioUrl(List<GameQuestionDO> allDoList) {
        if (CollUtil.isEmpty(allDoList)) {
            return;
        }

        log.info("导入连词成句题目,开始生成音频");
        for (GameQuestionDO question : allDoList) {

            try {
                String referenceAnswer = question.getReferenceAnswer();
                if (CharSequenceUtil.isNotBlank(referenceAnswer)) {
                    List<QuestionReferenceAnswerVO> answerVOList = JSONUtil.toList(referenceAnswer, QuestionReferenceAnswerVO.class);
                    for (QuestionReferenceAnswerVO answerVO : answerVOList) {
                        answerVO.setAudioUrl(getAudioUrl(answerVO.getReferenceAnswerCn()));
                    }

                    String newReferenceAnswer = JSONUtil.toJsonStr(answerVOList);

                    gameQuestionService.lambdaUpdate()
                            .set(GameQuestionDO::getReferenceAnswer, newReferenceAnswer)
                            .eq(GameQuestionDO::getId, question.getId())
                            .update();

                    gameQuestionVersionService.lambdaUpdate()
                            .set(GameQuestionVersionDO::getReferenceAnswer, newReferenceAnswer)
                            .eq(GameQuestionVersionDO::getQuestionId, question.getId())
                            .eq(GameQuestionVersionDO::getType, question.getType())
                            .eq(GameQuestionVersionDO::getVersion, question.getLatestVersion())
                            .update();
                }
            } catch (Exception e) {
                log.error("导入连词成句题目,更新音频失败,题目id:{}", question.getId(), e);
            }
        }

        log.info("导入连词成句题目,生成音频完成");

    }

    /**
     * 获取音频 URL
     *
     * @param questionContent 题目内容
     * @return 字符串
     */
    private String getAudioUrl(String questionContent) {

        if (CharSequenceUtil.isNotBlank(questionContent)) {
            try {
                return youDaoApi.getAudioUrl(questionContent, IdUtil.simpleUUID());
            } catch (Exception e) {
                log.error("导入连词成句题目,获取音频失败", e);
            }
        }


        return null;
    }

    private String handleReferenceAnswer(WordToSentenceQuestionImportExcelVO vo) {
        List<QuestionReferenceAnswerSaveReqVO> voList = new ArrayList<>();

        int sort = 1;

        QuestionReferenceAnswerSaveReqVO vo1 = new QuestionReferenceAnswerSaveReqVO();
        if (CharSequenceUtil.isNotBlank(vo.getReferenceAnswerCn1())
                || CharSequenceUtil.isNotBlank(vo.getReferenceAnswerEn1())
                || CharSequenceUtil.isNotBlank(vo.getReferenceAnswerOt1())) {
            vo1.setReferenceAnswerCn(vo.getReferenceAnswerCn1());
            vo1.setReferenceAnswerEn(vo.getReferenceAnswerEn1());
            vo1.setReferenceAnswerOt(vo.getReferenceAnswerOt1());
            vo1.setSort(sort);
            voList.add(vo1);

            sort += 1;
        }

        QuestionReferenceAnswerSaveReqVO vo2 = new QuestionReferenceAnswerSaveReqVO();
        if (CharSequenceUtil.isNotBlank(vo.getReferenceAnswerCn2())
                || CharSequenceUtil.isNotBlank(vo.getReferenceAnswerEn2())
                || CharSequenceUtil.isNotBlank(vo.getReferenceAnswerOt2())) {
            vo2.setReferenceAnswerCn(vo.getReferenceAnswerCn2());
            vo2.setReferenceAnswerEn(vo.getReferenceAnswerEn2());
            vo2.setReferenceAnswerOt(vo.getReferenceAnswerOt2());
            vo2.setSort(sort);
            voList.add(vo2);

            sort += 1;
        }

        QuestionReferenceAnswerSaveReqVO vo3 = new QuestionReferenceAnswerSaveReqVO();
        if (CharSequenceUtil.isNotBlank(vo.getReferenceAnswerCn3())
                || CharSequenceUtil.isNotBlank(vo.getReferenceAnswerEn3())
                || CharSequenceUtil.isNotBlank(vo.getReferenceAnswerOt3())) {
            vo3.setReferenceAnswerCn(vo.getReferenceAnswerCn3());
            vo3.setReferenceAnswerEn(vo.getReferenceAnswerEn3());
            vo3.setReferenceAnswerOt(vo.getReferenceAnswerOt3());
            vo3.setSort(sort);
            voList.add(vo3);

            sort += 1;
        }

        QuestionReferenceAnswerSaveReqVO vo4 = new QuestionReferenceAnswerSaveReqVO();
        if (CharSequenceUtil.isNotBlank(vo.getReferenceAnswerCn4())
                || CharSequenceUtil.isNotBlank(vo.getReferenceAnswerEn4())
                || CharSequenceUtil.isNotBlank(vo.getReferenceAnswerOt4())) {
            vo4.setReferenceAnswerCn(vo.getReferenceAnswerCn4());
            vo4.setReferenceAnswerEn(vo.getReferenceAnswerEn4());
            vo4.setReferenceAnswerOt(vo.getReferenceAnswerOt4());
            vo4.setSort(sort);
            voList.add(vo4);
            sort += 1;
        }

        return JSONUtil.toJsonStr(voList);

    }

    private Integer getHskCodeByLevel(Integer hskLevel) {
        if (hskLevel == 0) {
            return HskEnum.HSK_0.getCode();
        }
        if (hskLevel == 1) {
            return HskEnum.HSK_1.getCode();
        }
        if (hskLevel == 2) {
            return HskEnum.HSK_2.getCode();
        }
        if (hskLevel == 3) {
            return HskEnum.HSK_3.getCode();
        }
        if (hskLevel == 4) {
            return HskEnum.HSK_4.getCode();
        }
        if (hskLevel == 5) {
            return HskEnum.HSK_5.getCode();
        }
        if (hskLevel == 6) {
            return HskEnum.HSK_6.getCode();
        }
        return null;
    }
} 