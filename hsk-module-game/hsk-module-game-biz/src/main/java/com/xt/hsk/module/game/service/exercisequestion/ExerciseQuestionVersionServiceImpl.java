package com.xt.hsk.module.game.service.exercisequestion;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionRespVO;
import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionVersionPageReqVO;
import com.xt.hsk.module.game.dal.dataobject.exercisequestion.ExerciseQuestionVersionDO;
import com.xt.hsk.module.game.dal.mysql.exercisequestion.ExerciseQuestionVersionMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;


/**
 * 游戏-练习组题目版本库 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Service
public class ExerciseQuestionVersionServiceImpl extends ServiceImpl<ExerciseQuestionVersionMapper, ExerciseQuestionVersionDO> implements ExerciseQuestionVersionService {

    @Resource
    private ExerciseQuestionVersionMapper exerciseQuestionVersionMapper;

    @Override
    public PageResult<ExerciseQuestionVersionDO> selectPage(ExerciseQuestionVersionPageReqVO pageReqVO) {

        return exerciseQuestionVersionMapper.selectPage(pageReqVO);
    }

    /**
     * 批量新增数据
     *
     * @param doList 实例对象列表
     */
    @Override
    public void saveBatch(List<ExerciseQuestionVersionDO> doList) {
        if (CollUtil.isNotEmpty(doList)) {
            exerciseQuestionVersionMapper.saveBatch(doList);
        }
    }

    /**
     * 根据专项练习ID获取最大的版本
     *
     * @param specialExercisesId 专项练习ID
     * @return 最大的版本
     */
    @Override
    public Integer getMaxVersion(Long specialExercisesId) {
        return lambdaQuery()
            .eq(ExerciseQuestionVersionDO::getSpecialExerciseId, specialExercisesId)
                .select(ExerciseQuestionVersionDO::getVersion)
                .orderByDesc(ExerciseQuestionVersionDO::getVersion)
                .last("LIMIT 1")
                .oneOpt()
                .map(ExerciseQuestionVersionDO::getVersion)
                .orElse(0);
    }

    /**
     * 获取专项练习组题目列表
     *
     * @param specialExerciseId 专项练习 ID
     * @param type              类型
     * @param version           版本
     * @return 练习组的题目列表
     */
    @Override
    public List<ExerciseQuestionRespVO> getExerciseQuestion(Long specialExerciseId, Integer type, Integer version) {
        return exerciseQuestionVersionMapper.getExerciseQuestion(specialExerciseId, type, version);
    }

    /**
     * 根据题目版本ID列表和类型获取练习题目
     *
     * @param exerciseQuestionVersionId 练习题目版本ID
     * @param type                      题目类型
     * @return 练习题目
     */
    @Override
    public ExerciseQuestionRespVO getExerciseQuestionById(Long exerciseQuestionVersionId, Integer type) {
        return exerciseQuestionVersionMapper.getExerciseQuestionById(exerciseQuestionVersionId, type);
    }

    /**
     * 根据题目版本ID列表和类型获取练习题目
     *
     * @param exerciseQuestionVersionIdList 练习题目版本ID列表
     * @param type                          题目类型
     * @return 练习题目列表
     */
    @Override
    public List<ExerciseQuestionRespVO> getExerciseQuestionByIdList(List<Long> exerciseQuestionVersionIdList, Integer type) {
        if (CollUtil.isEmpty(exerciseQuestionVersionIdList)) {
            return Collections.emptyList();
        }
        return exerciseQuestionVersionMapper.getExerciseQuestionByIdList(exerciseQuestionVersionIdList, type);
    }

}