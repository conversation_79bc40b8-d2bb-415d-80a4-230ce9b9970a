package com.xt.hsk.module.game.service.question;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.game.controller.admin.question.vo.GameQuestionExerciseReferenceVO;
import com.xt.hsk.module.game.controller.admin.question.vo.GameQuestionPageReqVO;
import com.xt.hsk.module.game.dal.dataobject.question.GameQuestionDO;

import java.util.List;

/**
 * 游戏-题库 Service 接口
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
public interface GameQuestionService extends IService<GameQuestionDO> {

    /**
     * 分页获取题目
     *
     * @param pageReqVO 查询条件请求对象
     * @return 题目分页
     */
    PageResult<GameQuestionDO> getQuestionPage(GameQuestionPageReqVO pageReqVO);

    /**
     * 题目总数
     *
     * @param pageReqVO 查询条件请求对象
     * @return 题目总数
     */
    Long countQuestion(GameQuestionPageReqVO pageReqVO);

    /**
     * 获取引用题目的专项练习组信息
     *
     * @param questionId 题目ID
     * @param type       类型
     * @return 专项练习组引用信息列表
     */
    List<GameQuestionExerciseReferenceVO> getExerciseReferences(Long questionId, Integer type);

}