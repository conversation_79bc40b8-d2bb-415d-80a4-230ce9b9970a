package com.xt.hsk.module.game.enums.record;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 练习状态枚举
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Getter
@AllArgsConstructor
public enum GameRecordPracticeStatusEnum implements BasicEnum<Integer>, VO {
    /**
     * 练习状态枚举
     */
    IN_PROGRESS(1, "进行中"),
    COMPLETED(2, "已完成"),
    NOT_STARTED(3, "未开始");

    private final Integer code;
    private final String desc;

    /**
     * 根据练习状态码获取对应的描述信息
     *
     * @param code 练习状态码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (GameRecordPracticeStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据练习状态码获取对应的枚举实例
     *
     * @param code 练习状态码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static GameRecordPracticeStatusEnum getByCode(Integer code) {
        for (GameRecordPracticeStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
