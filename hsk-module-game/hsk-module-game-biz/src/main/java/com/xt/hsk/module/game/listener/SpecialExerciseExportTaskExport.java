package com.xt.hsk.module.game.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.translate.core.TranslateUtils;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExercisePageReqVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExercisePageRespVO;
import com.xt.hsk.module.game.enums.specialexercise.DifficultyLevelEnum;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import com.xt.hsk.module.game.manager.specialexercise.admin.SpecialExerciseAdminManager;
import com.xt.hsk.module.game.service.specialexercise.SpecialExerciseService;
import com.xt.hsk.module.infra.listener.BaseEasyExcelExport;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 专项练习导出任务导出
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Slf4j
@Component
public class SpecialExerciseExportTaskExport extends
    BaseEasyExcelExport<SpecialExercisePageRespVO> {

    @Resource
    private SpecialExerciseService specialExerciseService;

    @Resource
    private SpecialExerciseAdminManager specialExerciseAdminManager;

    /**
     * 当前导出的专项练习类型
     */
    private Integer currentExportType;

    @Override
    protected List<List<String>> getExcelHead() {
        return getExcelHeadByType(currentExportType);
    }

    /**
     * 根据类型获取Excel表头
     *
     * @param type 专项练习类型
     * @return Excel表头
     */
    private List<List<String>> getExcelHeadByType(Integer type) {
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("ID"));
        head.add(Collections.singletonList("序号"));
        head.add(Collections.singletonList("练习组名称"));

        // 根据类型设置不同的内容字段名称
        if (SpecialExerciseTypeEnum.WORD_MATCHING.getCode().equals(type) ||
                SpecialExerciseTypeEnum.STROKE_WRITING.getCode().equals(type)) {
            head.add(Collections.singletonList("包含汉字"));
        } else {
            head.add(Collections.singletonList("包含题干"));
        }

        head.add(Collections.singletonList("HSK等级"));

        // 根据类型设置不同的互动课字段名称
        if (SpecialExerciseTypeEnum.KARAOKE.getCode().equals(type)) {
            head.add(Collections.singletonList("所属互动课"));
        } else {
            head.add(Collections.singletonList("引用互动课"));
        }

        head.add(Collections.singletonList("显示状态"));
        head.add(Collections.singletonList("难度"));
        head.add(Collections.singletonList("最近更新人"));
        head.add(Collections.singletonList("最近更新时间"));

        return head;
    }

    @Override
    protected Long dataTotalCount(Map<String, Object> conditions) {
        // 使用配置好的 ObjectMapper 将 Map 转换为 SpecialExercisePageReqVO 对象，忽略taskName字段
        SpecialExercisePageReqVO dto = objectMapper.convertValue(conditions, SpecialExercisePageReqVO.class);
        // 设置当前导出类型
        this.currentExportType = dto.getType();
        return specialExerciseService.countSpecialExercise(dto);
    }

    @Override
    protected void buildDataList(List<List<String>> resultList, Map<String, Object> queryCondition, Long pageNo, Long pageSize) {

        SpecialExercisePageReqVO pageReqVO = objectMapper.convertValue(queryCondition, SpecialExercisePageReqVO.class);
        pageReqVO.setPageNo(Math.toIntExact(pageNo));
        pageReqVO.setPageSize(Math.toIntExact(pageSize));

        // 确保当前导出类型已设置
        if (this.currentExportType == null) {
            this.currentExportType = pageReqVO.getType();
        }

        PageResult<SpecialExercisePageRespVO> wordPage = specialExerciseAdminManager.getSpecialExercisePage(pageReqVO);
        List<SpecialExercisePageRespVO> voList = wordPage.getList();
        if (CollUtil.isNotEmpty(voList)) {

            // 手动触发翻译
            if (CollUtil.isNotEmpty(voList)) {
                voList = TranslateUtils.translate(voList);
            }

            for (SpecialExercisePageRespVO vo : voList) {
                List<String> row = buildDataRowByType(vo, this.currentExportType);
                resultList.add(row);
            }
        }
        log.info("专项练习导出当前页：{}每页条数：{}总条数：{}", pageNo, pageSize,
                wordPage.getTotal());

    }

    /**
     * 根据类型构建数据行
     *
     * @param vo   专项练习数据
     * @param type 专项练习类型
     * @return 数据行
     */
    private List<String> buildDataRowByType(SpecialExercisePageRespVO vo, Integer type) {
        List<String> row = new ArrayList<>();
        row.add(vo.getId().toString());
        row.add(vo.getSort() == null ? "-" : vo.getSort().toString());
        row.add(vo.getNameCn());
        row.add(getQuestionStr(vo.getQuestionList(), vo.getType()));
        row.add(vo.getHskLevel() == null ? "" : HskEnum.getDescByCode(vo.getHskLevel()));
        row.add("/");
        row.add(vo.getIsShow() == 1 ? "显示" : "隐藏");
        String difficultyLevelDesc = DifficultyLevelEnum.getDescByCode(vo.getDifficultyLevel());
        row.add(difficultyLevelDesc == null ? "/" : difficultyLevelDesc);
        row.add(vo.getUpdaterName());
        row.add(vo.getUpdateTime() == null ? "-" : DateUtil.format(vo.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));

        return row;
    }

    @Override
    protected Long eachSheetTotalCount() {
        return 5000L;
    }

    @Override
    protected Long eachTimesWriteSheetTotalCount() {
        return 1000L;
    }

    /**
     * 获取题目字符串
     *
     * @param questionList 题目列表
     * @param type         类型
     * @return 题目字符串
     */
    private String getQuestionStr(List<String> questionList, Integer type) {
        if (CollUtil.isEmpty(questionList)) {
            return "";
        }

        String split = "\n";

        if (SpecialExerciseTypeEnum.WORD_MATCHING.getCode().equals(type) || SpecialExerciseTypeEnum.STROKE_WRITING.getCode().equals(type)) {
            split = "、";
        }

        return CollUtil.join(questionList, split);
    }
}