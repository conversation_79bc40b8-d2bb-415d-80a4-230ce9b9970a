package com.xt.hsk.module.game.dal.mysql.question;

import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.module.game.controller.admin.question.vo.GameQuestionExerciseReferenceVO;
import com.xt.hsk.module.game.dal.dataobject.question.GameQuestionDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 游戏-题库 Mapper
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Mapper
public interface GameQuestionMapper extends BaseMapperX<GameQuestionDO> {

    /**
     * 获取引用题目的专项练习组信息
     *
     * @param questionId 题目ID
     * @param type       类型
     * @return 专项练习组引用信息列表
     */
    List<GameQuestionExerciseReferenceVO> getExerciseReferences(@Param("questionId") Long questionId, @Param("type") Integer type);

}