package com.xt.hsk.module.game.listener.importtask;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.idev.excel.context.AnalysisContext;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.enums.IsShowEnum;
import com.xt.hsk.module.edu.api.word.WordApi;
import com.xt.hsk.module.edu.api.word.dto.WordRespDTO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.StrokeWritingImportExcelVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.StrokeWritingImportVO;
import com.xt.hsk.module.game.dal.dataobject.exercisequestion.ExerciseQuestionDO;
import com.xt.hsk.module.game.dal.dataobject.exercisequestion.ExerciseQuestionVersionDO;
import com.xt.hsk.module.game.dal.dataobject.specialexercise.SpecialExerciseDO;
import com.xt.hsk.module.game.enums.specialexercise.DifficultyLevelEnum;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import com.xt.hsk.module.game.service.exercisequestion.ExerciseQuestionService;
import com.xt.hsk.module.game.service.exercisequestion.ExerciseQuestionVersionService;
import com.xt.hsk.module.game.service.specialexercise.SpecialExerciseService;
import com.xt.hsk.module.infra.listener.BaseAnalysisEventListener;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

/**
 * 笔画书写导入监听器
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Slf4j
public class StrokeWritingImportListener extends
    BaseAnalysisEventListener<StrokeWritingImportExcelVO> {

    private final List<StrokeWritingImportVO> voList = new ArrayList<>();

    private StrokeWritingImportVO currentGroupHeader;

    private List<StrokeWritingImportVO.ExerciseQuestionImportVO> currentGroup;

    private final SpecialExerciseService specialExerciseService;

    private final WordApi wordApi;

    private final ExerciseQuestionService exerciseQuestionService;

    private final ExerciseQuestionVersionService exerciseQuestionVersionService;

    public StrokeWritingImportListener(SpecialExerciseService specialExerciseService, WordApi wordApi, ExerciseQuestionService exerciseQuestionService, ExerciseQuestionVersionService exerciseQuestionVersionService) {
        this.specialExerciseService = specialExerciseService;
        this.wordApi = wordApi;
        this.exerciseQuestionService = exerciseQuestionService;
        this.exerciseQuestionVersionService = exerciseQuestionVersionService;
    }

    @Override
    public void invoke(StrokeWritingImportExcelVO data, AnalysisContext context) {
        SUCCESS = false;

        // 行号 + 1是因为索引从0开始
        int rowIndex = context.readRowHolder().getRowIndex() + 1;

        try {

            // 判断是否是新的一组
            if (isNewGroup(data)) {
                currentGroupHeader = new StrokeWritingImportVO();
                currentGroupHeader.setNameCn(data.getNameCn());
                currentGroupHeader.setNameEn(data.getNameEn());
                currentGroupHeader.setNameOt(data.getNameOt());
                currentGroupHeader.setHskLevel(data.getHskLevel());
                currentGroupHeader.setDifficultyLevel(data.getDifficultyLevel());
                currentGroupHeader.setRowNum(rowIndex);
                voList.add(currentGroupHeader);

                currentGroup = new ArrayList<>();
                StrokeWritingImportVO.ExerciseQuestionImportVO exerciseQuestionImportVO = new StrokeWritingImportVO.ExerciseQuestionImportVO();

                exerciseQuestionImportVO.setQuestionContent(data.getQuestionContent());
                exerciseQuestionImportVO.setSort(data.getSort());
                exerciseQuestionImportVO.setRowNum(rowIndex);

                currentGroup.add(exerciseQuestionImportVO);

                currentGroupHeader.setQuestionImportList(currentGroup);

            } else {
                // 继承上面合并的单元格内容
                StrokeWritingImportVO.ExerciseQuestionImportVO exerciseQuestionImportVO = new StrokeWritingImportVO.ExerciseQuestionImportVO();

                exerciseQuestionImportVO.setQuestionContent(data.getQuestionContent());
                exerciseQuestionImportVO.setSort(data.getSort());
                exerciseQuestionImportVO.setRowNum(rowIndex);
                currentGroup.add(exerciseQuestionImportVO);
                currentGroupHeader.setQuestionImportList(currentGroup);
            }


            if (CharSequenceUtil.isBlank(data.getQuestionContent())) {
                msg.add(String.format("第%d行：汉字不能为空", rowIndex));
            }

            if (Objects.isNull(data.getSort())) {
                msg.add(String.format("第%d行：序号不能为空", rowIndex));
            }

            data.setRowNum(rowIndex + 1);
        } catch (Exception e) {
            // 记录未预期的异常
            log.error("处理第{}行数据时发生异常", rowIndex, e);
            msg.add(String.format("第%d行：处理失败，原因：%s", rowIndex, e.getMessage()));
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (CollUtil.isEmpty(voList)) {
            msg.add("未读取到数据");
            return;
        }

        for (StrokeWritingImportVO importVO : voList) {
            if (Objects.isNull(importVO.getHskLevel())) {
                msg.add(String.format("第%d行：HSK等级不能为空", importVO.getRowNum()));
            }

            Integer hskLevel = getHskCodeByLevel(importVO.getHskLevel());
            if (hskLevel == null) {
                msg.add(String.format("第%d行：HSK等级不存在", importVO.getRowNum()));
            }

            if (CharSequenceUtil.isNotBlank(importVO.getDifficultyLevel())
                    && DifficultyLevelEnum.getByDesc(importVO.getDifficultyLevel()) == null) {
                msg.add(String.format("第%d行：难度不存在", importVO.getRowNum()));
            }

            List<StrokeWritingImportVO.ExerciseQuestionImportVO> questionImportList = importVO.getQuestionImportList();

            if (CollUtil.isEmpty(questionImportList)) {
                msg.add(String.format("第%d行：练习组题目为空不存在", importVO.getRowNum()));
                continue;
            }


            Map<Integer, List<Integer>> sortMap = questionImportList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(
                            StrokeWritingImportVO.ExerciseQuestionImportVO::getSort,
                            Collectors.mapping(StrokeWritingImportVO.ExerciseQuestionImportVO::getRowNum, Collectors.toList())
                    ));

            for (Map.Entry<Integer, List<Integer>> sortEntry : sortMap.entrySet()) {
                List<Integer> sortList = sortEntry.getValue();
                if (CollUtil.isNotEmpty(sortList) && sortList.size() > 1) {
                    String roundNumberStr = sortList.stream().map(String::valueOf).collect(Collectors.joining(","));
                    msg.add(String.format("第%s行：练习组的序号存在重复", roundNumberStr));
                }
            }

        }

        // 将voList分为50条数据一组
        List<List<StrokeWritingImportVO>> split = CollUtil.split(voList, 50);
        for (List<StrokeWritingImportVO> importVOList : split) {
            List<String> list = importVOList.stream()
                    .flatMap(e -> e.getQuestionImportList().stream()
                            .map(StrokeWritingImportVO.ExerciseQuestionImportVO::getQuestionContent))
                    .toList();

            Map<String, WordRespDTO> wordMap = wordApi.listByWordList(list)
                    .stream()
                    .collect(Collectors.toMap(
                            WordRespDTO::getWord,
                            Function.identity(),
                            (k1, k2) -> k1
                    ));


            for (StrokeWritingImportVO importVO : importVOList) {

                for (StrokeWritingImportVO.ExerciseQuestionImportVO questionImportVO : importVO.getQuestionImportList()) {
                    WordRespDTO wordRespDTO = wordMap.get(questionImportVO.getQuestionContent());
                    if (wordRespDTO == null) {
                        msg.add(String.format("第%d行：汉字不存在", questionImportVO.getRowNum()));
                        continue;
                    }

                    String translationOt = wordRespDTO.getTranslationOts().stream().findFirst().orElse(null);
                    if (CharSequenceUtil.isEmpty(translationOt)) {
                        msg.add(String.format("第%d行：汉字未找到越南语", questionImportVO.getRowNum()));
                    }

                    questionImportVO.setTranslationOt(translationOt);
                    questionImportVO.setQuestionId(wordRespDTO.getId());

                }

            }

        }

        if (CollUtil.isEmpty(msg)) {
            saveBath(voList);
            VALID_COUNT = voList.size();
            SUCCESS = true;
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void saveBath(List<StrokeWritingImportVO> importVOList) {
        for (StrokeWritingImportVO importVO : importVOList) {
            SpecialExerciseDO specialExercise = new SpecialExerciseDO();
            specialExercise.setNameCn(importVO.getNameCn());
            specialExercise.setNameEn(importVO.getNameEn());
            specialExercise.setNameOt(importVO.getNameOt());
            specialExercise.setHskLevel(getHskCodeByLevel(importVO.getHskLevel()));
            specialExercise.setRelatedVersion(1);
            specialExercise.setDifficultyLevel(DifficultyLevelEnum.getCodeByDesc(importVO.getDifficultyLevel()));
            specialExercise.setType(SpecialExerciseTypeEnum.WORD_MATCHING.getCode());
            specialExercise.setIsShow(IsShowEnum.SHOW.getCode());

            specialExerciseService.save(specialExercise);

            List<ExerciseQuestionDO> addList = new ArrayList<>();

            for (StrokeWritingImportVO.ExerciseQuestionImportVO questionImportVO : importVO.getQuestionImportList()) {

                ExerciseQuestionDO exerciseQuestion = new ExerciseQuestionDO();
                exerciseQuestion.setSort(questionImportVO.getSort());
                exerciseQuestion.setIsShow(IsShowEnum.SHOW.getCode());
                exerciseQuestion.setQuestionId(questionImportVO.getQuestionId());
                exerciseQuestion.setTranslationOt(questionImportVO.getTranslationOt());
                exerciseQuestion.setSpecialExerciseId(specialExercise.getId());
                exerciseQuestion.setType(SpecialExerciseTypeEnum.WORD_MATCHING.getCode());
                exerciseQuestion.setVersion(1);
                addList.add(exerciseQuestion);

            }

            exerciseQuestionService.saveBatch(addList);


            List<ExerciseQuestionVersionDO> addVersionList = new ArrayList<>();

            addList.forEach(question -> {
                ExerciseQuestionVersionDO questionVersion = new ExerciseQuestionVersionDO();
                questionVersion.setExerciseQuestionId(question.getId());
                questionVersion.setSpecialExerciseId(specialExercise.getId());
                questionVersion.setVersion(1);
                questionVersion.setQuestionId(question.getQuestionId());
                questionVersion.setTranslationOt(question.getTranslationOt());
                questionVersion.setType(question.getType());
                questionVersion.setSort(question.getSort());
                questionVersion.setIsShow(question.getIsShow());
                addVersionList.add(questionVersion);
            });

            exerciseQuestionVersionService.saveBatch(addVersionList);

        }
    }

    private Integer getHskCodeByLevel(Integer hskLevel) {
        if (hskLevel == 0) {
            return HskEnum.HSK_0.getCode();
        }
        if (hskLevel == 1) {
            return HskEnum.HSK_1.getCode();
        }
        if (hskLevel == 2) {
            return HskEnum.HSK_2.getCode();
        }
        if (hskLevel == 3) {
            return HskEnum.HSK_3.getCode();
        }
        if (hskLevel == 4) {
            return HskEnum.HSK_4.getCode();
        }
        if (hskLevel == 5) {
            return HskEnum.HSK_5.getCode();
        }
        if (hskLevel == 6) {
            return HskEnum.HSK_6.getCode();
        }
        return null;
    }

    private boolean isNewGroup(StrokeWritingImportExcelVO data) {
        if (data.getNameCn() != null) {
            return data.getSort() != null
                    || CharSequenceUtil.isNotBlank(data.getQuestionContent());
        }
        return false;
    }
} 