package com.xt.hsk.module.game.dal.dataobject.record;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.AppBaseDO;
import com.xt.hsk.module.game.enums.record.GameRecordPracticeStatusEnum;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseRecordSourceEnum;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 专项练习记录汇总 DO
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@TableName("game_record_summary")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GameRecordSummaryDO extends AppBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 专项练习的id
     */
    private Long specialExerciseId;
    /**
     * 类型 1单词连连看 2笔画书写 3连词成句 4卡拉OK
     *
     * @see SpecialExerciseTypeEnum
     */
    private Integer type;
    /**
     * 答题数量
     */
    private Integer totalQuestions;
    /**
     * 已答数量
     */
    private Integer answeredQuestions;
    /**
     * 答对数量
     */
    private Integer correctQuestions;
    /**
     * 答错数量
     */
    private Integer wrongQuestions;
    /**
     * 未答数量
     */
    private Integer unansweredQuestions;
    /**
     * 答题开始时间
     */
    private LocalDateTime answerStartTime;
    /**
     * 答题结束时间
     */
    private LocalDateTime answerEndTime;
    /**
     * 答题结束时间
     */
    private LocalDate answerDate;
    /**
     * 进度
     */
    private BigDecimal progress;
    /**
     * 练习状态 1 进行中 2 已完成 3未开始
     *
     * @see GameRecordPracticeStatusEnum
     */
    private Integer practiceStatus;
    /**
     * 互动课单元ID（当练习来源于互动课时）
     */
    private Long interactiveCourseUnitId;
    /**
     * 本练习记录是否为最新数据 0 否 1 是
     */
    private Integer isNewest;
    /**
     * 记录来源 1专项练习 2互动课
     *
     * @see SpecialExerciseRecordSourceEnum
     */
    private Integer source;
    /**
     * 题目版本号
     */
    private Integer questionVersion;

    /**
     * 练习组题目版本ID列表
     */
    private String exerciseQuestionVersionIds;
    /**
     * 正确率
     */
    private BigDecimal accuracy;
    /**
     * 平均分
     */
    private BigDecimal averageScore;

    /**
     * 当前回合
     */
    private Integer currentRound;

}