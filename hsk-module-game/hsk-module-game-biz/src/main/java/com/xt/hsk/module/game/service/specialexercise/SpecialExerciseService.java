package com.xt.hsk.module.game.service.specialexercise;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionRespVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.ExerciseQuestionInfoRespVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExercisePageReqVO;
import com.xt.hsk.module.game.controller.app.specialexercise.vo.SpecialExerciseAppPageReqVO;
import com.xt.hsk.module.game.controller.app.specialexercise.vo.SpecialExerciseRecordRespVO;
import com.xt.hsk.module.game.dal.dataobject.specialexercise.SpecialExerciseDO;
import java.util.List;

/**
 * 游戏-专项练习-练习组 Service 接口
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
public interface SpecialExerciseService extends IService<SpecialExerciseDO> {

    /**
     * 分页获取专项练习
     *
     * @param pageReqVO 查询条件请求对象
     * @return 专项练习分页
     */
    PageResult<SpecialExerciseDO> getSpecialExercisePage(SpecialExercisePageReqVO pageReqVO);

    /**
     * 专项练习总数
     *
     * @param pageReqVO 查询条件请求对象
     * @return 专项练习总数
     */
    Long countSpecialExercise(SpecialExercisePageReqVO pageReqVO);

    /**
     * 获取专项练习的题目信息
     */
    List<ExerciseQuestionInfoRespVO> getQuestionInfoList(List<Long> specialExerciseIdList,
        Integer type);

    /**
     * 获取专项练习组的题目
     *
     * @param specialExerciseId 专项练习id
     * @param type              类型
     * @return 练习组的题目列表
     */
    List<ExerciseQuestionRespVO> getExerciseQuestion(Long specialExerciseId, Integer type);

    /**
     * 分页获取专项练习app
     */
    PageResult<SpecialExerciseRecordRespVO> getAppSpecialExercisePage(SpecialExerciseAppPageReqVO reqVO);
}