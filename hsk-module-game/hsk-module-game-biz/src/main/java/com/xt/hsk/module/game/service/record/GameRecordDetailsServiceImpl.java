package com.xt.hsk.module.game.service.record;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.module.game.dal.dataobject.record.GameRecordDetailsDO;
import com.xt.hsk.module.game.dal.mysql.record.GameRecordDetailsMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;


/**
 * 专项练习记录详情 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
public class GameRecordDetailsServiceImpl extends ServiceImpl<GameRecordDetailsMapper, GameRecordDetailsDO> implements GameRecordDetailsService {

    @Resource
    private GameRecordDetailsMapper gameRecordDetailsMapper;

}