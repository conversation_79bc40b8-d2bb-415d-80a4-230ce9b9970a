package com.xt.hsk.module.game.listener.importtask;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.idev.excel.context.AnalysisContext;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.enums.IsShowEnum;
import com.xt.hsk.module.edu.api.word.WordApi;
import com.xt.hsk.module.edu.api.word.dto.WordRespDTO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.WordMatchingImportExcelVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.WordMatchingImportVO;
import com.xt.hsk.module.game.dal.dataobject.exercisequestion.ExerciseQuestionDO;
import com.xt.hsk.module.game.dal.dataobject.exercisequestion.ExerciseQuestionVersionDO;
import com.xt.hsk.module.game.dal.dataobject.specialexercise.SpecialExerciseDO;
import com.xt.hsk.module.game.enums.specialexercise.DifficultyLevelEnum;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import com.xt.hsk.module.game.service.exercisequestion.ExerciseQuestionService;
import com.xt.hsk.module.game.service.exercisequestion.ExerciseQuestionVersionService;
import com.xt.hsk.module.game.service.specialexercise.SpecialExerciseService;
import com.xt.hsk.module.infra.listener.BaseAnalysisEventListener;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

/**
 * 单词连连看导入监听器
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Slf4j
public class WordMatchingImportListener extends
    BaseAnalysisEventListener<WordMatchingImportExcelVO> {

    private final List<WordMatchingImportVO> voList = new ArrayList<>();

    private WordMatchingImportVO currentGroupHeader;

    private List<WordMatchingImportVO.ExerciseQuestionImportVO> currentGroup;

    private final SpecialExerciseService specialExerciseService;

    private final WordApi wordApi;

    private final ExerciseQuestionService exerciseQuestionService;

    private final ExerciseQuestionVersionService exerciseQuestionVersionService;

    public WordMatchingImportListener(SpecialExerciseService specialExerciseService, WordApi wordApi, ExerciseQuestionService exerciseQuestionService, ExerciseQuestionVersionService exerciseQuestionVersionService) {
        this.specialExerciseService = specialExerciseService;
        this.wordApi = wordApi;
        this.exerciseQuestionService = exerciseQuestionService;
        this.exerciseQuestionVersionService = exerciseQuestionVersionService;
    }

    @Override
    public void invoke(WordMatchingImportExcelVO data, AnalysisContext context) {
        SUCCESS = false;

        // 行号 + 1是因为索引从0开始
        int rowIndex = context.readRowHolder().getRowIndex() + 1;

        try {

            // 判断是否是新的一组
            if (isNewGroup(data)) {
                currentGroupHeader = new WordMatchingImportVO();
                currentGroupHeader.setNameCn(data.getNameCn());
                currentGroupHeader.setNameEn(data.getNameEn());
                currentGroupHeader.setNameOt(data.getNameOt());
                currentGroupHeader.setHskLevel(data.getHskLevel());
                currentGroupHeader.setDifficultyLevel(data.getDifficultyLevel());
                currentGroupHeader.setRowNum(rowIndex);
                voList.add(currentGroupHeader);

                currentGroup = new ArrayList<>();
                WordMatchingImportVO.ExerciseQuestionImportVO exerciseQuestionImportVO = new WordMatchingImportVO.ExerciseQuestionImportVO();

                exerciseQuestionImportVO.setQuestionContent(data.getQuestionContent());
                exerciseQuestionImportVO.setTranslationOt(data.getTranslationOt());
                exerciseQuestionImportVO.setRoundNumber(data.getRoundNumber());
                exerciseQuestionImportVO.setRowNum(rowIndex);

                currentGroup.add(exerciseQuestionImportVO);

                currentGroupHeader.setQuestionImportList(currentGroup);

            } else {
                // 继承上面合并的单元格内容
                WordMatchingImportVO.ExerciseQuestionImportVO exerciseQuestionImportVO = new WordMatchingImportVO.ExerciseQuestionImportVO();

                exerciseQuestionImportVO.setQuestionContent(data.getQuestionContent());
                exerciseQuestionImportVO.setTranslationOt(data.getTranslationOt());
                exerciseQuestionImportVO.setRoundNumber(data.getRoundNumber());
                exerciseQuestionImportVO.setRowNum(rowIndex);
                currentGroup.add(exerciseQuestionImportVO);
                currentGroupHeader.setQuestionImportList(currentGroup);
            }

            data.setRowNum(rowIndex + 1);
        } catch (Exception e) {
            // 记录未预期的异常
            log.error("处理第{}行数据时发生异常", rowIndex, e);
            msg.add(String.format("第%d行：处理失败，原因：%s", rowIndex, e.getMessage()));
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (CollUtil.isEmpty(voList)) {
            msg.add("未读取到数据");
            return;
        }

        for (WordMatchingImportVO importVO : voList) {
            if (Objects.isNull(importVO.getHskLevel())) {
                msg.add(String.format("第%d行：HSK等级不能为空", importVO.getRowNum()));
            }

            Integer hskLevel = getHskCodeByLevel(importVO.getHskLevel());
            if (hskLevel == null) {
                msg.add(String.format("第%d行：HSK等级不存在", importVO.getRowNum()));
            }

            if (CharSequenceUtil.isNotBlank(importVO.getDifficultyLevel())
                    && DifficultyLevelEnum.getByDesc(importVO.getDifficultyLevel()) == null) {
                msg.add(String.format("第%d行：难度不存在", importVO.getRowNum()));
            }

            List<WordMatchingImportVO.ExerciseQuestionImportVO> questionImportList = importVO.getQuestionImportList();

            if (CollUtil.isEmpty(questionImportList)) {
                msg.add(String.format("第%d行：练习组题目为空不存在", importVO.getRowNum()));
                continue;
            }

            if (questionImportList.size() > 5) {
                msg.add(String.format("第%d行：练习组题目数量超过5题", importVO.getRowNum()));
            }

            questionImportList.forEach(questionImportVO -> {

                if (Objects.isNull(questionImportVO.getRoundNumber())) {
                    msg.add(String.format("第%d行：轮次不能为空", questionImportVO.getRowNum()));
                }

                if (CharSequenceUtil.isBlank(questionImportVO.getTranslationOt())) {
                    msg.add(String.format("第%d行：越南语不能为空", questionImportVO.getRowNum()));
                }

                if (CharSequenceUtil.isBlank(questionImportVO.getQuestionContent())) {
                    msg.add(String.format("第%d行：汉字不能为空", questionImportVO.getRowNum()));
                }
            });


            Map<Integer, List<Integer>> roundNumberMap = questionImportList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(
                            WordMatchingImportVO.ExerciseQuestionImportVO::getRoundNumber,
                            Collectors.mapping(WordMatchingImportVO.ExerciseQuestionImportVO::getRowNum, Collectors.toList())
                    ));

            for (Map.Entry<Integer, List<Integer>> roundNumberEntry : roundNumberMap.entrySet()) {
                List<Integer> roundNumberList = roundNumberEntry.getValue();
                if (CollUtil.isNotEmpty(roundNumberList) && roundNumberList.size() > 5) {
                    String roundNumberStr = roundNumberList.stream().map(String::valueOf).collect(Collectors.joining(","));
                    msg.add(String.format("第%s行：练习组轮次的题目数量大于5题", roundNumberStr));
                }
            }


            Map<Integer, List<WordMatchingImportVO.ExerciseQuestionImportVO>> roundVoMap = questionImportList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(WordMatchingImportVO.ExerciseQuestionImportVO::getRoundNumber));

            for (Map.Entry<Integer, List<WordMatchingImportVO.ExerciseQuestionImportVO>> entry : roundVoMap.entrySet()) {
                List<WordMatchingImportVO.ExerciseQuestionImportVO> roundNumberList = entry.getValue();

                List<String> strings = new ArrayList<>();
                for (WordMatchingImportVO.ExerciseQuestionImportVO exerciseQuestionImportVO : roundNumberList) {
                    if (strings.contains(exerciseQuestionImportVO.getQuestionContent())) {
                        msg.add(String.format("第%s行：第%s轮的练习组的汉字有重复", exerciseQuestionImportVO.getRowNum(), exerciseQuestionImportVO.getRoundNumber()));
                        continue;
                    }
                    strings.add(exerciseQuestionImportVO.getQuestionContent());
                }
            }

        }

        // 将voList分为50条数据一组
        List<List<WordMatchingImportVO>> split = CollUtil.split(voList, 50);
        for (List<WordMatchingImportVO> importVOList : split) {
            List<String> list = importVOList.stream()
                    .flatMap(e -> e.getQuestionImportList().stream()
                            .map(WordMatchingImportVO.ExerciseQuestionImportVO::getQuestionContent))
                    .toList();

            List<WordRespDTO> listedByWordList = wordApi.listByWordList(list);
            Map<String, WordRespDTO> wordMap = listedByWordList
                    .stream()
                    .collect(Collectors.toMap(
                            WordRespDTO::getWord,
                            Function.identity(),
                            (k1, k2) -> k1
                    ));


            for (WordMatchingImportVO importVO : importVOList) {

                for (WordMatchingImportVO.ExerciseQuestionImportVO questionImportVO : importVO.getQuestionImportList()) {
                    WordRespDTO wordRespDTO = wordMap.get(questionImportVO.getQuestionContent());
                    if (wordRespDTO == null) {
                        msg.add(String.format("第%d行：汉字不存在", questionImportVO.getRowNum()));
                        continue;
                    }
                    if (!wordRespDTO.getTranslationOts().contains(questionImportVO.getTranslationOt())) {
                        msg.add(String.format("第%d行：越南语不存在", questionImportVO.getRowNum()));
                    }

                    questionImportVO.setQuestionId(wordRespDTO.getId());

                }

            }

        }

        if (CollUtil.isEmpty(msg)) {
            saveBath(voList);
            VALID_COUNT = voList.size();
            SUCCESS = true;
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void saveBath(List<WordMatchingImportVO> importVOList) {
        for (WordMatchingImportVO importVO : importVOList) {
            SpecialExerciseDO specialExercise = new SpecialExerciseDO();
            specialExercise.setNameCn(importVO.getNameCn());
            specialExercise.setNameEn(importVO.getNameEn());
            specialExercise.setNameOt(importVO.getNameOt());
            specialExercise.setHskLevel(getHskCodeByLevel(importVO.getHskLevel()));
            specialExercise.setRelatedVersion(1);
            specialExercise.setDifficultyLevel(DifficultyLevelEnum.getCodeByDesc(importVO.getDifficultyLevel()));
            specialExercise.setType(SpecialExerciseTypeEnum.WORD_MATCHING.getCode());
            specialExercise.setIsShow(IsShowEnum.SHOW.getCode());

            specialExerciseService.save(specialExercise);

            List<ExerciseQuestionDO> addList = new ArrayList<>();

            for (WordMatchingImportVO.ExerciseQuestionImportVO questionImportVO : importVO.getQuestionImportList()) {

                ExerciseQuestionDO exerciseQuestion = new ExerciseQuestionDO();
                exerciseQuestion.setRoundNumber(questionImportVO.getRoundNumber());
                exerciseQuestion.setShowRound(1);
                exerciseQuestion.setIsShow(IsShowEnum.SHOW.getCode());
                exerciseQuestion.setQuestionId(questionImportVO.getQuestionId());
                exerciseQuestion.setTranslationOt(questionImportVO.getTranslationOt());
                exerciseQuestion.setSpecialExerciseId(specialExercise.getId());
                exerciseQuestion.setType(SpecialExerciseTypeEnum.WORD_MATCHING.getCode());
                exerciseQuestion.setVersion(1);
                addList.add(exerciseQuestion);

            }

            exerciseQuestionService.saveBatch(addList);


            List<ExerciseQuestionVersionDO> addVersionList = new ArrayList<>();

            addList.forEach(question -> {
                ExerciseQuestionVersionDO questionVersion = new ExerciseQuestionVersionDO();
                questionVersion.setExerciseQuestionId(question.getId());
                questionVersion.setSpecialExerciseId(specialExercise.getId());
                questionVersion.setVersion(1);
                questionVersion.setQuestionId(question.getQuestionId());
                questionVersion.setTranslationOt(question.getTranslationOt());
                questionVersion.setType(question.getType());
                questionVersion.setShowRound(question.getShowRound());
                questionVersion.setRoundNumber(question.getRoundNumber());
                questionVersion.setIsShow(question.getIsShow());
                addVersionList.add(questionVersion);
            });

            exerciseQuestionVersionService.saveBatch(addVersionList);

        }
    }

    private Integer getHskCodeByLevel(Integer hskLevel) {
        if (hskLevel == 0) {
            return HskEnum.HSK_0.getCode();
        }
        if (hskLevel == 1) {
            return HskEnum.HSK_1.getCode();
        }
        if (hskLevel == 2) {
            return HskEnum.HSK_2.getCode();
        }
        if (hskLevel == 3) {
            return HskEnum.HSK_3.getCode();
        }
        if (hskLevel == 4) {
            return HskEnum.HSK_4.getCode();
        }
        if (hskLevel == 5) {
            return HskEnum.HSK_5.getCode();
        }
        if (hskLevel == 6) {
            return HskEnum.HSK_6.getCode();
        }
        return null;
    }

    private boolean isNewGroup(WordMatchingImportExcelVO data) {
        if (data.getNameCn() != null) {
            return data.getRoundNumber() != null
                    || CharSequenceUtil.isNotBlank(data.getQuestionContent())
                    || CharSequenceUtil.isNotBlank(data.getTranslationOt());
        }
        return false;
    }

    private static String buildKey(WordMatchingImportExcelVO vo) {
        String nameCn = vo.getNameCn() != null ? vo.getNameCn().trim() : "";
        Integer hskLevel = vo.getHskLevel();
        String difficultyLevel = vo.getDifficultyLevel();

        if (difficultyLevel != null && !difficultyLevel.trim().isEmpty()) {
            return nameCn + "_" + hskLevel + "_" + difficultyLevel.trim();
        } else {
            return nameCn + "_" + hskLevel;
        }
    }
} 