package com.xt.hsk.module.game.manager.exercisequestion.admin;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.xt.hsk.framework.common.enums.IsShowEnum;
import com.xt.hsk.framework.web.core.util.WebFrameworkUtils;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExerciseQuestionSaveReqVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExerciseRoundSaveReqVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExerciseSaveReqVO;
import com.xt.hsk.module.game.dal.dataobject.exercisequestion.ExerciseQuestionDO;
import com.xt.hsk.module.game.dal.dataobject.exercisequestion.ExerciseQuestionVersionDO;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import com.xt.hsk.module.game.service.exercisequestion.ExerciseQuestionService;
import com.xt.hsk.module.game.service.exercisequestion.ExerciseQuestionVersionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.game.enums.ErrorCodeConstants.*;

/**
 * 游戏-练习组题目 后台管理 Manager
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Slf4j
@Component
public class ExerciseQuestionAdminManager {

    /**
     * 每组题目数量限制
     */
    private static final Integer QUESTIONS_PER_GROUP_NUMBER = 5;

    /**
     * 初始版本号
     */
    private static final Integer INITIAL_VERSION = 1;

    @Resource
    private ExerciseQuestionService exerciseQuestionService;

    @Resource
    private ExerciseQuestionVersionService exerciseQuestionVersionService;

    /**
     * 创建练习组
     *
     * @param createReqVO  创建请求对象
     * @param exerciseType 练习类型枚举
     */
    @Transactional(rollbackFor = Exception.class)
    public void create(SpecialExerciseSaveReqVO createReqVO, SpecialExerciseTypeEnum exerciseType) {
        log.info("保存{}题目: 参数{}", exerciseType.getDesc(), JSON.toJSON(createReqVO));

        // 根据练习类型构建题目列表，初始版本为1
        List<ExerciseQuestionDO> questionList = buildQuestionList(createReqVO, exerciseType,
            INITIAL_VERSION);

        // 批量保存题目数据和版本信息
        saveQuestionsAndVersions(questionList, createReqVO.getId(), INITIAL_VERSION);
    }

    /**
     * 更新练习组
     *
     * @param updateReqVO  更新请求对象
     * @param exerciseType 练习类型枚举
     * @return 新的版本号
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer update(SpecialExerciseSaveReqVO updateReqVO,
        SpecialExerciseTypeEnum exerciseType) {
        log.info("更新{}题目: 参数{}", exerciseType.getDesc(), JSON.toJSON(updateReqVO));

        Long specialExerciseId = updateReqVO.getId();
        // 获取下一个版本号
        Integer newVersion = getNextVersion(specialExerciseId);

        // 构建新版本的题目列表
        List<ExerciseQuestionDO> questionList = buildQuestionList(updateReqVO, exerciseType,
            newVersion);

        // 执行更新操作：删除旧题目、保存新题目、创建版本记录
        updateQuestionsAndVersions(questionList, specialExerciseId, newVersion);

        return newVersion;
    }

    /**
     * 根据练习类型构建题目列表
     *
     * @param reqVO        请求对象
     * @param exerciseType 练习类型
     * @param version      版本号
     * @return 构建完成的题目列表
     */
    private List<ExerciseQuestionDO> buildQuestionList(SpecialExerciseSaveReqVO reqVO,
        SpecialExerciseTypeEnum exerciseType,
        Integer version) {
        return exerciseType == SpecialExerciseTypeEnum.WORD_MATCHING
            ? buildWordMatchingQuestions(reqVO, version)
            : buildRegularQuestions(reqVO, exerciseType, version);
    }

    /**
     * 构建单词连连看题目列表
     *
     * @param reqVO   请求对象，包含轮次列表
     * @param version 版本号
     * @return 单词连连看题目列表
     */
    private List<ExerciseQuestionDO> buildWordMatchingQuestions(SpecialExerciseSaveReqVO reqVO,
        Integer version) {
        List<SpecialExerciseRoundSaveReqVO> roundList = reqVO.getRoundList();

        // 校验：轮次列表不能为空
        validateRoundListNotEmpty(roundList);

        // 用于检测轮次编号重复
        Set<Integer> usedRoundNumbers = new HashSet<>();
        // 存储所有轮次的题目
        List<ExerciseQuestionDO> allQuestions = new ArrayList<>();

        // 遍历每个轮次
        for (SpecialExerciseRoundSaveReqVO round : roundList) {
            // 校验轮次信息：编号唯一性、题目数量等
            validateRoundInfo(round, usedRoundNumbers);

            // 校验重复的题目
            validateQuestionList(round.getRoundNumber(), round.getExerciseQuestionList());

            // 将当前轮次的题目转换为数据库实体
            List<ExerciseQuestionDO> roundQuestions = convertRoundToQuestions(round, reqVO.getId(),
                version);

            // 添加到总列表中
            allQuestions.addAll(roundQuestions);
        }

        return allQuestions;
    }

    /**
     * 验证题目列表
     *
     * @param roundNumber          轮次
     * @param exerciseQuestionList 练习题列表
     */
    private void validateQuestionList(Integer roundNumber, List<SpecialExerciseQuestionSaveReqVO> exerciseQuestionList) {
        if (CollUtil.isEmpty(exerciseQuestionList)) {
            return;
        }

        Set<Long> seenQuestionIds = new HashSet<>();
        Set<String> seenQuestionNames = new HashSet<>();
        for (SpecialExerciseQuestionSaveReqVO question : exerciseQuestionList) {
            Long questionId = question.getQuestionId();

            // 检查题目 ID 是否重复
            if (questionId != null && !seenQuestionIds.add(questionId)) {
                throw exception(EXERCISE_QUESTION_DUPLICATE_GROUP_CONTENT, roundNumber);
            }

            String word = question.getWord();

            // 检查题目名称是否重复（忽略空字符串）
            if (!CharSequenceUtil.isBlank(word) && !seenQuestionNames.add(word)) {
                throw exception(EXERCISE_QUESTION_DUPLICATE_GROUP_CONTENT, roundNumber);
            }
        }
    }


    /**
     * 构建常规练习题目列表
     *
     * @param reqVO        请求对象，包含题目列表
     * @param exerciseType 练习类型
     * @param version      版本号
     * @return 常规练习题目列表
     */
    private List<ExerciseQuestionDO> buildRegularQuestions(SpecialExerciseSaveReqVO reqVO,
        SpecialExerciseTypeEnum exerciseType,
        Integer version) {
        List<SpecialExerciseQuestionSaveReqVO> questionList = reqVO.getExerciseQuestionList();

        // 校验：题目列表不能为空
        validateQuestionListNotEmpty(questionList);

        // 用于检测排序号重复
        Set<Integer> usedSortNumbers = new HashSet<>();

        // 使用Stream API进行转换，提高代码简洁性
        return questionList.stream()
            .map(question -> {
                // 校验排序号唯一性
                validateSort(question, usedSortNumbers);
                // 转换为数据库实体
                return convertToExerciseQuestion(question, reqVO.getId(), version, exerciseType);
            })
            .toList();
    }

    /**
     * 保存题目和版本信息（新增场景）
     *
     * @param questionList      题目列表
     * @param specialExerciseId 专项练习ID
     * @param version           版本号
     */
    private void saveQuestionsAndVersions(List<ExerciseQuestionDO> questionList,
        Long specialExerciseId,
        Integer version) {
        // 批量保存题目主表
        exerciseQuestionService.saveBatch(questionList);

        // 批量保存版本表（用于版本管理和历史追溯）
        saveBatchQuestionVersion(questionList, specialExerciseId, version);
    }

    /**
     * 更新题目和版本信息（更新场景）
     *
     * @param questionList      题目列表
     * @param specialExerciseId 专项练习ID
     * @param version           新版本号
     */
    private void updateQuestionsAndVersions(List<ExerciseQuestionDO> questionList,
        Long specialExerciseId,
        Integer version) {
        // 提取需要保留的题目ID（有ID表示是更新，无ID表示是新增）
        List<Long> retainedQuestionIds = extractExistingQuestionIds(questionList);

        // 逻辑删除不在保留列表中的旧题目
        deleteObsoleteQuestions(specialExerciseId, retainedQuestionIds);

        // 批量保存或更新题目数据
        questionList.forEach(question -> {
            question.setUpdateTime(LocalDateTime.now());
            question.setUpdater(String.valueOf(WebFrameworkUtils.getLoginUserId()));
        });
        exerciseQuestionService.saveOrUpdateBatch(questionList);

        // 保存新版本的版本记录
        saveBatchQuestionVersion(questionList, specialExerciseId, version);
    }

    /**
     * 获取下一个版本号
     *
     * @param specialExerciseId 专项练习ID
     * @return 下一个版本号
     */
    private Integer getNextVersion(Long specialExerciseId) {
        return exerciseQuestionVersionService.getMaxVersion(specialExerciseId) + 1;
    }

    /**
     * 提取现有题目ID列表
     *
     * @param questionList 题目列表
     * @return 现有题目的ID列表
     */
    private List<Long> extractExistingQuestionIds(List<ExerciseQuestionDO> questionList) {
        return questionList.stream()
            .map(ExerciseQuestionDO::getId)
            .filter(Objects::nonNull)
            .toList();
    }

    /**
     * 删除过时的题目
     *
     * @param specialExerciseId   专项练习ID
     * @param retainedQuestionIds 需要保留的题目ID列表
     */
    private void deleteObsoleteQuestions(Long specialExerciseId, List<Long> retainedQuestionIds) {
        exerciseQuestionService.lambdaUpdate()
            .set(ExerciseQuestionDO::getDeleted, true)
            .set(ExerciseQuestionDO::getUpdateTime, LocalDateTime.now())
            .set(ExerciseQuestionDO::getUpdater, WebFrameworkUtils.getLoginUserId())
            // 只有当保留列表不为空时，才添加notIn条件
            .notIn(CollUtil.isNotEmpty(retainedQuestionIds), ExerciseQuestionDO::getId,
                retainedQuestionIds)
            .eq(ExerciseQuestionDO::getSpecialExerciseId, specialExerciseId)
            .update();
    }

    /**
     * 将轮次数据转换为题目实体列表
     *
     * @param round             轮次数据
     * @param specialExerciseId 专项练习ID
     * @param version           版本号
     * @return 该轮次的题目实体列表
     */
    private List<ExerciseQuestionDO> convertRoundToQuestions(SpecialExerciseRoundSaveReqVO round,
        Long specialExerciseId,
        Integer version) {
        Integer roundNumber = round.getRoundNumber();
        // 显示轮次默认为1，用于前端显示控制
        Integer showRound = ObjectUtil.defaultIfNull(round.getShowRound(), 1);

        return round.getExerciseQuestionList().stream()
            .map(question -> {
                // 先进行通用字段转换
                ExerciseQuestionDO exerciseQuestion = convertToExerciseQuestion(
                    question, specialExerciseId, version, SpecialExerciseTypeEnum.WORD_MATCHING);

                // 设置轮次相关的特殊字段
                exerciseQuestion.setRoundNumber(roundNumber);
                exerciseQuestion.setShowRound(showRound);

                return exerciseQuestion;
            })
            .toList();
    }

    /**
     * 将请求对象转换为题目实体
     *
     * @param reqVO             请求对象
     * @param specialExerciseId 专项练习ID
     * @param version           版本号
     * @param exerciseType      练习类型
     * @return 题目实体对象
     */
    private ExerciseQuestionDO convertToExerciseQuestion(SpecialExerciseQuestionSaveReqVO reqVO,
        Long specialExerciseId,
        Integer version,
        SpecialExerciseTypeEnum exerciseType) {
        ExerciseQuestionDO exerciseQuestion = new ExerciseQuestionDO();

        // 基本信息
        exerciseQuestion.setId(reqVO.getExerciseQuestionId());
        exerciseQuestion.setQuestionId(reqVO.getQuestionId());
        exerciseQuestion.setTranslationOt(reqVO.getTranslationOt());

        // 显示控制（如果未设置则默认为显示）
        exerciseQuestion.setIsShow(
            IsShowEnum.validCodeOrDefault(reqVO.getIsShow(), IsShowEnum.SHOW));
        exerciseQuestion.setSort(reqVO.getSort());

        // 关联信息
        exerciseQuestion.setSpecialExerciseId(specialExerciseId);
        exerciseQuestion.setType(exerciseType.getCode());
        exerciseQuestion.setVersion(version);

        return exerciseQuestion;
    }

    /**
     * 批量保存题目版本信息
     *
     * @param questionList      题目列表
     * @param specialExerciseId 专项练习ID
     * @param version           版本号
     */
    private void saveBatchQuestionVersion(List<ExerciseQuestionDO> questionList,
        Long specialExerciseId,
        Integer version) {
        // 将题目实体转换为版本实体
        List<ExerciseQuestionVersionDO> versionList = questionList.stream()
            .map(question -> buildQuestionVersion(question, specialExerciseId, version))
            .toList();

        // 批量保存版本记录
        exerciseQuestionVersionService.saveBatch(versionList);
    }

    /**
     * 构建题目版本实体
     *
     * @param question          题目实体
     * @param specialExerciseId 专项练习ID
     * @param version           版本号
     * @return 题目版本实体
     */
    private ExerciseQuestionVersionDO buildQuestionVersion(ExerciseQuestionDO question,
        Long specialExerciseId,
        Integer version) {
        ExerciseQuestionVersionDO questionVersion = new ExerciseQuestionVersionDO();

        // 关联信息
        questionVersion.setExerciseQuestionId(question.getId());
        questionVersion.setSpecialExerciseId(specialExerciseId);
        questionVersion.setVersion(version);

        // 题目数据快照
        questionVersion.setQuestionId(question.getQuestionId());
        questionVersion.setTranslationOt(question.getTranslationOt());
        questionVersion.setIsShow(question.getIsShow());
        questionVersion.setSort(question.getSort());
        questionVersion.setType(question.getType());

        // 轮次相关字段（仅单词连连看使用）
        questionVersion.setShowRound(question.getShowRound());
        questionVersion.setRoundNumber(question.getRoundNumber());

        return questionVersion;
    }

    // ==================== 校验方法 ====================

    /**
     * 校验轮次列表不为空
     *
     * @param roundList 轮次列表
     */
    private void validateRoundListNotEmpty(List<SpecialExerciseRoundSaveReqVO> roundList) {
        if (CollUtil.isEmpty(roundList)) {
            throw exception(WORD_MATCHING_ROUNDS_EMPTY);
        }
    }

    /**
     * 校验题目列表不为空
     *
     * <p>常规练习必须包含至少一个题目</p>
     *
     * @param questionList 题目列表
     */
    private void validateQuestionListNotEmpty(List<SpecialExerciseQuestionSaveReqVO> questionList) {
        if (CollUtil.isEmpty(questionList)) {
            throw exception(EXERCISE_QUESTION_EMPTY);
        }
    }

    /**
     * 校验轮次信息
     *
     * @param round            当前轮次数据
     * @param usedRoundNumbers 已使用的轮次编号集合
     */
    private void validateRoundInfo(SpecialExerciseRoundSaveReqVO round,
        Set<Integer> usedRoundNumbers) {
        Integer roundNumber = round.getRoundNumber();

        // 校验：轮次编号不能重复
        if (!usedRoundNumbers.add(roundNumber)) {
            throw exception(WORD_MATCHING_ROUND_DUPLICATE, roundNumber);
        }

        List<SpecialExerciseQuestionSaveReqVO> questions = round.getExerciseQuestionList();

        // 校验：轮次必须包含题目
        if (CollUtil.isEmpty(questions)) {
            throw exception(WORD_MATCHING_ROUND_EMPTY, roundNumber);
        }

        // 校验：题目数量不能超过限制
        if (questions.size() > QUESTIONS_PER_GROUP_NUMBER) {
            throw exception(WORD_MATCHING_ROUND_EXCEED_LIMIT, roundNumber,
                QUESTIONS_PER_GROUP_NUMBER);
        }
    }

    /**
     * 校验题目排序字段
     *
     * @param question        题目数据
     * @param usedSortNumbers 已使用的排序号集合
     */
    private void validateSort(SpecialExerciseQuestionSaveReqVO question,
        Set<Integer> usedSortNumbers) {
        Integer sort = question.getSort();

        // 校验：排序号不能为空
        if (sort == null) {
            throw exception(EXERCISE_QUESTION_SORT_EMPTY);
        }

        // 校验：排序号不能重复
        if (!usedSortNumbers.add(sort)) {
            throw exception(EXERCISE_QUESTION_SORT_DUPLICATE, sort);
        }
    }
}