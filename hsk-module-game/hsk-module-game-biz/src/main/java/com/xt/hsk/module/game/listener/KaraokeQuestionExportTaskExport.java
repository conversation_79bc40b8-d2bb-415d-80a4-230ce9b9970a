package com.xt.hsk.module.game.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.translate.core.TranslateUtils;
import com.xt.hsk.module.game.controller.admin.question.vo.GameQuestionPageReqVO;
import com.xt.hsk.module.game.controller.admin.question.vo.GameQuestionRespVO;
import com.xt.hsk.module.game.manager.question.admin.GameQuestionAdminManager;
import com.xt.hsk.module.game.service.question.GameQuestionService;
import com.xt.hsk.module.infra.listener.BaseEasyExcelExport;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 卡拉OK题目导出任务导出
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Slf4j
@Component
public class KaraokeQuestionExportTaskExport extends BaseEasyExcelExport<GameQuestionRespVO> {

    @Resource
    private GameQuestionService gameQuestionService;

    @Resource
    private GameQuestionAdminManager gameQuestionAdminManager;

    @Override
    protected List<List<String>> getExcelHead() {
        // 直接在这里定义表头，方便开发人员查看和维护
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("ID"));
        head.add(Collections.singletonList("题干内容"));
        head.add(Collections.singletonList("题干拼音"));
        head.add(Collections.singletonList("HSK等级"));
        head.add(Collections.singletonList("更新人"));
        head.add(Collections.singletonList("更新时间"));
        head.add(Collections.singletonList("显示状态"));
        return head;
    }

    @Override
    protected Long dataTotalCount(Map<String, Object> conditions) {
        // 使用配置好的 ObjectMapper 将 Map 转换为 GameQuestionPageReqVO 对象，忽略taskName字段
        GameQuestionPageReqVO dto = objectMapper.convertValue(conditions, GameQuestionPageReqVO.class);

        return gameQuestionService.countQuestion(dto);
    }

    @Override
    protected void buildDataList(List<List<String>> resultList, Map<String, Object> queryCondition, Long pageNo, Long pageSize) {

        GameQuestionPageReqVO pageReqVO = objectMapper.convertValue(queryCondition, GameQuestionPageReqVO.class);
        pageReqVO.setPageNo(Math.toIntExact(pageNo));
        pageReqVO.setPageSize(Math.toIntExact(pageSize));

        PageResult<GameQuestionRespVO> questionPage = gameQuestionAdminManager.getQuestionPage(pageReqVO);
        List<GameQuestionRespVO> voList = questionPage.getList();
        if (CollUtil.isNotEmpty(voList)) {

            // 手动触发翻译
            if (CollUtil.isNotEmpty(voList)) {
                voList = TranslateUtils.translate(voList);
            }

            for (GameQuestionRespVO vo : voList) {
                List<String> row = new ArrayList<>();
                row.add(vo.getId().toString());
                row.add(vo.getQuestionContent() == null ? "" : vo.getQuestionContent());
                row.add(vo.getPinyin() == null ? "" : vo.getPinyin());
                row.add(vo.getHskLevel() == null ? "" : HskEnum.getDescByCode(vo.getHskLevel()));
                row.add(vo.getUpdaterName() == null ? "" : vo.getUpdaterName());
                row.add(vo.getUpdateTime() == null ? "-" : DateUtil.format(vo.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
                row.add(vo.getIsShow() == 1 ? "显示" : "隐藏");
                resultList.add(row);
            }
        }
        log.info("卡拉OK题目导出当前页：{}每页条数：{}总条数：{}", pageNo, pageSize,
                questionPage.getTotal());

    }

    @Override
    protected Long eachSheetTotalCount() {
        return 5000L;
    }

    @Override
    protected Long eachTimesWriteSheetTotalCount() {
        return 1000L;
    }
} 