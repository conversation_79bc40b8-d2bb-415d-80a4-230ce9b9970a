package com.xt.hsk.module.game.convert.specialexercise;

import com.xt.hsk.module.game.api.dto.SpecialExercisePageRespDTO;
import com.xt.hsk.framework.common.util.i18n.LanguageUtils;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExerciseInfoRespVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExercisePageRespVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExerciseSaveReqVO;
import com.xt.hsk.module.game.controller.app.specialexercise.vo.SpecialExerciseAppPageRespVO;
import com.xt.hsk.module.game.controller.app.specialexercise.vo.SpecialExerciseAppRespVO;
import com.xt.hsk.module.game.controller.app.specialexercise.vo.SpecialExerciseRecordRespVO;
import com.xt.hsk.module.game.dal.dataobject.specialexercise.SpecialExerciseDO;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 专项练习转换类
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Mapper
public interface SpecialExerciseConvert {

    SpecialExerciseConvert INSTANCE = Mappers.getMapper(SpecialExerciseConvert.class);

    /**
     * 保存 req vo类转do类
     */
    SpecialExerciseDO saveReqVoToDo(SpecialExerciseSaveReqVO createReqVO);

    /**
     * do类转分页resp vo类
     */
    @Mapping(source = "id", target = "specialExerciseId")
    SpecialExercisePageRespVO doToPageRespVo(SpecialExerciseDO specialExerciseDO);

    /**
     * do列表转分页resp vo列表
     */
    List<SpecialExercisePageRespVO> doListToPageRespVoList(List<SpecialExerciseDO> doList);

    /**
     * do类转信息vo类
     */
    SpecialExerciseInfoRespVO doToInfoRespVO(SpecialExerciseDO specialExercise);

    /**
     * do列表转分页resp dto 类
     */
    @Mapping(source = "id", target = "specialExerciseId")
    List<SpecialExercisePageRespDTO> doListToPageRespDtoList(List<SpecialExerciseDO> doList);

    /**
     * DO类转APP响应VO类
     */
    @Mapping(source = "id", target = "specialExerciseId")
    SpecialExerciseAppPageRespVO doToAppPageVO(SpecialExerciseDO specialExercise);

    /**
     * DO类列表转APP响应VO类列表
     */
    List<SpecialExerciseAppPageRespVO> doListToAppPageVOList(List<SpecialExerciseDO> doList);

    /**
     * 记录VO类转APP响应VO类
     */
    SpecialExerciseAppPageRespVO toAppPageVO(SpecialExerciseRecordRespVO specialExercise);

    /**
     * 记录VO类列表转APP响应VO类列表
     */
    List<SpecialExerciseAppPageRespVO> toAppPageVOList(List<SpecialExerciseRecordRespVO> doList);

    /**
     * DO类转记录响应VO类
     */
    @Mapping(source = "id", target = "specialExerciseId")
    SpecialExerciseRecordRespVO doToRecordRespVO(SpecialExerciseDO specialExercise);

    /**
     * DO类列表转记录响应VO类列表
     */
    List<SpecialExerciseRecordRespVO> doListToRecordRespVOList(List<SpecialExerciseDO> list);


    /**
     * DO类转APP响应VO类
     */
    @Mapping(source = "id", target = "specialExerciseId")
    SpecialExerciseAppRespVO doToAppRespVo(SpecialExerciseDO specialExercise);

    @AfterMapping
    default void fillLocalizedFields(
            SpecialExerciseDO bean, @MappingTarget SpecialExerciseAppPageRespVO respVO) {
        // 这段代码会在基本映射完成后执行
        // 可以访问源对象bean的所有属性
        // 可以修改目标对象respVO的属性
        respVO.setName(LanguageUtils.getLocalizedValue(
                bean.getNameCn(),
                bean.getNameEn(),
                bean.getNameOt()));
    }

    @AfterMapping
    default void fillLocalizedFields(
            SpecialExerciseDO bean, @MappingTarget SpecialExerciseAppRespVO respVO) {
        // 这段代码会在基本映射完成后执行
        // 可以访问源对象bean的所有属性
        // 可以修改目标对象respVO的属性
        respVO.setName(LanguageUtils.getLocalizedValue(
                bean.getNameCn(),
                bean.getNameEn(),
                bean.getNameOt()));
    }

    @AfterMapping
    default void fillLocalizedFields(
            SpecialExerciseRecordRespVO bean, @MappingTarget SpecialExerciseAppPageRespVO respVO) {
        // 这段代码会在基本映射完成后执行
        // 可以访问源对象bean的所有属性
        // 可以修改目标对象respVO的属性
        respVO.setName(LanguageUtils.getLocalizedValue(
                bean.getNameCn(),
                bean.getNameEn(),
                bean.getNameOt()));
    }
    /**
     * do转分页resp dto 类
     *
     * @param bean 豆
     * @return {@code SpecialExercisePageRespDTO }
     */
    @Mapping(source = "id", target = "specialExerciseId")
    SpecialExercisePageRespDTO doListToPageRespDto(SpecialExerciseDO bean);

}
