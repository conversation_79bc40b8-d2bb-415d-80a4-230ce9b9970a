<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.xt.hsk</groupId>
        <artifactId>hsk</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hsk-server</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        后端 Server 的主项目，通过引入需要 hsk-module-xxx 的依赖，
        从而实现提供 RESTful API 给 hsk-ui-admin、hsk-ui-user 等前端项目。
        本质上来说，它就是个空壳（容器）！
    </description>
    <url>hsk-app</url>

    <dependencies>
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-module-system-biz</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-module-infra-biz</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-module-edu-biz</artifactId>
            <version>${revision}</version>
        </dependency>
      <dependency>
        <groupId>com.xt.hsk</groupId>
        <artifactId>hsk-module-user-biz</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <artifactId>hsk-module-marketing-biz</artifactId>
        <groupId>com.xt.hsk</groupId>
        <version>${revision}</version>
      </dependency>
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-module-game-biz</artifactId>
            <version>${revision}</version>
        </dependency>
      <dependency>
        <groupId>com.xt.hsk</groupId>
        <artifactId>hsk-module-trade-biz</artifactId>
        <version>${revision}</version>
      </dependency>

        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-module-thirdparty-biz</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- spring boot 配置所需依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-spring-boot-starter-protection</artifactId>
        </dependency>

    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
          <plugin>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-maven-plugin</artifactId>
            <version>${spring.boot.version}</version>
            <configuration>
              <excludes>
                <exclude>
                  <groupId>org.projectlombok</groupId>
                  <artifactId>lombok</artifactId>
                </exclude>
              </excludes>
              <!-- 确保本地打的jar包能用 项目中有大汉云sms的sdk 不加这个配置打包之后调用会报错-->
              <includeSystemScope>true</includeSystemScope>
            </configuration>
          </plugin>
        </plugins>
    </build>

</project>
