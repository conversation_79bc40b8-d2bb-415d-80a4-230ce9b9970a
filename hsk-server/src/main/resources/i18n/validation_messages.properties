# 通用校验消息
javax.validation.constraints.NotBlank.message=不能为空
javax.validation.constraints.NotNull.message=不能为null
javax.validation.constraints.NotEmpty.message=不能为空
javax.validation.constraints.Size.message=大小必须在{min}和{max}之间
javax.validation.constraints.Min.message=必须大于或等于{value}
javax.validation.constraints.Max.message=必须小于或等于{value}
javax.validation.constraints.Email.message=不是有效的电子邮件地址
javax.validation.constraints.Pattern.message=格式不正确
# 自定义校验消息

validation.id-card.invalid=身份证号码格式不正确
validation.password.weak=密码强度不够
validation.username.invalid=用户名格式不正确
# ============================登录注册验证消息================================
# 国家区号错误
validation.invalid.country.code=国家区号错误
#手机号格式错误
validation.mobile.invalid=手机号格式错误
#验证码必须是6位数字
validation.verification.code.invalid=验证码必须是6位数字
#密码格式不正确
validation.password.invalid=密码需在8-16位，必须包括字母和数字
