# Common validation messages
javax.validation.constraints.NotBlank.message=cannot be blank
javax.validation.constraints.NotNull.message=cannot be null
javax.validation.constraints.NotEmpty.message=cannot be empty
javax.validation.constraints.Size.message=size must be between {min} and {max}
javax.validation.constraints.Min.message=must be greater than or equal to {value}
javax.validation.constraints.Max.message=must be less than or equal to {value}
javax.validation.constraints.Email.message=not a valid email address
javax.validation.constraints.Pattern.message=format is incorrect
# Custom validation messages

validation.id-card.invalid=ID card number format is incorrect
validation.password.weak=password strength is not enough
validation.username.invalid=username format is incorrect
# ============================登录注册验证消息================================
# 国家区号错误
validation.invalid.country.code=Invalid country code
#手机号格式错误
validation.mobile.invalid=Invalid phone number format
#验证码必须是6位数字
validation.verification.code.invalid=6-digit code required
#密码格式不正确
validation.password.invalid=The password must be 8-16 characters long and must include letters and numbers.