package com.xt.hsk.server.controller;

import com.xt.hsk.framework.common.pojo.CommonResult;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 参数校验国际化演示控制器
 *
 * <AUTHOR>
 * @since 2025/06/16
 */
@RestController
@RequestMapping("/test/validation")
@Validated
@Slf4j
public class ValidationDemoController {

    /**
     * 测试参数校验国际化 可以通过发送不同的请求头测试不同语言的错误消息： - language:en - 英文错误消息 - language:zh-CN - 中文错误消息 -
     * language:vi - 越南语错误消息
     * <p>
     * 测试方法： 1. 使用 POST 请求访问 /test/validation/demo 2. 发送不完整或错误的 JSON 数据，如 {"username": ""} 3.
     * 观察返回的错误消息是否根据 language 请求头变化
     *
     * @param request 请求对象
     * @return 结果
     */
    @PostMapping("/demo")
    @PermitAll
    public CommonResult<String> testValidation(@Valid @RequestBody ValidationRequest request) {
        log.info("接收到请求数据: {}", request);
        return CommonResult.success("验证通过");
    }

    /**
     * 验证请求对象 包含各种常见的验证注解
     */
    @Data
    public static class ValidationRequest {

        @NotBlank(message = "{javax.validation.constraints.NotBlank.message}")
        @Length(min = 4, max = 20, message = "{javax.validation.constraints.Size.message}")
        private String username;

        @NotBlank(message = "{javax.validation.constraints.NotBlank.message}")
        @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{8,}$",
            message = "{validation.password.weak}")
        private String password;

        @NotBlank(message = "{javax.validation.constraints.NotBlank.message}")
        @Email(message = "{javax.validation.constraints.Email.message}")
        private String email;

        @Min(value = 18, message = "{javax.validation.constraints.Min.message}")
        @Max(value = 120, message = "{javax.validation.constraints.Max.message}")
        private Integer age;

        @Pattern(regexp = "^1[3-9]\\d{9}$", message = "{validation.mobile.invalid}")
        private String mobile;

        @Size(min = 1, max = 10, message = "idCard" + "{javax.validation.constraints.Size.message}")
        private String idCard;
    }
} 