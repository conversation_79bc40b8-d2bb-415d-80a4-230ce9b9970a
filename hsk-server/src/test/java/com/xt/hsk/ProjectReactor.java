package com.xt.hsk;

import cn.hutool.core.io.FileTypeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.xt.hsk.framework.common.util.collection.SetUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

import static java.io.File.separator;

/**
 * 项目修改器，一键替换 Maven 的 groupId、artifactId，项目的 package 等
 * <p>
 * 通过修改 groupIdNew、artifactIdNew、projectBaseDirNew 三个变量
 *
 * <AUTHOR>
 */
@Slf4j
public class ProjectReactor {

    private static final String GROUP_ID = "com.xt.hsk";
    private static final String ARTIFACT_ID = "hsk";
    private static final String PACKAGE_NAME = "com.xt.hsk";
    private static final String TITLE = "HSK";

    /**
     * 白名单文件，不进行重写，避免出问题
     */
    private static final Set<String> WHITE_FILE_TYPES = SetUtils.asSet("gif", "jpg", "svg", "png", // 图片
            "eot", "woff2", "ttf", "woff",  // 字体
            "xdb"); // IP 库

    public static void main(String[] args) {
        long start = System.currentTimeMillis();
        String projectBaseDir = getProjectBaseDir();
        log.info("[main][原项目路劲改地址 ({})]", projectBaseDir);

        // ========== 配置，需要你手动修改 ==========
        String groupIdNew = "com.xt.hsk";
        String artifactIdNew = "hsk";
        String packageNameNew = "com.xt.hsk";
        String titleNew = "HSK";
        String projectBaseDirNew = "D:\\workspace\\hsk-service"; // 一键改名后，"新"项目所在的目录 - 使用绝对路径
        log.info("[main][检测新项目目录 ({})是否存在]", projectBaseDirNew);
        if (FileUtil.exist(projectBaseDirNew)) {
            log.error("[main][新项目目录检测 ({})已存在，请更改新的目录！程序退出]", projectBaseDirNew);
            return;
        }
        // 如果新目录中存在 PACKAGE_NAME，ARTIFACT_ID 等关键字，路径会被替换，导致生成的文件不在预期目录
        if (StrUtil.containsAny(projectBaseDirNew, PACKAGE_NAME, ARTIFACT_ID, StrUtil.upperFirst(ARTIFACT_ID))) {
            log.error("[main][新项目目录 `projectBaseDirNew` 检测 ({}) 存在冲突名称「{}」或者「{}」，请更改新的目录！程序退出]",
                    projectBaseDirNew, PACKAGE_NAME, ARTIFACT_ID);
            return;
        }
        
        // 确保基本目录已创建
        File baseDir = new File(projectBaseDirNew);
        if (!baseDir.exists()) {
            boolean created = baseDir.mkdirs();
            if (!created) {
                log.error("[main][目录创建失败] 无法创建基本目录: {}", projectBaseDirNew);
                return;
            }
            log.info("[main][已创建基本目录] {}", projectBaseDirNew);
        }
        
        log.info("[main][完成新项目目录检测，新项目路径地址 ({})]", projectBaseDirNew);
        // 获得需要复制的文件
        log.info("[main][开始获得需要重写的文件，预计需要 10-20 秒]");
        Collection<File> files = listFiles(projectBaseDir);
        log.info("[main][需要重写的文件数量：{}，预计需要 15-30 秒]", files.size());

        // 统计变量
        AtomicInteger javaFileCount = new AtomicInteger(0);
        AtomicInteger changedJavaFileCount = new AtomicInteger(0);
        AtomicInteger totalChangedCount = new AtomicInteger(0);
        AtomicInteger ioErrorCount = new AtomicInteger(0);
        AtomicInteger fileWrittenCount = new AtomicInteger(0);

        // 写入文件
        files.forEach(file -> {
            // 如果是白名单的文件类型，不进行重写，直接拷贝
            String fileType = getFileType(file);
            boolean isJavaFile = file.getName().endsWith(".java");
            
            if (isJavaFile) {
                javaFileCount.incrementAndGet();
            }
            
            try {
                if (WHITE_FILE_TYPES.contains(fileType)) {
                    copyFile(file, projectBaseDir, projectBaseDirNew, packageNameNew, artifactIdNew);
                    fileWrittenCount.incrementAndGet();
                    return;
                }
                
                // 如果非白名单的文件类型，重写内容，在生成文件
                String oldContent = FileUtil.readString(file, StandardCharsets.UTF_8);
                String newContent = replaceFileContent(file, groupIdNew, artifactIdNew, packageNameNew, titleNew);
                
                if (!oldContent.equals(newContent)) {
                    if (isJavaFile) {
                        changedJavaFileCount.incrementAndGet();
                    }
                    totalChangedCount.incrementAndGet();
                }
                
                String newPath = buildNewFilePath(file, projectBaseDir, projectBaseDirNew, packageNameNew, artifactIdNew);
                FileUtil.writeUtf8String(newContent, newPath);
                
                // 验证文件是否成功写入
                if (new File(newPath).exists()) {
                    fileWrittenCount.incrementAndGet();
                    if (fileWrittenCount.get() % 100 == 0) {
                        log.info("[写入进度] 已成功写入 {} 个文件", fileWrittenCount.get());
                    }
                } else {
                    log.error("[写入失败] 文件未成功写入: {}", newPath);
                }
            } catch (Exception e) {
                ioErrorCount.incrementAndGet();
                log.error("[处理异常] 文件: {}, 异常: {}", file.getPath(), e.getMessage());
            }
        });
        
        log.info("[main][替换统计] Java文件总数: {}, 成功替换的Java文件数: {}, 替换率: {}%, 总替换文件数: {}, 成功写入文件数: {}, IO错误数: {}", 
                javaFileCount.get(), changedJavaFileCount.get(), 
                javaFileCount.get() > 0 ? (changedJavaFileCount.get() * 100 / javaFileCount.get()) : 0,
                totalChangedCount.get(), fileWrittenCount.get(), ioErrorCount.get());
        
        log.info("[main][重写完成]共耗时：{} 秒", (System.currentTimeMillis() - start) / 1000);
        
        // 验证目标目录中的文件数量
        if (FileUtil.exist(projectBaseDirNew)) {
            Collection<File> generatedFiles = FileUtil.loopFiles(projectBaseDirNew);
            int generatedJavaFiles = 0;
            for (File file : generatedFiles) {
                if (file.getName().endsWith(".java")) {
                    generatedJavaFiles++;
                }
            }
            log.info("[main][验证结果] 目标目录: {}, 文件总数: {}, Java文件数: {}", 
                    projectBaseDirNew, generatedFiles.size(), generatedJavaFiles);
            
            if (generatedJavaFiles == 0) {
                log.error("[main][验证失败] 目标目录中没有Java文件，请检查文件路径和buildNewFilePath方法的实现");
            } else {
                log.info("[main][验证成功] 已找到生成的Java文件，请检查新项目目录");
            }
        } else {
            log.error("[main][验证失败] 目标目录 {} 不存在，请检查目录创建逻辑", projectBaseDirNew);
        }
    }

    private static String getProjectBaseDir() {
        String baseDir = System.getProperty("user.dir");
        if (StrUtil.isEmpty(baseDir)) {
            throw new NullPointerException("项目基础路径不存在");
        }
        return baseDir;
    }

    private static Collection<File> listFiles(String projectBaseDir) {
        Collection<File> files = FileUtil.loopFiles(projectBaseDir);
        // 移除 IDEA、Git 自身的文件、Node 编译出来的文件
        files = files.stream()
                .filter(file -> !file.getPath().contains(separator + "target" + separator)
                        && !file.getPath().contains(separator + "node_modules" + separator)
                        && !file.getPath().contains(separator + ".idea" + separator)
                        && !file.getPath().contains(separator + ".git" + separator)
                        && !file.getPath().contains(separator + "dist" + separator)
                        && !file.getPath().contains(".iml")
                        && !file.getPath().contains(".html.gz"))
                .collect(Collectors.toList());
        
        // 统计Java文件
        int javaFileCount = 0;
        for (File file : files) {
            if (file.getName().endsWith(".java")) {
                javaFileCount++;
                // 每100个文件记录一次日志
                if (javaFileCount % 100 == 0) {
                    log.info("[listFiles] 已找到 {} 个Java文件", javaFileCount);
                }
            }
        }
        log.info("[listFiles] 总共找到 {} 个Java文件", javaFileCount);
        
        return files;
    }

    private static String replaceFileContent(File file, String groupIdNew,
                                             String artifactIdNew, String packageNameNew,
                                             String titleNew) {
        String content = FileUtil.readString(file, StandardCharsets.UTF_8);
        // 如果是白名单的文件类型，不进行重写
        String fileType = getFileType(file);
        if (WHITE_FILE_TYPES.contains(fileType)) {
            return content;
        }
        
        // 检查文件是否包含要替换的内容
        boolean containsGroupId = content.contains(GROUP_ID);
        boolean containsPackageName = content.contains(PACKAGE_NAME);
        boolean containsArtifactId = content.contains(ARTIFACT_ID);
        boolean containsTitle = content.contains(TITLE);
        boolean isJavaFile = file.getName().endsWith(".java");
        
        // 使用replace直接替换字符串，避免正则表达式问题
        String newContent = content;
        
        // 特殊处理Java文件，先处理package和import语句
        if (isJavaFile) {
            // 处理package语句 - 更彻底的替换
            if (content.contains("package " + PACKAGE_NAME) || content.contains("package " + PACKAGE_NAME + ";")) {
                newContent = newContent.replace("package " + PACKAGE_NAME + ";", "package " + packageNameNew + ";");
                // 不带分号的情况
                newContent = newContent.replace("package " + PACKAGE_NAME + "\n", "package " + packageNameNew + "\n");
                newContent = newContent.replace("package " + PACKAGE_NAME + "\r\n", "package " + packageNameNew + "\r\n");
                
                // 处理子包的情况
                for (int i = 0; i < 10; i++) { // 最多处理10层子包
                    String subpackage = PACKAGE_NAME + "." + "a".repeat(i);
                    if (newContent.contains("package " + subpackage)) {
                        String newSubpackage = packageNameNew + "." + "a".repeat(i);
                        newContent = newContent.replace("package " + subpackage, "package " + newSubpackage);
                    }
                }
                
                log.info("[内容替换] 包声明替换: package {} -> package {}", PACKAGE_NAME, packageNameNew);
            } else {
                // 查找是否有以PACKAGE_NAME开头的package语句
                int packageIndex = newContent.indexOf("package " + PACKAGE_NAME);
                if (packageIndex != -1) {
                    int endIndex = newContent.indexOf(";", packageIndex);
                    if (endIndex != -1) {
                        String oldPackage = newContent.substring(packageIndex, endIndex + 1);
                        String packagePart = oldPackage.substring(8, oldPackage.length() - 1); // 去掉"package "和";"
                        String newPackage = "package " + packagePart.replace(PACKAGE_NAME, packageNameNew) + ";";
                        newContent = newContent.replace(oldPackage, newPackage);
                        log.info("[内容替换] 复杂包声明替换: {} -> {}", oldPackage, newPackage);
                    }
                } else {
                    log.warn("[内容替换] 未找到包声明: {}", file.getPath());
                }
            }
            
            // 替换import语句 - 针对子包的处理
            String importPrefix = "import " + PACKAGE_NAME;
            if (newContent.contains(importPrefix)) {
                int startIndex = 0;
                while ((startIndex = newContent.indexOf(importPrefix, startIndex)) != -1) {
                    int semicolonIndex = newContent.indexOf(";", startIndex);
                    if (semicolonIndex != -1) {
                        String importStatement = newContent.substring(startIndex, semicolonIndex + 1);
                        String newImportStatement = importStatement.replace(importPrefix, "import " + packageNameNew);
                        newContent = newContent.replace(importStatement, newImportStatement);
                        log.info("[内容替换] 导入替换: {} -> {}", importStatement, newImportStatement);
                        startIndex = startIndex + newImportStatement.length();
                    } else {
                        break;
                    }
                }
            }
        }
        
        // 执行普通替换
        if (containsGroupId) {
            newContent = newContent.replace(GROUP_ID, groupIdNew);
        }
        
        if (containsPackageName) {
            newContent = newContent.replace(PACKAGE_NAME, packageNameNew);
        }
        
        // 必须放在最后替换，因为 ARTIFACT_ID 太短可能导致误替换
        if (containsArtifactId) {
            newContent = newContent.replace(ARTIFACT_ID, artifactIdNew);
        }
        
        // 替换首字母大写的artifactId
        String upperArtifactId = StrUtil.upperFirst(ARTIFACT_ID);
        if (content.contains(upperArtifactId)) {
            newContent = newContent.replace(upperArtifactId, StrUtil.upperFirst(artifactIdNew));
        }
        
        if (containsTitle) {
            newContent = newContent.replace(TITLE, titleNew);
        }
        
        // 检查内容是否有变化
        boolean hasChanged = !newContent.equals(content);
        
        if (hasChanged) {
            if (isJavaFile) {
                log.info("[替换成功] Java文件: {}", file.getPath());
            }
        } else if (isJavaFile && (containsGroupId || containsPackageName || containsArtifactId)) {
            log.warn("[替换失败] Java文件: {} 包含需替换内容但未成功替换", file.getPath());
        }
        
        return newContent;
    }

    private static void copyFile(File file, String projectBaseDir,
                                 String projectBaseDirNew, String packageNameNew, String artifactIdNew) {
        String newPath = buildNewFilePath(file, projectBaseDir, projectBaseDirNew, packageNameNew, artifactIdNew);
        try {
            FileUtil.copyFile(file, new File(newPath));
        } catch (Exception e) {
            log.error("[复制失败] 源文件: {}, 目标文件: {}, 错误: {}", file.getPath(), newPath, e.getMessage());
        }
    }

    private static String buildNewFilePath(File file, String projectBaseDir,
                                           String projectBaseDirNew, String packageNameNew, String artifactIdNew) {
        String oldPath = file.getPath();
        
        // 第一步：先替换项目基础目录
        String newPath = oldPath.replace(projectBaseDir, projectBaseDirNew);
        
        // 第二步：处理包路径替换
        // 先检查文件是否在Java源码目录中（通常是src/main/java或src/test/java）
        boolean isJavaFile = file.getName().endsWith(".java");
        
        if (isJavaFile) {
            // 处理包路径 - 针对Windows系统的特殊处理
            String oldPackagePath = PACKAGE_NAME.replace(".", separator);
            String newPackagePath = packageNameNew.replace(".", separator);
            
            // 检查路径中是否包含包目录结构
            if (newPath.contains(oldPackagePath)) {
                newPath = newPath.replace(oldPackagePath, newPackagePath);
                log.info("[路径替换] 包路径替换: {} -> {}", oldPackagePath, newPackagePath);
            } else {
                // 如果常规替换不成功，尝试更复杂的替换
                // 先找出潜在的包路径部分
                String potentialPackagePath = findPackagePathInFilePath(oldPath);
                if (potentialPackagePath != null && !potentialPackagePath.isEmpty()) {
                    String newPotentialPath = potentialPackagePath.replace(
                            PACKAGE_NAME.replace(".", separator), 
                            packageNameNew.replace(".", separator));
                    newPath = newPath.replace(potentialPackagePath, newPotentialPath);
                    log.info("[路径替换] 复杂包路径替换: {} -> {}", potentialPackagePath, newPotentialPath);
                } else {
                    log.warn("[路径替换] 无法找到包路径: {}", oldPath);
                }
            }
        }
        
        // 第三步：替换artifactId
        if (newPath.contains(ARTIFACT_ID)) {
            newPath = newPath.replace(ARTIFACT_ID, artifactIdNew);
        }
        
        // 第四步：替换首字母大写的artifactId
        String upperArtifactId = StrUtil.upperFirst(ARTIFACT_ID);
        if (newPath.contains(upperArtifactId)) {
            newPath = newPath.replace(upperArtifactId, StrUtil.upperFirst(artifactIdNew));
        }
        
        // 添加路径转换日志
        if (oldPath.endsWith(".java")) {
            log.info("[路径转换] 原路径: {}, 新路径: {}", oldPath, newPath);
        }
        
        // 确保目标目录存在
        File parentDir = new File(newPath).getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            boolean created = parentDir.mkdirs();
            if (!created) {
                log.error("[目录创建失败] 无法创建目录: {}", parentDir.getPath());
            }
        }
        
        return newPath;
    }

    /**
     * 在文件路径中查找包路径部分
     * 
     * @param filePath 文件路径
     * @return 包路径部分，如果没找到返回null
     */
    private static String findPackagePathInFilePath(String filePath) {
        // 查找常见的Java源码目录标志
        String[] javaDirMarkers = {"/src/main/java/", "/src/test/java/", "\\src\\main\\java\\", "\\src\\test\\java\\"};
        
        for (String marker : javaDirMarkers) {
            int index = filePath.indexOf(marker);
            if (index != -1) {
                int startIndex = index + marker.length();
                // 提取包路径部分
                String packagePath = filePath.substring(startIndex);
                // 如果文件路径包含多级目录，只返回直到文件名的部分
                int lastSeparatorIndex = packagePath.lastIndexOf(separator);
                if (lastSeparatorIndex != -1) {
                    return packagePath.substring(0, lastSeparatorIndex + 1);
                }
            }
        }
        
        return null;
    }

    private static String getFileType(File file) {
        return file.length() > 0 ? FileTypeUtil.getType(file) : "";
    }

}
