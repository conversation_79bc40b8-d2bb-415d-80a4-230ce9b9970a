package com.xt.hsk.module.infra.enums.export;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.Getter;

/**
 * 导出任务模块枚举
 *
 * <AUTHOR>
 * @since 2025/05/26
 */
@Getter
public enum ExportTaskTypeEnum implements BasicEnum<Integer> {
    /**
     * 异步导出任务记录表
     */
    INTERACTIVE_COURSE(1, "互动课"),
    INTERACTIVE_COURSE_UNIT(2, "互动课单元"),
    TEACHER(3, "讲师"),
    WORD(4, "字词库"),
    ELITE_COURSE(5, "精品课"),
    USER(6, "用户"),
    ELITE_COURSE_USER(7, "精品课学员"),
    ELITE_COURSE_STUDY_STATS(8, "精品课学习统计"),
    ELITE_CLASS_HOUR_STUDY_STATS(9, "精品课课时学习统计"),
    TEACHER_ELITE_COURSE(10, "讲师精品课"),
    ORDER(11, "订单"),
    WORD_MATCHING_EXERCISE(12, "单词连连看练习组"),
    STROKE_WRITING_EXERCISE(13, "笔画书写练习组"),
    WORD_TO_SENTENCE_EXERCISE(14, "连词成句练习组"),
    KARAOKE_EXERCISE(15, "卡拉OK练习组"),
    WORD_TO_SENTENCE_QUESTION(16, "连词成句题目"),
    KARAOKE_QUESTION(17, "卡拉OK题目"),
    EDU_QUESTION(18, "真题数据"),
    USER_EXAM_RECORD(19, "用户模考记录"),
    ;

    private final Integer code;
    private final String desc;

    ExportTaskTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
