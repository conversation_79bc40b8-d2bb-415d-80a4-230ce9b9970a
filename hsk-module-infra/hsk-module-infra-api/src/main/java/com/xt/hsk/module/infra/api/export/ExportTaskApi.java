package com.xt.hsk.module.infra.api.export;


import com.xt.hsk.module.infra.enums.export.ExportTaskTypeEnum;

/**
 * 导出任务 API
 *
 * <AUTHOR>
 * @since 2025/05/27
 */
public interface ExportTaskApi {

    /**
     * 创建导出任务
     *
     * @param taskName 任务名称
     * @param taskType 任务类型
     * @param params   任务参数JSON字符串
     * @return 任务ID
     */
    Long createExportTask(String taskName, ExportTaskTypeEnum taskType, String params);
}
