package com.xt.hsk.module.infra.enums.export;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 导出任务状态枚举
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Getter
@AllArgsConstructor
public enum ExportTaskStatusEnum implements BasicEnum<Integer> {

    /**
     *
     */
    WAITING(0, "未开始"),
    PROCESSING(1, "执行中"),
    COMPLETED(2, "完成"),
    FAILED(3, "失败"),
    EXPIRED(4, "已过期"),
    CANCELLED(5, "已取消");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String desc;


} 