package com.xt.hsk.module.infra.api.export;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.EXPORT_NAME_MISS;

import com.xt.hsk.framework.common.exception.ServerException;
import com.xt.hsk.module.infra.dal.dataobject.export.ExportTaskDO;
import com.xt.hsk.module.infra.enums.export.ExportTaskStatusEnum;
import com.xt.hsk.module.infra.enums.export.ExportTaskTypeEnum;
import com.xt.hsk.module.infra.service.export.ExportTaskExecutor;
import com.xt.hsk.module.infra.service.export.ExportTasksService;
import jakarta.annotation.Resource;
import java.util.Date;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 导出任务 API 实现
 *
 * <AUTHOR>
 * @since 2025/05/27
 */
@Service
@Slf4j
public class ExportTaskApiImpl implements ExportTaskApi {

    @Resource
    private ExportTasksService exportTasksService;

    @Resource
    private ExportTaskExecutor exportTaskExecutor;


    @Override
    public Long createExportTask(String taskName, ExportTaskTypeEnum taskType, String params) {
        if (taskName == null) {
            throw new ServerException(EXPORT_NAME_MISS);
        }
        ExportTaskDO exportTask = new ExportTaskDO();
        exportTask.setTaskName(taskName);
        exportTask.setType(taskType.getCode());
        exportTask.setParams(params);
        exportTask.setStatus(ExportTaskStatusEnum.WAITING.getCode());
        exportTask.setPriority(5);
        exportTask.setFileType("xlsx");
        exportTask.setRetryCount(0);
        exportTask.setMaxRetry(3);
        exportTask.setPlanTime(new Date());

        exportTasksService.save(exportTask);

        // 使用 CompletableFuture 异步执行导出任务
        CompletableFuture.runAsync(() -> {
            log.info("异步任务开始执行: taskId={}, taskName={},params = {} ", exportTask.getId(),
                taskName, params);
            try {
                exportTaskExecutor.executeExportTask(exportTask.getId());
                log.info("导出任务执行完成: taskId={}, taskName={}", exportTask.getId(), taskName);
            } catch (Exception e) {
                log.error("导出任务执行失败: taskId={}, taskName={}, 错误: {}",
                    exportTask.getId(), taskName, e.getMessage(), e);

                // 确保任务状态被更新为失败
                try {
                    ExportTaskDO failedTask = exportTasksService.getById(exportTask.getId());
                    if (failedTask != null) {
                        failedTask.setStatus(ExportTaskStatusEnum.FAILED.getCode());
                        failedTask.setError("异步执行失败: " + e.getMessage());
                        exportTasksService.updateById(failedTask);
                        log.info("已更新失败任务状态: taskId={}", exportTask.getId());
                    }
                } catch (Exception updateException) {
                    log.error("更新失败任务状态时出错: taskId={}, 错误: {}",
                        exportTask.getId(), updateException.getMessage(), updateException);
                }
            }
        }).exceptionally(throwable -> {
            log.error("CompletableFuture执行异常: taskId={}, taskName={}, 错误: {}",
                exportTask.getId(), taskName, throwable.getMessage(), throwable);
            return null;
        });

        log.info("已提交导出任务: taskId={}, taskName={}", exportTask.getId(), taskName);
        return exportTask.getId();
    }
}
