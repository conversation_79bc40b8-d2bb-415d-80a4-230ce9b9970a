package com.xt.hsk.module.infra.dal.dataobject.export;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import com.xt.hsk.module.infra.enums.export.ExportTaskTypeEnum;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 导出任务 DO
 *
 * <AUTHOR>
 * @since 2025/05/26
 */
@TableName("system_export_tasks")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ExportTaskDO extends BaseDO {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 导出Excel名称
     */
    private String taskName;

    /**
     * 导出类型 1-互动课 2-互动课单元 3-讲师
     * @see ExportTaskTypeEnum
     */
    private Integer type;

    /**
     * 导出时需要的参数信息
     */
    private String params;

    /**
     * 执行状态 0-未开始 1-执行中 2-完成 3-失败 4-已过期 5-已取消
     */
    private Integer status;

    /**
     * 任务优先级 1-最高 5-普通 10-最低
     */
    private Integer priority;

    /**
     * 下载链接
     */
    private String downloadUrl;

    /**
     * 文件大小(字节)
     */
    private Long fileSize;

    /**
     * 文件类型 xlsx/csv/pdf
     */
    private String fileType;

    /**
     * 错误信息
     */
    private String error;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetry;

    /**
     * 回调通知地址
     */
    private String notifyUrl;

    /**
     * 计划执行时间
     */
    private Date planTime;

    /**
     * 任务开始时间
     */
    private Date startTime;

    /**
     * 任务完成时间
     */
    private Date finishedAt;

    /**
     * 文件过期时间
     */
    private Date expireTime;

    /**
     * 进程ID
     */
    private String pid;
}
