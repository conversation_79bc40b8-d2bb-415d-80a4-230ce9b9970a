package com.xt.hsk.module.infra.dal.mysql.export;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.infra.controller.admin.export.vo.ExportTaskPageReqVO;
import com.xt.hsk.module.infra.dal.dataobject.export.ExportTaskDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 导出任务 Mapper
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@Mapper
public interface ExportTaskMapper extends BaseMapperX<ExportTaskDO> {

    default PageResult<ExportTaskDO> selectPage(ExportTaskPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ExportTaskDO>()
            .eqIfPresent(ExportTaskDO::getType, reqVO.getType())
            .likeIfPresent(ExportTaskDO::getTaskName, reqVO.getTaskName())
            .inIfPresent(ExportTaskDO::getCreator, reqVO.getCreatorIds())
            .eqIfPresent(ExportTaskDO::getStatus, reqVO.getStatus())
            .orderByDesc(ExportTaskDO::getCreateTime));
    }
} 