package com.xt.hsk.module.system.api.captcha;

import static com.xt.hsk.module.system.controller.admin.captcha.CaptchaController.getRemoteId;

import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Service;

/**
 * CAPTCHA API 实现
 *
 * <AUTHOR>
 * @since 2025/05/30
 */
@Service
public class CaptchaApiImpl implements CaptchaApi {

    @Resource
    private CaptchaService captchaService;

    @Override
    public ResponseModel get(CaptchaVO data, HttpServletRequest request) {
        if (request.getRemoteHost() == null) {
            throw new AssertionError();
        }
        data.setBrowserInfo(getRemoteId(request));
        return captchaService.get(data);
    }

    @Override
    public ResponseModel check(CaptchaVO data, HttpServletRequest request) {
        data.setBrowserInfo(getRemoteId(request));
        return captchaService.check(data);
    }

    @Override
    public ResponseModel verification(CaptchaVO var1) {
        return captchaService.verification(var1);
    }
}
