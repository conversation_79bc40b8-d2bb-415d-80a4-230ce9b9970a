package com.xt.hsk.module.system.dal.mysql.sms;

import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.QueryWrapperX;
import com.xt.hsk.module.system.dal.dataobject.sms.SmsCodeDO;
import java.time.LocalDate;
import java.time.LocalDateTime;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface SmsCodeMapper extends BaseMapperX<SmsCodeDO> {

    /**
     * 获得手机号的最后一个手机验证码
     *
     * @param mobile 手机号
     * @param scene 发送场景，选填
     * @param code 验证码 选填
     * @return 手机验证码
     */
    default SmsCodeDO selectLastByMobile(String countryCode, String mobile, String code,
        Integer scene) {
        return selectOne(new QueryWrapperX<SmsCodeDO>()
                .eq("mobile", mobile)
            .eq("country_code", countryCode)
                .eqIfPresent("scene", scene)
                .eqIfPresent("code", code)
                .orderByDesc("id")
                .limitN(1));
    }

    /**
     * 根据ip查询今日发送次数
     */
    default Long validateSendConutByIp(String ip) {
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        LocalDateTime endOfDay = LocalDate.now().plusDays(1).atStartOfDay();
        return selectCount(new QueryWrapperX<SmsCodeDO>()
            .eq("create_ip", ip)
            .between("create_time", startOfDay, endOfDay));
    }

}
