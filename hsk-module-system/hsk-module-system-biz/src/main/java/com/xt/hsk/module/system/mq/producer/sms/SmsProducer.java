package com.xt.hsk.module.system.mq.producer.sms;

import com.xt.hsk.framework.common.core.KeyValue;
import com.xt.hsk.module.system.mq.message.sms.SmsSendMessage;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * Sms 短信相关消息的 Producer
 *
 * <AUTHOR>
 * @since 2021/3/9 16:35
 */
@Slf4j
@Component
public class SmsProducer {

    @Resource
    private ApplicationContext applicationContext;

    /**
     * 发送 {@link SmsSendMessage} 消息
     *
     * @param logId          短信日志编号
     * @param mobile         手机号
     * @param channelId      渠道编号
     * @param apiTemplateId  短信模板编号
     * @param templateParams 短信模板参数
     * @param content 短信内容 主要是牛信需要用来发送 其他渠道可以不用
     */
    public void sendSmsSendMessage(String countryCode, Long logId, String mobile,
                                   Long channelId, String apiTemplateId, List<KeyValue<String, Object>> templateParams,
        String content) {
        SmsSendMessage message = new SmsSendMessage().setLogId(logId).setMobile(mobile);
        message.setChannelId(channelId).setApiTemplateId(apiTemplateId)
            .setTemplateParams(templateParams).setCountryCode(countryCode).setContent(content);
        applicationContext.publishEvent(message);
    }

}
