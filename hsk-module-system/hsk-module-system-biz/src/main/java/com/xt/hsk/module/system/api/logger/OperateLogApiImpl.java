package com.xt.hsk.module.system.api.logger;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.system.api.logger.dto.OperateLogCreateReqDTO;
import com.xt.hsk.module.system.api.logger.dto.OperateLogPageReqDTO;
import com.xt.hsk.module.system.api.logger.dto.OperateLogRespDTO;
import com.xt.hsk.module.system.dal.dataobject.logger.OperateLogDO;
import com.xt.hsk.module.system.service.logger.OperateLogService;
import com.fhs.core.trans.anno.TransMethodResult;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 操作日志 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OperateLogApiImpl implements OperateLogApi {

    @Resource
    private OperateLogService operateLogService;

    @Override
    public void createOperateLog(OperateLogCreateReqDTO createReqDTO) {
        operateLogService.createOperateLog(createReqDTO);
    }

    @Override
    @TransMethodResult
    public PageResult<OperateLogRespDTO> getOperateLogPage(OperateLogPageReqDTO pageReqDTO) {
        PageResult<OperateLogDO> operateLogPage = operateLogService.getOperateLogPage(pageReqDTO);
        return BeanUtils.toBean(operateLogPage, OperateLogRespDTO.class);
    }

}
