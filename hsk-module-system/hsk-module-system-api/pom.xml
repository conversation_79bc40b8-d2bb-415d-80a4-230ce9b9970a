<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.xt.hsk</groupId>
        <artifactId>hsk-module-system</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>hsk-module-system-api</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        system 模块 API，暴露给其它模块调用
    </description>

    <dependencies>
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-common</artifactId>
        </dependency>

        <!-- 参数校验 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <optional>true</optional>
        </dependency>

      <dependency>
        <groupId>com.anji-plus</groupId>
        <artifactId>captcha-spring-boot-starter</artifactId>
      </dependency>

      <!-- Servlet API，仅编译时需要，避免传递依赖 -->
      <dependency>
        <groupId>jakarta.servlet</groupId>
        <artifactId>jakarta.servlet-api</artifactId>
        <scope>provided</scope>
      </dependency>

      <!-- 移除 Web/Security 相关依赖，因为 API 模块不需要这些依赖 -->
      <!-- 这样可以解决与 hsk-spring-boot-starter-web 和 hsk-spring-boot-starter-security 的循环依赖问题 -->
    </dependencies>

</project>
