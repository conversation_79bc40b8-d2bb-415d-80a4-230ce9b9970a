package com.xt.hsk.module.system.enums.sms;

import cn.hutool.core.util.ArrayUtil;
import com.xt.hsk.framework.common.core.ArrayValuable;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户短信验证码发送场景的枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SmsSceneEnum implements ArrayValuable<Integer> {



    // ======================================登陆用短信模板================================
    MEMBER_LOGIN_CN(1, "user-sms-login-cn", "app用户 - 国内手机号登陆 - 中文短信"),
    MEMBER_LOGIN_EN(1, "user-sms-login-en", "app用户 - 非国内手机号登陆 - 英文短信"),
    MEMBER_LOGIN_VI(1, "user-sms-login-vi", "app用户 - 非国内手机号登陆 - 越南语短信"),

    // ======================================忘记密码用短信模板=============================
    MEMBER_RESET_PASSWORD_CN(4, "user-reset-password-cn", "app用户 - 忘记密码 - 中文短信"),
    MEMBER_RESET_PASSWORD_EN(4, "user-reset-password-en", "app用户 - 忘记密码 - 英文短信"),
    MEMBER_RESET_PASSWORD_VI(4, "user-reset-password-vi", "app用户 - 忘记密码 - 越南语短信"),
    // ======================================修改密码用短信模板=============================
    MEMBER_UPDATE_PASSWORD(3, "user-update-password", "app用户 - 修改密码"),
    // ======================================更换手机号用短信模板=============================
    MEMBER_UPDATE_MOBILE_CN(5, "user-update-mobile-cn", "app用户 - 更换手机号 - 中文短信"),
    MEMBER_UPDATE_MOBILE_EN(5, "user-update-mobile-en", "app用户 - 更换手机号 - 英文短信"),
    MEMBER_UPDATE_MOBILE_VI(5, "user-update-mobile-vi", "app用户 - 更换手机号 - 越南语短信"),
    // =====================================原框架定义的枚举目前用不到================================
    MEMBER_UPDATE_MOBILE(2, "user-update-mobile", "app用户 - 修改手机"),
    ADMIN_MEMBER_LOGIN(21, "admin-sms-login", "后台用户 - 手机号登录"),
    ADMIN_MEMBER_REGISTER(22, "admin-sms-register", "后台用户 - 手机号注册"),
    ADMIN_MEMBER_RESET_PASSWORD(23, "admin-reset-password", "后台用户 - 忘记密码");

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(SmsSceneEnum::getScene).toArray(Integer[]::new);

    /**
     * 验证场景的编号
     */
    private final Integer scene;
    /**
     * 模版编码
     */
    private final String templateCode;
    /**
     * 描述
     */
    private final String description;

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

    /**
     * 按场景获取短信模版
     *
     * @param scene 场景
     * @return {@code SmsSceneEnum }
     */
    public static SmsSceneEnum getCodeByScene(Integer scene) {
        return ArrayUtil.firstMatch(sceneEnum -> sceneEnum.getScene().equals(scene),
                values());
    }

    /**
     * 按场景和手机区号获取短信模版
     * 86 -> 发中文短信
     * 84 -> 发越南语短信
     * 其他国家 -> 默认发送英文短信
     */
    public static SmsSceneEnum getCodeBySceneAndCountryCode(Integer scene, String countryCode) {
        // 目前只有登陆、忘记密码和更换手机号用
        if (scene == 1 || scene == 4 || scene == 5) {
            if (scene == 1) {
                return switch (countryCode) {
                    case "86" -> MEMBER_LOGIN_CN;
                    case "84" -> MEMBER_LOGIN_VI;
                    default -> MEMBER_LOGIN_EN;
                };
            }
            if (scene == 4) {
                return switch (countryCode) {
                    case "86" -> MEMBER_RESET_PASSWORD_CN;
                    case "84" -> MEMBER_RESET_PASSWORD_VI;
                    default -> MEMBER_RESET_PASSWORD_EN;
                };
            }
            return switch (countryCode) {
                case "86" -> MEMBER_UPDATE_MOBILE_CN;
                case "84" -> MEMBER_UPDATE_MOBILE_VI;
                default -> MEMBER_UPDATE_MOBILE_EN;
            };
        }
        return getCodeByScene(scene);
    }

}
