package com.xt.hsk.module.system.api.captcha;

import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import jakarta.servlet.http.HttpServletRequest;

/**
 * CAPTCHA API 验证码 API
 *
 * <AUTHOR>
 * @since 2025/05/30
 */
public interface CaptchaApi {

    /**
     * 获得验证码
     */
    ResponseModel get(CaptchaVO data, HttpServletRequest request);

    /**
     * 校验验证码
     */
    ResponseModel check(CaptchaVO data, HttpServletRequest request);

    /**
     * 验证码校验
     */
    ResponseModel verification(CaptchaVO var1);
}
