package com.xt.hsk.module.system.api.sms.dto.code;

import com.xt.hsk.framework.common.validation.InEnum;
import com.xt.hsk.framework.common.validation.Mobile;
import com.xt.hsk.module.system.enums.sms.SmsSceneEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 短信验证码的校验 Request DTO
 *
 * <AUTHOR>
 */
@Data
public class SmsCodeValidateReqDTO {

    /**
     * 手机号
     */
    @Mobile
    @NotEmpty(message = "手机号不能为空")
    private String mobile;

    /**
     * 区号
     */
    private String countryCode;
    /**
     * 发送场景
     */
    @NotNull(message = "发送场景不能为空")
    @InEnum(SmsSceneEnum.class)
    private Integer scene;
    /**
     * 验证码
     */
    @NotEmpty(message = "验证码")
    private String code;

}
