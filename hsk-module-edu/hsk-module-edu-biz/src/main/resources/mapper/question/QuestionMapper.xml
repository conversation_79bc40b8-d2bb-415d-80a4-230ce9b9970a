<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.edu.dal.mysql.question.QuestionMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="queryQuestionCount"
            resultType="java.lang.Long">
        SELECT
          count(1)
        FROM edu_question q
                 left join edu_chapter c on c.id = q.chapter_id
                 left join edu_unit u on u.id = q.unit_id
                 left join edu_textbook t on t.id = q.textbook_id
                 left join edu_question_type qt on qt.id = q.type_id
        WHERE q.deleted = 0
        <if test="req.textbookId != null">
            AND q.textbook_id = #{req.textbookId}
        </if>
        <if test="req.chapterId != null">
            AND q.chapter_id = #{req.chapterId}
        </if>
          <if test="req.questionCode != null and req.questionCode!=''">
            AND q.question_code = #{req.questionCode}
        </if>
        <if test="req.materialContent != null and req.materialContent !=''">
            AND q.material_content = #{req.materialContent}
        </if>
        <if test="req.unitId != null">
            AND q.unit_id = #{req.unitId}
        </if>
        <if test="req.questionId != null">
            AND q.question_id = #{req.questionId}
        </if>
        <if test="req.typeId != null">
            AND q.type_id = #{req.typeId}
        </if>
        <if test="req.hskLevel != null">
            AND q.hsk_level = #{req.hskLevel}
        </if>
        <if test="req.subject != null">
            AND q.subject = #{req.subject}
        </if>
        <if test="req.status != null">
            AND q.status = #{req.status}
        </if>
        <if test="req.isShow != null">
            AND q.is_show = #{req.isShow}
        </if>
        <if test="req.textbookType != null">
            AND t.type = #{req.textbookType}
        </if>
    </select>


    <select id="queryQuestionPage"
            resultType="com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRespVO">
        SELECT
        q.id,
        q.question_code AS questionCode,
        q.hsk_level,
        q.subject,
        q.status,
        q.updater ,
        q.type_id AS typeId,
        q.create_time AS createTime,
        q.update_time AS updateTime,
        q.deleted,
        q.is_show AS isShow,
        q.textbook_id AS textbookId,
        q.unit_id AS unitId,
        q.id AS id,
        q.material_content AS meterialContent,
        q.material_audio AS meterialAudio,
        q.material_image AS meterialImage,
        q.options AS options,
        q.version AS version,
        t.type AS textbookType,
        t.name_cn AS textbookNameCn,
        c.id AS chapterId,
        c.chapter_name_cn AS chapterNameCn,
        u.unit_name_cn AS unitNameCn,
        qt.name_cn AS questionTypeNameCn
        FROM edu_question q
        left join edu_chapter c on c.id = q.chapter_id
        left join edu_unit u on u.id = q.unit_id
        left join edu_textbook t on t.id = q.textbook_id
        left join edu_question_type qt on qt.id = q.type_id
        WHERE q.deleted = 0
        <if test="req.textbookId != null">
            AND q.textbook_id = #{req.textbookId}
        </if>
        <if test="req.chapterId != null">
            AND q.chapter_id = #{req.chapterId}
        </if>
        <if test="req.questionCode != null and req.questionCode!=''">
            AND q.question_code like concat('%' , #{req.questionCode} , '%')
        </if>
        <if test="req.materialContent != null and req.materialContent !=''">
            AND q.material_content LIKE CONCAT(#{req.materialContent}, '%')
        </if>
        <if test="req.unitId != null">
            AND q.unit_id = #{req.unitId}
        </if>
        <if test="req.questionId != null">
            AND q.id = #{req.questionId}
        </if>
        <if test="req.typeId != null">
            AND q.type_id = #{req.typeId}
        </if>
        <if test="req.hskLevel != null">
            AND q.hsk_level = #{req.hskLevel}
        </if>
        <if test="req.subject != null">
            AND q.subject = #{req.subject}
        </if>
        <if test="req.status != null">
            AND q.status = #{req.status}
        </if>
        <if test="req.isShow != null">
            AND q.is_show = #{req.isShow}
        </if>
        <if test="req.textbookType != null">
            AND t.type = #{req.textbookType}
        </if>
        <if test="req.unitSort != null">
            AND u.sort = #{req.unitSort}
        </if>
        <if test="req.typeIdList != null and req.typeIdList.size() > 0">
            AND q.type_id IN
            <foreach item="typeId" index="index" collection="req.typeIdList" open="(" separator="," close=")">
                #{typeId}
            </foreach>
        </if>
        order by q.id desc
    </select>

    <select id="selectIdList" resultType="java.lang.Long" >
        SELECT id FROM edu_question WHERE deleted = 0 and textbook_id=0 order by id asc
    </select>


    <select id="queryQuestionById"
            resultType="com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRespVO">
        SELECT
        q.id,
        q.question_code AS questionCode,
        q.material_audio AS materialAudio,
        q.material_image AS materialImage,
        q.material_content AS materialContent,
        q.options AS options,
        q.hsk_level,
        q.subject,
        q.status,
        q.updater ,
        q.type_id AS typeId,
        q.create_time AS createTime,
        q.update_time AS updateTime,
        q.deleted,
        q.textbook_id AS textbookId,
        q.unit_id AS unitId,
        q.id AS id,
        q.version AS version,
        t.type AS textbookType,
        t.name_cn AS textbookNameCn,
        c.id AS chapterId,
        c.chapter_name_cn AS chapterNameCn,
        u.unit_name_cn AS unitNameCn,
        qt.name_cn AS questionTypeNameCn
        FROM edu_question q
        left join edu_chapter c on c.id = q.chapter_id
        left join edu_unit u on u.id = q.unit_id
        left join edu_textbook t on t.id = q.textbook_id
        left join edu_question_type qt on qt.id = q.type_id
        WHERE q.deleted = 0 and q.id= #{id}
    </select>

    <select id="getQuestionTypeCountRespVOByUnitIds"
            resultType="com.xt.hsk.module.edu.controller.app.question.vo.QuestionTypeCountRespVO">
        SELECT u.sort            as unit_sort,
        q.type_id AS type_id,
               sum(question_num) as question_count
        FROM edu_question q
                 inner Join edu_unit u on q.unit_id = u.id
        where u.deleted = 0
          and q.deleted = 0
        and q.is_show = 1
        and q.status = 0
        and q.subject = #{reqVo.subject}
        and q.hsk_level = #{reqVo.hskLevel}
        <if test="reqVo.typeId != null ">
            and q.type_id = #{reqVo.typeId}
        </if>
        <if test="reqVo.unitSort != null ">
            and u.sort = #{reqVo.unitSort}
        </if>
        <if test="reqVo.textbookId != null">
            and q.textbook_id = #{reqVo.textbookId}
        </if>
        <if test="reqVo.chapterId != null">
            and q.chapter_id = #{reqVo.chapterId}
        </if>
        GROUP BY u.sort, q.type_id
        having sum(question_num) is not null and sum(question_num) > 0
    </select>
    <select id="getTextbookChapterQuestions"
            resultType="com.xt.hsk.module.edu.controller.app.question.vo.TextbookChapterQuestionRespVO">
        select eq.textbook_id,eq.chapter_id,sum(question_num) as question_count
        from edu_question eq
        inner join edu_textbook et on eq.textbook_id = et.id
        inner join edu_unit eu on eq.unit_id = eu.id
        where eq.deleted = 0 and et.deleted = 0
        and eq.subject = #{reqVo.subject}
        and eq.hsk_level = #{reqVo.hskLevel}
        <if test="reqVo.typeId != null">
            and eq.type_id = #{reqVo.typeId}
        </if>
        <if test="reqVo.unitSort != null">
            and eu.sort = #{reqVo.unitSort}
        </if>
        and eq.status = 0
        and eq.is_show = 1
        <if test="reqVo.textBookTypes != null and reqVo.textBookTypes.size() > 0">
            and et.type in
            <foreach collection="reqVo.textBookTypes" close=")" open="(" separator="," item="type">
                #{type}
            </foreach>
        </if>
        group by eq.textbook_id,eq.chapter_id
        having sum(question_num) is not null and sum(question_num) > 0
    </select>
    <select id="selectUserPracticeQuestions"
            resultType="java.lang.Long">
        select eq.id
        from edu_question eq
                 inner join edu_unit eu on eq.unit_id = eu.id
        where eq.deleted = 0
          and eq.status = 0
          and eq.is_show = 1
          and eq.subject = #{subject}
          and eq.hsk_level = #{hskLevel}
          and eq.type_id = #{typeId}
          and eq.textbook_id = #{textbookId}
          and eq.chapter_id = #{chapterId}
          and eu.sort = #{unitSort}
        order by eq.id asc
    </select>
    <select id="countByTextbookIds"
            resultType="com.xt.hsk.module.edu.controller.admin.question.vo.QuestionTextbookCount">
        SELECT textbook_id AS textbookId,
        count(*) AS count
        FROM edu_question
        WHERE deleted = 0
        -- and status = 0
        -- and is_show = 1
        and textbook_id in
        <foreach item="item" index="index" collection="textbookIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY textbook_id
    </select>
    <select id="countByUnitIds"
            resultType="com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionUnitCount">
        SELECT unit_id AS unitId,
        count(*) AS count
        FROM edu_question
        WHERE deleted = 0
        -- and status = 0
        -- and is_show = 1
        and unit_id in
        <foreach item="item" index="index" collection="unitIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY unit_id
    </select>

    <select id="queryQuestionByIdList" resultType="com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRespVO">
        SELECT q.id,
               q.question_code    AS questionCode,
               q.material_audio   AS materialAudio,
               q.material_image   AS materialImage,
               q.material_content AS materialContent,
               q.options          AS options,
               q.hsk_level,
               q.subject,
               q.status,
               q.updater,
               q.type_id          AS typeId,
               q.create_time      AS createTime,
               q.update_time      AS updateTime,
               q.deleted,
               q.textbook_id      AS textbookId,
               q.unit_id          AS unitId,
               q.id               AS id,
               q.version          AS version,
               t.type             AS textbookType,
               t.name_cn          AS textbookNameCn,
               c.id               AS chapterId,
               c.chapter_name_cn  AS chapterNameCn,
               u.unit_name_cn     AS unitNameCn,
               qt.name_cn         AS questionTypeNameCn
        FROM edu_question q
                 left join edu_chapter c on c.id = q.chapter_id
                 left join edu_unit u on u.id = q.unit_id
                 left join edu_textbook t on t.id = q.textbook_id
                 left join edu_question_type qt on qt.id = q.type_id
        WHERE q.deleted = 0
          and q.id IN
            <foreach item="id" index="index" collection="idList" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>
    <select id="getRandomQuestionIds" resultType="java.lang.Long">
        select id
        from edu_question
        where deleted = 0
          and status = 0
          and is_show = 1
          and hsk_level = #{hskLevel}
          and subject = #{subject}
        order by rand()
        limit #{limit}
    </select>
</mapper>