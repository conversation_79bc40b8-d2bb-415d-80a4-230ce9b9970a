package com.xt.hsk.module.edu.convert.interactivecourse;

import com.xt.hsk.framework.common.util.i18n.LanguageUtils;
import com.xt.hsk.module.edu.api.interactivecourse.dto.InteractiveCourseBaseInfoRespDTO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.*;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseDO;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 互动课课程转换类
 *
 * <AUTHOR>
 * @since 2025/05/24
 */
@Mapper
public interface InteractiveCourseConvert {

    InteractiveCourseConvert INSTANCE = Mappers.getMapper(InteractiveCourseConvert.class);

    /**
     * 创建VO类转DO类
     */
    InteractiveCourseDO createReqVOToDO(InteractiveCourseSaveReqVO createReqVO);

    /**
     * DO类转列表VO类
     */
    List<InteractiveCourseRespVO> doListToCreateReqVOList(List<InteractiveCourseDO> interactiveCourseDO);

    /**
     * DO类转app端列表页VO类
     */
    List<InteractiveCourseAppRespVO> doListToAppPageVOList(
        List<InteractiveCourseDO> interactiveCourseDO);

    /**
     * DO类转app端详情页VO类 InteractiveCourseAppDetailVO
     */
    InteractiveCourseAppDetailVO doToAppDetailVO(InteractiveCourseDO bean);

    /**
     * 课程基本信息VO列表转课程基本信息DTO列表
     */
    List<InteractiveCourseBaseInfoRespDTO> voListToInfoDtoList(List<InteractiveCourseBaseInfoRespVO> voList);

    @AfterMapping
    default void fillLocalizedFields(InteractiveCourseDO bean,
        @MappingTarget InteractiveCourseAppRespVO respVO) {
        // 这段代码会在基本映射完成后执行
        // 可以访问源对象bean的所有属性
        // 可以修改目标对象respVO的属性
        respVO.setCourseName(LanguageUtils.getLocalizedValue(
            bean.getCourseNameCn(),
            bean.getCourseNameEn(),
            bean.getCourseNameOt()));
    }

    @AfterMapping
    default void fillLocalizedFields2(InteractiveCourseDO bean,
        @MappingTarget InteractiveCourseAppDetailVO respVO) {
        // 这段代码会在基本映射完成后执行
        // 可以访问源对象bean的所有属性
        // 可以修改目标对象respVO的属性
        respVO.setCourseName(LanguageUtils.getLocalizedValue(
            bean.getCourseNameCn(),
            bean.getCourseNameEn(),
            bean.getCourseNameOt()));
    }
}
