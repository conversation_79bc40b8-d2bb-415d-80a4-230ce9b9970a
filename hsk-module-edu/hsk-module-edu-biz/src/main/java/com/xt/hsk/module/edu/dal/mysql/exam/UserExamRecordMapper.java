package com.xt.hsk.module.edu.dal.mysql.exam;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.module.edu.controller.admin.exam.vo.UserExamRecordPageReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.UserExamRecordPageRespVO;
import com.xt.hsk.module.edu.controller.app.exam.vo.ExamAppPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.UserExamRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户模考记录 Mapper
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Mapper
public interface UserExamRecordMapper extends BaseMapperX<UserExamRecordDO> {

    /**
     * 分页获取用户模考记录
     */
    IPage<UserExamRecordPageRespVO> getUserExamRecordPage(Page<UserExamRecordPageRespVO> page, @Param("req") UserExamRecordPageReqVO pageReqVO);

    /**
     * 用户模考记录总数
     */
    Long countUserExamRecordPage(@Param("req") UserExamRecordPageReqVO pageReqVO);

    /**
     * 获取用户模考记录总数
     *
     * @param userId
     * @return
     */
    Long getUserExamRecordCount(Long userId);

    IPage<UserExamRecordDO> getMyExamRecordPage(Page<Object> objectPage, @Param("req") ExamAppPageReqVO reqVO);
}