package com.xt.hsk.module.edu.dal.mysql.userquestionanswerrecord;

import java.util.*;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionSearchReqVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionStatisticsRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionTypeCountRespVO;
import com.xt.hsk.module.edu.dal.dataobject.userquestionanswerrecord.UserQuestionAnswerRecordDO;
import org.apache.ibatis.annotations.Mapper;
import com.xt.hsk.module.edu.controller.admin.userquestionanswerrecord.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 用户题目作答记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserQuestionAnswerRecordMapper extends BaseMapperX<UserQuestionAnswerRecordDO> {

    default PageResult<UserQuestionAnswerRecordDO> selectPage(UserQuestionAnswerRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<UserQuestionAnswerRecordDO>()
                .eqIfPresent(UserQuestionAnswerRecordDO::getUserId, reqVO.getUserId())
                .eqIfPresent(UserQuestionAnswerRecordDO::getQuestionId, reqVO.getQuestionId())
                .eqIfPresent(UserQuestionAnswerRecordDO::getHskLevel, reqVO.getHskLevel())
                .eqIfPresent(UserQuestionAnswerRecordDO::getTextbookId, reqVO.getTextbookId())
                .eqIfPresent(UserQuestionAnswerRecordDO::getChapterId, reqVO.getChapterId())
                .eqIfPresent(UserQuestionAnswerRecordDO::getUnitId, reqVO.getUnitId())
                .eqIfPresent(UserQuestionAnswerRecordDO::getQuestionTypeId, reqVO.getQuestionTypeId())
                .eqIfPresent(UserQuestionAnswerRecordDO::getVersion, reqVO.getVersion())
                .eqIfPresent(UserQuestionAnswerRecordDO::getPracticeMode, reqVO.getPracticeMode())
                .eqIfPresent(UserQuestionAnswerRecordDO::getPracticeId, reqVO.getPracticeId())
                .betweenIfPresent(UserQuestionAnswerRecordDO::getAnswerTime, reqVO.getAnswerTime())
                .betweenIfPresent(UserQuestionAnswerRecordDO::getAnswerDate, reqVO.getAnswerDate())
                .betweenIfPresent(UserQuestionAnswerRecordDO::getStartTime, reqVO.getStartTime())
                .betweenIfPresent(UserQuestionAnswerRecordDO::getEndTime, reqVO.getEndTime())
                .betweenIfPresent(UserQuestionAnswerRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(UserQuestionAnswerRecordDO::getId));
    }

    List<QuestionTypeCountRespVO> getUserUnitSortQuestionTypeCount(@Param("reqVo") QuestionSearchReqVO reqVO);

    Long getUserPracticeQuestionCount(Long userId);
}