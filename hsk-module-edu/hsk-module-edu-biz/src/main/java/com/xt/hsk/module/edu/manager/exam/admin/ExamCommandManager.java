package com.xt.hsk.module.edu.manager.exam.admin;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_DETAIL_LIST_CANNOT_BE_EMPTY;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_DUPLICATE_SUBJECT;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_DUPLICATE_UNIT;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_MISSING_REQUIRED_UNITS;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_PAPER_RULE_NO_VALID_QUESTION_TYPE;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_PUBLISHED_CANNOT_UPDATE;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_QUESTION_DETAIL_NOT_FOUND;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_SUBJECT_NOT_EXISTS;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_SUBJECT_NOT_FOUND_IN_RULE;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_SUBJECT_UNIT_LIST_CANNOT_BE_EMPTY;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_UNIT_NOT_EXISTS;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_UNIT_NOT_FOUND_IN_RULE;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_UNIT_QUESTION_COUNT_MISMATCH;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_UNIT_QUESTION_LIST_CANNOT_BE_EMPTY;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.constants.RedisKeyPrefix;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.framework.web.core.util.WebFrameworkUtils;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamDetailQuestionReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamDetailQuestionVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamDetailSubjectReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamDetailUnitReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleQuestionTypeRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamSaveReqVO;
import com.xt.hsk.module.edu.convert.exam.ExamConvert;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamDO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamDetailDO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamDetailVersionDO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamPaperRuleDO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiondetail.QuestionDetailDO;
import com.xt.hsk.module.edu.enums.exam.ExamPublishStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamQuestionTypeUnitEnum;
import com.xt.hsk.module.edu.service.exam.ExamDetailService;
import com.xt.hsk.module.edu.service.exam.ExamDetailVersionService;
import com.xt.hsk.module.edu.service.exam.ExamPaperRuleDetailService;
import com.xt.hsk.module.edu.service.exam.ExamPaperRuleService;
import com.xt.hsk.module.edu.service.exam.ExamService;
import com.xt.hsk.module.edu.service.question.questiondetail.QuestionDetailService;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;


/**
 * 模考命令操作 Manager
 * 负责模考的创建、更新等操作
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Slf4j
@Component
public class ExamCommandManager {

    @Resource
    private ExamService examService;

    @Resource
    private ExamDetailService examDetailService;

    @Resource
    private ExamPaperRuleService examPaperRuleService;

    @Resource
    private ExamPaperRuleDetailService examPaperRuleDetailService;

    @Resource
    private QuestionDetailService questionDetailService;

    @Resource
    private ExamDetailVersionService examDetailVersionService;

    /**
     * 创建模考
     */
    @CacheEvict(value = RedisKeyPrefix.EXAM_AVAILABILITY, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public Long createExam(ExamSaveReqVO createReqVO) {
        // 获取模考组卷规则
        ExamPaperRuleDO paperRule = examPaperRuleService.getPaperRule(createReqVO.getPaperRuleId());

        // 获取并验证组卷规则详情
        Map<Integer, Map<Integer, ExamPaperRuleQuestionTypeRespVO>> subjectToUnitRuleMap =
                buildSubjectToUnitRuleMap(paperRule.getId());

        // 添加完整性和重复性验证
        validateExamDetailCompleteness(createReqVO.getExamDetailSubjectList(), subjectToUnitRuleMap);
        validateExamDetailDuplication(createReqVO.getExamDetailSubjectList());

        // 先构建并验证模考详情列表
        List<ExamDetailDO> examDetailList = buildExamDetailList(
                createReqVO.getExamDetailSubjectList(),
                subjectToUnitRuleMap
        );

        // 构建模考基础信息
        ExamDO exam = buildExamBasicInfo(createReqVO, paperRule);
        exam.setPublishStatus(ExamPublishStatusEnum.UNPUBLISHED.getCode());
        exam.setExamCount(0);
        exam.setTotalScore(100 * subjectToUnitRuleMap.size());
        exam.setSort(examService.getNextSort(createReqVO.getHskLevel()));
        exam.setExamDetailVersion(1);
        examService.save(exam);

        examDetailList.forEach(examDetail -> {
            examDetail.setExamId(exam.getId());
            examDetail.setVersion(1);
        });

        // 批量保存模考详情
        examDetailService.saveBatch(examDetailList);

        List<ExamDetailVersionDO> examDetailVersionDOList = examDetailList.stream()
                .map(e -> {
                    ExamDetailVersionDO versionDO = BeanUtil.copyProperties(e, ExamDetailVersionDO.class, "id");
                    versionDO.setExamDetailId(e.getId());
                    return versionDO;
                })
                .toList();

        examDetailVersionService.saveBatch(examDetailVersionDOList);

        // 设置日志上下文变量
        LogRecordContext.putVariable("examId", exam.getId());
        LogRecordContext.putVariable("exam", exam);

        return exam.getId();
    }

    /**
     * 构建模考基础信息
     */
    private ExamDO buildExamBasicInfo(ExamSaveReqVO createReqVO, ExamPaperRuleDO paperRule) {
        ExamDO exam = ExamConvert.INSTANCE.saveReqVoToDo(createReqVO);

        // 从组卷规则设置相关信息
        exam.setType(paperRule.getExamType());
        exam.setListeningDuration(paperRule.getListeningDuration());
        exam.setReadingDuration(paperRule.getReadingDuration());
        exam.setWritingDuration(paperRule.getWritingDuration());
        return exam;
    }

    /**
     * 构建科目单元规则映射
     */
    private Map<Integer, Map<Integer, ExamPaperRuleQuestionTypeRespVO>> buildSubjectToUnitRuleMap(Long paperRuleId) {
        List<ExamPaperRuleQuestionTypeRespVO> ruleQuestionTypes =
                examPaperRuleDetailService.getPaperRuleQuestionType(paperRuleId);

        Map<Integer, Map<Integer, ExamPaperRuleQuestionTypeRespVO>> subjectToUnitRuleMap =
                ruleQuestionTypes.stream()
                        .filter(e -> e.getQuestionCount() != null && e.getQuestionCount() > 0)
                        .collect(Collectors.groupingBy(
                                ExamPaperRuleQuestionTypeRespVO::getSubject,
                                Collectors.toMap(
                                        ExamPaperRuleQuestionTypeRespVO::getUnit,
                                        Function.identity(),
                                        (existing, replacement) -> replacement
                                )
                        ));

        if (CollUtil.isEmpty(subjectToUnitRuleMap)) {
            throw exception(EXAM_PAPER_RULE_NO_VALID_QUESTION_TYPE);
        }

        return subjectToUnitRuleMap;
    }

    /**
     * 构建模考详情列表
     */
    private List<ExamDetailDO> buildExamDetailList(
            List<ExamDetailSubjectReqVO> examDetailSubjectList,
            Map<Integer, Map<Integer, ExamPaperRuleQuestionTypeRespVO>> subjectToUnitRuleMap) {

        if (CollUtil.isEmpty(examDetailSubjectList)) {
            throw exception(EXAM_DETAIL_LIST_CANNOT_BE_EMPTY);
        }

        List<Long> questionIdList = examDetailSubjectList.stream()
                .map(ExamDetailSubjectReqVO::getExamDetailUnitList)
                .flatMap(List::stream)
                .map(ExamDetailUnitReqVO::getQuestionList)
                .flatMap(List::stream)
                .map(ExamDetailQuestionReqVO::getQuestionId)
                .distinct()
                .toList();

        Map<Long, List<Long>> questionDetailIdMap = questionDetailService.lambdaQuery()
                .select(QuestionDetailDO::getId, QuestionDetailDO::getQuestionId)
                .in(QuestionDetailDO::getQuestionId, questionIdList)
                .orderByAsc(QuestionDetailDO::getQuestionId, QuestionDetailDO::getSort)
                .list()
                .stream()
                .collect(Collectors.groupingBy(
                        QuestionDetailDO::getQuestionId,
                        Collectors.mapping(QuestionDetailDO::getId, Collectors.toList())
                ));

        List<ExamDetailDO> examDetailList = new ArrayList<>();

        for (ExamDetailSubjectReqVO examDetailSubject : examDetailSubjectList) {
            List<ExamDetailDO> subjectDetails = processSubjectDetails(
                    examDetailSubject,
                    subjectToUnitRuleMap,
                    questionDetailIdMap
            );
            examDetailList.addAll(subjectDetails);
        }

        return examDetailList;
    }

    /**
     * 处理科目详情
     */
    private List<ExamDetailDO> processSubjectDetails(
            ExamDetailSubjectReqVO examDetailSubject,
            Map<Integer, Map<Integer, ExamPaperRuleQuestionTypeRespVO>> subjectToUnitRuleMap,
            Map<Long, List<Long>> questionDetailIdMap) {

        Integer subject = examDetailSubject.getSubject();
        SubjectEnum subjectEnum = validateAndGetSubjectEnum(subject);

        Map<Integer, ExamPaperRuleQuestionTypeRespVO> unitMap = subjectToUnitRuleMap.get(subject);

        if (CollUtil.isEmpty(unitMap)) {
            throw exception(EXAM_SUBJECT_NOT_FOUND_IN_RULE, EXAM_SUBJECT_NOT_FOUND_IN_RULE.getMsg(), subjectEnum.getDesc());
        }

        List<ExamDetailUnitReqVO> examDetailUnitList = examDetailSubject.getExamDetailUnitList();
        if (CollUtil.isEmpty(examDetailUnitList)) {
            throw exception(EXAM_SUBJECT_UNIT_LIST_CANNOT_BE_EMPTY, EXAM_SUBJECT_UNIT_LIST_CANNOT_BE_EMPTY.getMsg(), subjectEnum.getDesc());
        }

        List<ExamDetailDO> subjectDetails = new ArrayList<>();

        for (ExamDetailUnitReqVO unitReqVO : examDetailUnitList) {
            ExamDetailDO examDetail = createExamDetail(subjectEnum, unitReqVO, unitMap, questionDetailIdMap);
            subjectDetails.add(examDetail);
        }

        return subjectDetails;
    }

    /**
     * 创建模考详情对象
     */
    private ExamDetailDO createExamDetail(
            SubjectEnum subjectEnum,
            ExamDetailUnitReqVO unitReqVO,
            Map<Integer, ExamPaperRuleQuestionTypeRespVO> unitMap,
            Map<Long, List<Long>> questionDetailIdMap) {

        Integer unit = unitReqVO.getUnit();
        ExamQuestionTypeUnitEnum unitEnum = ExamQuestionTypeUnitEnum.getByCode(unit);
        if (unitEnum == null) {
            throw exception(EXAM_UNIT_NOT_EXISTS);
        }
        ExamPaperRuleQuestionTypeRespVO questionTypeRespVO = unitMap.get(unit);

        if (questionTypeRespVO == null) {
            throw exception(EXAM_UNIT_NOT_FOUND_IN_RULE, EXAM_UNIT_NOT_FOUND_IN_RULE.getMsg(), subjectEnum.getDesc(), unitEnum.getDesc());
        }

        List<ExamDetailQuestionReqVO> questionReqVOList = unitReqVO.getQuestionList();
        List<ExamDetailQuestionVO> questionVOList = questionReqVOList.stream()
                .map(reqVO -> {

                    List<Long> questionDetailIdList = questionDetailIdMap.get(reqVO.getQuestionId());
                    if (CollUtil.isEmpty(questionDetailIdList)) {
                        throw exception(EXAM_QUESTION_DETAIL_NOT_FOUND);
                    }

                    ExamDetailQuestionVO questionVO = new ExamDetailQuestionVO();
                    questionVO.setQuestionId(reqVO.getQuestionId());
                    questionVO.setSort(reqVO.getSort());
                    questionVO.setQuestionDetailIdList(questionDetailIdList);
                    return questionVO;
                })
                .toList();
        validateQuestionList(questionVOList, questionTypeRespVO, subjectEnum, unitEnum);

        ExamDetailDO examDetail = new ExamDetailDO();
        examDetail.setId(unitReqVO.getExamDetailId());
        examDetail.setSubject(subjectEnum.getCode());
        examDetail.setUnit(unit);
        examDetail.setExamQuestionTypeId(questionTypeRespVO.getExamQuestionTypeId());
        examDetail.setQuestionTypeIds(questionTypeRespVO.getQuestionTypeIds());
        examDetail.setQuestionNames(buildQuestionNames(questionTypeRespVO.getQuestionTypeNameList()));
        examDetail.setQuestionCount(questionTypeRespVO.getQuestionCount());
        examDetail.setQuestions(JSONUtil.toJsonStr(questionVOList));

        return examDetail;
    }

    /**
     * 验证题目列表
     */
    private void validateQuestionList(
            List<ExamDetailQuestionVO> questionList,
            ExamPaperRuleQuestionTypeRespVO questionTypeRespVO,
            SubjectEnum subjectEnum,
            ExamQuestionTypeUnitEnum unitEnum) {

        if (CollUtil.isEmpty(questionList)) {
            throw exception(EXAM_UNIT_QUESTION_LIST_CANNOT_BE_EMPTY, EXAM_UNIT_QUESTION_LIST_CANNOT_BE_EMPTY.getMsg(), subjectEnum.getDesc(), unitEnum.getDesc());
        }

        int ruleCount = questionTypeRespVO.getQuestionCount();
        int actualCount = questionList.stream()
                .filter(Objects::nonNull)
                .map(ExamDetailQuestionVO::getQuestionDetailIdList)
                .filter(CollUtil::isNotEmpty)
                .mapToInt(List::size)
                .sum();

        // 验证三个数量是否一致
        if (actualCount != ruleCount) {
            throw exception(EXAM_UNIT_QUESTION_COUNT_MISMATCH, EXAM_UNIT_QUESTION_COUNT_MISMATCH.getMsg(), subjectEnum.getDesc(), unitEnum.getDesc(), ruleCount, actualCount);
        }
    }

    /**
     * 构建题型名称字符串
     */
    private String buildQuestionNames(List<String> questionTypeNameList) {
        if (CollUtil.isEmpty(questionTypeNameList)) {
            return "";
        }
        return String.join(",", questionTypeNameList);
    }

    /**
     * 验证传入的科目单元是否完整覆盖了组卷规则
     */
    private void validateExamDetailCompleteness(
            List<ExamDetailSubjectReqVO> examDetailSubjectList,
            Map<Integer, Map<Integer, ExamPaperRuleQuestionTypeRespVO>> subjectToUnitRuleMap) {

        // 构建传入的科目单元映射
        Map<Integer, Set<Integer>> inputSubjectUnitMap = examDetailSubjectList.stream()
                .collect(Collectors.toMap(
                        ExamDetailSubjectReqVO::getSubject,
                        subject -> Optional.ofNullable(subject.getExamDetailUnitList())
                                .orElse(Collections.emptyList())
                                .stream()
                                .map(ExamDetailUnitReqVO::getUnit)
                                .collect(Collectors.toSet())
                ));

        // 验证每个规则中的科目单元是否都被传入
        for (Map.Entry<Integer, Map<Integer, ExamPaperRuleQuestionTypeRespVO>> entry : subjectToUnitRuleMap.entrySet()) {
            Integer subject = entry.getKey();
            SubjectEnum subjectEnum = validateAndGetSubjectEnum(subject);

            Set<Integer> requiredUnits = entry.getValue().keySet();
            Set<Integer> inputUnits = inputSubjectUnitMap.getOrDefault(subject, Collections.emptySet());

            // 检查是否有遗漏的单元
            Set<Integer> missingUnits = new HashSet<>(requiredUnits);
            missingUnits.removeAll(inputUnits);

            if (CollUtil.isNotEmpty(missingUnits)) {
                String missingUnitNames = missingUnits.stream()
                        .map(ExamQuestionTypeUnitEnum::getDescByCode)
                        .collect(Collectors.joining(", "));
                throw exception(EXAM_MISSING_REQUIRED_UNITS, EXAM_MISSING_REQUIRED_UNITS.getMsg(), subjectEnum.getDesc(), missingUnitNames);
            }
        }
    }

    /**
     * 验证传入数据的重复性
     */
    private void validateExamDetailDuplication(List<ExamDetailSubjectReqVO> examDetailSubjectList) {
        // 检查科目重复
        validateSubjectDuplication(examDetailSubjectList);

        // 检查每个科目下的单元重复
        validateUnitDuplication(examDetailSubjectList);
    }

    /**
     * 验证科目重复
     */
    private void validateSubjectDuplication(List<ExamDetailSubjectReqVO> examDetailSubjectList) {
        List<Integer> subjects = examDetailSubjectList.stream()
                .map(ExamDetailSubjectReqVO::getSubject)
                .toList();

        Set<Integer> duplicateSubjects = findDuplicates(subjects);
        if (CollUtil.isNotEmpty(duplicateSubjects)) {
            String duplicateSubjectNames = duplicateSubjects.stream()
                    .map(this::validateAndGetSubjectEnum)
                    .map(SubjectEnum::getDesc)
                    .collect(Collectors.joining(", "));
            throw exception(EXAM_DUPLICATE_SUBJECT, EXAM_DUPLICATE_SUBJECT.getMsg(), duplicateSubjectNames);
        }
    }

    /**
     * 验证单元重复
     */
    private void validateUnitDuplication(List<ExamDetailSubjectReqVO> examDetailSubjectList) {
        for (ExamDetailSubjectReqVO subjectReqVO : examDetailSubjectList) {
            List<Integer> units = subjectReqVO.getExamDetailUnitList().stream()
                    .map(ExamDetailUnitReqVO::getUnit)
                    .toList();

            Set<Integer> duplicateUnits = findDuplicates(units);
            if (CollUtil.isNotEmpty(duplicateUnits)) {
                SubjectEnum subjectEnum = validateAndGetSubjectEnum(subjectReqVO.getSubject());
                String duplicateUnitNames = duplicateUnits.stream()
                        .map(ExamQuestionTypeUnitEnum::getDescByCode)
                        .collect(Collectors.joining(", "));
                throw exception(EXAM_DUPLICATE_UNIT, EXAM_DUPLICATE_UNIT.getMsg(), subjectEnum.getDesc(), duplicateUnitNames);
            }
        }
    }

    /**
     * 查找列表中的重复项
     */
    private <T> Set<T> findDuplicates(List<T> list) {
        Set<T> seen = new HashSet<>();
        Set<T> duplicates = new HashSet<>();

        for (T item : list) {
            if (!seen.add(item)) {
                duplicates.add(item);
            }
        }

        return duplicates;
    }

    /**
     * 验证并获取科目枚举
     */
    private SubjectEnum validateAndGetSubjectEnum(Integer subject) {
        SubjectEnum subjectEnum = SubjectEnum.getByCode(subject);
        if (subjectEnum == null) {
            throw exception(EXAM_SUBJECT_NOT_EXISTS, subject);
        }
        return subjectEnum;
    }

    /**
     * 更新模考
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateExam(ExamSaveReqVO updateReqVO) {

        ExamDO examDO = examService.getExam(updateReqVO.getId());
        if (ExamPublishStatusEnum.PUBLISHED.getCode().equals(examDO.getPublishStatus())) {
            throw exception(EXAM_PUBLISHED_CANNOT_UPDATE);
        }

        // 获取模考组卷规则
        ExamPaperRuleDO paperRule = examPaperRuleService.getPaperRule(updateReqVO.getPaperRuleId());

        // 获取并验证组卷规则详情
        Map<Integer, Map<Integer, ExamPaperRuleQuestionTypeRespVO>> subjectToUnitRuleMap =
                buildSubjectToUnitRuleMap(paperRule.getId());

        // 添加完整性和重复性验证
        validateExamDetailCompleteness(updateReqVO.getExamDetailSubjectList(), subjectToUnitRuleMap);
        validateExamDetailDuplication(updateReqVO.getExamDetailSubjectList());

        // 先构建并验证模考详情列表
        List<ExamDetailDO> examDetailList = buildExamDetailList(
                updateReqVO.getExamDetailSubjectList(),
                subjectToUnitRuleMap
        );

        Integer version = examDetailVersionService.lambdaQuery()
                .eq(ExamDetailVersionDO::getExamId, updateReqVO.getId())
                .orderByDesc(ExamDetailVersionDO::getVersion)
                .last("limit 1")
                .oneOpt()
                .map(ExamDetailVersionDO::getVersion)
                .orElse(0) + 1;

        // 构建模考基础信息
        ExamDO exam = buildExamBasicInfo(updateReqVO, paperRule);
        exam.setExamDetailVersion(version);
        exam.setUpdateTime(LocalDateTime.now());
        exam.setUpdater(String.valueOf(WebFrameworkUtils.getLoginUserId()));
        examService.updateById(exam);

        // 更新排序
        examService.updateSort(updateReqVO.getHskLevel(), examDO.getHskLevel(), examDO.getSort());


        List<Long> examDetailIds = new ArrayList<>();
        examDetailList.forEach(examDetail -> {
            examDetail.setExamId(exam.getId());
            examDetail.setVersion(version);
            examDetail.setUpdateTime(LocalDateTime.now());
            examDetail.setUpdater(String.valueOf(WebFrameworkUtils.getLoginUserId()));

            if (examDetail.getId() != null) {
                examDetailIds.add(examDetail.getId());
            }
        });

        // 逻辑删除
        examDetailService.lambdaUpdate()
                .set(ExamDetailDO::getDeleted, true)
                .set(ExamDetailDO::getUpdateTime, LocalDateTime.now())
                .set(ExamDetailDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                // 只有当保留列表不为空时，才添加notIn条件
                .notIn(CollUtil.isNotEmpty(examDetailIds), ExamDetailDO::getId,
                        examDetailIds)
                .eq(ExamDetailDO::getExamId, exam.getId())
                .update();

        // 批量保存模考详情
        examDetailService.saveOrUpdateBatch(examDetailList);

        List<ExamDetailVersionDO> examDetailVersionDOList = examDetailList.stream()
                .map(e -> {
                    ExamDetailVersionDO versionDO = BeanUtil.copyProperties(e, ExamDetailVersionDO.class, "id");
                    versionDO.setExamDetailId(e.getId());
                    return versionDO;
                })
                .toList();

        examDetailVersionService.saveBatch(examDetailVersionDOList);

        // 设置日志上下文变量
        LogRecordContext.putVariable("exam", exam);
    }
}