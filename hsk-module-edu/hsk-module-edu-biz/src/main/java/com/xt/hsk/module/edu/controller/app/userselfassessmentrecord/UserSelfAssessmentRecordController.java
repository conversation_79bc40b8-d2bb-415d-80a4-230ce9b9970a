package com.xt.hsk.module.edu.controller.app.userselfassessmentrecord;

import com.xt.hsk.module.edu.controller.app.userselfassessmentrecord.vo.UserSelfAssessmentRecordPageReqVO;
import com.xt.hsk.module.edu.controller.app.userselfassessmentrecord.vo.UserSelfAssessmentRecordRespVO;
import com.xt.hsk.module.edu.controller.app.userselfassessmentrecord.vo.UserSelfAssessmentRecordSaveReqVO;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.xt.hsk.module.edu.dal.dataobject.userselfassessmentrecord.UserSelfAssessmentRecordDO;
import com.xt.hsk.module.edu.service.userselfassessmentrecord.UserSelfAssessmentRecordManager;

@Tag(name = "App - 用户等级自测记录")
@RestController
@RequestMapping("/edu/user-self-assessment-record")
@Validated
public class UserSelfAssessmentRecordController {

    @Resource
    private UserSelfAssessmentRecordManager userSelfAssessmentRecordManager;

    @PostMapping("/start")
    @Operation(summary = "用户等级自测开始")
    public CommonResult<UserSelfAssessmentRecordRespVO> createUserSelfAssessmentRecord() {
        return success(userSelfAssessmentRecordManager.createUserSelfAssessmentRecord());
    }

    @PutMapping("/submit")
    @Operation(summary = "用户等级自测结束")
    public CommonResult<Boolean> submitUserSelfAssessmentRecord(@Valid @RequestBody UserSelfAssessmentRecordSaveReqVO updateReqVO) {
        userSelfAssessmentRecordManager.submitUserSelfAssessmentRecord(updateReqVO);
        return success(true);
    }
}