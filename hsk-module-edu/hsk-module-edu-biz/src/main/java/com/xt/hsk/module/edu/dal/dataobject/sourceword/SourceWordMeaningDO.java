package com.xt.hsk.module.edu.dal.dataobject.sourceword;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordExampleSaveReqVO;
import lombok.*;

import java.util.List;

/**
 * 词语多释义表（冗余word） DO
 *
 * <AUTHOR>
 */
@TableName("word_meanings")
@KeySequence("word_meanings_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SourceWordMeaningDO  {

    /**
     * 例句唯一ID（自增）
     */
    @TableId
    private Long meaningId;
    /**
     * 冗余主表words的id（如38544）
     */
    private Long wordId;
    /**
     * 中文单词
     */
    private String word;

    private String mean;

    private String explainCn;

    private Integer status;

    private String meanVi;

    private String kind;



}