package com.xt.hsk.module.edu.convert.exam;

import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPageRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamSaveReqVO;
import com.xt.hsk.module.edu.controller.app.exam.vo.ExamAppPageRespVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 模考题型 转换
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Mapper
public interface ExamConvert {

    ExamConvert INSTANCE = Mappers.getMapper(ExamConvert.class);

    /**
     * 创建VO类转DO类
     */
    ExamDO saveReqVoToDo(ExamSaveReqVO createReqVO);

    /**
     * DO列表转响应VO列表
     */
    List<ExamRespVO> doListToRespVoList(List<ExamDO> list);

    /**
     * DO类转分页响应VO类
     */
    ExamPageRespVO doToPageRespVo(ExamDO examDO);

    /**
     * DO列表转分页响应VO列表
     */
    List<ExamPageRespVO> toPageRespVoList(List<ExamDO> list);

    /**
     * DO类转app分页响应VO类
     */
    @Mapping(source = "id", target = "examId")
    ExamAppPageRespVO doToAppPageRespVo(ExamDO examDO);

    /**
     * DO列表转app分页响应VO列表
     */
    List<ExamAppPageRespVO> toAppPageRespVoList(List<ExamDO> list);
}
