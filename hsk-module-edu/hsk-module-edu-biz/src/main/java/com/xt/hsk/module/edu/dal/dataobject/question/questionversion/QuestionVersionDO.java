package com.xt.hsk.module.edu.dal.dataobject.question.questionversion;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 题目表版本库 DO
 *
 * <AUTHOR>
 */
@TableName("edu_question_version")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionVersionDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 题目ID
     */
    private Long questionId;
    /**
     * HSK等级
     */
    private Integer hskLevel;
    /**
     * 教材ID
     */
    private Long textbookId;
    /**
     * 章节ID
     */
    private Long chapterId;
    /**
     * 单元ID
     */
    private Long unitId;
    /**
     * 题型ID
     */
    private Long typeId;
    /**
     * 科目
     */
    private Integer subject;
    /**
     * 材料音频
     */
    private String materialAudio;
    /**
     * 材料音频
     */
    private String materialAudioContent;
    /**
     * 材料图片
     */
    private String materialImage;
    /**
     * 材料文字
     */
    private String materialContent;
    /**
     * 题目选项 json
     */
    private String options;
    /**
     * 小题数量
     */
    private Integer questionNum;
    /**
     * 状态 0启用 1禁用
     */
    private Integer status;
    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;
    /**
     * 版本号
     */
    private Integer version;
    /**
     * 题目编码 #100000开始
     */
    private String questionCode;
    /**
     * 正确作答次数
     */
    private Integer correctAnswerCount;
    /**
     * 总作答次数
     */
    private Integer totalAnswerCount;
}