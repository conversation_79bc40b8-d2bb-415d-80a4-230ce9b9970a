package com.xt.hsk.module.edu.dal.mysql.elitecourse;

import cn.hutool.core.collection.CollUtil;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCoursePageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 精品课 Mapper
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface EliteCourseMapper extends BaseMapperX<EliteCourseDO> {

    default PageResult<EliteCourseDO> selectPage(EliteCoursePageReqVO reqVO) {
        LambdaQueryWrapperX<EliteCourseDO> query = new LambdaQueryWrapperX<>();

        if (CollUtil.isNotEmpty(reqVO.getIds())) {
            query.in(EliteCourseDO::getId, reqVO.getIds());
        } else {
            // 没有选中ID时，使用其他查询条件
            query.likeIfPresent(EliteCourseDO::getCourseNameCn, reqVO.getCourseNameCn())
                    .eqIfPresent(EliteCourseDO::getHskLevel, reqVO.getHskLevel())
                    .eqIfPresent(EliteCourseDO::getPrimaryCategoryId, reqVO.getPrimaryCategoryId())
                    .eqIfPresent(EliteCourseDO::getListingStatus, reqVO.getListingStatus())
                    .eqIfPresent(EliteCourseDO::getIsShow, reqVO.getIsShow());
        }

        return selectPage(reqVO, query.orderByAsc(EliteCourseDO::getHskLevel, EliteCourseDO::getSort));
    }

}