package com.xt.hsk.module.edu.dal.mysql.textbook;

import java.util.*;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.module.edu.dal.dataobject.textbook.TextbookDO;
import org.apache.ibatis.annotations.Mapper;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.*;

/**
 * 教材 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TextbookMapper extends BaseMapperX<TextbookDO> {

    default PageResult<TextbookDO> selectPage(TextbookPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TextbookDO>()
                .likeIfPresent(TextbookDO::getNameCn, reqVO.getNameCn())
                .eqIfPresent(TextbookDO::getIsShow, reqVO.getIsShow())
                .eqIfPresent(TextbookDO::getType, reqVO.getType())
                .eqIfPresent(TextbookDO::getHskLevel, reqVO.getHskLevel())
                .orderByAsc(TextbookDO::getHskLevel)
                .orderByAsc(TextbookDO::getSort)
                .orderByDesc(TextbookDO::getUpdateTime));
    }

}