package com.xt.hsk.module.edu.dal.dataobject.elitecourse;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 精品课章节 DO
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@TableName("edu_elite_chapter")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EliteChapterDO extends BaseDO {

    /**
     * 章节ID
     */
    @TableId
    private Long id;
    /**
     * 章节名称-中文
     */
    private String chapterNameCn;
    /**
     * 章节名称-英文
     */
    private String chapterNameEn;
    /**
     * 章节名称-其他
     */
    private String chapterNameOt;
    /**
     * 课程id
     */
    private Long courseId;
    /**
     * 排序序号
     */
    private Integer sort;

}