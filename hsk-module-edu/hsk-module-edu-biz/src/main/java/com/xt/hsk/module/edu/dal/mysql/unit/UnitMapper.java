package com.xt.hsk.module.edu.dal.mysql.unit;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.UnitPageReqVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.UnitTextBookVO;
import com.xt.hsk.module.edu.dal.dataobject.unit.UnitDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 单元 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UnitMapper extends BaseMapperX<UnitDO> {

    default PageResult<UnitDO> selectPage(UnitPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<UnitDO>()
                .eqIfPresent(UnitDO::getChapterId, reqVO.getChapterId())
                .eqIfPresent(UnitDO::getTextbookId, reqVO.getTextbookId())
                .eqIfPresent(UnitDO::getUnitNameCn, reqVO.getUnitNameCn())
                .eqIfPresent(UnitDO::getUnitNameEn, reqVO.getUnitNameEn())
                .eqIfPresent(UnitDO::getUnitNameOt, reqVO.getUnitNameOt())
                .eqIfPresent(UnitDO::getSort, reqVO.getSort())
                .betweenIfPresent(UnitDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(UnitDO::getId));
    }

    List<UnitTextBookVO> queryUnitUnitData(
            @Param("unitNameCn") String unitNameCn,
            @Param("chapterNameCn") String chapterNameCn,
            @Param("textBookNameCn") String textBookNameCn,
            @Param("subject") Integer subject
    );

}