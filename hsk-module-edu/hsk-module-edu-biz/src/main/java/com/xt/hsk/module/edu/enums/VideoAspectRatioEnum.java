package com.xt.hsk.module.edu.enums;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 视频尺寸比例枚举
 *
 * <AUTHOR>
 * @since 2025/06/12
 */
@Getter
@AllArgsConstructor
public enum VideoAspectRatioEnum implements BasicEnum<Integer> {

    /**
     * 9:16 比例（竖屏）
     */
    RATIO_9_16(1, "9:16"),

    /**
     * 16:9 比例（横屏）
     */
    RATIO_16_9(2, "16:9");

    /**
     * 类型
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

} 