package com.xt.hsk.module.edu.manager.elitecourse;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_COURSE_NOT_EXISTS;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_COURSE_NOT_ON_SHELF;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.i18n.LanguageUtils;
import com.xt.hsk.framework.common.util.number.NumberUtils;
import com.xt.hsk.framework.common.util.servlet.ServletUtils;
import com.xt.hsk.module.edu.controller.app.elitecourse.vo.*;
import com.xt.hsk.module.edu.convert.elitecourse.EliteCourseConvert;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.*;
import com.xt.hsk.module.edu.dal.dataobject.teacher.TeacherDO;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseListingStatusEnum;
import com.xt.hsk.module.edu.manager.elitecourse.dto.CourseAndRegisterDto;
import com.xt.hsk.module.edu.manager.elitecourse.dto.CourseHourCountDto;
import com.xt.hsk.module.edu.service.course.RecommendedCourseService;
import com.xt.hsk.module.edu.service.elitecourse.*;
import com.xt.hsk.module.edu.service.teacher.TeacherService;
import jakarta.annotation.Resource;
import lombok.Builder;
import lombok.Data;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 精品课 APP端 Manager，负责APP端业务逻辑的编排
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Component
public class EliteCourseAppManager {

    @Resource
    private EliteCourseService eliteCourseService;

    @Resource
    private EliteClassHourService eliteClassHourService;

    @Resource
    private EliteChapterService eliteChapterService;

    @Resource
    private EliteCourseTeacherService eliteCourseTeacherService;

    @Resource
    private TeacherService teacherService;

    @Resource
    private RecommendedCourseService recommendedCourseService;

    @Resource
    private EliteCourseVideoService eliteCourseVideoService;
    @Resource
    private EliteCourseRegisterService eliteCourseRegisterService;
    @Autowired
    private EliteCourseStudyRecordService eliteCourseStudyRecordService;


    /**
     * 获取APP端精品课分页信息，包含课时数和报名人数
     *
     * @param pageVO 分页请求
     * @return 精品课分页信息
     */
    public PageResult<EliteCourseAppRespVO> getEliteCoursePage(EliteCourseAppPageReqVO pageVO) {
        // 获取课程分页
        PageResult<EliteCourseDO> pageResult = eliteCourseService.getEliteCoursePage(pageVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return new PageResult<>(Collections.emptyList(), pageResult.getTotal());
        }

        List<EliteCourseDO> courses = pageResult.getList();
        // 获取所有课程ID
        List<Long> courseIds = courses.stream()
            .map(EliteCourseDO::getId)
            .toList();

        // 查询课时数据并按课程ID分组
        Map<Long, Long> courseClassHourCountMap = eliteClassHourService.lambdaQuery()
            .in(EliteClassHourDO::getCourseId, courseIds)
            .list()
            .stream()
            .collect(Collectors.groupingBy(
                EliteClassHourDO::getCourseId,
                Collectors.counting()
            ));

        // 转换为响应对象
        List<EliteCourseAppRespVO> list = courses.stream()
            .map(course -> {
                EliteCourseAppRespVO respVO = EliteCourseConvert.INSTANCE.doToAppRespVO(course);
                // 设置课时数
                respVO.setClassHourCount(
                    courseClassHourCountMap.getOrDefault(course.getId(), 0L).intValue());
                // 设置报名人数（使用销售基数）+ 实际销售人数
                respVO.setEnrollmentCount(
                    NumberUtils.add(course.getSalesBase() + course.getEnrollmentCount()));
                return respVO;
            })
            .toList();

        // 返回结果
        return new PageResult<>(list, pageResult.getTotal());
    }

    /**
     * 获取精品课详情
     *
     * @param courseId 课程ID
     * @return 精品课详情
     */
    public EliteCourseDetailRespVO getEliteCourseDetail(Long courseId) {

        // 课程基础信息
        EliteCourseDetailRespVO baseInfo = getEliteCourseBaseInfo(courseId);

        // 课程大纲
        List<EliteCourseDetailRespVO.ChapterVO> chapters = getCourseOutline(courseId);
        baseInfo.setChapters(chapters);

        // 课程讲师信息
        List<EliteCourseDetailRespVO.TeacherVO> teachers = getCourseTeachers(courseId);
        baseInfo.setTeachers(teachers);

        // 课程推荐列表
        List<EliteCourseDetailRespVO.RecommendedCourseVO> recommendedCourses = getRecommendedCourses(
            baseInfo.getHskLevel(), baseInfo.getId());
        baseInfo.setRecommendedCourses(recommendedCourses);

        return baseInfo;
    }

    /**
     * 获取推荐课程列表
     *
     * @param hskLevel HSK等级
     * @return 推荐课程信息列表
     */
    private List<EliteCourseDetailRespVO.RecommendedCourseVO> getRecommendedCourses(
        Integer hskLevel, Long notCourseId) {
        // 使用连表查询直接获取推荐课程列表
        List<EliteCourseDetailRespVO.RecommendedCourseVO> courseVOList = recommendedCourseService.selectRecommendedCoursesByHskLevel(
            hskLevel, notCourseId);
        if (CollUtil.isEmpty(courseVOList)) {
            return Collections.emptyList();
        }

        // 获取所有课程ID
        List<Long> courseIds = courseVOList.stream()
            .map(EliteCourseDetailRespVO.RecommendedCourseVO::getId)
            .toList();

        // 查询课时数据并按课程ID分组
        Map<Long, Long> courseClassHourCountMap = eliteClassHourService.lambdaQuery()
            .in(EliteClassHourDO::getCourseId, courseIds)
            .list()
            .stream()
            .collect(Collectors.groupingBy(
                EliteClassHourDO::getCourseId,
                Collectors.counting()
            ));

        // 设置课时数
        courseVOList.forEach(vo -> {
            vo.setCourseName(LanguageUtils.getLocalizedValue(
                vo.getCourseNameCn(),
                vo.getCourseNameEn(),
                vo.getCourseNameOt()));
            vo.setSellingPrice(LanguageUtils.getLocalizedValue(
                vo.getSellingPriceCn(),
                vo.getSellingPriceEn(),
                vo.getSellingPriceOt()));
            // 设置课时数
            vo.setClassHourCount(courseClassHourCountMap.getOrDefault(vo.getId(), 0L).intValue());
            // 设置报名人数（使用销售基数 + 实际销售人数）
            vo.setEnrollmentCount(NumberUtils.add(vo.getEnrollmentCount(), vo.getSalesBase()));
        });

        return courseVOList;
    }

    /**
     * 获取课程讲师信息
     *
     * @param courseId 课程ID
     * @return 讲师信息列表
     */
    private List<EliteCourseDetailRespVO.TeacherVO> getCourseTeachers(Long courseId) {
        // 查询课程讲师关联信息
        List<EliteCourseTeacherDO> courseTeachers = eliteCourseTeacherService.lambdaQuery()
            .eq(EliteCourseTeacherDO::getEliteCourseId, courseId)
            .list();

        if (CollUtil.isEmpty(courseTeachers)) {
            return Collections.emptyList();
        }

        // 获取讲师ID列表
        List<Long> teacherIds = courseTeachers.stream()
            .map(EliteCourseTeacherDO::getTeacherId)
            .toList();

        // 查询讲师信息
        List<TeacherDO> teachers = teacherService.lambdaQuery()
            .in(TeacherDO::getId, teacherIds)
            .eq(TeacherDO::getDisplayStatus, true)
            .orderByAsc(TeacherDO::getSort)
            .list();

        // 转换为VO对象
        return EliteCourseConvert.INSTANCE.teacherDoListToTeacherVOList(teachers);
    }

    /**
     * 获取课程大纲信息
     *
     * @param courseId 课程ID
     * @return 课程大纲信息列表
     */
    private List<EliteCourseDetailRespVO.ChapterVO> getCourseOutline(Long courseId) {
        // 查询课程相关的所有章节
        List<EliteChapterDO> chapters = eliteChapterService.lambdaQuery()
            .eq(EliteChapterDO::getCourseId, courseId)
            .orderByAsc(EliteChapterDO::getSort)
            .list();

        if (CollUtil.isEmpty(chapters)) {
            return Collections.emptyList();
        }

        // 获取所有章节ID
        List<Long> chapterIds = chapters.stream()
            .map(EliteChapterDO::getId)
            .toList();

        // 查询所有课时并按章节ID分组
        Map<Long, List<EliteClassHourDO>> chapterClassHoursMap = eliteClassHourService.lambdaQuery()
            .eq(EliteClassHourDO::getCourseId, courseId)
            .in(EliteClassHourDO::getChapterId, chapterIds)
            .orderByAsc(EliteClassHourDO::getSort)
            .list()
            .stream()
            .collect(Collectors.groupingBy(EliteClassHourDO::getChapterId));

        // 收集视频ID
        List<Long> videoIds = chapterClassHoursMap.values().stream()
            .flatMap(List::stream)
            .map(EliteClassHourDO::getVideoId)
            .toList();

        // 查询视频时长信息
        Map<Long, Integer> videoIntegerMap;
        if (CollUtil.isEmpty(videoIds)) {
            videoIntegerMap = Collections.emptyMap();
        } else {
            videoIntegerMap = eliteCourseVideoService.lambdaQuery()
                .in(EliteCourseVideoDO::getVideoId, videoIds)
                .select(EliteCourseVideoDO::getVideoId, EliteCourseVideoDO::getLength)
                .list()
                .stream()
                .collect(Collectors.toMap(
                    EliteCourseVideoDO::getVideoId,
                    item -> item.getLength() == null ? 0 : item.getLength()
                ));
        }

        // 构建课程大纲数据
        return chapters.stream().map(chapter -> {
            EliteCourseDetailRespVO.ChapterVO chapterVO = new EliteCourseDetailRespVO.ChapterVO();
            chapterVO.setId(chapter.getId());
            chapterVO.setChapterName(LanguageUtils.getLocalizedValue(
                chapter.getChapterNameCn(),
                chapter.getChapterNameEn(),
                chapter.getChapterNameOt()));
            chapterVO.setSort(chapter.getSort());

            // 获取章节下的课时列表
            List<EliteClassHourDO> classHours = chapterClassHoursMap.getOrDefault(chapter.getId(),
                Collections.emptyList());

            List<EliteCourseDetailRespVO.ClassHourVO> classHourVOList = classHours.stream()
                .map(classHour -> {
                    EliteCourseDetailRespVO.ClassHourVO classHourVO = new EliteCourseDetailRespVO.ClassHourVO();
                    classHourVO.setId(classHour.getId());
                    classHourVO.setClassHourName(LanguageUtils.getLocalizedValue(
                        classHour.getClassHourNameCn(),
                        classHour.getClassHourNameEn(),
                        classHour.getClassHourNameOt()));
                    classHourVO.setClassHourType(classHour.getClassHourType());
                    classHourVO.setSort(classHour.getSort());
                    classHourVO.setDuration(videoIntegerMap.get(classHour.getVideoId()));
                    return classHourVO;
                })
                .sorted(Comparator.comparing(EliteCourseDetailRespVO.ClassHourVO::getSort))
                .toList();

            chapterVO.setClassHours(classHourVOList);
            return chapterVO;
        }).toList();
    }


    /**
     * 课程基础信息
     *
     * @param courseId 课程ID
     * @return 精品课详情
     */
    private EliteCourseDetailRespVO getEliteCourseBaseInfo(Long courseId) {
        // 获取课程信息
        EliteCourseDO course = eliteCourseService.getEliteCourse(courseId);
        // 校验课程存在
        if (course == null) {
            throw exception(ELITE_COURSE_NOT_EXISTS);
        }

        if (!EliteCourseListingStatusEnum.LISTED.getCode().equals(course.getListingStatus())) {
            throw exception(ELITE_COURSE_NOT_ON_SHELF);
        }

        // 转换为响应对象
        EliteCourseDetailRespVO respVO = EliteCourseConvert.INSTANCE.doToDetailRespVO(course);

        // 设置课时数
        Long classHourCount = eliteClassHourService.lambdaQuery()
            .eq(EliteClassHourDO::getCourseId, courseId)
            .count();
        respVO.setClassHourCount(classHourCount.intValue());

        // 设置报名人数（使用销售基数）+ 实际销售人数
        int enrollmentCount = course.getSalesBase() + course.getEnrollmentCount();
        respVO.setEnrollmentCount(enrollmentCount);

        return respVO;
    }

    public PageResult<UserCenterCourseVo> getMyCourse(EliteCourseAppPageReqVO pageVO) {
        pageVO.setUserId(StpUtil.getLoginIdAsLong());
        // 获取用户已购买的课程
        PageResult<CourseAndRegisterDto> courseRegister = eliteCourseRegisterService.getUserCourseRegister(pageVO);
        if (courseRegister.getList() == null || courseRegister.getList().isEmpty()) {
            return PageResult.empty();
        }
        // 查询课时数
        List<Long> courseIds = courseRegister.getList().stream().map(CourseAndRegisterDto::getCourseId).toList();
        List<CourseHourCountDto> eliteClassHourCount = eliteClassHourService.getEliteClassHourCount(courseIds);
        Map<Long, Integer> courseHourCountMap = eliteClassHourCount.stream().collect(Collectors.toMap(CourseHourCountDto::getCourseId, CourseHourCountDto::getClassHourCount));
//        eliteClassHourService.
        // 构建返回参数
        List<UserCenterCourseVo> list = new ArrayList<>();
        for (CourseAndRegisterDto dto : courseRegister.getList()) {
            UserCenterCourseVo vo = new UserCenterCourseVo();
            vo.setCourseId(dto.getCourseId());
            vo.setCourseName(LanguageUtils.getLocalizedValue(dto.getCourseNameCn(), dto.getCourseNameEn(), dto.getCourseNameOt()));
            vo.setCoverUrlSmall(dto.getCoverUrlSmall());
            vo.setLearningValidityPeriod(dto.getLearningValidityPeriod());
            vo.setEndTime(dto.getEndTime());
            vo.setClassHourCount(courseHourCountMap.getOrDefault(dto.getCourseId(), 0));
            vo.setCourseRegisterId(dto.getId());
            list.add(vo);
        }

        return new PageResult<>(list, courseRegister.getTotal());
    }

    public List<EliteCourseDetailRespVO.ChapterVO> getCourseChapterInfo(Long courseId) {
        // 1. 获取基础数据
        CourseChapterData courseData = loadCourseChapterData(courseId);
        if (courseData.isEmpty()) {
            return Collections.emptyList();
        }

        // 2. 构建映射关系
        CourseDataMaps dataMaps = buildDataMaps(courseData);

        // 3. 组装返回结果
        return buildChapterVOList(courseData.getChapters(), dataMaps);
    }

    /**
     * 加载课程章节相关数据
     */
    private CourseChapterData loadCourseChapterData(Long courseId) {
        // 查询章节信息
        List<EliteChapterDO> chapters = eliteChapterService.lambdaQuery()
                .eq(EliteChapterDO::getCourseId, courseId)
                .orderByAsc(EliteChapterDO::getSort)
                .list();

        if (CollUtil.isEmpty(chapters)) {
            return CourseChapterData.empty();
        }

        List<Long> chapterIds = chapters.stream()
                .map(EliteChapterDO::getId)
                .toList();

        // 查询课时信息
        List<EliteClassHourDO> classHours = eliteClassHourService.lambdaQuery()
                .in(EliteClassHourDO::getChapterId, chapterIds)
                .orderByAsc(EliteClassHourDO::getSort)
                .list();

        if (CollUtil.isEmpty(classHours)) {
            return CourseChapterData.builder()
                    .chapters(chapters)
                    .classHours(Collections.emptyList())
                    .studyRecords(Collections.emptyList())
                    .videos(Collections.emptyList())
                    .build();
        }

        // 获取课时ID和视频ID列表
        List<Long> hourIds = classHours.stream()
                .map(EliteClassHourDO::getId)
                .toList();
        List<Long> videoIds = classHours.stream()
                .map(EliteClassHourDO::getVideoId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        // 查询学习记录和视频信息
        List<EliteCourseStudyRecordDO> studyRecords = loadStudyRecords(hourIds);
        List<EliteCourseVideoDO> videos = loadVideos(videoIds);

        return CourseChapterData.builder()
                .chapters(chapters)
                .classHours(classHours)
                .studyRecords(studyRecords)
                .videos(videos)
                .build();
    }

    /**
     * 加载学习记录
     */
    private List<EliteCourseStudyRecordDO> loadStudyRecords(List<Long> hourIds) {
        if (!StpUtil.isLogin() || CollUtil.isEmpty(hourIds)) {
            return Collections.emptyList();
        }

        return eliteCourseStudyRecordService.getUserMaxStudyRecord(hourIds, StpUtil.getLoginIdAsLong());
    }

    /**
     * 加载视频信息
     */
    private List<EliteCourseVideoDO> loadVideos(List<Long> videoIds) {
        if (CollUtil.isEmpty(videoIds)) {
            return Collections.emptyList();
        }

        return eliteCourseVideoService.lambdaQuery()
                .in(EliteCourseVideoDO::getVideoId, videoIds)
                .list();
    }

    /**
     * 构建数据映射关系
     */
    private CourseDataMaps buildDataMaps(CourseChapterData courseData) {
        // 构建课时按章节分组的映射
        Map<Long, List<EliteClassHourDO>> classHoursByChapter = courseData.getClassHours().stream()
                .collect(Collectors.groupingBy(EliteClassHourDO::getChapterId));

        // 构建视频映射
        Map<Long, EliteCourseVideoDO> videoMap = courseData.getVideos().stream()
                .collect(Collectors.toMap(EliteCourseVideoDO::getVideoId, Function.identity(), (v1, v2) -> v1));

        // 构建学习记录映射
        Map<Long, EliteCourseStudyRecordDO> studyRecordMap = courseData.getStudyRecords().stream()
                .collect(Collectors.toMap(EliteCourseStudyRecordDO::getClassHourId, Function.identity(), (r1, r2) -> r1));

        return CourseDataMaps.builder()
                .classHoursByChapter(classHoursByChapter)
                .videoMap(videoMap)
                .studyRecordMap(studyRecordMap)
                .build();
    }

    /**
     * 构建章节VO列表
     */
    private List<EliteCourseDetailRespVO.ChapterVO> buildChapterVOList(List<EliteChapterDO> chapters, CourseDataMaps dataMaps) {
        return chapters.stream()
                .map(chapter -> buildChapterVO(chapter, dataMaps))
                .toList();
    }

    /**
     * 构建单个章节VO
     */
    private EliteCourseDetailRespVO.ChapterVO buildChapterVO(EliteChapterDO chapter, CourseDataMaps dataMaps) {
        EliteCourseDetailRespVO.ChapterVO chapterVO = new EliteCourseDetailRespVO.ChapterVO();
        chapterVO.setId(chapter.getId());
        chapterVO.setChapterName(LanguageUtils.getLocalizedValue(
                chapter.getChapterNameCn(),
                chapter.getChapterNameEn(),
                chapter.getChapterNameOt()));
        chapterVO.setSort(chapter.getSort());

        // 获取章节下的课时列表
        List<EliteClassHourDO> classHoursInChapter = dataMaps.getClassHoursByChapter()
                .getOrDefault(chapter.getId(), Collections.emptyList());

        List<EliteCourseDetailRespVO.ClassHourVO> classHourVOList = classHoursInChapter.stream()
                .map(hourDO -> buildClassHourVO(hourDO, dataMaps))
                .toList();

        chapterVO.setClassHours(classHourVOList);
        return chapterVO;
    }

    /**
     * 构建课时VO
     */
    private EliteCourseDetailRespVO.ClassHourVO buildClassHourVO(EliteClassHourDO hourDO, CourseDataMaps dataMaps) {
        EliteCourseDetailRespVO.ClassHourVO classHourVO = new EliteCourseDetailRespVO.ClassHourVO();
        classHourVO.setId(hourDO.getId());
        classHourVO.setClassHourName(LanguageUtils.getLocalizedValue(
                hourDO.getClassHourNameCn(),
                hourDO.getClassHourNameEn(),
                hourDO.getClassHourNameOt()));
        classHourVO.setClassHourType(hourDO.getClassHourType());
        classHourVO.setSort(hourDO.getSort());

        // 设置视频信息
        EliteCourseVideoDO video = dataMaps.getVideoMap().get(hourDO.getVideoId());
        if (video != null) {
            classHourVO.setDuration(video.getLength());
            classHourVO.setVideoUrl(video.getVideoUrl());
//            X＜1分钟，展示“00:X”
//            1分钟≤X<60分钟，展示“A:B”，如1分钟展示“01:00”
//            60分钟≤X，展示“A:B:C”，如1小时展示“01:00:00
            // 格式化视频时长：X＜1分钟展示"00:X"，1分钟≤X<60分钟展示"A:B"，60分钟≤X展示"A:B:C"
            classHourVO.setDurationStr(formatVideoDuration(video.getLength()));
        } else {
            classHourVO.setDuration(0);
            classHourVO.setVideoUrl(null);
            classHourVO.setDurationStr("00:00");
        }

        // 设置用户学习进度
        EliteCourseStudyRecordDO studyRecord = dataMaps.getStudyRecordMap().get(hourDO.getId());
        classHourVO.setUserStudyLength(studyRecord != null ? studyRecord.getPlayLength() : 0);

        return classHourVO;
    }

    public AppEliteCourseStudyRecordRespVO getStudyRecord(EliteCourseAppPageReqVO pageVO) {
//        eliteCourseStudyRecordService.
        LambdaQueryWrapper<EliteCourseStudyRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EliteCourseStudyRecordDO::getUserId, StpUtil.getLoginIdAsLong())
                .eq(EliteCourseStudyRecordDO::getCourseRegisterId, pageVO.getCourseRegisterId())
                .eq(EliteCourseStudyRecordDO::getClassHourId, pageVO.getClassHourId())
                .orderByDesc(EliteCourseStudyRecordDO::getId)
                .last("limit 1");
        EliteCourseStudyRecordDO studyRecordDO = eliteCourseStudyRecordService.getOne(queryWrapper);
        AppEliteCourseStudyRecordRespVO studyRecord = new AppEliteCourseStudyRecordRespVO();
        // 构建新的学习记录
        studyRecord.setCourseRegisterId(pageVO.getCourseRegisterId());
        studyRecord.setClassHourId(pageVO.getClassHourId());
        studyRecord.setPlayLength(0);
        if (studyRecordDO != null) {
            studyRecord = BeanUtil.toBean(studyRecordDO, AppEliteCourseStudyRecordRespVO.class);
        }

        // 获取视频信息
        EliteClassHourDO classHourDO = eliteClassHourService.getById(pageVO.getClassHourId());
        EliteCourseVideoDO videoDO = eliteCourseVideoService.lambdaQuery().eq(EliteCourseVideoDO::getVideoId, classHourDO.getVideoId()).last("limit 1").one();
        if (videoDO != null) {
            studyRecord.setVideoId(videoDO.getVideoId());
            studyRecord.setVideoUrl(videoDO.getVideoUrl());
        }

        return studyRecord;
    }

    public Long saveStudyRecord(AppEliteCourseStudyRecordSaveReqVO reqVO) {
        reqVO.setUserId(StpUtil.getLoginIdAsLong());
        // 补充课程id 章节id
        EliteClassHourDO classHourDO = eliteClassHourService.getById(reqVO.getClassHourId());
        reqVO.setCourseId(classHourDO.getCourseId());
        reqVO.setChapterId(classHourDO.getChapterId());
        reqVO.setVideoId(classHourDO.getVideoId());

        // 补充用户ip 和 设备信息
        reqVO.setUserIp(ServletUtils.getClientIP());
//        reqVO.setDeviceInfo(ServletUtils.getUserAgent());
        reqVO.setDeviceInfo(StpUtil.getLoginDeviceType());

        EliteCourseStudyRecordDO recordDO = BeanUtil.toBean(reqVO, EliteCourseStudyRecordDO.class);
        eliteCourseStudyRecordService.save(recordDO);
        return recordDO.getId();

    }

    /**
     * 课程章节数据
     */
    @Data
    @Builder
    private static class CourseChapterData {
        private List<EliteChapterDO> chapters;
        private List<EliteClassHourDO> classHours;
        private List<EliteCourseStudyRecordDO> studyRecords;
        private List<EliteCourseVideoDO> videos;

        public boolean isEmpty() {
            return CollUtil.isEmpty(chapters);
        }

        public static CourseChapterData empty() {
            return CourseChapterData.builder()
                    .chapters(Collections.emptyList())
                    .classHours(Collections.emptyList())
                    .studyRecords(Collections.emptyList())
                    .videos(Collections.emptyList())
                    .build();
        }
    }

    /**
     * 课程数据映射关系
     */
    @Data
    @Builder
    private static class CourseDataMaps {
        private Map<Long, List<EliteClassHourDO>> classHoursByChapter;
        private Map<Long, EliteCourseVideoDO> videoMap;
        private Map<Long, EliteCourseStudyRecordDO> studyRecordMap;
    }

    /**
     * 格式化视频时长（秒）为时间字符串
     * 规则：
     * - X < 1分钟，展示"00:X"（如：00:30）
     * - 1分钟 ≤ X < 60分钟，展示"A:B"（如：01:00, 25:30）
     * - 60分钟 ≤ X，展示"A:B:C"（如：01:00:00, 01:25:30）
     *
     * @param seconds 视频时长（秒）
     * @return 格式化后的时间字符串
     */
    private String formatVideoDuration(Integer seconds) {
        if (seconds == null || seconds <= 0) {
            return "00:00";
        }

        int hours = seconds / 3600;
        int minutes = (seconds % 3600) / 60;
        int remainingSeconds = seconds % 60;

        if (hours > 0) {
            // 60分钟≤X，展示"A:B:C"，如1小时展示"01:00:00"
            return String.format("%02d:%02d:%02d", hours, minutes, remainingSeconds);
        } else if (minutes > 0) {
            // 1分钟≤X<60分钟，展示"A:B"，如1分钟展示"01:00"
            return String.format("%02d:%02d", minutes, remainingSeconds);
        } else {
            // X＜1分钟，展示"00:X"
            return String.format("00:%02d", remainingSeconds);
        }
    }
}