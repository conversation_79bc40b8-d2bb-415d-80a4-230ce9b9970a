package com.xt.hsk.module.edu.convert.course;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.course.vo.RecommendedCourseRespVO;
import com.xt.hsk.module.edu.dal.dataobject.course.RecommendedCourseDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 推荐课程 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface RecommendedCourseConvert {

    RecommendedCourseConvert INSTANCE = Mappers.getMapper(RecommendedCourseConvert.class);

    /**
     * 合并推荐课程与课程信息
     *
     * @param recommendedCourse 推荐课程
     * @param course            课程信息
     * @return 推荐课程响应VO
     */
    @Mapping(source = "recommendedCourse.id", target = "id")
    @Mapping(source = "recommendedCourse.courseId", target = "courseId")
    @Mapping(source = "recommendedCourse.sort", target = "sort")
    @Mapping(source = "recommendedCourse.createTime", target = "createTime")
    @Mapping(source = "recommendedCourse.updateTime", target = "updateTime")
    @Mapping(source = "recommendedCourse.creator", target = "creator")
    @Mapping(source = "recommendedCourse.updater", target = "updater")
    @Mapping(source = "course.courseNameCn", target = "courseNameCn")
    @Mapping(source = "course.coverUrlLarge", target = "coverUrlLarge")
    @Mapping(source = "course.sellingPriceCn", target = "sellingPriceCn")
    @Mapping(source = "course.hskLevel", target = "hskLevel")
    @Mapping(source = "course.listingStatus", target = "listingStatus")
    @Mapping(source = "course.isShow", target = "isShow")
    RecommendedCourseRespVO convert(RecommendedCourseDO recommendedCourse, EliteCourseDO course);

    /**
     * 合并推荐课程列表与课程信息
     *
     * @param pageResult 分页结果
     * @param courseMap  课程信息Map
     * @return 推荐课程响应VO分页结果
     */
    PageResult<RecommendedCourseRespVO> convertPage(PageResult<RecommendedCourseDO> pageResult);

    /**
     * 将推荐课程DO列表转换为响应VO列表
     *
     * @param list 推荐课程DO列表
     * @return 响应VO列表
     */
    List<RecommendedCourseRespVO> convertList(List<RecommendedCourseDO> list);
} 