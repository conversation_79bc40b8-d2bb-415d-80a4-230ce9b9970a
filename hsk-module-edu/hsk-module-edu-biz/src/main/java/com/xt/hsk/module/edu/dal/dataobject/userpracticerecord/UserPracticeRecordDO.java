package com.xt.hsk.module.edu.dal.dataobject.userpracticerecord;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 用户练习记录 DO
 *
 * <AUTHOR>
 */
@TableName("edu_user_practice_record")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserPracticeRecordDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * HSK等级
     */
    private Integer hskLevel;
    /**
     * 教材ID
     */
    private Long textbookId;
    /**
     * 章节ID
     */
    private Long chapterId;
    /**
     * 科目
     */
    private Integer subject;
    /**
     * 单元序号
     */
    private Integer unitSort;
    /**
     * 题型ID
     */
    private Long questionTypeId;
    /**
     * 题目总数量
     */
    private Integer questionNum;
    /**
     * 已作答数量
     */
    private Integer answerNum;
    /**
     * 已正确数量
     */
    private Integer correctNum;
    /**
     * 作答总耗时（秒）
     */
    private Integer answerTime;
    /**
     * 开始作答时间
     */
    private LocalDateTime startTime;
    /**
     * 结束作答时间
     */
    private LocalDateTime endTime;
    /**
     * 记录状态 1 进行中 2 生成报告
     */
    private Integer recordStatus;
    /**
     * 本练习记录是否为最新数据 0 否 1 是
     */
    private Boolean isNewest;
    /**
     * 本次练习的全部题目id(英文逗号拼接)
     */
    private String questionIds;
    /**
     * 互动课单元ID（当练习来源于互动课时）
     */
    private Long interactiveCourseUnitId;

    /**
     * 练习模式：1-单独练习 2-全真模考 3-30分钟模考 4-15分钟模考 5 互动课
     */
    private Integer practiceMode;

}