package com.xt.hsk.module.edu.convert.exam;

import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRulePageRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleBaseInfoRespVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamPaperRuleDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 模考组卷规则 转换
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Mapper
public interface ExamPaperRuleConvert {

    ExamPaperRuleConvert INSTANCE = Mappers.getMapper(ExamPaperRuleConvert.class);

    /**
     * 创建VO类转DO类
     */
    ExamPaperRuleDO saveReqVoToDo(ExamPaperRuleSaveReqVO createReqVO);

    /**
     * DO列表转响应VO列表
     */
    List<ExamPaperRulePageRespVO> doListToRespVoList(List<ExamPaperRuleDO> list);

    /**
     * DO类转响应VO类
     */
    ExamPaperRuleRespVO doToRespVO(ExamPaperRuleDO examPaperRule);

    /**
     * DO 转换为基本信息响应 VO
     */
    ExamPaperRuleBaseInfoRespVO toBaseInfoRespVO(ExamPaperRuleDO examPaperRuleDO);

    /**
     * DO 列表转换为基本信息响应 VO 列表
     */
    List<ExamPaperRuleBaseInfoRespVO> toBaseInfoRespVOList(List<ExamPaperRuleDO> examPaperRuleDOList);
}
