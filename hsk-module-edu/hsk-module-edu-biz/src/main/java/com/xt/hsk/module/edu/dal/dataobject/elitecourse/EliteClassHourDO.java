package com.xt.hsk.module.edu.dal.dataobject.elitecourse;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import com.xt.hsk.module.edu.enums.elitecourse.EliteClassHourTypeEnum;
import lombok.*;

/**
 * 精品课课时 DO
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@TableName("edu_elite_class_hour")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EliteClassHourDO extends BaseDO {

    /**
     * 课时ID
     */
    @TableId
    private Long id;
    /**
     * 课程id
     */
    private Long courseId;
    /**
     * 章节id
     */
    private Long chapterId;
    /**
     * 复用id
     */
    private Long reuseId;
    /**
     * 课时名称-中文
     */
    private String classHourNameCn;
    /**
     * 课时名称-英文
     */
    private String classHourNameEn;
    /**
     * 课时名称-其他
     */
    private String classHourNameOt;
    /**
     * 课时类型 1：直播课 2：录播课
     * @see EliteClassHourTypeEnum
     */
    private Integer classHourType;
    /**
     * 视频id
     */
    private Long videoId;
    /**
     * 排序序号
     */
    private Integer sort;

}