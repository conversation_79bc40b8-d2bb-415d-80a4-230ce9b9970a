package com.xt.hsk.module.edu.dal.mysql.sourceword;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.sourceword.SourceWordDO;
import com.xt.hsk.module.edu.dal.dataobject.sourceword.SourceWordMeaningDO;
import org.apache.ibatis.annotations.Mapper;


/**
 * 汉语词典基础数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SourceWordMapper extends BaseMapperX<SourceWordDO> {

    default PageResult<SourceWordDO> selectPage(WordPageReqVO reqVO) {
        MPJLambdaWrapper<SourceWordDO> sourceWordDOMPJLambdaWrapper = new MPJLambdaWrapperX<SourceWordDO>()
                .leftJoin(SourceWordMeaningDO.class, SourceWordMeaningDO::getWordId, SourceWordDO::getId)
                .eqIfPresent(SourceWordDO::getId, reqVO.getId())
                .likeIfPresent(SourceWordDO::getWord, reqVO.getWord())
                .inIfPresent(SourceWordDO::getId, reqVO.getIds())
                .distinct()
                .orderByDesc(SourceWordDO::getId);
        return selectJoinPage(reqVO, SourceWordDO.class, sourceWordDOMPJLambdaWrapper);
    }

}