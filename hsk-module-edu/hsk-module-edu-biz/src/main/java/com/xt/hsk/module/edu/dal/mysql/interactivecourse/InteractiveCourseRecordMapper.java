package com.xt.hsk.module.edu.dal.mysql.interactivecourse;

import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.UserInteractiveCourseRecordDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 互动课-用户学习记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InteractiveCourseRecordMapper extends BaseMapperX<UserInteractiveCourseRecordDO> {

    /**
     * 批量查询用户视频完成记录的最高资源版本号
     * 在数据库层面进行聚合，避免查询大量数据
     *
     * @param userId 用户ID
     * @param unitIds 单元ID列表
     * @return 视频完成记录列表
     */
    List<UserInteractiveCourseRecordDO> selectMaxResourceVersionByUnitIds(@Param("userId") Long userId, @Param("unitIds") List<Long> unitIds);

    /**
     * 统计学习过指定课程任意单元的去重用户数
     * @param courseId 课程ID
     * @param unitIds 单元ID列表
     * @return 去重用户数
     */
    @Select({
        "<script>",
        "SELECT COUNT(DISTINCT user_id) FROM edu_user_interactive_course_record ",
        "WHERE course_id = #{courseId} ",
        "AND unit_id IN ",
        "<foreach item='unitId' collection='unitIds' open='(' separator=',' close=')'>",
        "#{unitId}",
        "</foreach>",
        "</script>"
    })
    Long countDistinctUsersByCourseAndUnits(@Param("courseId") Long courseId, @Param("unitIds") List<Long> unitIds);
}
