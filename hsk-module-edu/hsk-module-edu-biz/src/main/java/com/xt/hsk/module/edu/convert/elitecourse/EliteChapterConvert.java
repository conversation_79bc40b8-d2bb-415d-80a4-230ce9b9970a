package com.xt.hsk.module.edu.convert.elitecourse;

import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteChapterRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteChapterSaveReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteChapterDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 精品课-章节 转换类
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Mapper
public interface EliteChapterConvert {

    EliteChapterConvert INSTANCE = Mappers.getMapper(EliteChapterConvert.class);

    /**
     * 创建VO类转DO类
     */
    EliteChapterDO saveReqVOToDO(EliteChapterSaveReqVO saveReqVO);

    /**
     * do类转vo类
     */
    @Mapping(source = "id", target = "chapterId")
    EliteChapterRespVO doToVo(EliteChapterDO chapterDO);

    /**
     * do列表转vo列表
     */
    List<EliteChapterRespVO> doListToCreateReqVOList(List<EliteChapterDO> list);
}