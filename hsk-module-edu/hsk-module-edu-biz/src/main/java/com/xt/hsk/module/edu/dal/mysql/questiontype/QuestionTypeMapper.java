package com.xt.hsk.module.edu.dal.mysql.questiontype;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionTypePageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiontype.QuestionTypeDO;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.ibatis.annotations.Mapper;

/**
 * 题型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionTypeMapper extends BaseMapperX<QuestionTypeDO> {

    default PageResult<QuestionTypeDO> selectPage(QuestionTypePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<QuestionTypeDO>()
                .eqIfPresent(QuestionTypeDO::getNameCn, reqVO.getNameCn())
                .eqIfPresent(QuestionTypeDO::getNameEn, reqVO.getNameEn())
                .eqIfPresent(QuestionTypeDO::getNameOt, reqVO.getNameOt())
                .eqIfPresent(QuestionTypeDO::getSubject, reqVO.getSubject())
                .eqIfPresent(QuestionTypeDO::getHskLevel, reqVO.getHskLevel())
                .betweenIfPresent(QuestionTypeDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(QuestionTypeDO::getId));
    }

    /**
     * 根据题型ID列表查询题型名称映射
     *
     * @param questionTypeIds 题型ID列表
     * @return 题型ID到题型名称的映射
     */
    default Map<Long, String> selectNamesByIds(Collection<Long> questionTypeIds) {
        if (questionTypeIds == null || questionTypeIds.isEmpty()) {
            return new HashMap<>();
        }

        // 查询题型信息
        List<QuestionTypeDO> questionTypes = selectList(
                new LambdaQueryWrapperX<QuestionTypeDO>()
                        .in(QuestionTypeDO::getId, questionTypeIds)
                        .select(QuestionTypeDO::getId, QuestionTypeDO::getNameCn));

        // 转换为Map
        return questionTypes.stream()
                .collect(Collectors.toMap(
                        QuestionTypeDO::getId,
                        QuestionTypeDO::getNameCn,
                        (v1, v2) -> v1
                ));
    }
}