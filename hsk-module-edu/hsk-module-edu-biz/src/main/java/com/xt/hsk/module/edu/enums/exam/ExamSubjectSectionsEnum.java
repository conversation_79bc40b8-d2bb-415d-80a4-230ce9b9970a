package com.xt.hsk.module.edu.enums.exam;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 模考科目部分枚举
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Getter
@AllArgsConstructor
public enum ExamSubjectSectionsEnum implements BasicEnum<Integer>, VO, ArrayValuable<Integer> {
    /**
     * 模考科目部分枚举
     */
    FULL(0, "完整模考"),
    LISTENING(1, "听力模考"),
    READING(2, "阅读模考"),
    WRITING(4, "书写模考"),
    ;

    private static final Integer[] ARRAYS = Arrays.stream(values()).map(ExamSubjectSectionsEnum::getCode).toArray(Integer[]::new);
    private final Integer code;
    private final String desc;

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

    /**
     * 根据模考科目部分码获取对应的描述信息
     *
     * @param code 模考科目部分码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (ExamSubjectSectionsEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据模考科目部分码获取对应的枚举实例
     *
     * @param code 模考科目部分码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static ExamSubjectSectionsEnum getByCode(Integer code) {
        for (ExamSubjectSectionsEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
} 