package com.xt.hsk.module.edu.dal.mysql.elitecourse;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseVideoPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseVideoDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 精品课-视频 Mapper
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Mapper
public interface EliteCourseVideoMapper extends BaseMapperX<EliteCourseVideoDO> {

    default PageResult<EliteCourseVideoDO> selectPage(EliteCourseVideoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<EliteCourseVideoDO>()
                .eqIfPresent(EliteCourseVideoDO::getVideoId, reqVO.getVideoId())
                .eqIfPresent(EliteCourseVideoDO::getRoomId, reqVO.getRoomId())
                .eqIfPresent(EliteCourseVideoDO::getNameCn, reqVO.getNameCn())
                .eqIfPresent(EliteCourseVideoDO::getNameEn, reqVO.getNameEn())
                .eqIfPresent(EliteCourseVideoDO::getNameOt, reqVO.getNameOt())
                .eqIfPresent(EliteCourseVideoDO::getVideoUrl, reqVO.getVideoUrl())
                .eqIfPresent(EliteCourseVideoDO::getPrefaceUrl, reqVO.getPrefaceUrl())
                .betweenIfPresent(EliteCourseVideoDO::getVideoCreateTime, reqVO.getVideoCreateTime())
                .eqIfPresent(EliteCourseVideoDO::getTotalSize, reqVO.getTotalSize())
                .eqIfPresent(EliteCourseVideoDO::getLength, reqVO.getLength())
                .eqIfPresent(EliteCourseVideoDO::getType, reqVO.getType())
                .betweenIfPresent(EliteCourseVideoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(EliteCourseVideoDO::getId));
    }

}