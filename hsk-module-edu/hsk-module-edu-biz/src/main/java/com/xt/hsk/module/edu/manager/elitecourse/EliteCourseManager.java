package com.xt.hsk.module.edu.manager.elitecourse;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_COURSE_CUSTOM_CLASS_HOUR_REQUIRED;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_COURSE_DEADLINE_REQUIRED;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_COURSE_EFFECTIVE_DAYS_REQUIRED;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_COURSE_HAS_CHAPTERS;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_COURSE_LISTING_TIME_REQUIRED;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_COURSE_NOT_EXISTS;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_COURSE_ORIGINAL_PRICE_EN_REQUIRED;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_COURSE_ORIGINAL_PRICE_OT_REQUIRED;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_COURSE_SELLING_PRICE_CN_REQUIRED;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_COURSE_TEACHER_EXCEED_LIMIT;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_COURSE_TEACHER_REQUIRED;
import static com.xt.hsk.module.edu.enums.elitecourse.ClassHourNumberStatusEnum.CUSTOM_HOURS;
import static com.xt.hsk.module.edu.enums.elitecourse.EliteCourseListingMethodEnum.IMMEDIATE;
import static com.xt.hsk.module.edu.enums.elitecourse.EliteCourseListingMethodEnum.SCHEDULED;
import static com.xt.hsk.module.edu.enums.elitecourse.LearningValidityPeriodEnum.BY_DAYS;
import static com.xt.hsk.module.edu.enums.elitecourse.LearningValidityPeriodEnum.BY_DEADLINE;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.enums.IsEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.web.core.util.WebFrameworkUtils;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteClassHourRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseDirectoryRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseOverviewRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCoursePageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteDeleteCheckRespVO;
import com.xt.hsk.module.edu.controller.admin.teacher.vo.TeacherBasicInfoRespVO;
import com.xt.hsk.module.edu.convert.elitecourse.EliteClassHourConvert;
import com.xt.hsk.module.edu.convert.elitecourse.EliteCourseConvert;
import com.xt.hsk.module.edu.dal.dataobject.course.RecommendedCourseDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteChapterDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteClassHourDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseCategoryDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseRegisterDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseTeacherDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseVideoDO;
import com.xt.hsk.module.edu.enums.elitecourse.EliteClassHourTypeEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseLearningStatusEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseListingStatusEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseSaleTypeEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseTypeEnum;
import com.xt.hsk.module.edu.enums.elitecourse.LearningValidityPeriodEnum;
import com.xt.hsk.module.edu.service.course.RecommendedCourseService;
import com.xt.hsk.module.edu.service.elitecourse.EliteChapterService;
import com.xt.hsk.module.edu.service.elitecourse.EliteClassHourService;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseCategoryService;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseRegisterService;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseService;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseTeacherService;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseVideoService;
import com.xt.hsk.module.edu.service.teacher.TeacherService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 精品课 管理端 Manager，负责管理端业务逻辑的编排
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Component
@Service
public class EliteCourseManager {

    @Resource
    private EliteCourseService eliteCourseService;

    @Resource
    private EliteCourseCategoryService eliteCourseCategoryService;

    @Resource
    private EliteChapterService eliteChapterService;

    @Resource
    private EliteClassHourService eliteClassHourService;

    @Resource
    private EliteCourseRegisterService eliteCourseRegisterService;

    @Resource
    private EliteCourseTeacherService eliteCourseTeacherService;

    @Resource
    private TeacherService teacherService;

    @Resource
    private EliteCourseVideoService eliteCourseVideoService;

    @Resource
    private RecommendedCourseService recommendedCourseService;


    /**
     * 创建精品课
     *
     * @param createReqVO 创建精品课请求参数
     * @return 精品课ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createEliteCourse(EliteCourseSaveReqVO createReqVO) {
        // 参数校验
        validateEliteCourseParams(createReqVO);

        // 插入
        EliteCourseDO eliteCourse = EliteCourseConvert.INSTANCE.saveReqVOToDO(createReqVO);

        // 设置类型为普通课
        eliteCourse.setType(EliteCourseTypeEnum.REGULAR_COURSE.getCode());
        // 排序默认为1
        eliteCourse.setSort(1);

        // 更新该等级下的所有序号
        eliteCourseService.lambdaUpdate()
                .eq(EliteCourseDO::getType, EliteCourseTypeEnum.REGULAR_COURSE.getCode())
                .eq(EliteCourseDO::getHskLevel, eliteCourse.getHskLevel())
                .setSql("sort = sort + 1")
                .set(EliteCourseDO::getUpdateTime, LocalDateTime.now())
                .set(EliteCourseDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                .update();

        eliteCourseService.save(eliteCourse);

        Long courseId = eliteCourse.getId();

        // 保存课程的讲师
        eliteCourseTeacherService.saveBatch(createReqVO.getTeacherIdList(), courseId);

        // 设置日志上下文变量
        EliteCourseCategoryDO category = eliteCourseCategoryService.getById(eliteCourse.getPrimaryCategoryId());
        LogRecordContext.putVariable("courseId", courseId);
        LogRecordContext.putVariable("course", eliteCourse);
        LogRecordContext.putVariable("categoryName", category != null ? category.getNameCn() : "未知分类");

        // 返回
        return courseId;
    }

    /**
     * 校验精品课参数
     *
     * @param reqVO 精品课请求参数
     */
    private void validateEliteCourseParams(EliteCourseSaveReqVO reqVO) {

        // 校验讲师不能为空
        if (CollUtil.isEmpty(reqVO.getTeacherIdList())) {
            throw exception(ELITE_COURSE_TEACHER_REQUIRED);
        }

        // 对讲师id去重
        List<Long> teacherIdList = reqVO.getTeacherIdList().stream()
            .distinct()
            .filter(Objects::nonNull)
            .toList();
        reqVO.setTeacherIdList(teacherIdList);

        // 校验讲师数量不能超过20
        if (reqVO.getTeacherIdList().size() > 20) {
            throw exception(ELITE_COURSE_TEACHER_EXCEED_LIMIT);
        }


        // 校验自定义课时数
        if (CUSTOM_HOURS.getCode().equals(reqVO.getClassHourNumberStatus())
                && reqVO.getCustomClassHourNumber() == null) {
            throw exception(ELITE_COURSE_CUSTOM_CLASS_HOUR_REQUIRED);
        }

        // 校验定时上架时间
        if (SCHEDULED.getCode().equals(reqVO.getListingMethod())
                && reqVO.getListingTime() == null) {
            throw exception(ELITE_COURSE_LISTING_TIME_REQUIRED);
        }

        // 校验按截止日期的有效期
        if (BY_DEADLINE.getCode().equals(reqVO.getLearningValidityPeriod())) {

            // 如果截止时间为空
            if (reqVO.getDeadline() == null) {
                throw exception(ELITE_COURSE_DEADLINE_REQUIRED);
            }

            // 获取当天的结束时间
            Date endOfDay = DateUtil.endOfDay(DateUtil.date(reqVO.getDeadline()));

            // 转回 LocalDateTime
            LocalDateTime localDateTime = DateUtil.toLocalDateTime(endOfDay);

            reqVO.setDeadline(localDateTime);
        }

        // 校验按天数的有效期
        if (BY_DAYS.getCode().equals(reqVO.getLearningValidityPeriod())
                && reqVO.getEffectiveDays() == null) {
            throw exception(ELITE_COURSE_EFFECTIVE_DAYS_REQUIRED);
        }

        // 立即上架设置上架状态和上架时间
        LocalDateTime now = LocalDateTime.now();
        Integer listingMethod = reqVO.getListingMethod();

        if (IMMEDIATE.getCode().equals(listingMethod)) {
            reqVO.setListingStatus(EliteCourseListingStatusEnum.LISTED.getCode());
            reqVO.setListingTime(now);
        } else if (SCHEDULED.getCode().equals(listingMethod)) {
            LocalDateTime listingTime = reqVO.getListingTime();
            if (listingTime != null && now.isAfter(listingTime)) {
                reqVO.setListingStatus(EliteCourseListingStatusEnum.LISTED.getCode());
            }
        } else {
            reqVO.setListingStatus(EliteCourseListingStatusEnum.PENDING.getCode());
        }

        // 售卖价格
        if (EliteCourseSaleTypeEnum.FREE.getCode().equals(reqVO.getSaleType())) {
            reqVO.setSellingPriceCn(BigDecimal.ZERO);
            reqVO.setSellingPriceEn(BigDecimal.ZERO);
            reqVO.setSellingPriceOt(BigDecimal.ZERO);
        } else {
            if (reqVO.getSellingPriceCn() == null) {
                throw exception(ELITE_COURSE_SELLING_PRICE_CN_REQUIRED);
            }
            if (reqVO.getSellingPriceEn() == null) {
                throw exception(ELITE_COURSE_ORIGINAL_PRICE_EN_REQUIRED);
            }
            if (reqVO.getSellingPriceOt() == null) {
                throw exception(ELITE_COURSE_ORIGINAL_PRICE_OT_REQUIRED);
            }
        }
    }

    /**
     * 更新精品课程
     *
     * @param updateReqVO 更新精品课请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateEliteCourse(EliteCourseSaveReqVO updateReqVO) {
        // 校验存在
        EliteCourseDO eliteCourse = validateEliteCourseExists(updateReqVO.getId());

        // 参数校验
        validateEliteCourseParams(updateReqVO);

        // 更新
        EliteCourseDO updateObj = EliteCourseConvert.INSTANCE.saveReqVOToDO(updateReqVO);

        // 修改等级的时候【修改后的等级和之前的等级不一样的时候】，也重新按照新增 逻辑：排在新等级中的序号为1
        if (!Objects.equals(eliteCourse.getHskLevel(), updateReqVO.getHskLevel())) {
            updateObj.setSort(1);

            eliteCourseService.lambdaUpdate()
                    .eq(EliteCourseDO::getHskLevel, updateReqVO.getHskLevel())
                    .setSql("sort = sort + 1")
                    .set(EliteCourseDO::getUpdateTime, LocalDateTime.now())
                    .set(EliteCourseDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                    .update();
        }

        updateObj.setUpdateTime(LocalDateTime.now());
        updateObj.setUpdater(String.valueOf(WebFrameworkUtils.getLoginUserId()));
        eliteCourseService.updateById(updateObj);

        // 删除课程的讲师
        eliteCourseTeacherService.lambdaUpdate()
                .set(EliteCourseTeacherDO::getDeleted, IsEnum.YES.getCode())
                .eq(EliteCourseTeacherDO::getEliteCourseId, updateReqVO.getId())
                .set(EliteCourseTeacherDO::getUpdateTime, LocalDateTime.now())
                .set(EliteCourseTeacherDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                .update();

        // 保存课程的讲师
        eliteCourseTeacherService.saveBatch(updateReqVO.getTeacherIdList(), updateReqVO.getId());

        // 设置日志上下文变量
        EliteCourseCategoryDO category = eliteCourseCategoryService.getById(updateObj.getPrimaryCategoryId());
        LogRecordContext.putVariable("course", updateObj);
        LogRecordContext.putVariable("categoryName", category != null ? category.getNameCn() : "未知分类");

    }

    /**
     * 删除精品课
     */
    public void deleteEliteCourse(Long id) {
        // 校验存在
        EliteCourseDO course = validateEliteCourseExists(id);

        // 校验课程下是否有章节
        validateCourseHasNoChapters(id);

        // 设置日志上下文变量
        EliteCourseCategoryDO category = eliteCourseCategoryService.getById(course.getPrimaryCategoryId());
        LogRecordContext.putVariable("course", course);
        LogRecordContext.putVariable("categoryName", category != null ? category.getNameCn() : "未知分类");

        // 删除
        eliteCourseService.removeById(id);
    }

    /**
     * 校验课程下是否有章节
     *
     * @param courseId 课程ID
     */
    private void validateCourseHasNoChapters(Long courseId) {
        boolean exists = eliteChapterService.lambdaQuery()
                .eq(EliteChapterDO::getCourseId, courseId)
                .exists();

        if (exists) {
            throw exception(ELITE_COURSE_HAS_CHAPTERS);
        }
    }

    /**
     * 验证精品课程是否存在
     *
     * @param id 精品课id
     * @return 精品课DO
     */
    private EliteCourseDO validateEliteCourseExists(Long id) {
        EliteCourseDO eliteCourseDO = eliteCourseService.getById(id);
        if (eliteCourseDO == null) {
            throw exception(ELITE_COURSE_NOT_EXISTS);
        }
        return eliteCourseDO;
    }

    /**
     * 根据精英课程ID获取课程详情信息，包括课程关联的教师信息。
     *
     * @param id 精英课程ID
     * @return 精英课程响应视图对象，包含课程基本信息和教师列表
     */
    public EliteCourseRespVO getEliteCourse(Long id) {
        // 根据课程ID从数据库中获取课程实体
        EliteCourseDO eliteCourse = eliteCourseService.getById(id);

        // 将课程实体转换为响应对象
        EliteCourseRespVO vo = EliteCourseConvert.INSTANCE.doToRespVO(eliteCourse);

        // 查询该课程关联的教师基本信息列表
        List<TeacherBasicInfoRespVO> teacherBasicInfoList = eliteCourseTeacherService.getBasicInfoByCourseId(id);

        // 如果教师信息列表为空，直接返回课程信息
        if (CollUtil.isEmpty(teacherBasicInfoList)) {
            return vo;
        }

        // 设置教师信息列表
        vo.setTeacherList(teacherBasicInfoList);

        // 设置教师ID列表
        List<Long> teacherIdList = teacherBasicInfoList.stream()
                .map(TeacherBasicInfoRespVO::getTeacherId)
                .toList();
        vo.setTeacherIdList(teacherIdList);

        return vo;
    }

    /**
     * 分页获取精品课
     */
    public PageResult<EliteCourseRespVO> getEliteCoursePage(@Valid EliteCoursePageReqVO pageReqVO) {
        PageResult<EliteCourseDO> page = eliteCourseService.selectPage(pageReqVO);

        List<EliteCourseDO> list = page.getList();

        List<EliteCourseRespVO> voList = EliteCourseConvert.INSTANCE.doListToRespVOList(list);

        // 设置学习有效期
        setLearningValidityPeriod(voList);

        // 设置在读人数
        setReadCount(voList);
        
        // 是否是推荐课程
        setIsRecommended(voList);

        return new PageResult<>(voList, page.getTotal());
    }

    private void setIsRecommended(List<EliteCourseRespVO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        List<RecommendedCourseDO> courseDOS = recommendedCourseService.lambdaQuery()
            .select(RecommendedCourseDO::getId, RecommendedCourseDO::getCourseId)
            .in(RecommendedCourseDO::getCourseId, voList
                .stream()
                .map(EliteCourseRespVO::getId)
                .toList())
            .list();
        voList.forEach(item -> item.setIsRecommended(courseDOS.stream()
            .anyMatch(courseDO -> courseDO.getCourseId().equals(item.getId()))));
    }

    /**
     * 设置在读人数
     *
     * @param voList 精品课VO列表
     */
    private void setReadCount(List<EliteCourseRespVO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        List<Long> courseIdList = voList.stream().map(EliteCourseRespVO::getId).toList();

        Map<Long, Integer> map = eliteCourseRegisterService.lambdaQuery()
                .in(EliteCourseRegisterDO::getCourseId, courseIdList)
                .list()
                .stream()
                .collect(Collectors.groupingBy(
                        EliteCourseRegisterDO::getCourseId,
                        Collectors.collectingAndThen(Collectors.counting(), Long::intValue)
                ));

        voList.forEach(item -> item.setReadingCount(map.getOrDefault(item.getId(), 0)));

    }

    /**
     * 设置学习有效期
     *
     * @param voList 精品课VO列表
     */
    private void setLearningValidityPeriod(List<EliteCourseRespVO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        // 遍历VO列表，设置学习有效期
        for (EliteCourseRespVO vo : voList) {
            // 长期有效
            if (LearningValidityPeriodEnum.PERMANENT.getCode().equals(vo.getLearningValidityPeriod())) {
                vo.setLearningValidityPeriodStr(LearningValidityPeriodEnum.PERMANENT.getDesc());

            } else if (LearningValidityPeriodEnum.BY_DEADLINE.getCode().equals(vo.getLearningValidityPeriod())
                    && vo.getDeadline() != null) {
                // 按截止日期
                vo.setLearningValidityPeriodStr(DateUtil.format(vo.getDeadline(), DatePattern.NORM_DATE_PATTERN));

            } else if (LearningValidityPeriodEnum.BY_DAYS.getCode().equals(vo.getLearningValidityPeriod())) {
                // 按天数
                vo.setLearningValidityPeriodStr(vo.getEffectiveDays() + "天");
            }
        }

    }

    /**
     * 获取课程的章节及课时目录
     * <p>
     * 功能步骤：
     * 1. 查询课程下的所有章节；
     * 2. 若无章节则返回空列表；
     * 3. 查询章节下的课时，并按章节分组；
     * 4. 组装章节与课时信息为目录结构返回。
     *
     * @param courseId 课程ID
     * @return 课程目录响应对象列表
     */
    public List<EliteCourseDirectoryRespVO> getCourseDirectory(Long courseId) {
        // 1. 查询课程对应的章节信息，并按排序字段升序排列
        List<EliteChapterDO> chapters = eliteChapterService.lambdaQuery()
                .eq(EliteChapterDO::getCourseId, courseId)
                .orderByAsc(EliteChapterDO::getSort)
                .list();

        // 如果没有找到任何章节，直接返回空列表
        if (CollUtil.isEmpty(chapters)) {
            return Collections.emptyList();
        }

        // 2. 查询所有章节下的课时，并按章节ID进行分组

        // 获取章节ID列表
        List<Long> chapterIds = chapters.stream()
                .map(EliteChapterDO::getId)
                .toList();

        // 查询课时
        List<EliteClassHourDO> classHourList = eliteClassHourService.lambdaQuery()
                .eq(EliteClassHourDO::getCourseId, courseId)
                .in(EliteClassHourDO::getChapterId, chapterIds)
                .orderByAsc(EliteClassHourDO::getSort)
                .list();

        // 按章节ID分组（用于后续组装章节和课时的对应关系）
        Map<Long, List<EliteClassHourDO>> chapterClassHoursMap = classHourList
                .stream()
                .collect(Collectors.groupingBy(EliteClassHourDO::getChapterId));

        // 查询录播课的视频信息
        List<Long> videoIdList = classHourList.stream()
                .filter(e -> EliteClassHourTypeEnum.RECORDED.getCode().equals(e.getClassHourType()))
                .map(EliteClassHourDO::getVideoId)
                .toList();

        // 根据视频id获取视频信息,根据视频id转成map
        Map<Long, EliteCourseVideoDO> videoMap = getVideoInfoMap(videoIdList);

        // 3. 构造返回结果，将章节信息与对应课时信息封装为响应对象列表
        return chapters.stream()
                .map(chapter -> {
                    // 将章节实体转换为响应对象
                    EliteCourseDirectoryRespVO vo = EliteCourseConvert.INSTANCE.doToDirectoryRespVO(chapter);

                    // 获取当前章节下的课时列表（如果没有课时则为默认空列表）
                    List<EliteClassHourDO> classHours = chapterClassHoursMap.getOrDefault(chapter.getId(), Collections.emptyList());

                    // 将课时实体列表转换为响应对象列表
                    List<EliteClassHourRespVO> classHourVO = EliteClassHourConvert.INSTANCE.doListToRespVOList(classHours);

                    // 设置录播课的视频信息
                    setClassHourVideoInfo(classHourVO, videoMap);

                    vo.setClassHourList(classHourVO);
                    return vo;
                })
                .toList();
    }

    /**
     * 根据视频id列表获取视频map
     *
     * @param videoIdList 视频ID列表
     * @return 视频map
     */
    private Map<Long, EliteCourseVideoDO> getVideoInfoMap(List<Long> videoIdList) {
        if (CollUtil.isEmpty(videoIdList)) {
            return Collections.emptyMap();
        }

        return eliteCourseVideoService.lambdaQuery()
                .in(EliteCourseVideoDO::getVideoId, videoIdList)
                .list()
                .stream()
                .collect(Collectors.toMap(EliteCourseVideoDO::getVideoId, Function.identity()));
    }

    /**
     * 设置课时视频信息
     *
     * @param voList   课时vo列表
     * @param videoMap 视频map
     */
    private void setClassHourVideoInfo(List<EliteClassHourRespVO> voList, Map<Long, EliteCourseVideoDO> videoMap) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        for (EliteClassHourRespVO vo : voList) {
            if (!EliteClassHourTypeEnum.RECORDED.getCode().equals(vo.getClassHourType())) {
                continue;
            }

            EliteCourseVideoDO courseVideo = videoMap.get(vo.getVideoId());

            if (courseVideo == null) {
                continue;
            }

            vo.setVideoUrl(courseVideo.getVideoUrl());

            if (courseVideo.getLength() != null) {
                vo.setDuration(DateUtil.formatBetween(courseVideo.getLength() * 1000L, BetweenFormatter.Level.SECOND));
            }
        }

    }

    /**
     * 显示/隐藏精品课
     *
     * @param id 精品课ID
     */
    public void updateStatus(Long id) {
        // 校验存在
        EliteCourseDO courseDO = validateEliteCourseExists(id);

        // 显示状态
        Integer newStatus = IsEnum.YES.getCode().equals(courseDO.getIsShow())
                ? IsEnum.NO.getCode() : IsEnum.YES.getCode();
        courseDO.setIsShow(newStatus);

        // 更新
        eliteCourseService.lambdaUpdate()
                .set(EliteCourseDO::getIsShow, newStatus)
                .eq(EliteCourseDO::getId, id)
                .set(EliteCourseDO::getUpdateTime, LocalDateTime.now())
                .set(EliteCourseDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                .update();

        // 设置日志上下文变量
        String statusText = IsEnum.YES.getCode().equals(newStatus) ? "显示" : "隐藏";
        LogRecordContext.putVariable("statusText", statusText);
        LogRecordContext.putVariable("course", courseDO);
    }

    /**
     * 上架/下架精品课
     *
     * @param id 精品课ID
     */
    public void updateListingStatus(Long id) {
        // 校验存在
        EliteCourseDO courseDO = validateEliteCourseExists(id);

        // 计算新的上架状态
        Integer currentStatus = courseDO.getListingStatus();
        Integer newStatus;

        boolean isListed = EliteCourseListingStatusEnum.LISTED.getCode().equals(currentStatus);

        if (isListed) {
            // 当前是状态：上架，改为：下架
            newStatus = EliteCourseListingStatusEnum.UNLISTED.getCode();
        } else {
            // 当前状态是：下架或待上架，改为：上架
            newStatus = EliteCourseListingStatusEnum.LISTED.getCode();
        }

        // 更新数据库
        eliteCourseService.lambdaUpdate()
                .eq(EliteCourseDO::getId, id)
                .set(EliteCourseDO::getListingStatus, newStatus)
                .set(!isListed, EliteCourseDO::getListingTime, LocalDateTime.now())
                .set(EliteCourseDO::getUpdateTime, LocalDateTime.now())
                .set(EliteCourseDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                .update();

        // 设置日志上下文变量
        String listingStatusText = EliteCourseListingStatusEnum.LISTED.getCode().equals(newStatus) ? "上架" : "下架";
        LogRecordContext.putVariable("listingStatusText", listingStatusText);
        LogRecordContext.putVariable("course", courseDO);
    }

    /**
     * 修改精品课排序
     * <p>
     * 校验课程存在性后，依据新旧序号调整同等级课程顺序，并更新当前课程的序号。
     *
     * @param id      课程id
     * @param newSort 新排序
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSort(Long id, Integer newSort) {
        // 校验课程是否存在
        EliteCourseDO originalCourse = validateEliteCourseExists(id);

        Integer oldSort = originalCourse.getSort();
        Integer hskLevel = originalCourse.getHskLevel();

        // 设置日志上下文变量
        LogRecordContext.putVariable("oldSort", oldSort);
        LogRecordContext.putVariable("course", originalCourse);

        // 如果新旧序号相同，直接返回
        if (oldSort.equals(newSort)) {
            return;
        }

        // 调整同HSK等级下其他课程的序号
        if (oldSort < newSort) {
            // 旧序号小于新序号：将(旧序号+1)到新序号范围内的课程序号-1
            eliteCourseService.lambdaUpdate()
                    .eq(EliteCourseDO::getHskLevel, hskLevel)
                    .between(EliteCourseDO::getSort, oldSort + 1, newSort)
                    .setSql("sort = sort - 1")
                    .set(EliteCourseDO::getUpdateTime, LocalDateTime.now())
                    .set(EliteCourseDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                    .update();
        } else {
            // 旧序号大于新序号：将新序号到(旧序号-1)范围内的课程序号+1
            eliteCourseService.lambdaUpdate()
                    .eq(EliteCourseDO::getHskLevel, hskLevel)
                    .between(EliteCourseDO::getSort, newSort, oldSort - 1)
                    .setSql("sort = sort + 1")
                    .set(EliteCourseDO::getUpdateTime, LocalDateTime.now())
                    .set(EliteCourseDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                    .update();
        }

        // 更新当前课程的序号
        eliteCourseService.lambdaUpdate()
                .eq(EliteCourseDO::getId, id)
                .set(EliteCourseDO::getSort, newSort)
                .set(EliteCourseDO::getUpdateTime, LocalDateTime.now())
                .set(EliteCourseDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                .update();
    }

    /**
     * 获取课程概览信息
     *
     * @param courseId 课程ID
     * @return 课程概览响应对象
     */
    public EliteCourseOverviewRespVO getCourseOverview(Long courseId) {
        EliteCourseDO eliteCourse = validateEliteCourseExists(courseId);

        EliteCourseOverviewRespVO overview = new EliteCourseOverviewRespVO();
        
        // 设置课程ID
        overview.setCourseId(courseId);
        
        // 课程封面
        overview.setCoverUrlLarge(eliteCourse.getCoverUrlLarge());
        overview.setCoverUrlSmall(eliteCourse.getCoverUrlSmall());

        // 课程名称
        overview.setCourseNameCn(eliteCourse.getCourseNameCn());
        overview.setCourseNameEn(eliteCourse.getCourseNameEn());
        overview.setCourseNameOt(eliteCourse.getCourseNameOt());

        // 总章节数
        Long chapterCount = eliteChapterService.lambdaQuery()
                .eq(EliteChapterDO::getCourseId, courseId)
                .count();
        overview.setChapterCount(chapterCount);

        // 总课时数
        Long classHourCount = eliteClassHourService.lambdaQuery()
                .eq(EliteClassHourDO::getCourseId, courseId)
                .count();
        overview.setClassHourCount(classHourCount);

        // 学习总人数
        Long totalStudentCount = eliteCourseRegisterService.lambdaQuery()
                .eq(EliteCourseRegisterDO::getCourseId, courseId)
                .count();
        overview.setTotalStudentCount(totalStudentCount);

        // 已过期人数
        EliteCourseStudyPageReqVO reqVO = new EliteCourseStudyPageReqVO();
        reqVO.setCourseId(courseId);
        reqVO.setLearningStatus(EliteCourseLearningStatusEnum.EXPIRED.getCode());
        Long expiredStudentCount = eliteCourseRegisterService.countCourseUser(reqVO);
        overview.setExpiredStudentCount(expiredStudentCount);

        // 录播课数量
        Long recordedClassCount = eliteClassHourService.lambdaQuery()
                .eq(EliteClassHourDO::getCourseId, courseId)
                .eq(EliteClassHourDO::getClassHourType, EliteClassHourTypeEnum.RECORDED.getCode())
                .count();
        overview.setRecordedClassCount(recordedClassCount);

        return overview;
    }

    /**
     * 检查精品课是否可以删除
     *
     * @param id 精品课id
     * @return 删除检查结果
     */
    public EliteDeleteCheckRespVO checkCourseCanDelete(Long id) {
        // 校验精品课是否存在
        validateEliteCourseExists(id);

        // 查询该精品课下的章节数量
        long courseCount = eliteChapterService.lambdaQuery()
                .eq(EliteChapterDO::getCourseId, id)
                .count();

        return new EliteDeleteCheckRespVO(courseCount <= 0, courseCount);
    }
}