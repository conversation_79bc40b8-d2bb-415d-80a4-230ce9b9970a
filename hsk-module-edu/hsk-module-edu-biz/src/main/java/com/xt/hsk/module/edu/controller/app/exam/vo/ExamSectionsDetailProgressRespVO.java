package com.xt.hsk.module.edu.controller.app.exam.vo;

import com.xt.hsk.module.edu.enums.exam.ExamCorrectionStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamRecordPracticeStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamSubjectSectionsEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 模考部分详情进度 resp VO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExamSectionsDetailProgressRespVO {

    /**
     * 模考id
     */
    private Long examId;

    /**
     * 模考记录id
     */
    private Long examRecordId;

    /**
     * 练习状态 1进行中 2已完成 3未开始
     *
     * @see ExamRecordPracticeStatusEnum
     */
    private Integer practiceStatus;

    /**
     * 批改状态 1进行中 2待批改 3已批改 4批改失败
     *
     * @see ExamCorrectionStatusEnum
     */
    private Integer correctionStatus;

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 得分
     */
    private Integer score;

    /**
     * 进度
     */
    private Integer progress;

    /**
     * 参与的模考科目 0-完整模考 1-听力 2-阅读 4-书写
     *
     * @see ExamSubjectSectionsEnum
     */
    private Integer examSections;


    /**
     * 创建一个空的
     *
     * @param examSections 参与的模考科目 0-完整模考 1-听力 2-阅读 4-书写
     * @return 看的对象
     */
    public static ExamSectionsDetailProgressRespVO empty(Integer examSections) {
        ExamSectionsDetailProgressRespVO vo = new ExamSectionsDetailProgressRespVO();
        vo.setPracticeStatus(ExamRecordPracticeStatusEnum.NOT_STARTED.getCode());
        vo.setExamSections(examSections);
        return vo;
    }
}