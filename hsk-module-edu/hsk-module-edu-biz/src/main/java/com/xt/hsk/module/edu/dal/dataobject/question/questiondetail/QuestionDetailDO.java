package com.xt.hsk.module.edu.dal.dataobject.question.questiondetail;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 题目详情 DO
 *
 * <AUTHOR>
 */
@TableName("edu_question_detail")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionDetailDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 题目ID
     */
    private Long questionId;
    /**
     * 题干音频
     */
    private String attachmentAudio;
    /**
     * 题干音频
     */
    private String attachmentAudioContent;
    /**
     * 题干音频时长
     */
    private Integer attachmentAudioTime;
    /**
     * 题干图片
     */
    private String attachmentImage;
    /**
     * 题干图片描述
     */
    private String attachmentImageDesc;
    /**
     * 题干内容
     */
    private String attachmentContent;
    /**
     * 参考答案
     */
    private String answer;
    /**
     * 题目选项 json
     */
    private String options;
    /**
     * 版本号
     */
    private Integer version;
    /**
     * 文字题目解析
     */
    private String explainTextCn;
    /**
     * 文字题目解析
     */
    private String explainTextEn;
    /**
     * 文字题目解析
     */
    private String explainTextOt;
    /**
     * 音频题目解析
     */
    private String explainAudio;
    /**
     * 视频题目解析
     */
    private String explainVideo;

    private Integer sort;

    private String creator;


}