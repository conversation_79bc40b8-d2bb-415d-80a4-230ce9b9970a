package com.xt.hsk.module.edu.convert.exam;

import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleQuestionTypeRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamQuestionTypeRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamQuestionTypeSaveReqVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamQuestionTypeDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 模考题型 转换
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Mapper
public interface ExamQuestionTypeConvert {

    ExamQuestionTypeConvert INSTANCE = Mappers.getMapper(ExamQuestionTypeConvert.class);

    /**
     * 创建VO类转DO类
     */
    ExamQuestionTypeDO saveReqVoToDo(ExamQuestionTypeSaveReqVO createReqVO);

    /**
     * DO列表转响应VO列表
     */
    List<ExamQuestionTypeRespVO> doListToRespVoList(List<ExamQuestionTypeDO> list);

    /**
     * 模考题型DO转组卷规则题型响应VO
     */
    @Mapping(target = "examQuestionTypeId", source = "id")
    @Mapping(target = "questionCount", constant = "0")
    @Mapping(target = "paperRuleId", ignore = true)
    @Mapping(target = "questionTypeNameList", ignore = true)
    @Mapping(target = "unitDesc", ignore = true)
    @Mapping(target = "id", ignore = true)
    ExamPaperRuleQuestionTypeRespVO doToQuestionTypeRespVO(ExamQuestionTypeDO examQuestionTypeDO);
}
