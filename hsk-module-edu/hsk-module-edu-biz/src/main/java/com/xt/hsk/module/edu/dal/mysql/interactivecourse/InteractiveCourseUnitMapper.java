package com.xt.hsk.module.edu.dal.mysql.interactivecourse;

import cn.hutool.core.collection.CollUtil;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseUnitDO;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 互动课单元 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InteractiveCourseUnitMapper extends BaseMapperX<InteractiveCourseUnitDO> {

    default PageResult<InteractiveCourseUnitDO> selectPage(InteractiveCourseUnitPageReqVO reqVO) {
        if (CollUtil.isNotEmpty(reqVO.getIds())) {
            return selectPage(reqVO, new LambdaQueryWrapperX<InteractiveCourseUnitDO>()
                .in(InteractiveCourseUnitDO::getId, reqVO.getIds())
            );
         }
        return selectPage(reqVO, new LambdaQueryWrapperX<InteractiveCourseUnitDO>()
            .eqIfPresent(InteractiveCourseUnitDO::getCourseId, reqVO.getCourseId())
            .likeIfPresent(InteractiveCourseUnitDO::getUnitNameCn, reqVO.getUnitNameCn())
            .eqIfPresent(InteractiveCourseUnitDO::getDisplayStatus, reqVO.getDisplayStatus())
            .eqIfPresent(InteractiveCourseUnitDO::getHskLevel, reqVO.getHskLevel())
            .eqIfPresent(InteractiveCourseUnitDO::getQuestionSource, reqVO.getQuestionSource())
            .orderByAsc(InteractiveCourseUnitDO::getSort));
    }

    /**
     * 批量统计课程单元数量
     *
     * @param courseIds 课程ID列表
     * @return 课程单元数量统计结果
     */
    @Select("<script>" +
        "SELECT course_id, COUNT(*) as count FROM edu_interactive_course_unit " +
        "WHERE display_status = 1 and course_id IN " +
        "<foreach collection='courseIds' item='id' open='(' separator=',' close=')'>" +
        "#{id}" +
        "</foreach>" +
        " GROUP BY course_id" +
        "</script>")
    List<Map<String, Object>> selectCountByCourseIds(
        @Param("courseIds") Collection<Long> courseIds);

} 