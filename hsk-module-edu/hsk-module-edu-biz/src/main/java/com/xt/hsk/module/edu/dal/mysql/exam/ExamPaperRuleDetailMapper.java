package com.xt.hsk.module.edu.dal.mysql.exam;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleDetailPageReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleQuestionTypeRespVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamPaperRuleDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 模考组卷规则明细 Mapper
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Mapper
public interface ExamPaperRuleDetailMapper extends BaseMapperX<ExamPaperRuleDetailDO> {

    default PageResult<ExamPaperRuleDetailDO> selectPage(ExamPaperRuleDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ExamPaperRuleDetailDO>()
                .eqIfPresent(ExamPaperRuleDetailDO::getPaperRuleId, reqVO.getPaperRuleId())
                .eqIfPresent(ExamPaperRuleDetailDO::getExamQuestionTypeId, reqVO.getExamQuestionTypeId())
                .eqIfPresent(ExamPaperRuleDetailDO::getHskLevel, reqVO.getHskLevel())
                .eqIfPresent(ExamPaperRuleDetailDO::getSubject, reqVO.getSubject())
                .eqIfPresent(ExamPaperRuleDetailDO::getUnit, reqVO.getUnit())
                .eqIfPresent(ExamPaperRuleDetailDO::getQuestionCount, reqVO.getQuestionCount())
                .betweenIfPresent(ExamPaperRuleDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ExamPaperRuleDetailDO::getId));
    }

    /**
     * 获取模考组卷规则明细的题型列表
     *
     * @param paperRuleId 组卷规则ID
     * @return 模考组卷规则明细的题型列表
     */
    List<ExamPaperRuleQuestionTypeRespVO> getPaperRuleQuestionType(@Param("paperRuleId") Long paperRuleId);
}