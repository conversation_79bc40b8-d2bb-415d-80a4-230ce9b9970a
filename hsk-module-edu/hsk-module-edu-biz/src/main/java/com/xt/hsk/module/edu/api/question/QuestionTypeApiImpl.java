package com.xt.hsk.module.edu.api.question;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.xt.hsk.module.edu.api.question.dto.QuestionTypeDTO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiontype.QuestionTypeDO;
import com.xt.hsk.module.edu.service.question.questiontype.QuestionTypeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 题型 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Service
public class QuestionTypeApiImpl implements QuestionTypeApi {

    @Resource
    private QuestionTypeService questionTypeService;

    /**
     * 通过ID列表获取
     *
     * @param idList 题型 ID 列表
     * @return 题型列表
     */
    @Override
    public List<QuestionTypeDTO> getByIds(List<Long> idList) {

        if (CollUtil.isEmpty(idList)) {
            return Collections.emptyList();
        }

        return questionTypeService.lambdaQuery()
                .select(QuestionTypeDO::getId, QuestionTypeDO::getNameCn, QuestionTypeDO::getNameEn, QuestionTypeDO::getNameOt)
                .in(QuestionTypeDO::getId, idList)
                .list()
                .stream()
                .map(questionTypeDO -> BeanUtil.copyProperties(questionTypeDO, QuestionTypeDTO.class))
                .toList();
    }
}
