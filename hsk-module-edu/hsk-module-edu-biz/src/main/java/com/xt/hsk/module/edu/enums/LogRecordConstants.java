package com.xt.hsk.module.edu.enums;

/**
 * 教育模板日志记录常量
 *
 * <AUTHOR>
 * @since 2025/05/24
 */
public interface LogRecordConstants {

    // ====================互动课====================

    String INTERACTIVE_COURSE_SAVE_TYPE = "创建互动课";
    String INTERACTIVE_COURSE_UPDATE_SUB_TYPE = "更新互动课";
    String INTERACTIVE_COURSE_DELETE_SUB_TYPE = "删除互动课";
    String INTERACTIVE_COURSE_UPDATE_STATUS_SUB_TYPE = "修改互动课状态";
    String INTERACTIVE_COURSE_UPDATE_STATUS_SUB_TYPE_SUCCESS = "将互动课【{{#courseDO.courseNameCn}}】的状态修改为【{{#courseDO.displayStatus}}】";
    String INTERACTIVE_COURSE_UPDATE_SORT_SUB_TYPE = "将互动课【{{#courseDO.courseNameCn}}】的排序从【{{#oldSort}}】修改为【{{#courseDO.sort}}】";

    // ====================互动课单元====================

    String INTERACTIVE_COURSE_UNIT_SAVE_TYPE = "创建互动课单元【{{#unitDO.unitNameCn}}】";
    String INTERACTIVE_COURSE_UNIT_UPDATE_SUB_TYPE = "更新互动课单元【{{#unitDO.unitNameCn}}】: {{#updateReqVO}}";
    String INTERACTIVE_COURSE_UNIT_DELETE_SUB_TYPE = "删除互动课单元【{{#unitDO.unitNameCn}}】的信息";
    String INTERACTIVE_COURSE_UNIT_UPDATE_STATUS_SUB_TYPE = "修改互动课单元状态";
    String INTERACTIVE_COURSE_UNIT_UPDATE_STATUS_SUB_TYPE_SUCCESS = "将互动课单元【{{#unitDO.unitNameCn}}】的状态修改为【{{#displayStatus ? '显示' : '隐藏'}}】";
    String INTERACTIVE_COURSE_UNIT_UPDATE_SORT_SUB_TYPE = "将互动课单元【{{#unitDO.unitNameCn}}】的状态从【{{#oldSort}}】修改为【{{#unitDO.sort}}】";

    // ====================讲师====================

    String TEACHER_SAVE_TYPE = "创建讲师";
    String TEACHER_UPDATE_SUB_TYPE = "更新讲师";
    String TEACHER_DELETE_SUB_TYPE = "删除讲师";
    String TEACHER_UPDATE_STATUS_SUB_TYPE = "修改讲师状态";
    String TEACHER_UPDATE_STATUS_SUB_TYPE_SUCCESS = "将讲师【{{#teacherDO.teacherNameCn}}】的状态修改为【{{#displayStatus ? '显示' : '隐藏'}}】";
    String TEACHER_UPDATE_SORT_SUB_TYPE = "修改讲师序号";

    // ====================精品课====================

    String ELITE_COURSE_UPDATE_STATUS_SUB_TYPE = "修改精品课状态";
    String ELITE_COURSE_UPDATE_STATUS_SUB_TYPE_SUCCESS = "将精品课【{{#courseDO.courseNameCn}}】的状态修改为【{{#statusText}}】";
    
    String ELITE_COURSE_UPDATE_LISTING_STATUS_SUB_TYPE = "修改精品课上架状态";
    String ELITE_COURSE_UPDATE_LISTING_STATUS_SUB_TYPE_SUCCESS = "将精品课【{{#courseDO.courseNameCn}}】的上架状态修改为【{{#listingStatusText}}】";

    // ====================模考组卷规则====================

    String EXAM_PAPER_RULE_UPDATE_STATUS_SUB_TYPE = "修改模考组卷规则";
    String EXAM_PAPER_RULE_UPDATE_STATUS_SUB_TYPE_SUCCESS = "将模考组卷规则【{{#examPaperRule.name}}】的状态修改为【{{#examPaperRule.status}}】";

    // ====================模考====================

    String EXAM_UPDATE_PUBLISH_STATUS_SUB_TYPE = "修改模考发布状态";
    String EXAM_UPDATE_PUBLISH_STATUS_SUB_TYPE_SUCCESS = "将模考【{{#exam.name}}】的发布状态修改为【{{#publishStatusDesc}}】";

}
