package com.xt.hsk.module.edu.dal.dataobject.teacher;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 讲师 DO
 *
 * <AUTHOR>
 */
@TableName("edu_teacher")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeacherDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 讲师名称-中文
     */
    private String teacherNameCn;

    /**
     * 讲师名称-英文
     */
    private String teacherNameEn;

    /**
     * 讲师名称-其他
     */
    private String teacherNameOt;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 手机区号
     */
    private String countryCode;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 讲师介绍-中文
     */
    private String teacherIntroCn;

    /**
     * 讲师介绍-英文
     */
    private String teacherIntroEn;

    /**
     * 讲师介绍-其他
     */
    private String teacherIntroOt;

    /**
     * 营销语-中文
     */
    private String marketingSloganCn;

    /**
     * 营销语-英文
     */
    private String marketingSloganEn;

    /**
     * 营销语-其他
     */
    private String marketingSloganOt;

    /**
     * 展示状态 0-隐藏 1-显示
     */
    private Boolean displayStatus;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 删除时间(秒级时间戳)
     */
    private Long deleteTime;

} 