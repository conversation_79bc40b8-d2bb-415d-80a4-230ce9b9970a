package com.xt.hsk.module.edu.manager.elitecourse;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_COURSE_CATEGORY_HAS_COURSES;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_COURSE_CATEGORY_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.web.core.util.WebFrameworkUtils;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseCategoryPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseCategoryRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseCategorySaveReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteDeleteCheckRespVO;
import com.xt.hsk.module.edu.convert.elitecourse.EliteCourseCategoryConvert;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseCategoryDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseDO;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseCategoryService;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;


/**
 * 精品课-分类 管理端 Manager，负责管理端业务逻辑的编排
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Service
@Validated
public class EliteCourseCategoryManager {

    @Resource
    private EliteCourseCategoryService eliteCourseCategoryService;

    @Resource
    private EliteCourseService eliteCourseService;

    /**
     * 创建课程分类
     * 更新同HSK等级下已有分类的序号（所有分类序号+1）；创建新的分类对象并设置序号为1（确保新分类排在最前面）
     *
     * @param createReqVO 创建课程分类请求参数
     * @return 新创建的课程分类ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createCourseCategory(EliteCourseCategorySaveReqVO createReqVO) {

        // 1. 设置序号为1，并更新同等级下的序号+1
        updateSortForSameHskLevel(createReqVO.getHskLevel());

        // 2. 插入新分类
        EliteCourseCategoryDO courseCategory = EliteCourseCategoryConvert.INSTANCE.saveReqVOToDO(createReqVO);
        // 新增的分类排序默认为1
        courseCategory.setSort(1);
        courseCategory.setType(1);
        eliteCourseCategoryService.save(courseCategory);

        // 记录操作日志上下文
        LogRecordContext.putVariable("categoryId", courseCategory.getId());
        LogRecordContext.putVariable("category", courseCategory);

        // 返回
        return courseCategory.getId();
    }

    /**
     * 更新同等级下的序号+1
     */
    private void updateSortForSameHskLevel(Integer hskLevel) {
        // 查询同等级的所有分类
        eliteCourseCategoryService.lambdaUpdate()
                .eq(EliteCourseCategoryDO::getHskLevel, hskLevel)
                .setSql("sort = sort + 1")
                .set(EliteCourseCategoryDO::getUpdateTime, LocalDateTime.now())
                .set(EliteCourseCategoryDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                .update();
    }

    /**
     * 更新课程分类
     * 如果HSK等级发生变化，需要在新等级下为当前分类的序号腾出空间（将新等级下大于等于当前序号的分类序号+1）
     *
     * @param updateReqVO 更新课程分类请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateCourseCategory(EliteCourseCategorySaveReqVO updateReqVO) {
        // 校验存在
        EliteCourseCategoryDO originalCategory = validateCourseCategoryExists(updateReqVO.getId());

        // 检查HSK等级是否发生变化
        Integer originalHskLevel = originalCategory.getHskLevel();
        Integer newHskLevel = updateReqVO.getHskLevel();

        if (!originalHskLevel.equals(newHskLevel)) {
            // HSK等级发生变化，需要在新等级下调整序号
            updateSortForNewHskLevel(newHskLevel, originalCategory.getSort());
        }

        // 更新分类
        EliteCourseCategoryDO updateObj = EliteCourseCategoryConvert.INSTANCE.saveReqVOToDO(updateReqVO);
        updateObj.setUpdateTime(LocalDateTime.now());
        updateObj.setUpdater(String.valueOf(WebFrameworkUtils.getLoginUserId()));
        eliteCourseCategoryService.updateById(updateObj);

        // 记录操作日志上下文
        LogRecordContext.putVariable("category", updateObj);
    }

    /**
     * 更新新HSK等级下大于等于指定序号的分类序号+1
     */
    private void updateSortForNewHskLevel(Integer hskLevel, Integer targetSort) {
        // 将新HSK等级下大于等于目标序号的分类序号+1
        eliteCourseCategoryService.lambdaUpdate()
                .eq(EliteCourseCategoryDO::getHskLevel, hskLevel)
                .ge(EliteCourseCategoryDO::getSort, targetSort)
                .setSql("sort = sort + 1")
                .set(EliteCourseCategoryDO::getUpdateTime, LocalDateTime.now())
                .set(EliteCourseCategoryDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                .update();
    }

    /**
     * 删除课程分类
     *
     * @param id 课程分类id
     */
    public void deleteCourseCategory(Long id) {
        // 校验存在
        EliteCourseCategoryDO categoryDO = validateCourseCategoryExists(id);

        // 校验该分类下是否有课程
        validateCategoryHasNoCourse(id);

        // 删除
        eliteCourseCategoryService.removeById(id);

        // 记录操作日志上下文
        LogRecordContext.putVariable("category", categoryDO);
    }

    /**
     * 校验该分类下是否有课程
     *
     * @param id 分类id
     */
    private void validateCategoryHasNoCourse(Long id) {
        boolean exists = eliteCourseService.lambdaQuery()
                .eq(EliteCourseDO::getPrimaryCategoryId, id)
                .exists();
        if (exists) {
            throw exception(ELITE_COURSE_CATEGORY_HAS_COURSES);
        }
    }

    private EliteCourseCategoryDO validateCourseCategoryExists(Long id) {
        EliteCourseCategoryDO eliteCourseCategoryDO = eliteCourseCategoryService.getById(id);
        if (eliteCourseCategoryDO == null) {
            throw exception(ELITE_COURSE_CATEGORY_NOT_EXISTS);
        }
        return eliteCourseCategoryDO;
    }


    public EliteCourseCategoryDO getCourseCategory(Long id) {
        return eliteCourseCategoryService.getById(id);
    }

    /**
     * 分页获取精品课分类
     */
    public PageResult<EliteCourseCategoryRespVO> getCourseCategoryPage(@Valid EliteCourseCategoryPageReqVO pageReqVO) {
        PageResult<EliteCourseCategoryDO> categoryPage = eliteCourseCategoryService.selectPage(pageReqVO);
        List<EliteCourseCategoryDO> list = categoryPage.getList();

        List<EliteCourseCategoryRespVO> voList = EliteCourseCategoryConvert.INSTANCE.doListToCreateReqVOList(list);

        // 设置课程数量
        setCourseCount(voList);

        return new PageResult<>(voList, categoryPage.getTotal());
    }

    /**
     * 设置每个课程分类对应的课程数量。
     *
     * <p>
     * 该方法会根据传入的课程分类列表，从数据库中获取课程数据。
     * 统计每个分类下的课程数量，并将统计结果设置到对应的分类响应对象中。
     *
     * @param voList 包含课程分类信息的响应对象列表
     */
    private void setCourseCount(List<EliteCourseCategoryRespVO> voList) {
        // 如果传入的列表为空，则无需处理，直接返回
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        // 提取所有非空的课程分类ID
        List<Long> categoryIds = voList.stream()
                .map(EliteCourseCategoryRespVO::getId)
                .filter(Objects::nonNull)
                .toList();

        // 如果没有有效的分类ID，同样无需处理
        if (CollUtil.isEmpty(categoryIds)) {
            return;
        }

        // 查询数据库中所有课程，按主分类ID分组，并统计每组的数量
        Map<Long, Long> categoryCountMap = eliteCourseService.lambdaQuery()
                .in(EliteCourseDO::getPrimaryCategoryId, categoryIds)
                .list()
                .stream()
                .collect(Collectors.groupingBy(
                        EliteCourseDO::getPrimaryCategoryId,
                        Collectors.counting()
                ));

        // 遍历响应对象列表，将统计得到的课程数量设置到对应的分类中
        voList.forEach(vo ->
                vo.setCourseCount(categoryCountMap.getOrDefault(vo.getId(), 0L))
        );
    }


    /**
     * 修改课程分类排序
     * <p>
     * 1.校验分类是否存在；
     * 2.根据新旧序号大小关系调整同HSK等级下其他分类的序号；
     * 3.更新当前分类序号
     *
     * @param id      分类id
     * @param newSort 新排序
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateCourseCategorySort(Long id, Integer newSort) {
        // 1. 校验分类是否存在
        EliteCourseCategoryDO originalCategory = validateCourseCategoryExists(id);

        Integer oldSort = originalCategory.getSort();
        Integer hskLevel = originalCategory.getHskLevel();

        // 2. 如果新旧序号相同，直接返回
        if (oldSort.equals(newSort)) {
            return;
        }

        // 3. 调整同HSK等级下其他分类的序号
        if (oldSort < newSort) {
            // 旧序号小于新序号：将(旧序号+1)到新序号范围内的分类序号-1
            eliteCourseCategoryService.lambdaUpdate()
                    .eq(EliteCourseCategoryDO::getHskLevel, hskLevel)
                    .between(EliteCourseCategoryDO::getSort, oldSort + 1, newSort)
                    .setSql("sort = sort - 1")
                    .set(EliteCourseCategoryDO::getUpdateTime, LocalDateTime.now())
                    .set(EliteCourseCategoryDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                    .update();
        } else {
            // 旧序号大于新序号：将新序号到(旧序号-1)范围内的分类序号+1
            eliteCourseCategoryService.lambdaUpdate()
                    .eq(EliteCourseCategoryDO::getHskLevel, hskLevel)
                    .between(EliteCourseCategoryDO::getSort, newSort, oldSort - 1)
                    .setSql("sort = sort + 1")
                    .set(EliteCourseCategoryDO::getUpdateTime, LocalDateTime.now())
                    .set(EliteCourseCategoryDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                    .update();
        }

        // 4. 更新当前分类的序号
        eliteCourseCategoryService.lambdaUpdate()
                .eq(EliteCourseCategoryDO::getId, id)
                .set(EliteCourseCategoryDO::getSort, newSort)
                .set(EliteCourseCategoryDO::getUpdateTime, LocalDateTime.now())
                .set(EliteCourseCategoryDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                .update();

        // 记录操作日志上下文
        LogRecordContext.putVariable("category", originalCategory);
        LogRecordContext.putVariable("oldSort", oldSort);
    }

    /**
     * 获取所有精品课分类
     */
    public List<EliteCourseCategoryRespVO> getAll() {
        // 查询所有分类，按id倒序
        List<EliteCourseCategoryDO> categoryList = eliteCourseCategoryService.lambdaQuery()
                .orderByDesc(EliteCourseCategoryDO::getId)
                .list();

        return EliteCourseCategoryConvert.INSTANCE.doListToCreateReqVOList(categoryList);
    }

    /**
     * 检查分类是否可以删除
     *
     * @param id 分类id
     * @return 删除检查结果
     */
    public EliteDeleteCheckRespVO checkCategoryCanDelete(Long id) {
        // 校验分类是否存在
        validateCourseCategoryExists(id);

        // 查询该分类下的课程数量
        long courseCount = eliteCourseService.lambdaQuery()
                .eq(EliteCourseDO::getPrimaryCategoryId, id)
                .count();

        return new EliteDeleteCheckRespVO(courseCount <= 0, courseCount);
    }

    /**
     * 根据HSK等级获取精品课分类列表
     *
     * @param hskLevel HSK等级
     * @return 精品课分类列表
     */
    public List<EliteCourseCategoryRespVO> getByHskLevel(Integer hskLevel) {
        // 查询指定HSK等级的分类，按排序序号升序排列
        List<EliteCourseCategoryDO> categoryList = eliteCourseCategoryService.lambdaQuery()
                .eq(EliteCourseCategoryDO::getHskLevel, hskLevel)
                .orderByAsc(EliteCourseCategoryDO::getSort)
                .orderByDesc(EliteCourseCategoryDO::getId)
                .list();

        return EliteCourseCategoryConvert.INSTANCE.doListToCreateReqVOList(categoryList);
    }
}