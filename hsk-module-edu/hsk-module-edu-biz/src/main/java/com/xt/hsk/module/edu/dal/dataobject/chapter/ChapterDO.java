package com.xt.hsk.module.edu.dal.dataobject.chapter;

import lombok.*;

import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;

/**
 * 课程大纲 DO
 *
 * <AUTHOR>
 */
@TableName("edu_chapter")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChapterDO extends BaseDO {

    /**
     * 章节id
     */
    @TableId
    private Long id;
    /**
     * 教材id
     */
    private Long textbookId;
    /**
     * 科目（1=听力，2=阅读，4=书写）
     */
    private Integer subject;
    /**
     * 章节名称
     */
    private String chapterNameCn;
    /**
     * 章节名称 英文
     */
    private String chapterNameEn;
    /**
     * 章节名称 其他
     */
    private String chapterNameOt;
    /**
     * 章节序号
     */
    private Integer chapterOrder;
    /**
     * 状态 0开启 1关闭
     */
    private Integer status;
    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;


}