package com.xt.hsk.module.edu.convert.elitecourse;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.i18n.LanguageUtils;
import com.xt.hsk.module.edu.api.dto.EliteCourseRespDTO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseDirectoryRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseSaveReqVO;
import com.xt.hsk.module.edu.controller.app.elitecourse.vo.EliteCourseAppRespVO;
import com.xt.hsk.module.edu.controller.app.elitecourse.vo.EliteCourseDetailRespVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteChapterDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseDO;
import com.xt.hsk.module.edu.dal.dataobject.teacher.TeacherDO;
import java.util.List;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * 精品课转换类
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface EliteCourseConvert {

    EliteCourseConvert INSTANCE = Mappers.getMapper(EliteCourseConvert.class);

    /**
     * 创建VO类转DO类
     */
    EliteCourseDO saveReqVOToDO(EliteCourseSaveReqVO saveReqVO);
    /**
     * DO类转app响应VO类
     */
    EliteCourseAppRespVO doToAppRespVO(EliteCourseDO bean);

    @AfterMapping
    default void fillLocalizedFields(
        EliteCourseDO bean, @MappingTarget EliteCourseAppRespVO respVO) {
        // 这段代码会在基本映射完成后执行
        // 可以访问源对象bean的所有属性
        // 可以修改目标对象respVO的属性
        respVO.setCourseName(LanguageUtils.getLocalizedValue(
            bean.getCourseNameCn(),
            bean.getCourseNameEn(),
            bean.getCourseNameOt()));

        respVO.setOriginalPrice(LanguageUtils.getLocalizedValue(
            bean.getOriginalPriceCn(),
            bean.getOriginalPriceEn(),
            bean.getOriginalPriceOt()));

        respVO.setSellingPrice(LanguageUtils.getLocalizedValue(
            bean.getSellingPriceCn(),
            bean.getSellingPriceEn(),
            bean.getSellingPriceOt()));
    }

    @AfterMapping
    default void fillLocalizedFields2(
        EliteCourseDO bean, @MappingTarget EliteCourseDetailRespVO respVO) {
        // 这段代码会在基本映射完成后执行
        // 可以访问源对象bean的所有属性
        // 可以修改目标对象respVO的属性
        respVO.setCourseName(LanguageUtils.getLocalizedValue(
            bean.getCourseNameCn(),
            bean.getCourseNameEn(),
            bean.getCourseNameOt()));

        respVO.setOriginalPrice(LanguageUtils.getLocalizedValue(
            bean.getOriginalPriceCn(),
            bean.getOriginalPriceEn(),
            bean.getOriginalPriceOt()));

        respVO.setSellingPrice(LanguageUtils.getLocalizedValue(
            bean.getSellingPriceCn(),
            bean.getSellingPriceEn(),
            bean.getSellingPriceOt()));
    }

    /**
     * DO类转详情响应VO类
     */
    EliteCourseDetailRespVO doToDetailRespVO(EliteCourseDO bean);

    /**
     * DO类转推荐课程VO类
     */
    EliteCourseDetailRespVO.RecommendedCourseVO doToRecommendedCourseVO(EliteCourseDO bean);

    /**
     * DO列表转推荐课程VO列表
     */
    List<EliteCourseDetailRespVO.RecommendedCourseVO> doListToRecommendedCourseVOList(
        List<EliteCourseDO> list);

    PageResult<EliteCourseRespVO> convertPage(PageResult<EliteCourseDO> page);
    /**
     * DO类转响应VO类
     */
    EliteCourseRespVO doToRespVO(EliteCourseDO eliteCourseDO);

    /**
     * DO列表转响应VO列表
     */
    List<EliteCourseRespVO> doListToRespVOList(List<EliteCourseDO> list);

    /**
     * 讲师DO转详情讲师VO
     */
    EliteCourseDetailRespVO.TeacherVO teacherDoToTeacherVO(TeacherDO teacherDO);

    /**
     * 讲师DO列表转详情讲师VO列表
     */
    List<EliteCourseDetailRespVO.TeacherVO> teacherDoListToTeacherVOList(List<TeacherDO> list);

    @AfterMapping
    default void fillLocalizedFields(TeacherDO bean,
        @MappingTarget EliteCourseDetailRespVO.TeacherVO respVO) {
        // 这段代码会在基本映射完成后执行
        // 可以访问源对象bean的所有属性
        // 可以修改目标对象respVO的属性
        respVO.setTeacherName(LanguageUtils.getLocalizedValue(
            bean.getTeacherNameCn(),
            bean.getTeacherNameEn(),
            bean.getTeacherNameOt()));

        respVO.setMarketingSlogan(LanguageUtils.getLocalizedValue(
            bean.getMarketingSloganCn(),
            bean.getMarketingSloganEn(),
            bean.getMarketingSloganOt()));

        respVO.setTeacherIntro(LanguageUtils.getLocalizedValue(
            bean.getTeacherIntroCn(),
            bean.getTeacherIntroEn(),
            bean.getTeacherIntroOt()));
    }

    /**
     * DO类课程目录响应VO类
     */
    @Mapping(source = "id", target = "chapterId")
    EliteCourseDirectoryRespVO doToDirectoryRespVO(EliteChapterDO chapter);

    /**
     * DO转其他模块调用的DTO
     */
    @Mapping(source = "id", target = "courseId")
    EliteCourseRespDTO doToDTO(EliteCourseDO bean);

    @Mapping(source = "id", target = "courseId")
    List<EliteCourseRespDTO> doListToDTOList(List<EliteCourseDO> beanList);
}