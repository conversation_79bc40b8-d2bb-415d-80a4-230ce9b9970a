package com.xt.hsk.module.edu.dal.mysql.questiondetail;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionDetailPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiondetail.QuestionDetailDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 题目详情 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionDetailMapper extends BaseMapperX<QuestionDetailDO> {

    default PageResult<QuestionDetailDO> selectPage(QuestionDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<QuestionDetailDO>()
                .eqIfPresent(QuestionDetailDO::getQuestionId, reqVO.getQuestionId())
                .eqIfPresent(QuestionDetailDO::getAttachmentAudio, reqVO.getAttachmentAudio())
                .betweenIfPresent(QuestionDetailDO::getAttachmentAudioTime, reqVO.getAttachmentAudioTime())
                .eqIfPresent(QuestionDetailDO::getAttachmentImage, reqVO.getAttachmentImage())
                .eqIfPresent(QuestionDetailDO::getAttachmentContent, reqVO.getAttachmentContent())
                .eqIfPresent(QuestionDetailDO::getAnswer, reqVO.getAnswer())
                .eqIfPresent(QuestionDetailDO::getOptions, reqVO.getOptions())
                .eqIfPresent(QuestionDetailDO::getVersion, reqVO.getVersion())
                .eqIfPresent(QuestionDetailDO::getExplainTextCn, reqVO.getExplainTextCn())
                .eqIfPresent(QuestionDetailDO::getExplainTextEn, reqVO.getExplainTextEn())
                .eqIfPresent(QuestionDetailDO::getExplainTextOt, reqVO.getExplainTextOt())
                .eqIfPresent(QuestionDetailDO::getExplainAudio, reqVO.getExplainAudio())
                .eqIfPresent(QuestionDetailDO::getExplainVideo, reqVO.getExplainVideo())
                .betweenIfPresent(QuestionDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(QuestionDetailDO::getId));
    }

    default List<QuestionDetailDO> selectListByQuestionId(Long questionId){
        return selectList(new LambdaQueryWrapperX<QuestionDetailDO>()
                .eq(QuestionDetailDO::getQuestionId, questionId));
    }
}