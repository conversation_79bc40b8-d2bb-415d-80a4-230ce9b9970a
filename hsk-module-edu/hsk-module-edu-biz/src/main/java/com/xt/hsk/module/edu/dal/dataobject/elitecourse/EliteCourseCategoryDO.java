package com.xt.hsk.module.edu.dal.dataobject.elitecourse;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 精品课-分类 DO
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@TableName("edu_elite_course_category")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EliteCourseCategoryDO extends BaseDO {

    /**
     * 精品课分类ID
     */
    @TableId
    private Long id;
    /**
     * 分类名称-中文
     */
    private String nameCn;
    /**
     * 分类名称-英文
     */
    private String nameEn;
    /**
     * 分类名称-其他
     */
    private String nameOt;
    /**
     * 一等分类id
     */
    private Long parentId;
    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;
    /**
     * 排序序号
     */
    private Integer sort;
    /**
     * 类型 1.普通课程 2.公开课
     */
    private Integer type;

}