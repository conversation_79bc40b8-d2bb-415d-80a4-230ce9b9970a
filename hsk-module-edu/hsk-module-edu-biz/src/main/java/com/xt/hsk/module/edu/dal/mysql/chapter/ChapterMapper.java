package com.xt.hsk.module.edu.dal.mysql.chapter;

import java.util.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.xt.hsk.framework.common.enums.CommonStatusEnum;
import com.xt.hsk.framework.common.enums.IsShowEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.ChapterPageReqVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.ChapterRespVO;
import com.xt.hsk.module.edu.dal.dataobject.chapter.ChapterDO;
import com.xt.hsk.module.edu.dal.dataobject.textbook.TextbookDO;
import com.xt.hsk.module.edu.dal.dataobject.unit.UnitDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 课程大纲 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ChapterMapper extends BaseMapperX<ChapterDO> {

    default PageResult<ChapterDO> selectPage(ChapterPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ChapterDO>()
                .eqIfPresent(ChapterDO::getTextbookId, reqVO.getTextbookId())
                .eqIfPresent(ChapterDO::getSubject, reqVO.getSubject())
                .eqIfPresent(ChapterDO::getChapterNameCn, reqVO.getChapterNameCn())
                .eqIfPresent(ChapterDO::getChapterNameEn, reqVO.getChapterNameEn())
                .eqIfPresent(ChapterDO::getChapterNameOt, reqVO.getChapterNameOt())
                .eqIfPresent(ChapterDO::getChapterOrder, reqVO.getChapterOrder())
                .betweenIfPresent(ChapterDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(ChapterDO::getStatus, reqVO.getStatus())
                .orderByAsc(ChapterDO::getChapterOrder)
                .orderByDesc(ChapterDO::getUpdateTime));
    }

    default List<ChapterDO> getByTextbookId(Long id) {
        return selectList(new LambdaQueryWrapperX<ChapterDO>()
                .eq(ChapterDO::getTextbookId, id)
                .eq(ChapterDO::getStatus, CommonStatusEnum.ENABLE.getStatus())

                .orderByAsc(ChapterDO::getChapterOrder));
    }

    IPage<ChapterRespVO> selectChapterRespVOPage(IPage<ChapterRespVO> page, @Param("reqVO") ChapterPageReqVO reqVO);
//    {
//        // 计算count
//        Long count = selectCount(buildWrapper(reqVO).isNotNull(ChapterDO::getId));
//        Page<ChapterDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
//
//        MPJLambdaWrapper<ChapterDO> lambdaWrapper = buildWrapper(reqVO)
//                .orderByDesc(ChapterDO::getUpdateTime)
//                .orderByAsc(ChapterDO::getChapterOrder)
//                .orderByAsc(UnitDO::getSort)
//                .isNotNull(ChapterDO::getId)
//                .select(ChapterDO::getId, ChapterDO::getTextbookId, ChapterDO::getSubject,
//                        ChapterDO::getChapterNameCn, ChapterDO::getChapterNameEn, ChapterDO::getStatus,
//                        ChapterDO::getChapterNameOt, ChapterDO::getChapterOrder)
//                .selectAs(UnitDO::getSubject, "unit_subject")
//                .selectAs(UnitDO::getUnitNameCn, "unit_name")
//                .selectAs(UnitDO::getId, "unit_id")
//                .last("limit " + (page.getCurrent() - 1) * page.getSize() + "," + page.getSize());
//        List<ChapterRespVO> pageResult = selectJoinList(ChapterRespVO.class, lambdaWrapper);
//
//
//        long count = selectChapterRespVOPage

//        return new PageResult<>(pageResult, count);
//    }

    default MPJLambdaWrapperX<ChapterDO> buildWrapper(ChapterPageReqVO reqVO) {
        return new MPJLambdaWrapperX<ChapterDO>()
                .leftJoin(UnitDO.class, UnitDO::getChapterId, ChapterDO::getId)
                .eqIfPresent(ChapterDO::getTextbookId, reqVO.getTextbookId())
                .eqIfPresent(ChapterDO::getSubject, reqVO.getSubject())
                .likeIfPresent(ChapterDO::getChapterNameCn, reqVO.getChapterNameCn())
                .eqIfPresent(ChapterDO::getChapterNameEn, reqVO.getChapterNameEn())
                .eqIfPresent(ChapterDO::getChapterNameOt, reqVO.getChapterNameOt())
                .eqIfPresent(ChapterDO::getChapterOrder, reqVO.getChapterOrder())
                .betweenIfPresent(ChapterDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(ChapterDO::getStatus, reqVO.getStatus())
                ;
    }
}