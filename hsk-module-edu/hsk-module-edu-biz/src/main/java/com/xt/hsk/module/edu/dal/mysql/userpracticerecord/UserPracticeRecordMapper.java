package com.xt.hsk.module.edu.dal.mysql.userpracticerecord;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.userpracticerecord.vo.UserPracticeRecordPageReqVO;
import com.xt.hsk.module.edu.controller.app.question.vo.AppUserPracticeRecordRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionSearchReqVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionStatisticsRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionTypeCountRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.TextbookChapterQuestionRespVO;
import com.xt.hsk.module.edu.dal.dataobject.userpracticerecord.UserPracticeRecordDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户练习记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserPracticeRecordMapper extends BaseMapperX<UserPracticeRecordDO> {

    default PageResult<UserPracticeRecordDO> selectPage(UserPracticeRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<UserPracticeRecordDO>()
                .eqIfPresent(UserPracticeRecordDO::getUserId, reqVO.getUserId())
                .eqIfPresent(UserPracticeRecordDO::getHskLevel, reqVO.getHskLevel())
                .eqIfPresent(UserPracticeRecordDO::getTextbookId, reqVO.getTextbookId())
                .eqIfPresent(UserPracticeRecordDO::getChapterId, reqVO.getChapterId())
                .eqIfPresent(UserPracticeRecordDO::getSubject, reqVO.getSubject())
                .eqIfPresent(UserPracticeRecordDO::getUnitSort, reqVO.getUnitSort())
                .eqIfPresent(UserPracticeRecordDO::getQuestionTypeId, reqVO.getQuestionTypeId())
                .eqIfPresent(UserPracticeRecordDO::getQuestionNum, reqVO.getQuestionNum())
                .eqIfPresent(UserPracticeRecordDO::getAnswerNum, reqVO.getAnswerNum())
                .eqIfPresent(UserPracticeRecordDO::getCorrectNum, reqVO.getCorrectNum())
                .betweenIfPresent(UserPracticeRecordDO::getAnswerTime, reqVO.getAnswerTime())
                .betweenIfPresent(UserPracticeRecordDO::getStartTime, reqVO.getStartTime())
                .betweenIfPresent(UserPracticeRecordDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(UserPracticeRecordDO::getRecordStatus, reqVO.getRecordStatus())
                .eqIfPresent(UserPracticeRecordDO::getIsNewest, reqVO.getIsNewest())
                .eqIfPresent(UserPracticeRecordDO::getInteractiveCourseUnitId, reqVO.getInteractiveCourseUnitId())
                .betweenIfPresent(UserPracticeRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(UserPracticeRecordDO::getId));
    }

    QuestionStatisticsRespVO getQuestionNewestPracticeRecord(QuestionSearchReqVO reqVO);

    List<QuestionTypeCountRespVO> getUserUnitSortQuestionTypeCount(QuestionSearchReqVO reqVO);

    List<TextbookChapterQuestionRespVO> getUserTextbookChapterQuestions(QuestionSearchReqVO reqVO);

    AppUserPracticeRecordRespVO getUserNotFinishedPracticeRecord(QuestionSearchReqVO reqVO);

    /**
     * 获取用户练习中心的答题数据
     *
     * @param pageReqVO
     * @return
     */
    List<TextbookChapterQuestionRespVO> getUserPracticeTextbookChapterList(QuestionSearchReqVO pageReqVO);

    QuestionStatisticsRespVO getQuestionNewestHaveReportPracticeRecord(QuestionSearchReqVO pageReqVO);

    List<QuestionTypeCountRespVO> getHistoryUserUnitSortQuestionTypeCount(QuestionSearchReqVO pageReqVO);
}