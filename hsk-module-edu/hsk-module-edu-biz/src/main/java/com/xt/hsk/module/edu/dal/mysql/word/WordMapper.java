package com.xt.hsk.module.edu.dal.mysql.word;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.xt.hsk.framework.common.enums.WordKindEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordDO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordMeaningDO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordTagDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;
import java.util.List;

/**
 * 汉语词典基础数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WordMapper extends BaseMapperX<WordDO> {

    default PageResult<WordDO> selectPage(WordPageReqVO reqVO) {
        MPJLambdaWrapper<WordDO> wordDOMPJLambdaWrapper = new MPJLambdaWrapperX<WordDO>()
                .leftJoin(WordMeaningDO.class, WordMeaningDO::getWordId, WordDO::getId)
                .leftJoin(WordTagDO.class, WordTagDO::getWordId, WordDO::getId)
                .eqIfPresent(WordDO::getId, reqVO.getId())
                .likeIfPresent(WordDO::getWord, reqVO.getWord())
                .eqIfPresent(WordDO::getIsSpecial, reqVO.getIsSpecial())
                .betweenIfPresent(WordDO::getUpdateTime, reqVO.getUpdateTime())
                .inIfPresent(WordTagDO::getTagId, reqVO.getTags())
//                .inIfPresent(WordMeaningDO::getKind, reqVO.getKinds())
                .inIfPresent(WordDO::getId, reqVO.getIds())
                .likeIfPresent(WordMeaningDO::getTranslationEn, reqVO.getTranslationEn())
                .likeIfPresent(WordMeaningDO::getTranslationOt, reqVO.getTranslationOt())
                .distinct()
                .orderByDesc(WordDO::getId);
        if (reqVO.getKinds() != null && !reqVO.getKinds().isEmpty()) {
            List<Integer> kinds = new ArrayList<>();
            for (String kind : reqVO.getKinds()) {
                kinds.add(WordKindEnum.getValueByCode(kind));
            }
            String sql = "kind != -1 and (";
            for (Integer kind : kinds) {
                sql = sql.concat("(kind & " + kind + " = " + kind + ") or");
            }
            sql = sql.concat(" (1=0))");
            wordDOMPJLambdaWrapper.apply(sql);
        }
        if (reqVO.getHskLevels() != null && !reqVO.getHskLevels().isEmpty()) {
            String sql = "hsk_level != -1 and (";
            for (Integer hskLevel : reqVO.getHskLevels()) {
                if (hskLevel == 0) {
                    sql = sql.concat("(hsk_level = 0) or");
                } else {
                    sql = sql.concat("(hsk_level & " + hskLevel + " = " + hskLevel + ") or");
                }
            }
            sql = sql.concat(" (1=0))");
            wordDOMPJLambdaWrapper.apply(sql);
        }

        if (Boolean.TRUE.equals(reqVO.getSingleChar())) {
            wordDOMPJLambdaWrapper.apply("CHAR_LENGTH(t.word) = 1");
        }


        return selectJoinPage(reqVO, WordDO.class, wordDOMPJLambdaWrapper);
    }

    default Long dataTotalCount(WordPageReqVO reqVO) {
        return selectJoinCount(new MPJLambdaWrapperX<WordDO>()
                .leftJoin(WordMeaningDO.class, WordMeaningDO::getWordId, WordDO::getId)
                .leftJoin(WordTagDO.class, WordTagDO::getWordId, WordDO::getId)
                .eqIfPresent(WordDO::getId, reqVO.getId())
                .likeIfPresent(WordDO::getWord, reqVO.getWord())
                .eqIfPresent(WordDO::getIsSpecial, reqVO.getIsSpecial())
                .betweenIfPresent(WordDO::getCreateTime, reqVO.getUpdateTime())
                .inIfPresent(WordTagDO::getTagId, reqVO.getTags())
                .inIfPresent(WordMeaningDO::getKind, reqVO.getKinds())
                .inIfPresent(WordDO::getId, reqVO.getIds())
                .distinct());
    }


}