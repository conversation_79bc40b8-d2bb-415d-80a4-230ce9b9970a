package com.xt.hsk.module.edu.api.elitecourse;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.xt.hsk.module.edu.api.dto.EliteCourseRespDTO;
import com.xt.hsk.module.edu.api.dto.EliteCourseValidityPeriodReqDTO;
import com.xt.hsk.module.edu.convert.elitecourse.EliteCourseConvert;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseDO;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_COURSE_DEADLINE_REQUIRED;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_COURSE_EFFECTIVE_DAYS_REQUIRED;
import static com.xt.hsk.module.edu.enums.elitecourse.LearningValidityPeriodEnum.*;

/**
 * 精品课程 API 实现
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Service
public class EliteCourseApiImpl implements EliteCourseApi {

    @Resource
    private EliteCourseService eliteCourseService;

    @Override
    public EliteCourseRespDTO getById(Long id) {
        if (id == null) {
            return null;
        }
        EliteCourseDO courseDO = eliteCourseService.getById(id);
        return EliteCourseConvert.INSTANCE.doToDTO(courseDO);
    }

    @Override
    public List<EliteCourseRespDTO> getListByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<EliteCourseDO> courseDOS = eliteCourseService.lambdaQuery()
            .in(EliteCourseDO::getId, ids)
            .list();
        // 转为DTO
        return EliteCourseConvert.INSTANCE.doListToDTOList(courseDOS);
    }

    /**
     * 根据学校有效期获取课程id列表
     */
    @Override
    public List<Long> listCourseIdByValidityPeriod(EliteCourseValidityPeriodReqDTO reqDTO) {
        if (reqDTO.getLearningValidityPeriod() == null) {
            return Collections.emptyList();
        }

        if (BY_DEADLINE.getCode().equals(reqDTO.getLearningValidityPeriod())
                && reqDTO.getDeadline() == null) {
            throw exception(ELITE_COURSE_DEADLINE_REQUIRED);
        }


        if (BY_DAYS.getCode().equals(reqDTO.getLearningValidityPeriod())
                && reqDTO.getEffectiveDays() == null) {
            throw exception(ELITE_COURSE_EFFECTIVE_DAYS_REQUIRED);
        }

        return eliteCourseService.lambdaQuery()
                .eq(EliteCourseDO::getLearningValidityPeriod, reqDTO.getLearningValidityPeriod())
                .eq(Objects.nonNull(reqDTO.getDeadline()), EliteCourseDO::getDeadline, reqDTO.getDeadline())
                .eq(Objects.nonNull(reqDTO.getEffectiveDays()), EliteCourseDO::getEffectiveDays, reqDTO.getEffectiveDays())
                .list()
                .stream()
                .map(EliteCourseDO::getId)
                .toList();
    }

    /**
     * 通过ID列表获取有效期
     *
     * @param ids ID列表
     * @return 有效期map
     */
    @Override
    public Map<Long, String> getPeriodValidityByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }

        List<EliteCourseDO> courseList = eliteCourseService.lambdaQuery()
                .in(EliteCourseDO::getId, ids)
                .list();

        if (CollUtil.isEmpty(courseList)) {
            return Collections.emptyMap();
        }

        Map<Long, String> periodValidityMap = new HashMap<>();

        for (EliteCourseDO course : courseList) {
            Long courseId = course.getId();
            Integer validityType = course.getLearningValidityPeriod();

            if (PERMANENT.getCode().equals(validityType)) {
                // 长期有效
                periodValidityMap.put(courseId, PERMANENT.getDesc());
            } else if (BY_DEADLINE.getCode().equals(validityType) && course.getDeadline() != null) {
                // 按截止日期
                String formattedDate = DateUtil.format(course.getDeadline(), DatePattern.NORM_DATE_PATTERN);
                periodValidityMap.put(courseId, formattedDate + "前");
            } else if (BY_DAYS.getCode().equals(validityType) && course.getEffectiveDays() != null) {
                // 按天数
                periodValidityMap.put(courseId, course.getEffectiveDays() + "天");
            }
        }

        return periodValidityMap;
    }
}
