package com.xt.hsk.module.edu.api.elitecourse;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.xt.hsk.module.edu.api.dto.EliteCourseRegisterCreateReqDTO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseRegisterDO;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseRegisterService;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 精品课程注册 API 实现类
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Service
@Validated
@Slf4j
public class EliteCourseRegisterApiImpl implements EliteCourseRegisterApi {

    @Resource
    private EliteCourseRegisterService eliteCourseRegisterService;

    @Override
    public boolean batchRegister(List<EliteCourseRegisterCreateReqDTO> reqList) {
        log.info("[batchRegister] 批量注册课程，数量：{}", reqList.size());
        if (CollUtil.isEmpty(reqList)) {
            return true;
        }

        List<EliteCourseRegisterDO> registerDOList = new ArrayList<>();
        for (EliteCourseRegisterCreateReqDTO req : reqList) {
            EliteCourseRegisterDO registerDO = BeanUtil.copyProperties(req,
                EliteCourseRegisterDO.class);
            registerDOList.add(registerDO);
        }

        boolean result = eliteCourseRegisterService.saveBatch(registerDOList);
        log.info("[batchRegister] 批量注册课程完成，结果：{}", result);
        return result;
    }

    @Override
    public Long getUserCourseRegisterCount(Long userId) {
        return eliteCourseRegisterService.getUserCourseRegisterCount(userId);
    }
} 