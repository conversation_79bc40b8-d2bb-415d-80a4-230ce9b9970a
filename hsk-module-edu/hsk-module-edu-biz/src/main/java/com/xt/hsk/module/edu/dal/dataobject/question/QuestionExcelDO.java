package com.xt.hsk.module.edu.dal.dataobject.question;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 题目 DO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = false)
public class QuestionExcelDO {
    /**
     * HSK等级
     */
    @ExcelProperty("HSK等级")
    private Integer hskLevel;
    /**
     * 教材ID
     */
    @ExcelProperty("教材")
    private String textbookNameCn;

    // 章节
    @ExcelProperty("章节")
    private String chapterNameCn;

    // 单元
    @ExcelProperty("单元")
    private String unitNameCn;

    // 题型
    @ExcelProperty("题型")
    private String typeNameCn;

    // 科目
    @ExcelProperty("科目")
    private String subjectName;

    /**
     * 材料音频
     */
    @ExcelProperty("材料音频")
    private String materialAudio;

    /**
     * 材料图片
     */
    @ExcelProperty("材料图片")
    private String materialImage;
    /**
     * 材料文字
     */
    @ExcelProperty("材料文字")
    private String materialContent;
    /**
     * 题目选项 json
     */
    @ExcelProperty("材料选项A")
    private String materialOptionA;

    @ExcelProperty("材料选项B")
    private String materialOptionB;

    @ExcelProperty("材料选项C")
    private String materialOptionC;

    @ExcelProperty("材料选项D")
    private String materialOptionD;

    @ExcelProperty("材料选项E")
    private String materialOptionE;

    /**
     * 题干文字
     */
    @ExcelProperty("题干文字")
    private String attachmentContent;

    /**
     * 题干图片
     */
    @ExcelProperty("题干图片")
    private String attachmentImage;

    /**
     * 题干音频
     */
    @ExcelProperty("题干音频")
    private String attachmentAudio;

    /**
     * 参考答案
     */
    @ExcelProperty("参考答案")
    private String answer;

    /**
     * 文字题目解析
     */
    @ExcelProperty("文字题目解析中文")
    private String explainTextCn;

    /**
     * 文字题目解析
     */
    @ExcelProperty("文字题目解析英文")
    private String explainTextEn;

    /**
     * 文字题目解析
     */
    @ExcelProperty("文字题目解析越南文")
    private String explainTextOt;

    /**
     * 音频题目解析
     */
    @ExcelProperty("音频题目解析")
    private String explainAudio;

    /**
     * 视频题目解析
     */
    @ExcelProperty("视频题目解析")
    private String explainVideo;


    @ExcelProperty("题目选项A")
    private String detailOptionA;

    @ExcelProperty("题目选项B")
    private String detailOptionB;

    @ExcelProperty("题目选项C")
    private String detailOptionC;

    @ExcelProperty("题目选项D")
    private String detailOptionD;

    @ExcelProperty("题目选项E")
    private String detailOptionE;
    @ExcelProperty("题目选项F")
    private String detailOptionF;
    @ExcelProperty("题目选项G")
    private String detailOptionG;

}