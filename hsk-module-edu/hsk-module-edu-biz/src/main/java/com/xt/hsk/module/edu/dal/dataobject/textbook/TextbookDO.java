package com.xt.hsk.module.edu.dal.dataobject.textbook;

import lombok.*;

import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;

/**
 * 教材 DO
 *
 * <AUTHOR>
 */
@TableName("edu_textbook")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TextbookDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 教程名 中文
     */
    private String nameCn;
    /**
     * 教程名 英文
     */
    private String nameEn;
    /**
     * 教程名 其他语种
     */
    private String nameOt;
    /**
     * 排序序号
     */
    private Integer sort;
    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;
    /**
     * 教材分类
     */
    private Integer type;
    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;

}