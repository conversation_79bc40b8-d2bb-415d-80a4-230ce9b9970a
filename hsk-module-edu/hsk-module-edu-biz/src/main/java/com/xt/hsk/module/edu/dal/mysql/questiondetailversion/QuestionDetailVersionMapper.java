package com.xt.hsk.module.edu.dal.mysql.questiondetailversion;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.question.questiondetailversion.vo.QuestionDetailVersionPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiondetailversion.QuestionDetailVersionDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 题目详情表版本库 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionDetailVersionMapper extends BaseMapperX<QuestionDetailVersionDO> {

    default PageResult<QuestionDetailVersionDO> selectPage(QuestionDetailVersionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<QuestionDetailVersionDO>()
                .eqIfPresent(QuestionDetailVersionDO::getQuestionId, reqVO.getQuestionId())
                .eqIfPresent(QuestionDetailVersionDO::getQuestionDetailId, reqVO.getQuestionDetailId())
                .eqIfPresent(QuestionDetailVersionDO::getSort, reqVO.getSort())
                .eqIfPresent(QuestionDetailVersionDO::getAttachmentAudio, reqVO.getAttachmentAudio())
                .betweenIfPresent(QuestionDetailVersionDO::getAttachmentAudioTime, reqVO.getAttachmentAudioTime())
                .eqIfPresent(QuestionDetailVersionDO::getAttachmentImage, reqVO.getAttachmentImage())
                .eqIfPresent(QuestionDetailVersionDO::getAttachmentContent, reqVO.getAttachmentContent())
                .eqIfPresent(QuestionDetailVersionDO::getAnswer, reqVO.getAnswer())
                .eqIfPresent(QuestionDetailVersionDO::getOptions, reqVO.getOptions())
                .eqIfPresent(QuestionDetailVersionDO::getVersion, reqVO.getVersion())
                .eqIfPresent(QuestionDetailVersionDO::getExplainTextCn, reqVO.getExplainTextCn())
                .eqIfPresent(QuestionDetailVersionDO::getExplainTextEn, reqVO.getExplainTextEn())
                .eqIfPresent(QuestionDetailVersionDO::getExplainTextOt, reqVO.getExplainTextOt())
                .eqIfPresent(QuestionDetailVersionDO::getExplainAudio, reqVO.getExplainAudio())
                .eqIfPresent(QuestionDetailVersionDO::getExplainVideo, reqVO.getExplainVideo())
                .betweenIfPresent(QuestionDetailVersionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(QuestionDetailVersionDO::getId));
    }

}