package com.xt.hsk.module.edu.dal.mysql.exam;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRulePageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamPaperRuleDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 模考组卷规则 Mapper
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Mapper
public interface ExamPaperRuleMapper extends BaseMapperX<ExamPaperRuleDO> {

    default PageResult<ExamPaperRuleDO> selectPage(ExamPaperRulePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ExamPaperRuleDO>()
                .likeIfPresent(ExamPaperRuleDO::getName, reqVO.getName())
                .eqIfPresent(ExamPaperRuleDO::getHskLevel, reqVO.getHskLevel())
                .eqIfPresent(ExamPaperRuleDO::getExamType, reqVO.getExamType())
                .eqIfPresent(ExamPaperRuleDO::getListeningDuration, reqVO.getListeningDuration())
                .eqIfPresent(ExamPaperRuleDO::getReadingDuration, reqVO.getReadingDuration())
                .eqIfPresent(ExamPaperRuleDO::getWritingDuration, reqVO.getWritingDuration())
                .eqIfPresent(ExamPaperRuleDO::getQuestionCount, reqVO.getQuestionCount())
                .eqIfPresent(ExamPaperRuleDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(ExamPaperRuleDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ExamPaperRuleDO::getId));
    }

}