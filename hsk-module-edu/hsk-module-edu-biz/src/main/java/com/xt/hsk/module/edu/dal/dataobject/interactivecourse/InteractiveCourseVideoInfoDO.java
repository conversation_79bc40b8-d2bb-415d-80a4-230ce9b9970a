package com.xt.hsk.module.edu.dal.dataobject.interactivecourse;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 互动课视频信息 DO
 *
 * <AUTHOR>
 */
@TableName("edu_interactive_course_video_info")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InteractiveCourseVideoInfoDO extends BaseDO {

    /**
     * 视频信息ID
     */
    @TableId
    private Long id;
    /**
     * 互动课单元 ID 关联#InteractiveCourseUnitDO.id
     */
    private Long unitId;

    /**
     * 视频名称-中文
     */
    private String videoNameCn;

    /**
     * 视频名称-英文
     */
    private String videoNameEn;

    /**
     * 视频名称-其他
     */
    private String videoNameOt;

    /**
     * 视频类型 1-视频带课件 2-视频带生词
     *
     * @see com.xt.hsk.module.edu.enums.interactivecourse.VideoTypeEnum
     */
    private Integer videoType;

    /**
     * 视频尺寸比例 1-9:16 2-16:9
     *
     * @see com.xt.hsk.module.edu.enums.interactivecourse.VideoAspectRatioEnum
     */
    private Integer aspectRatio;

    /**
     * 视频链接JSON
     */
    private String videoLinks;
} 