package com.xt.hsk.module.edu.dal.mysql.teacher;

import cn.hutool.core.collection.CollUtil;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.teacher.vo.TeacherPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.teacher.TeacherDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 讲师 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TeacherMapper extends BaseMapperX<TeacherDO> {

    default PageResult<TeacherDO> selectPage(TeacherPageReqVO reqVO) {
        LambdaQueryWrapperX<TeacherDO> query = new LambdaQueryWrapperX<TeacherDO>();

        // 如果有选中的ID，优先使用ID列表查询，忽略其他条件
        if (CollUtil.isNotEmpty(reqVO.getIds())) {
            query.in(TeacherDO::getId, reqVO.getIds());
        } else {
            // 没有选中ID时，使用其他查询条件
            query.likeIfPresent(TeacherDO::getTeacherNameCn, reqVO.getTeacherNameCn())
                .likeIfPresent(TeacherDO::getMobile, reqVO.getMobile())
                .eqIfPresent(TeacherDO::getDisplayStatus, reqVO.getDisplayStatus());
        }

        return selectPage(reqVO,
            query.orderByAsc(TeacherDO::getSort).orderByDesc(TeacherDO::getId));
    }

    default TeacherDO selectByCountryCodeAndMobile(String countryCode, String mobile) {
        return selectOne(new LambdaQueryWrapperX<TeacherDO>()
            .eq(TeacherDO::getCountryCode, countryCode)
            .eq(TeacherDO::getMobile, mobile));
    }

} 