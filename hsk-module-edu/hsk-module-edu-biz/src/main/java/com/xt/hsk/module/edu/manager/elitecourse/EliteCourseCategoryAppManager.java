package com.xt.hsk.module.edu.manager.elitecourse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.enums.IsEnum;
import com.xt.hsk.module.edu.controller.app.elitecourse.vo.EliteCourseCategoryAppRespVO;
import com.xt.hsk.module.edu.controller.app.elitecourse.vo.HskLevelCategoryVO;
import com.xt.hsk.module.edu.convert.elitecourse.EliteCourseCategoryConvert;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseCategoryDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseDO;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseListingStatusEnum;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseCategoryService;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseService;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

/**
 * 精品课分类 APP端 Manager，负责APP端业务逻辑的编排
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Component
public class EliteCourseCategoryAppManager {

    @Resource
    private EliteCourseCategoryService eliteCourseCategoryService;

    @Resource
    private EliteCourseService eliteCourseService;

    /**
     * 获取展示的精品课分类列表 仅返回存在上架且显示的精品课的分类
     * 支持按HSK等级筛选
     * 展示ALL分类逻辑 ->
     * 1.没有任何上架展示课程时，分类列表为空
     * 2.有课程 没有挂任何分类时 拼一个ALL
     * 3.有课程 有挂分类时 拼一个ALL
     *
     * @param hskLevel HSK等级，如果为null则返回所有分类
     * @return 精品课分类列表
     */
    public List<EliteCourseCategoryAppRespVO> getShowCategories(Integer hskLevel) {
        List<EliteCourseCategoryDO> categories = eliteCourseCategoryService.getShowCategories(
            hskLevel);
        if (categories.isEmpty()) {
            return Collections.emptyList();
        }

        return EliteCourseCategoryConvert.INSTANCE.doListToAppRespVOList(categories);
    }

    /**
     * 按HSK等级获取分类列表 仅返回存在已上架且显示的精品课的分类，按HSK等级分组
     *
     * @return HSK等级分类列表
     */
    public List<HskLevelCategoryVO> getHskLevelCategories() {
        // 1. 直接查询已上架且显示的课程，并且预先关联分类表获取必要数据
        List<EliteCourseDO> courses = eliteCourseService.list(
            new LambdaQueryWrapper<EliteCourseDO>()
                .eq(EliteCourseDO::getListingStatus, EliteCourseListingStatusEnum.LISTED.getCode())
                .eq(EliteCourseDO::getIsShow, IsEnum.YES.getCode())
                .select(EliteCourseDO::getHskLevel, EliteCourseDO::getPrimaryCategoryId));

        if (courses.isEmpty()) {
            return Collections.emptyList();
        }

        // 2. 一次性创建 ALL 分类对象
        EliteCourseCategoryDO allCategory = buildAllCategory();
        EliteCourseCategoryAppRespVO allCategoryVO =
            EliteCourseCategoryConvert.INSTANCE.doToAppRespVO(allCategory);

        // 3. 获取有效HSK等级和分类ID，并预处理
        Map<Integer, Set<Long>> hskToCategoryIds = courses.stream()
            .filter(course -> course.getHskLevel() != null)
            .collect(Collectors.groupingBy(
                EliteCourseDO::getHskLevel,
                Collectors.mapping(EliteCourseDO::getPrimaryCategoryId,
                    Collectors.filtering(Objects::nonNull, Collectors.toSet()))));

        // 4. 批量查询所有相关分类
        Set<Long> allCategoryIds = hskToCategoryIds.values().stream()
            .flatMap(Set::stream)
            .collect(Collectors.toSet());

        Map<Long, EliteCourseCategoryDO> categoryMap = allCategoryIds.isEmpty() ?
            Collections.emptyMap() :
            eliteCourseCategoryService.list(new LambdaQueryWrapper<EliteCourseCategoryDO>()
                    .in(EliteCourseCategoryDO::getId, allCategoryIds)
                    .orderByAsc(EliteCourseCategoryDO::getSort))
                .stream()
                .collect(Collectors.toMap(EliteCourseCategoryDO::getId, category -> category));

        // 5. 构建结果
        List<HskLevelCategoryVO> result = new ArrayList<>();
        for (HskEnum hskEnum : HskEnum.values()) {
            if (hskEnum == HskEnum.HSK_0 || !hskToCategoryIds.containsKey(hskEnum.getCode())) {
                continue;
            }

            HskLevelCategoryVO hskLevelCategory = new HskLevelCategoryVO();
            hskLevelCategory.setHskLevel(hskEnum.getCode());
            hskLevelCategory.setHskLevelDesc(hskEnum.getDesc());

            // 构建分类列表
            List<EliteCourseCategoryAppRespVO> categories = new ArrayList<>();
            // 添加ALL分类
            categories.add(allCategoryVO);

            // 添加当前HSK等级下的其他分类
            Set<Long> categoryIds = hskToCategoryIds.get(hskEnum.getCode());
            if (!categoryIds.isEmpty()) {
                categories.addAll(categoryIds.stream()
                    .map(categoryMap::get)
                    .filter(Objects::nonNull)
                    .map(EliteCourseCategoryConvert.INSTANCE::doToAppRespVO)
                    .toList());
            }

            // 对分类列表按sort字段从小到大排序
            Optional.of(categories)
                .filter(list -> !list.isEmpty())
                .ifPresent(list -> list.sort(Comparator.comparing(
                    EliteCourseCategoryAppRespVO::getSort, 
                    Comparator.nullsLast(Integer::compareTo))));

            hskLevelCategory.setCategories(categories);
            result.add(hskLevelCategory);
        }

        return result;
    }

    /**
     * 构建ALL分类
     *
     * @return ALL分类
     */
    private EliteCourseCategoryDO buildAllCategory() {
        return EliteCourseCategoryDO.builder()
            .nameCn("全部")
            .nameEn("All")
            .nameOt("Toàn bộ")
            // 只要是ALL 排序永远在最前面
            .sort(0)
            .build();
    }
} 