package com.xt.hsk.module.edu.dal.mysql.sourceword;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordExamplePageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.sourceword.SourceWordExampleDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 释义关联例句 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SourceWordExampleMapper extends BaseMapperX<SourceWordExampleDO> {

    default PageResult<SourceWordExampleDO> selectPage(WordExamplePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SourceWordExampleDO>()
                .eqIfPresent(SourceWordExampleDO::getMeaningId, reqVO.getMeaningId())
                .eqIfPresent(SourceWordExampleDO::getWordId, reqVO.getWordId())
                .eqIfPresent(SourceWordExampleDO::getWord, reqVO.getWord())
                .eqIfPresent(SourceWordExampleDO::getExampleType, reqVO.getExampleType())
                .eqIfPresent(SourceWordExampleDO::getTranslationCn, reqVO.getTranslationCn())
                .eqIfPresent(SourceWordExampleDO::getTranslationEn, reqVO.getTranslationEn())
                .eqIfPresent(SourceWordExampleDO::getPinyinWithTone, reqVO.getPinyinWithTone())
                .eqIfPresent(SourceWordExampleDO::getPinyinWithoutTone, reqVO.getPinyinWithoutTone())
                .eqIfPresent(SourceWordExampleDO::getAudioId, reqVO.getAudioId())
                .orderByDesc(SourceWordExampleDO::getExampleId));
    }

}