package com.xt.hsk.module.edu.service.userselfassessmentrecord;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.edu.dal.dataobject.userselfassessmentrecord.UserSelfAssessmentRecordDO;
import com.xt.hsk.framework.common.pojo.PageResult;
import jakarta.validation.Valid;
import com.xt.hsk.module.edu.controller.app.userselfassessmentrecord.vo.UserSelfAssessmentRecordPageReqVO;

/**
 * 用户等级自测记录 Service 接口
 *
 * <AUTHOR>
 */
public interface UserSelfAssessmentRecordService extends IService<UserSelfAssessmentRecordDO> {
   PageResult<UserSelfAssessmentRecordDO> selectPage(@Valid UserSelfAssessmentRecordPageReqVO pageReqVO);

}