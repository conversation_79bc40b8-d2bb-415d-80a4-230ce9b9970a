package com.xt.hsk.module.edu.dal.dataobject.exam;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import com.xt.hsk.module.edu.enums.exam.ExamQuestionTypeUnitEnum;
import lombok.*;

/**
 * 模考详情 DO
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@TableName("edu_exam_detail")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExamDetailDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 模考id
     */
    private Long examId;
    /**
     * 科目
     *
     * @see SubjectEnum
     */
    private Integer subject;
    /**
     * 单元部分
     *
     * @see ExamQuestionTypeUnitEnum
     */
    private Integer unit;
    /**
     * 模考题型ID
     */
    private Long examQuestionTypeId;
    /**
     * 题型id列表
     */
    private String questionTypeIds;
    /**
     * 题型名称列表
     */
    private String questionNames;
    /**
     * 题目数量
     */
    private Integer questionCount;
    /**
     * 题目信息
     */
    private String questions;
    /**
     * 版本号
     */
    private Integer version;

}