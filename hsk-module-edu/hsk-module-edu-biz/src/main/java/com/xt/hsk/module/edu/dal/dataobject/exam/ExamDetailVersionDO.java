package com.xt.hsk.module.edu.dal.dataobject.exam;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 模考详情版本库 DO
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@TableName("edu_exam_detail_version")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExamDetailVersionDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 模考id
     */
    private Long examId;
    /**
     * 模考详情id
     */
    private Long examDetailId;
    /**
     * 科目
     */
    private Integer subject;
    /**
     * 单元部分
     */
    private Integer unit;
    /**
     * 模考题型ID
     */
    private Long examQuestionTypeId;
    /**
     * 题型id列表
     */
    private String questionTypeIds;
    /**
     * 题型名称列表
     */
    private String questionNames;
    /**
     * 版本号
     */
    private Integer version;
    /**
     * 题目数量
     */
    private Integer questionCount;
    /**
     * 题目信息
     */
    private String questions;

}