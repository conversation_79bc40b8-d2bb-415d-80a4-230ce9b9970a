package com.xt.hsk.module.edu.convert.interactivecourse;

import com.xt.hsk.framework.common.util.i18n.LanguageUtils;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitRespVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitSaveReqVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.InteractiveCourseUnitAppDetailsVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.InteractiveCourseUnitAppRespVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.LastRecordInfoVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.LastVideoRecordInfoVO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseUnitDO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.UserInteractiveCourseRecordDO;
import java.util.List;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * 互动课单元转换类
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Mapper
public interface InteractiveCourseUnitConvert {

    InteractiveCourseUnitConvert INSTANCE = Mappers.getMapper(InteractiveCourseUnitConvert.class);

    /**
     * DO类转列表VO类
     */
    List<InteractiveCourseUnitRespVO> doListToRespVOList(List<InteractiveCourseUnitDO> interactiveCourseUnitDO);

    /**
     * DO类转响应VO类
     */
    InteractiveCourseUnitRespVO doToRespVO(InteractiveCourseUnitDO interactiveCourseUnitDO);


    /**
     * DO类转APP响应VO类
     */
    InteractiveCourseUnitAppDetailsVO doToAppRespVO(InteractiveCourseUnitDO interactiveCourseUnitDO);

    /**
     * DO类转APP响应VO类
     */
    List<InteractiveCourseUnitAppRespVO> doListToAppPageVOList(
        List<InteractiveCourseUnitDO> courseUnitDOList);

        
    /**
     * 互动课程记录DO转视频记录信息VO
     */
    @Mapping(source = "id", target = "videoRecordId")
    LastVideoRecordInfoVO recordToLastVideoRecordInfoVO(UserInteractiveCourseRecordDO recordDO);

    @AfterMapping
    default void fillLocalizedFields(
        InteractiveCourseUnitDO bean, @MappingTarget InteractiveCourseUnitAppRespVO respVO) {
        // 这段代码会在基本映射完成后执行
        // 可以访问源对象bean的所有属性
        // 可以修改目标对象respVO的属性
        respVO.setUnitName(LanguageUtils.getLocalizedValue(
            bean.getUnitNameCn(),
            bean.getUnitNameEn(),
            bean.getUnitNameOt()));
    }

    @AfterMapping
    default void fillLocalizedFields(
        InteractiveCourseUnitDO bean, @MappingTarget InteractiveCourseUnitAppDetailsVO respVO) {
        // 这段代码会在基本映射完成后执行
        // 可以访问源对象bean的所有属性
        // 可以修改目标对象respVO的属性
        respVO.setUnitName(LanguageUtils.getLocalizedValue(
            bean.getUnitNameCn(),
            bean.getUnitNameEn(),
            bean.getUnitNameOt()));
    }

    /**
     * 创建请求类转DO类
     */
    InteractiveCourseUnitDO doToDataObject(InteractiveCourseUnitSaveReqVO createReqVO);

    /**
     * 互动课程记录DO转记录信息VO
     */
    LastRecordInfoVO recordToLastRecordInfoVO(UserInteractiveCourseRecordDO recordDO);
}