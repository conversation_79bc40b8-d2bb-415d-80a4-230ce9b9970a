package com.xt.hsk.module.edu.enums.exam;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模考练习状态枚举
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Getter
@AllArgsConstructor
public enum ExamRecordPracticeStatusEnum implements BasicEnum<Integer>, VO {
    /**
     * 模考练习状态枚举
     */
    IN_PROGRESS(1, "进行中"),
    COMPLETED(2, "已完成"),
    NOT_STARTED(3, "未开始");

    private final Integer code;
    private final String desc;
}
