package com.xt.hsk.module.edu.dal.dataobject.exam;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import com.xt.hsk.module.edu.enums.exam.ExamPublishStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import lombok.*;

/**
 * 模考 DO
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@TableName("edu_exam")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExamDO extends BaseDO {

    /**
     * 模考ID
     */
    @TableId
    private Long id;
    /**
     * HSK等级
     *
     * @see ExamTypeEnum
     */
    private Integer hskLevel;
    /**
     * 模考组卷规则id
     */
    private Long paperRuleId;
    /**
     * 模考名称
     */
    private String name;
    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     */
    private Integer type;
    /**
     * 模考封面图片URL
     */
    private String coverUrl;
    /**
     * 模考描述
     */
    private String description;
    /**
     * 听力考试时长 (秒)
     */
    private Integer listeningDuration;
    /**
     * 阅读考试时长 (秒)
     */
    private Integer readingDuration;
    /**
     * 书写考试时长 (秒)
     */
    private Integer writingDuration;
    /**
     * 排序序号
     */
    private Integer sort;
    /**
     * 已参加考试人数
     */
    private Integer examCount;
    /**
     * 总分
     */
    private Integer totalScore;
    /**
     * 发布状态：0-未发布 1-已发布 2-已下架
     *
     * @see ExamPublishStatusEnum
     */
    private Integer publishStatus;
    /**
     * 模块详情版本号
     */
    private Integer examDetailVersion;

}