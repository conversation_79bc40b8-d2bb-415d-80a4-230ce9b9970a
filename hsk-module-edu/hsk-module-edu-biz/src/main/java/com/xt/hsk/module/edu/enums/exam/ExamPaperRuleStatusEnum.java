package com.xt.hsk.module.edu.enums.exam;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 模考组卷规则状态枚举
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Getter
@AllArgsConstructor
public enum ExamPaperRuleStatusEnum implements BasicEnum<Integer>, VO, ArrayValuable<Integer> {
    /**
     * 模考组卷规则状态枚举
     */
    DISABLED(0, "禁用"),
    ENABLED(1, "启用"),
    ;

    private static final Integer[] ARRAYS = Arrays.stream(values()).map(ExamPaperRuleStatusEnum::getCode).toArray(Integer[]::new);
    private final Integer code;
    private final String desc;

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

    /**
     * 根据模考组卷规则状态码获取对应的描述信息
     *
     * @param code 模考组卷规则状态码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (ExamPaperRuleStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据模考组卷规则状态码获取对应的枚举实例
     *
     * @param code 模考组卷规则状态码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static ExamPaperRuleStatusEnum getByCode(Integer code) {
        for (ExamPaperRuleStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}