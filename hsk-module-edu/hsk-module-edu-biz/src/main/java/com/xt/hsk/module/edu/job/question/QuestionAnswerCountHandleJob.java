package com.xt.hsk.module.edu.job.question;


import cn.hutool.core.date.DateUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.xt.hsk.framework.quartz.core.handler.JobHandler;
import com.xt.hsk.framework.redis.utils.RedisUtil;
import com.xt.hsk.module.edu.dal.dataobject.question.QuestionDO;
import com.xt.hsk.module.edu.service.question.QuestionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.xt.hsk.framework.common.constants.RedisKeyPrefix.*;

/**
 * 题目回答数处理任务
 */
@Component
@Slf4j
public class QuestionAnswerCountHandleJob implements JobHandler {

    private final int BATCH_SIZE = 300;
    @Resource
    private QuestionService questionService;
    @Resource
    private RedisUtil redisUtil;

    //    @Override
//    @Lock4j(name = "questionAnswerCountHandleJob")
//    public String execute(String param) throws Exception {
//        log.info("开始处理题目回答数任务...");
//        // 批量获取待处理的题目id+回答数+正确数
//        Long startId = 0L;
//        Long maxId = questionService.getMaxId();
//        String date = DateUtil.format(DateUtil.yesterday(), "yyyy-MM-dd");
//        while (startId <= maxId) {
//            List<QuestionJobVo> questionJobVos = questionService.getQuestionJobVos(startId, BATCH_SIZE);
//            List<QuestionDO> needUpdateList = new ArrayList<>();
//            questionJobVos.forEach(item -> {
//                log.info("{}处理题目id:{}", date, item.getId());
//
//                String answerCountKey = String.format(QUESTION_ANSWER_COUNT, date, item.getId());
//                String answerCorrectCountKey = String.format(QUESTION_ANSWER_CORRECT_COUNT, date, item.getId());
//                try {
//                    Integer answerCount = item.getTotalAnswerCount();
//                    Integer answerCorrectCount = item.getCorrectAnswerCount();
//                    Integer todayAnswerCount = redisUtil.getInteger(answerCountKey);
//                    if (todayAnswerCount != null && todayAnswerCount > 0) {
//                        answerCount += todayAnswerCount;
//                        answerCorrectCount += redisUtil.getInteger(answerCorrectCountKey);
//                        QuestionDO questionDO = new QuestionDO();
//                        questionDO.setId(item.getId());
//                        questionDO.setTotalAnswerCount(answerCount);
//                        questionDO.setCorrectAnswerCount(answerCorrectCount);
//                        needUpdateList.add(questionDO);
//                    }
//                } catch (Exception e) {
//                    log.error("统计题目正确率Redis操作失败: keys={}, {}", answerCountKey, answerCorrectCountKey, e);
//                } finally {
//                    redisUtil.delete(answerCountKey);
//                    redisUtil.delete(answerCorrectCountKey);
//                }
//
//            });
//            questionService.updateBatchById(needUpdateList);
//            startId = questionJobVos.stream().map(QuestionJobVo::getId).max(Long::compareTo).get();
//            if (questionJobVos.size() < BATCH_SIZE) {
//                break;
//            }
//        }
//
//        return "结束处理题目回答数任务...";
//    }
    @Override
    @Lock4j(name = "questionAnswerCountHandleJob")
    public String execute(String param) throws Exception {
        try {
            log.info("开始处理题目回答数任务...");
            String date = DateUtil.format(DateUtil.yesterday(), "yyyy-MM-dd");
            String answerKeyPrefix = String.format(HSK + "question:answer_count:%s:", date);
            long counted = redisUtil.countKeysByPrefix(answerKeyPrefix);

            // 批量获取待处理的题目id+回答数+正确数
            long handleCount = 0L;
            while (handleCount < counted) { // 修正循环条件
                List<String> keys = redisUtil.scanKeysByPrefix(answerKeyPrefix, BATCH_SIZE);
                if (keys == null || keys.isEmpty()) {
                    break;
                }
                // 获取所有key中的题目id
                List<Long> questionIds = new ArrayList<>();
                for (String key : keys) {
                    String questionId = key.substring(answerKeyPrefix.length());
                    questionIds.add(Long.valueOf(questionId));
                }
                List<QuestionJobVo> questionJobVos = questionService.getQuestionJobVos(questionIds);
                List<QuestionDO> needUpdateList = new ArrayList<>();
                questionJobVos.forEach(item -> {
                    questionIds.remove(item.getId());
                    log.info("{}处理题目id:{}", date, item.getId());
                    String answerCountKey = String.format(QUESTION_ANSWER_COUNT, date, item.getId());
                    String answerCorrectCountKey = String.format(QUESTION_ANSWER_CORRECT_COUNT, date, item.getId());
                    try {
                        Integer answerCount = item.getTotalAnswerCount();
                        Integer answerCorrectCount = item.getCorrectAnswerCount();
                        Integer todayAnswerCount = redisUtil.getInteger(answerCountKey);
                        if (todayAnswerCount != null && todayAnswerCount > 0) {
                            answerCount += todayAnswerCount;
                            answerCorrectCount += redisUtil.getInteger(answerCorrectCountKey);
                            QuestionDO questionDO = new QuestionDO();
                            questionDO.setId(item.getId());
                            questionDO.setTotalAnswerCount(answerCount);
                            questionDO.setCorrectAnswerCount(answerCorrectCount);
                            needUpdateList.add(questionDO);
                        }
                    } catch (Exception e) {
                        log.error("统计题目正确率Redis操作失败: answerCountKey={}, answerCorrectCountKey={}", answerCountKey, answerCorrectCountKey, e);
                    } finally {
                        // 只有在处理成功后才删除key
                        redisUtil.delete(answerCountKey);
                        redisUtil.delete(answerCorrectCountKey);
                    }
                });
                if (!questionIds.isEmpty()) {
                    log.error("题目回答数处理任务，Redis中存在未处理的题目id:{}", questionIds);
                    for (Long id : questionIds) {
                        redisUtil.delete(String.format(QUESTION_ANSWER_COUNT, date, id));
                        redisUtil.delete(String.format(QUESTION_ANSWER_CORRECT_COUNT, date, id));
                    }
                }

                questionService.updateBatchById(needUpdateList);
                handleCount += keys.size();
                if (keys.size() < BATCH_SIZE) {
                    break;
                }
                // 休息1秒
                Thread.sleep(1000);
            }

            return String.format("结束处理题目回答数任务... 处理题目数: {}", handleCount);
        } catch (Exception e) {
            log.error("题目回答数处理任务异常中断", e);
            throw e;
        }
    }
}
