package com.xt.hsk.module.edu.dal.dataobject.word;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;

/**
 * 词语标签 DO
 *
 * <AUTHOR>
 */
@TableName("edu_word_tag")
@KeySequence("edu_word_tag_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WordTagDO extends BaseDO {

    /**
     * ID（自增）
     */
    @TableId
    private Long id;
    /**
     * words的id
     */
    private Long wordId;
    /**
     * 汉字/词语 冗余一部分字词的信息，避开连表
     */
    private String word;
    /**
     * 拼音（如ài）
     */
    private String pinyin;
    /**
     * 标签id）
     */
    private Long tagId;
    /**
     * 是否删除
     */
    private Boolean deleted;

}