package com.xt.hsk.module.edu.dal.dataobject.exam;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.AppBaseDO;
import com.xt.hsk.module.edu.enums.exam.ExamCorrectionStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamRecordPracticeStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamSubjectSectionsEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户模考记录 DO
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@TableName("edu_user_exam_record")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserExamRecordDO extends AppBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 模考id
     */
    private Long examId;
    /**
     * HSK等级
     */
    private Integer hskLevel;
    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    private Integer examType;
    /**
     * 参与的模考科目 0-完整模考 1-听力 2-阅读 4-书写
     *
     * @see ExamSubjectSectionsEnum
     */
    private Integer examSections;
    /**
     * 总分
     */
    private Integer totalScore;
    /**
     * 总得分
     */
    private Integer actualScore;
    /**
     * 听力得分
     */
    private Integer listeningScore;
    /**
     * 阅读得分
     */
    private Integer readingScore;
    /**
     * 书写得分
     */
    private Integer writingScore;
    /**
     * 题目总数量
     */
    private Integer questionNum;
    /**
     * 已作答数量
     */
    private Integer answerNum;
    /**
     * 已正确数量
     */
    private Integer correctNum;
    /**
     * 已错误数量
     */
    private Integer wrongNum;
    /**
     * 未答数量
     */
    private Integer unansweredNum;
    /**
     * 作答总耗时（秒）
     */
    private Integer answerTime;
    /**
     * 开始作答时间
     */
    private LocalDateTime startTime;
    /**
     * 结束作答时间
     */
    private LocalDateTime endTime;
    /**
     * 批改状态 1进行中 2待批改 3已批改
     *
     * @see ExamCorrectionStatusEnum
     */
    private Integer correctionStatus;
    /**
     * 本练习记录是否为最新数据 0 否 1 是
     */
    private Integer isNewest;
    /**
     * 练习状态 1进行中 2已完成
     *
     * @see ExamRecordPracticeStatusEnum
     */
    private Integer practiceStatus;
    /**
     * 进度
     */
    private BigDecimal progress;

    /**
     * 听力剩余时长（秒）
     */
    private Integer listeningRemainingTime;

    /**
     * 阅读剩余时长（秒）
     */
    private Integer readingRemainingTime;

    /**
     * 书写剩余时长（秒）
     */
    private Integer writingRemainingTime;


}