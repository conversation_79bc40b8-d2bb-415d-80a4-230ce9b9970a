package com.xt.hsk.module.edu.service.userquestionanswerrecord;

import static com.xt.hsk.framework.common.constants.RedisKeyPrefix.WRITING_AI_CORRECTION_COUNT;
import static com.xt.hsk.framework.common.enums.QuestionTypeEnum.getFromMaterialQuestionTypeList;
import static com.xt.hsk.framework.common.enums.QuestionTypeEnum.getNeedShuffleQuestionTypeList;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.DATA_NOT_EXIST;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.LOCKED;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.QUESTION_ANSWERED_ERROR;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.QUESTION_ANSWERED_ERROR_TWO;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.QUESTION_DATA_ERROR;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.TOO_MANY_REQUESTS;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xt.hsk.framework.common.constants.RedisKeyPrefix;
import com.xt.hsk.framework.common.enums.CommonStatusEnum;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.enums.PracticeModeEnum;
import com.xt.hsk.framework.common.enums.QuestionTypeEnum;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.framework.common.exception.ServiceException;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.i18n.LanguageUtils;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.framework.redis.utils.RedisUtil;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRespVO;
import com.xt.hsk.module.edu.controller.admin.userquestionanswerrecord.vo.UserQuestionAnswerRecordPageReqVO;
import com.xt.hsk.module.edu.controller.admin.userquestionanswerrecord.vo.UserQuestionAnswerRecordSaveReqVO;
import com.xt.hsk.module.edu.controller.app.question.vo.AppOptionContentVO;
import com.xt.hsk.module.edu.controller.app.question.vo.AppQuestionDetailVO;
import com.xt.hsk.module.edu.controller.app.question.vo.AppQuestionVo;
import com.xt.hsk.module.edu.controller.app.question.vo.AppUserAnswerDetailSaveVO;
import com.xt.hsk.module.edu.controller.app.question.vo.AppUserAnswerSaveVO;
import com.xt.hsk.module.edu.controller.app.question.vo.AppUserPracticeRecordRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.AppUserQuestionAnswerData;
import com.xt.hsk.module.edu.controller.app.question.vo.AppUserQuestionStatusVO;
import com.xt.hsk.module.edu.controller.app.question.vo.CallAiCorrectionReqVO;
import com.xt.hsk.module.edu.controller.app.question.vo.ChapterQuestionCountVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionSearchReqVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionStatisticsRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionTypeCountRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionTypeCountVO;
import com.xt.hsk.module.edu.controller.app.question.vo.SortQuestionAnswerVo;
import com.xt.hsk.module.edu.controller.app.question.vo.TextbookChapterQuestionCountVO;
import com.xt.hsk.module.edu.controller.app.question.vo.TextbookChapterQuestionRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.UnitQuestionCountVO;
import com.xt.hsk.module.edu.dal.dataobject.chapter.ChapterDO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseUnitResourceRelDO;
import com.xt.hsk.module.edu.dal.dataobject.question.QuestionDO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiondetail.QuestionDetailDO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiondetailversion.QuestionDetailVersionDO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiontype.QuestionTypeDO;
import com.xt.hsk.module.edu.dal.dataobject.question.questionversion.QuestionVersionDO;
import com.xt.hsk.module.edu.dal.dataobject.textbook.TextbookDO;
import com.xt.hsk.module.edu.dal.dataobject.userpracticerecord.UserPracticeRecordDO;
import com.xt.hsk.module.edu.dal.dataobject.userquestionanswerdata.UserQuestionAnswerDataDO;
import com.xt.hsk.module.edu.dal.dataobject.userquestionanswerrecord.UserQuestionAnswerRecordDO;
import com.xt.hsk.module.edu.enums.interactivecourse.UnitResourceTypeEnum;
import com.xt.hsk.module.edu.enums.question.QuestionRecordStatusEnum;
import com.xt.hsk.module.edu.producer.QuestionProducer;
import com.xt.hsk.module.edu.service.chapter.ChapterService;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseUnitResourceRelService;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseUnitService;
import com.xt.hsk.module.edu.service.question.QuestionService;
import com.xt.hsk.module.edu.service.question.questiondetail.QuestionDetailService;
import com.xt.hsk.module.edu.service.question.questiondetailversion.QuestionDetailVersionService;
import com.xt.hsk.module.edu.service.question.questiontype.QuestionTypeService;
import com.xt.hsk.module.edu.service.question.questionversion.QuestionVersionService;
import com.xt.hsk.module.edu.service.textbook.TextbookService;
import com.xt.hsk.module.edu.service.unit.UnitService;
import com.xt.hsk.module.edu.service.userpracticerecord.UserPracticeRecordService;
import com.xt.hsk.module.edu.service.userquestionanswerrecord.userquestionanswerdata.UserQuestionAnswerDataService;
import com.xt.hsk.module.infra.api.config.ConfigApi;
import com.xt.hsk.module.thirdparty.api.CozeApi;
import com.xt.hsk.module.thirdparty.api.WriteAiCorrection.WritingAiCorrectionRecordApi;
import com.xt.hsk.module.thirdparty.api.aicorrectiontimes.AiCorrectionTimesApi;
import com.xt.hsk.module.thirdparty.dto.aiCorrection.AiCorrectionTimesSaveReqDto;
import com.xt.hsk.module.thirdparty.dto.coze.CozeCallDto;
import com.xt.hsk.module.thirdparty.dto.coze.CozeRespDto;
import com.xt.hsk.module.thirdparty.enums.AiCorrectBizTypeEnum;
import com.xt.hsk.module.user.api.AppUserApi;
import com.xt.hsk.module.user.enums.FavoriteSourceEnum;
import com.xt.hsk.module.user.favorite.UserFavoriteApi;
import com.xt.hsk.module.user.favorite.dto.UserFavoriteTargetDto;
import io.lettuce.core.RedisException;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;


/**
 * 用户题目作答记录 Manager
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class UserQuestionAnswerRecordManager {

    /**
     * 用户每日AI批改上限KEY
     */
    public static final String USER_AI_CORRECTION_COUNT_KEY = "ai:correction:question:max";

    @Resource
    private UserQuestionAnswerRecordService userQuestionAnswerRecordService;
    @Resource
    private UserQuestionAnswerDataService userQuestionAnswerDataService;
    @Resource
    private QuestionTypeService questionTypeService;
    @Resource
    private UnitService unitService;
    @Resource
    private QuestionService questionService;
    @Resource
    private UserPracticeRecordService userPracticeRecordService;
    @Resource
    private TextbookService textbookService;
    @Resource
    private ChapterService chapterService;
    @Resource
    private QuestionDetailService questionDetailService;
    @Resource
    private QuestionVersionService questionVersionService;
    @Resource
    private QuestionDetailVersionService questionDetailVersionService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private CozeApi cozeApi;
    @Resource
    private ConfigApi configApi;
    @Resource
    private WritingAiCorrectionRecordApi writingAiCorrectionRecordApi;
    @Resource
    private AiCorrectionTimesApi aiCorrectionTimesApi;
    @Resource
    private QuestionProducer questionProducer;
    @Resource
    private InteractiveCourseUnitResourceRelService interactiveCourseUnitResourceRelService;
    @Resource
    private AppUserApi appUserApi;
    @Resource
    private InteractiveCourseUnitService interactiveCourseUnitService;
    @Resource
    private UserFavoriteApi userFavoriteApi;


    // 生成随机数组
    public static int[] generateShuffledArray(int length) {
        // 1. 创建数组并填充顺序值
        int[] array = new int[length];
        for (int i = 0; i < length; i++) {
            array[i] = i;
        }

        // 2. 使用 Fisher-Yates 洗牌算法随机打乱顺序
        Random random = new Random();
        for (int i = length - 1; i > 0; i--) {
            int j = random.nextInt(i + 1);
            // 交换元素
            int temp = array[i];
            array[i] = array[j];
            array[j] = temp;
        }
        // 二次打乱，将在原位置的顺序再次打乱
        int swapIndex = -1;
        for (int i = 0; i < array.length; i++) {
            if (array[i] == i && swapIndex == -1) {
                swapIndex = i;
                continue;
            }
            if (array[i] == i && swapIndex != -1) {
                int temp = array[i];
                array[i] = array[swapIndex];
                array[swapIndex] = temp;
                swapIndex = -1;
            }
        }

        return array;
    }

    public Long createUserQuestionAnswerRecord(UserQuestionAnswerRecordSaveReqVO createReqVO) {
        // 插入
        UserQuestionAnswerRecordDO userQuestionAnswerRecord = BeanUtils.toBean(createReqVO, UserQuestionAnswerRecordDO.class);
        userQuestionAnswerRecordService.save(userQuestionAnswerRecord);

        // 返回
        return userQuestionAnswerRecord.getId();
    }

    public void updateUserQuestionAnswerRecord(UserQuestionAnswerRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateUserQuestionAnswerRecordExists(updateReqVO.getId());
        // 更新
        UserQuestionAnswerRecordDO updateObj = BeanUtils.toBean(updateReqVO, UserQuestionAnswerRecordDO.class);
        userQuestionAnswerRecordService.updateById(updateObj);
    }

    public void deleteUserQuestionAnswerRecord(Long id) {
        // 校验存在
        validateUserQuestionAnswerRecordExists(id);
        // 删除
        userQuestionAnswerRecordService.removeById(id);
    }

    private void validateUserQuestionAnswerRecordExists(Long id) {
        if (userQuestionAnswerRecordService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }

    public UserQuestionAnswerRecordDO getUserQuestionAnswerRecord(Long id) {
        return userQuestionAnswerRecordService.getById(id);
    }

    public PageResult<UserQuestionAnswerRecordDO> getUserQuestionAnswerRecordPage(@Valid UserQuestionAnswerRecordPageReqVO pageReqVO) {
        return userQuestionAnswerRecordService.selectPage(pageReqVO);
    }

    public QuestionStatisticsRespVO getQuestionStatistics(QuestionSearchReqVO reqVO) {
        QuestionStatisticsRespVO statistics = new QuestionStatisticsRespVO();
        if (reqVO.getUserId() != -1) {
            statistics = userPracticeRecordService.getQuestionNewestPracticeRecord(reqVO);
        }
        if (statistics == null) {
            statistics = new QuestionStatisticsRespVO();
        }
        List<QuestionTypeCountRespVO> questionTypeCountRespVOList = questionService.getQuestionTypeCountRespVOByUnitIds(reqVO);
        Map<Integer, List<QuestionTypeCountRespVO>> unitMap = questionTypeCountRespVOList.stream().collect(Collectors.groupingBy(QuestionTypeCountRespVO::getUnitSort));

        // 获取用户作答数据
        List<QuestionTypeCountRespVO> userAnswerCount = new ArrayList<>();
        if (reqVO.getUserId() != -1) {
            userAnswerCount = userPracticeRecordService.getUserUnitSortQuestionTypeCount(reqVO);
        }
        Map<Integer, List<QuestionTypeCountRespVO>> userAnswerMap = userAnswerCount.stream().collect(Collectors.groupingBy(QuestionTypeCountRespVO::getUnitSort));
        List<UnitQuestionCountVO> unitQuestionCountVOList = new ArrayList<>();

        // 获取题型名称
        List<QuestionTypeDO> questionTypeDOList = questionTypeService.list();
        Map<Long, QuestionTypeDO> questionTypeMap = questionTypeDOList.stream().collect(Collectors.toMap(QuestionTypeDO::getId, Function.identity(), (t1, t2) -> t1));
        for (Map.Entry<Integer, List<QuestionTypeCountRespVO>> entry : unitMap.entrySet()) {
            Integer sort = entry.getKey();
            List<QuestionTypeCountRespVO> unitSortList = entry.getValue();

            UnitQuestionCountVO unitQuestionCountVO = new UnitQuestionCountVO();
            unitQuestionCountVO.setUnitSort(sort);

            List<QuestionTypeCountRespVO> userAnswerList = userAnswerMap.get(sort);
            Map<Long, QuestionTypeCountRespVO> userAnswerCountMap = new HashMap<>();
            if (userAnswerList != null) {
                userAnswerCountMap = userAnswerList.stream().collect(Collectors.toMap(QuestionTypeCountRespVO::getTypeId, Function.identity(), (t1, t2) -> t1));

            }

            List<QuestionTypeCountVO> questionTypeCountVOList = new ArrayList<>();
            for (QuestionTypeCountRespVO questionTypeCountRespVO : unitSortList) {
                QuestionTypeCountVO questionTypeCountVO = new QuestionTypeCountVO();
                questionTypeCountVO.setTypeId(questionTypeCountRespVO.getTypeId());

                QuestionTypeDO questionTypeDO = questionTypeMap.get(questionTypeCountRespVO.getTypeId());
                questionTypeCountVO.setTypeName(LanguageUtils.getLocalizedValue(questionTypeDO.getNameCn(), questionTypeDO.getNameEn(), questionTypeDO.getNameOt()));

                questionTypeCountVO.setQuestionCount(questionTypeCountRespVO.getQuestionCount());

                // 用户答题数据
                if (userAnswerCountMap.containsKey(questionTypeCountRespVO.getTypeId())) {
                    QuestionTypeCountRespVO countRespVO = userAnswerCountMap.get(questionTypeCountRespVO.getTypeId());
                    questionTypeCountVO.setCompletedCount(countRespVO.getExerciseCount());
                    questionTypeCountVO.setCorrectCount(countRespVO.getCorrectCount());
                }
                questionTypeCountVOList.add(questionTypeCountVO);
            }
            questionTypeCountVOList.sort(Comparator.comparing(QuestionTypeCountVO::getTypeId));
            unitQuestionCountVO.setQuestionTypeCountVOList(questionTypeCountVOList);
            unitQuestionCountVOList.add(unitQuestionCountVO);
        }
        unitQuestionCountVOList.sort(Comparator.comparing(UnitQuestionCountVO::getUnitSort));
        statistics.setUnitQuestionCountVOList(unitQuestionCountVOList);

        return statistics;
    }

    public QuestionTypeCountRespVO getQuestionPracticeCountList(@Valid QuestionSearchReqVO reqVO) {
        QuestionTypeCountRespVO result = new QuestionTypeCountRespVO();
        // 题目总数
        List<QuestionTypeCountRespVO> questionTypeCountRespVOList = questionService.getQuestionTypeCountRespVOByUnitIds(reqVO);
        // 用户练习数
        if (StpUtil.isLogin()) {
            reqVO.setUserId(StpUtil.getLoginIdAsLong());
            List<QuestionTypeCountRespVO> userAnswerCount = userPracticeRecordService.getUserUnitSortQuestionTypeCount(reqVO);
            result.setExerciseCount(userAnswerCount != null && !userAnswerCount.isEmpty() ? userAnswerCount.get(0).getExerciseCount() : 0);
        }

        result.setQuestionCount(questionTypeCountRespVOList != null && !questionTypeCountRespVOList.isEmpty() && questionTypeCountRespVOList.get(0).getQuestionCount() != null ? questionTypeCountRespVOList.get(0).getQuestionCount() : 0);

        return result;
    }

    public List<TextbookChapterQuestionCountVO> getQuestionPracticeList(@Valid QuestionSearchReqVO reqVO) {
        // 获取包含 hskLevel subject unitSort的教材章节和题目数量
        List<TextbookChapterQuestionRespVO> respVOS = questionService.getTextbookChapterQuestions(reqVO);
        if (respVOS.isEmpty()) {
            return new ArrayList<>();
        }
        Map<Long, List<TextbookChapterQuestionRespVO>> textBookMap = respVOS.stream().collect(Collectors.groupingBy(TextbookChapterQuestionRespVO::getTextbookId));
        // 获取用户包含 hskLevel subject unitSort type_id 的做题记录
        List<TextbookChapterQuestionRespVO> userRecordRespVOS = userPracticeRecordService.getUserTextbookChapterQuestions(reqVO);
        if (userRecordRespVOS == null || userRecordRespVOS.isEmpty()) {
            userRecordRespVOS = new ArrayList<>();
        }
        // 获取是所有的练习记录
        List<Long> praticeIds = userRecordRespVOS.stream().map(TextbookChapterQuestionRespVO::getPracticeRecordId).toList();
        List<UserPracticeRecordDO> userPracticeRecordDOS = new ArrayList<>();
        if (!praticeIds.isEmpty()) {
            userPracticeRecordDOS = userPracticeRecordService.listByIds(praticeIds);
        }
        Map<Long, UserPracticeRecordDO> recordDOMap = userPracticeRecordDOS.stream().collect(Collectors.toMap(UserPracticeRecordDO::getId, v -> v, (v1, v2) -> v1));
        Map<Long, List<TextbookChapterQuestionRespVO>> userPracticeTexTBookMap = userRecordRespVOS.stream().collect(Collectors.groupingBy(TextbookChapterQuestionRespVO::getTextbookId));
        // 获取章节
        Set<Long> chapterIds = respVOS.stream().map(TextbookChapterQuestionRespVO::getChapterId).collect(Collectors.toSet());
        List<ChapterDO> chapterDOS = chapterService.listByIds(chapterIds);
        Map<Long, ChapterDO> chapterMap = chapterDOS.stream().collect(Collectors.toMap(ChapterDO::getId, Function.identity(), (k1, k2) -> k1));
        // 获取教材
        List<TextbookDO> textbooks = textbookService.listByIds(textBookMap.keySet());
        Map<Long, TextbookDO> textbookMap = textbooks.stream().collect(Collectors.toMap(TextbookDO::getId, Function.identity(), (k1, k2) -> k1));
        List<TextbookChapterQuestionCountVO> result = new ArrayList<>();
        for (Map.Entry<Long, List<TextbookChapterQuestionRespVO>> entry : textBookMap.entrySet()) {
            List<TextbookChapterQuestionRespVO> textbookChapterQuestionRespVOS = entry.getValue();
            if (textbookChapterQuestionRespVOS == null || textbookChapterQuestionRespVOS.isEmpty()) {
                continue;
            }
            textbookChapterQuestionRespVOS.removeIf(Objects::isNull);
            Long textBookId = entry.getKey();

            TextbookChapterQuestionCountVO textbookChapterQuestionCountVO = new TextbookChapterQuestionCountVO();
            textbookChapterQuestionCountVO.setTextbookId(entry.getKey());
            textbookChapterQuestionCountVO.setTextbookName(textbookMap.get(textBookId).getNameCn());
            textbookChapterQuestionCountVO.setSort(textbookMap.get(textBookId).getSort());

            List<TextbookChapterQuestionRespVO> userPracticeChapterList = userPracticeTexTBookMap.get(textBookId);
            if (userPracticeChapterList == null || userPracticeChapterList.isEmpty()) {
                userPracticeChapterList = new ArrayList<>();
            }
            Map<Long, TextbookChapterQuestionRespVO> userPracticeChapterMap = userPracticeChapterList.stream().collect(Collectors.toMap(TextbookChapterQuestionRespVO::getChapterId, Function.identity(), (v1, v2) -> v1));

            List<ChapterQuestionCountVO> chapterQuestionCountList = new ArrayList<>();
            for (TextbookChapterQuestionRespVO textbookChapterQuestionRespVO : textbookChapterQuestionRespVOS) {
                if (textbookChapterQuestionRespVO == null || textbookChapterQuestionRespVO.getQuestionCount() == null || textbookChapterQuestionRespVO.getQuestionCount() <= 0) {
                    continue;
                }

                TextbookChapterQuestionRespVO questionRespVO = userPracticeChapterMap.get(textbookChapterQuestionRespVO.getChapterId());
                if (reqVO.getStatus() == null || reqVO.getStatus().isEmpty() || reqVO.getStatus().contains(3) && questionRespVO == null || reqVO.getStatus().contains(2) && questionRespVO != null && recordDOMap.get(questionRespVO.getPracticeRecordId()).getRecordStatus() == QuestionRecordStatusEnum.SUBMITTED.getCode() || reqVO.getStatus().contains(1) && questionRespVO != null && recordDOMap.get(questionRespVO.getPracticeRecordId()).getRecordStatus() == QuestionRecordStatusEnum.IN_PROGRESS.getCode()) {

                    ChapterDO chapterDO = chapterMap.get(textbookChapterQuestionRespVO.getChapterId());
                    ChapterQuestionCountVO chapterQuestionCountVO = new ChapterQuestionCountVO();
                    chapterQuestionCountVO.setChapterId(textbookChapterQuestionRespVO.getChapterId());
                    chapterQuestionCountVO.setQuestionCount(textbookChapterQuestionRespVO.getQuestionCount());
                    chapterQuestionCountVO.setChapterName(LanguageUtils.getLocalizedValue(chapterDO.getChapterNameCn(), chapterDO.getChapterNameEn(), chapterDO.getChapterNameOt()));
                    chapterQuestionCountVO.setSort(chapterDO.getChapterOrder());

                    if (questionRespVO != null) {
                        chapterQuestionCountVO.setPracticeRecordId(questionRespVO.getPracticeRecordId() != null ? questionRespVO.getPracticeRecordId() : 0);
                        chapterQuestionCountVO.setCorrectCount(questionRespVO.getCorrectCount() != null ? questionRespVO.getCorrectCount() : 0);
                        chapterQuestionCountVO.setExerciseCount(questionRespVO.getExerciseCount() != null ? questionRespVO.getExerciseCount() : 0);
                        chapterQuestionCountVO.setErrorCount(chapterQuestionCountVO.getExerciseCount() - chapterQuestionCountVO.getCorrectCount());
                        if (recordDOMap.containsKey(questionRespVO.getPracticeRecordId())) {
                            chapterQuestionCountVO.setPracticeStatus(recordDOMap.get(questionRespVO.getPracticeRecordId()).getRecordStatus());
                        } else {
                            chapterQuestionCountVO.setPracticeStatus(3);
                        }
                    } else {
                        chapterQuestionCountVO.setPracticeStatus(3);
                        chapterQuestionCountVO.setPracticeRecordId(null);
                        chapterQuestionCountVO.setCorrectCount(0);
                        chapterQuestionCountVO.setExerciseCount(0);
                        chapterQuestionCountVO.setErrorCount(0);
                    }
                    chapterQuestionCountList.add(chapterQuestionCountVO);
                }

            }
            chapterQuestionCountList.sort(Comparator.comparing(ChapterQuestionCountVO::getSort).thenComparing(ChapterQuestionCountVO::getChapterId));
            textbookChapterQuestionCountVO.setChapterQuestionCountList(chapterQuestionCountList);
            result.add(textbookChapterQuestionCountVO);
        }
        result.removeIf(Objects::isNull);
        result.removeIf(t -> t.getChapterQuestionCountList().isEmpty());
        result.sort(Comparator.comparing(TextbookChapterQuestionCountVO::getSort).thenComparing(TextbookChapterQuestionCountVO::getTextbookId));
        return result;
    }

    @Lock4j(name = "startPractice", keys = {"#reqVO.userId", "#reqVO.practiceRecordId"}, acquireTimeout = 0)
    public AppUserPracticeRecordRespVO startPractice(QuestionSearchReqVO reqVO) {
        Long practiceRecordId = reqVO.getPracticeRecordId();
        AppUserPracticeRecordRespVO practiceRecord = null;

        boolean isFromInteractiveCourse = reqVO.getInteractiveCourseUnitId() != null;

        if (practiceRecordId != null && reqVO.getAnswerType() == 2) {// 只有继续作答才需要查找新数据，其他都是新增数据
            UserPracticeRecordDO recordDO = userPracticeRecordService.getById(practiceRecordId);
            if (recordDO == null) {
                throw exception(DATA_NOT_EXIST);
            }
            practiceRecord = BeanUtils.toBean(recordDO, AppUserPracticeRecordRespVO.class);
        }
//
//        if (reqVO.getAnswerType() == 2 && practiceRecord == null) {
//            // 尝试寻找未作答的记录
//            practiceRecord = userPracticeRecordService.getUserNotFinishedPracticeRecord(reqVO);
//        }
        if (practiceRecord == null) {// 生成新作答数据
            practiceRecord = new AppUserPracticeRecordRespVO();
            practiceRecord.setUserId(reqVO.getUserId());
            practiceRecord.setTextbookId(reqVO.getTextbookId());
            practiceRecord.setChapterId(reqVO.getChapterId());
            practiceRecord.setQuestionTypeId(reqVO.getTypeId());
            practiceRecord.setSubject(reqVO.getSubject());
            practiceRecord.setUnitSort(reqVO.getUnitSort());
            practiceRecord.setRecordStatus(1);
            practiceRecord.setHskLevel(reqVO.getHskLevel());
            practiceRecord.setStartTime(LocalDateTime.now());
            practiceRecord.setIsNewest(Boolean.TRUE);

            UserPracticeRecordDO recordDO = BeanUtils.toBean(practiceRecord, UserPracticeRecordDO.class);
            recordDO.setCreator(String.valueOf(reqVO.getUserId()));
            recordDO.setUpdater(String.valueOf(reqVO.getUserId()));
            recordDO.setPracticeMode(reqVO.getPracticeMode() == null ? 1 : reqVO.getPracticeMode());
            // 如果是来自互动课的练习，设置互动课单元ID 并且设置题目类型ID
            if (isFromInteractiveCourse) {
                Long questionTypeId = interactiveCourseUnitService.getUnitQuestionTypeId(
                    reqVO.getInteractiveCourseUnitId());
                recordDO.setQuestionTypeId(questionTypeId);
                recordDO.setInteractiveCourseUnitId(reqVO.getInteractiveCourseUnitId());
            }
            userPracticeRecordService.save(recordDO);
            practiceRecord.setId(recordDO.getId());

        }
        // 更新其他作答记录为非最新
        LambdaUpdateWrapper<UserPracticeRecordDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserPracticeRecordDO::getUserId, practiceRecord.getUserId())
                .eq(UserPracticeRecordDO::getTextbookId, practiceRecord.getTextbookId())
                .eq(UserPracticeRecordDO::getChapterId, practiceRecord.getChapterId())
                .eq(UserPracticeRecordDO::getHskLevel, practiceRecord.getHskLevel())
                .eq(UserPracticeRecordDO::getSubject, practiceRecord.getSubject())
                .eq(UserPracticeRecordDO::getUnitSort, practiceRecord.getUnitSort())
                .eq(UserPracticeRecordDO::getQuestionTypeId, practiceRecord.getQuestionTypeId())
                .eq(UserPracticeRecordDO::getUserId, practiceRecord.getUserId())
                .eq(UserPracticeRecordDO::getIsNewest, true)
                .ne(UserPracticeRecordDO::getId, practiceRecord.getId())
                .set(UserPracticeRecordDO::getIsNewest, false)
        ;
        userPracticeRecordService.update(updateWrapper);
        // ps 题目可能会被更新，所以每次请求获取最新的题目数量，在生成报告的时候再写入数据表中
        // 找到所有的题目明细id
        // =======================================从互动课进来要走的逻辑==============================
        List<Long> questionIds;
        if (isFromInteractiveCourse) {
            // 互动课来源：从互动课关联表查询
            // 互动课来源：从互动课关联表查询
            questionIds = getQuestionIdsFromInteractiveCourse(reqVO.getInteractiveCourseUnitId());
            // 发送互动课练习开始事件
            if (questionIds != null && !questionIds.isEmpty()) {
                try {
                    questionProducer.sendPracticeStartedEvent(
                            reqVO.getUserId(),
                            reqVO.getInteractiveCourseUnitId(),
                            questionIds,
                            practiceRecord.getId()
                    );
                } catch (Exception e) {
                    log.error("发送互动课练习开始事件失败: userId={}, unitId={}, practiceId={}",
                            reqVO.getUserId(), reqVO.getInteractiveCourseUnitId(), practiceRecord.getId(), e);
                }
            }
        } else {
            // 普通练习：使用原有逻辑
            questionIds = questionService.selectUserPracticeQuestions(reqVO);
        }

        if (questionIds == null || questionIds.isEmpty()) {
            throw exception(QUESTION_DATA_ERROR);
        }
        // 获取题目数
        LambdaQueryWrapper<QuestionDetailDO> questionQueryWrapper = new LambdaQueryWrapper<>();
        questionQueryWrapper.in(QuestionDetailDO::getQuestionId, questionIds);
        questionQueryWrapper.orderByAsc(QuestionDetailDO::getQuestionId);
        questionQueryWrapper.orderByAsc(QuestionDetailDO::getSort);
        List<QuestionDetailDO> detailDOS = questionDetailService.list(questionQueryWrapper);
        practiceRecord.setQuestionDetailIds(detailDOS.stream().map(QuestionDetailDO::getId).map(Object::toString).collect(Collectors.joining(",")));
        String questionIdsStr = questionIds.stream().map(String::valueOf).collect(Collectors.joining(","));
        practiceRecord.setQuestionIds(questionIdsStr);
        practiceRecord.setQuestionNum(detailDOS.size());
        // 查找用户作答数据
        LambdaQueryWrapper<UserQuestionAnswerRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserQuestionAnswerRecordDO::getUserId, practiceRecord.getUserId())
                .eq(UserQuestionAnswerRecordDO::getPracticeId, practiceRecord.getId())
                .eq(UserQuestionAnswerRecordDO::getPracticeMode, reqVO.getPracticeMode())
                .in(UserQuestionAnswerRecordDO::getQuestionId, questionIds)
                .orderByAsc(UserQuestionAnswerRecordDO::getId);
        List<UserQuestionAnswerRecordDO> answerRecordDOS = userQuestionAnswerRecordService.list(queryWrapper);
        if (answerRecordDOS == null || answerRecordDOS.isEmpty()) {
            return practiceRecord;
        }
        // 获取用户的作答明细
        Set<Long> userRecordIds = answerRecordDOS.stream().map(UserQuestionAnswerRecordDO::getId).collect(Collectors.toSet());
        LambdaQueryWrapper<UserQuestionAnswerDataDO> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.in(UserQuestionAnswerDataDO::getQuestionId, questionIds);
        queryWrapper1.in(UserQuestionAnswerDataDO::getRecordId, userRecordIds);
        queryWrapper1.orderByAsc(UserQuestionAnswerDataDO::getQuestionId);
        queryWrapper1.orderByAsc(UserQuestionAnswerDataDO::getQuestionDetailId);
        List<UserQuestionAnswerDataDO> userQuestionAnswerDataDOs = userQuestionAnswerDataService.list(queryWrapper1);

        Map<Long, List<UserQuestionAnswerDataDO>> userQuestionAnswerDataDOMap = userQuestionAnswerDataDOs.stream().collect(Collectors.groupingBy(UserQuestionAnswerDataDO::getRecordId));

//        List<String> answerQuestionIds = new ArrayList<>();
        List<String> answerQuestionDetailIds = new ArrayList<>();
        List<QuestionTypeEnum> writingQuestionTypeList = QuestionTypeEnum.getWritingQuestionTypeList();
        List<QuestionTypeEnum> singleQuestionTypeList = QuestionTypeEnum.getSingleQuestionTypeList();
        for (UserQuestionAnswerRecordDO answerRecordDO : answerRecordDOS) {
            List<UserQuestionAnswerDataDO> answerDataDOList = userQuestionAnswerDataDOMap.get(answerRecordDO.getId());
            if (answerDataDOList == null || answerDataDOList.isEmpty()) {
                continue;
            }
            if (writingQuestionTypeList.contains(QuestionTypeEnum.getByCode(answerRecordDO.getQuestionTypeId())) && answerRecordDO.getRecordStatus() != 1) {
//                answerQuestionIds.add(answerRecordDO.getQuestionId().toString());
                answerQuestionDetailIds.addAll(answerDataDOList.stream().map(UserQuestionAnswerDataDO::getQuestionDetailId).map(String::valueOf).collect(Collectors.toSet()));
            } else if (singleQuestionTypeList.contains(QuestionTypeEnum.getByCode(answerRecordDO.getQuestionTypeId()))) {
                answerQuestionDetailIds.addAll(answerDataDOList.stream().map(UserQuestionAnswerDataDO::getQuestionDetailId).map(String::valueOf).collect(Collectors.toSet()));
            } else if (!Objects.equals(answerRecordDO.getRecordStatus(), QuestionRecordStatusEnum.IN_PROGRESS.getCode())) {
//                answerQuestionIds.add(answerRecordDO.getQuestionId().toString());
                answerQuestionDetailIds.addAll(answerDataDOList.stream().map(UserQuestionAnswerDataDO::getQuestionDetailId).map(String::valueOf).collect(Collectors.toSet()));
            }
        }

        practiceRecord.setAnswerQuestionDetailIds(String.join(",", answerQuestionDetailIds));
//        Set<Long> userHalfAnswerIds = answerRecordDOS.stream().filter(answerRecordDO -> 1 == answerRecordDO.getRecordStatus()).map(UserQuestionAnswerRecordDO::getQuestionId).collect(Collectors.toSet());
//        practiceRecord.setAnswerQuestionIds(String.join( ",",answerQuestionIds));
//        practiceRecord.setHalfAnswerQuestionIds(userHalfAnswerIds.stream().map(String::valueOf).collect(Collectors.joining(",")));

        return practiceRecord;
    }


    /**
     * 从互动课关联表查询题目ID
     */
    private List<Long> getQuestionIdsFromInteractiveCourse(Long unitId) {
        // 查询互动课单元关联的题目ID
        List<InteractiveCourseUnitResourceRelDO> resourceRels = interactiveCourseUnitResourceRelService.lambdaQuery()
                .eq(InteractiveCourseUnitResourceRelDO::getUnitId, unitId)
                .eq(InteractiveCourseUnitResourceRelDO::getResourceType, UnitResourceTypeEnum.QUESTION.getCode())
                .list();

        if (CollUtil.isEmpty(resourceRels)) {
            return Collections.emptyList();
        }

        // 获取题目ID列表
        List<Long> questionIds = resourceRels.stream()
                .map(InteractiveCourseUnitResourceRelDO::getResourceId)
                .toList();

        // 验证题目状态，只返回未禁用的题目
        return questionService.lambdaQuery()
                .in(QuestionDO::getId, questionIds)
                .eq(QuestionDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                .select(QuestionDO::getId)
                .list()
                .stream()
                .map(QuestionDO::getId)
                .toList();
    }

    public List<AppQuestionVo> getQuestionDetails(QuestionSearchReqVO reqVO) {
        // 获取题目详情
        List<Long> questionIds = reqVO.getQuestionIds();
        if (questionIds == null || questionIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 保存原始的questionIds顺序，用于最后排序（因为后续处理中questionIds可能会被修改）
        List<Long> originalQuestionIds = new ArrayList<>(questionIds);
        // 获取所有题目的小题
        List<QuestionDetailDO> questionDetailDOS = new ArrayList<>();
        if (Objects.equals(reqVO.getPracticeMode(), PracticeModeEnum.EXAM.getCode()) || Objects.equals(reqVO.getPracticeMode(), PracticeModeEnum.THIRTY_MINUTE_EXAM.getCode()) || Objects.equals(reqVO.getPracticeMode(), PracticeModeEnum.FIFTEEN_MINUTE_EXAM.getCode())) {
            if (reqVO.getQuestionDetailIds() == null || reqVO.getQuestionDetailIds().isEmpty()) {
                throw new ServiceException(500, "模考练习必须传入questionDetailIds");
            }
            questionDetailDOS = questionDetailService.listByIds(reqVO.getQuestionDetailIds());
        } else {
            questionDetailDOS = questionDetailService.lambdaQuery()
                    .in(QuestionDetailDO::getQuestionId, questionIds)
                    .list();
        }
        // 获取用户作答数据
        List<AppUserQuestionAnswerData> answerData = new ArrayList<>();
        List<QuestionDO> questionDOS = new ArrayList<>();
        List<QuestionDetailDO> detailDOS = new ArrayList<>();
        if (StpUtil.isLogin() && reqVO.getPracticeRecordId() != null) {
            long userId = StpUtil.getLoginIdAsLong();
            LambdaQueryWrapper<UserQuestionAnswerRecordDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserQuestionAnswerRecordDO::getUserId, userId)
                    .eq(UserQuestionAnswerRecordDO::getPracticeId, reqVO.getPracticeRecordId())
                    .eq(UserQuestionAnswerRecordDO::getPracticeMode, reqVO.getPracticeMode());
            List<UserQuestionAnswerRecordDO> userQuestionAnswerRecordDOs = userQuestionAnswerRecordService.list(queryWrapper);
            if (userQuestionAnswerRecordDOs != null && !userQuestionAnswerRecordDOs.isEmpty()) {
                Set<Long> recordIds = userQuestionAnswerRecordDOs.stream().map(UserQuestionAnswerRecordDO::getId).collect(Collectors.toSet());
                LambdaQueryWrapper<UserQuestionAnswerDataDO> queryWrapper1 = new LambdaQueryWrapper<>();
                queryWrapper1.in(UserQuestionAnswerDataDO::getRecordId, recordIds);
                List<UserQuestionAnswerDataDO> answerDataDOS = userQuestionAnswerDataService.list(queryWrapper1);
                answerData = BeanUtils.toBean(answerDataDOS, AppUserQuestionAnswerData.class);

                questionIds.removeAll(userQuestionAnswerRecordDOs.stream().map(UserQuestionAnswerRecordDO::getQuestionId).collect(Collectors.toSet()));
                // 已作答題目使用歷史版本，未作答数据使用最新版本
                Set<Long> questionVersionIds = userQuestionAnswerRecordDOs.stream().map(UserQuestionAnswerRecordDO::getQuestionVersionId).collect(Collectors.toSet());
                List<QuestionVersionDO> questionVersionDOS = questionVersionService.listByIds(questionVersionIds);
                for (QuestionVersionDO questionVersionDO : questionVersionDOS) {
                    QuestionDO questionDO = BeanUtils.toBean(questionVersionDO, QuestionDO.class);
                    questionDO.setId(questionVersionDO.getQuestionId());
                    questionDOS.add(questionDO);
                }
                Set<Long> questionDetailVersionIds = answerDataDOS.stream().map(UserQuestionAnswerDataDO::getQuestionDetailVersionId).collect(Collectors.toSet());
                List<QuestionDetailVersionDO> questionDetailVersionDOS = questionDetailVersionService.listByIds(questionDetailVersionIds);
                for (QuestionDetailVersionDO detailDO : questionDetailVersionDOS) {
                    QuestionDetailDO questionDetailDO = BeanUtils.toBean(detailDO, QuestionDetailDO.class);
                    questionDetailDO.setId(detailDO.getQuestionDetailId());
                    detailDOS.add(questionDetailDO);
                }
            }

        }
        Map<Long, AppUserQuestionAnswerData> answerDataMap = answerData.stream().collect(Collectors.toMap(AppUserQuestionAnswerData::getQuestionDetailId, Function.identity(), (v1, v2) -> v1));
        if (!questionIds.isEmpty()) {
            questionDOS.addAll(questionService.listByIds(questionIds));
        }
        List<AppQuestionVo> questionVos = BeanUtils.toBean(questionDOS, AppQuestionVo.class);
        // 将json 转化为对象
        questionVos.forEach(questionVO -> questionVO.setOptionContents(JSON.parseArray(questionVO.getOptions(), AppOptionContentVO.class)));
        // 将未作答的数据补全到detailDOS中
        if (detailDOS.size() != questionDetailDOS.size()) {
            List<Long> detailIds = detailDOS.stream().map(QuestionDetailDO::getId).toList();
            for (QuestionDetailDO questionDetailDO : questionDetailDOS) {
                if (detailIds.contains(questionDetailDO.getId())) {
                    continue;
                }
                detailDOS.add(questionDetailDO);
            }
        }
        List<AppQuestionDetailVO> detailVOS = new ArrayList<>();
        for (QuestionDetailDO detailDO : detailDOS) {
            AppQuestionDetailVO detailVO = BeanUtils.toBean(detailDO, AppQuestionDetailVO.class);
            detailVO.setExplainText(LanguageUtils.getLocalizedValue(detailDO.getExplainTextCn(), detailDO.getExplainTextEn(), detailDO.getExplainTextOt()));
            detailVO.setOptionContents(JSON.parseArray(detailDO.getOptions(), AppOptionContentVO.class));

            if (answerDataMap.containsKey(detailDO.getId())) {
                AppUserQuestionAnswerData userAnswerDatas = answerDataMap.get(detailDO.getId());
                detailVO.setUserQuestionAnswerData(userAnswerDatas);
            }
            detailVOS.add(detailVO);
        }

        Map<Long, List<AppQuestionDetailVO>> questionDeatilMap = detailVOS.stream().collect(Collectors.groupingBy(AppQuestionDetailVO::getQuestionId));
        // 需要乱序的题型
        List<QuestionTypeEnum> needShuffleQuestionTypeList = getNeedShuffleQuestionTypeList();
        for (AppQuestionVo detailVO : questionVos) {
            List<AppQuestionDetailVO> detailVOList = questionDeatilMap.get(detailVO.getId());
            List<AppOptionContentVO> materialOptions = detailVO.getOptionContents();
            Long questionRecordId = null;
            Long typeId = detailVO.getTypeId();
            QuestionTypeEnum typeEnum = QuestionTypeEnum.getByCode(typeId);
            List<QuestionTypeEnum> questionTypeList = getFromMaterialQuestionTypeList();
            boolean fromMaterial = false;// 答案来源
            boolean needShuffle = false;// 需要打乱选项顺序
            if (questionTypeList.contains(typeEnum)) {
                fromMaterial = true;
            }
            if (needShuffleQuestionTypeList.contains(typeEnum)) {
                needShuffle = true;
            }
            if (detailVOList != null) {
                // 将用户的选项内容赋值到题目详情中
                for (AppQuestionDetailVO detailVO1 : detailVOList) {
                    List<AppOptionContentVO> attachmentOptions = detailVO1.getOptionContents();
                    AppUserQuestionAnswerData questionAnswerData = detailVO1.getUserQuestionAnswerData();
                    if (questionRecordId == null && questionAnswerData != null) {
                        questionRecordId = questionAnswerData.getRecordId();
                    }

                    if (fromMaterial) {
                        if (materialOptions == null) {
                            continue;
                        }
                        for (AppOptionContentVO optionContentVO : materialOptions) {
                            if (questionAnswerData != null) {
                                if (optionContentVO.getKey().equals(questionAnswerData.getUserAnswer())) {
                                    questionAnswerData.setUserAnswerContent(optionContentVO.getContent());
                                }
                            }
                            if (optionContentVO.getKey().equals(detailVO1.getAnswer())) {
                                detailVO1.setAnswerContent(optionContentVO.getContent());
                            }
                        }
                    } else {
                        if (attachmentOptions == null) {
                            continue;
                        }
                        if (needShuffle) {
                            StringBuilder answerContent = new StringBuilder();
                            StringBuilder userAnswerContent = new StringBuilder();

                            String answer = detailVO1.getAnswer();
                            Map<String, String> optMap = attachmentOptions.stream().collect(Collectors.toMap(AppOptionContentVO::getKey, AppOptionContentVO::getContent));

                            if (questionAnswerData != null) {
                                String userAnswer = questionAnswerData.getUserAnswer();
                                // 这儿需要解析
                                SortQuestionAnswerVo sortQuestionAnswerVo = JSON.parseObject(userAnswer, SortQuestionAnswerVo.class);

                                if (!userAnswer.isEmpty()) {
                                    // 处理用户答案为前端展示的顺序,前端展示的顺序 是题目按照顺序展示ABC,因为储存的是原来的key,所以前端回显时需要重新计算用户展示的选项
                                    String questionSort = sortQuestionAnswerVo.getQuestionSort();
                                    StringBuilder userAnswerShow = new StringBuilder();
                                    StringBuilder answerShow = new StringBuilder();
                                    char[] userAnswerCharArray = sortQuestionAnswerVo.getUserAnswerSort().toCharArray();
                                    for (int i = 0; i < userAnswerCharArray.length; i++) {
                                        if (optMap.containsKey(String.valueOf(userAnswerCharArray[i]))) {
                                            userAnswerContent.append(optMap.get(String.valueOf(userAnswerCharArray[i])));
                                        }
                                        userAnswerShow.append((char) ('A' + questionSort.indexOf(userAnswerCharArray[i])));
                                        answerShow.append((char) ('A' + questionSort.indexOf(answer.charAt(i))));
                                    }
                                    questionAnswerData.setUserAnswerShow(userAnswerShow.toString());
                                    questionAnswerData.setAnswerShow(answerShow.toString());
                                    // 还原选项顺序
                                    for (int i = 0; i < attachmentOptions.size(); i++) {
                                        attachmentOptions.get(i).setShowKey(String.valueOf((char) ('A' + questionSort.indexOf(attachmentOptions.get(i).getKey()))));
                                    }
                                    attachmentOptions.sort(Comparator.comparing(AppOptionContentVO::getShowKey));

                                }
                                questionAnswerData.setUserAnswerContent(userAnswerContent.toString());

                            } else {// 用户未作答就需要打乱选项顺序
                                int[] ints = generateShuffledArray(attachmentOptions.size());
                                for (int i = 0; i < attachmentOptions.size(); i++) {
                                    attachmentOptions.get(i).setShowKey(String.valueOf((char) ('A' + ints[i])));
                                }
                                attachmentOptions.sort(Comparator.comparing(AppOptionContentVO::getShowKey));
                            }
                            char[] answerCharArray = answer.toCharArray();
                            for (int i = 0; i < answerCharArray.length; i++) {
                                if (optMap.containsKey(String.valueOf(answerCharArray[i]))) {
                                    answerContent.append(optMap.get(String.valueOf(answerCharArray[i])));
                                }
                            }
                            detailVO1.setAnswerContent(answerContent.toString());
                        } else {
                            for (AppOptionContentVO optionContentVO : attachmentOptions) {
                                if (questionAnswerData != null) {
                                    if (optionContentVO.getKey().equals(questionAnswerData.getUserAnswer())) {
                                        questionAnswerData.setUserAnswerContent(optionContentVO.getContent());
                                    }
                                }
                                if (optionContentVO.getKey().equals(detailVO1.getAnswer())) {
                                    detailVO1.setAnswerContent(optionContentVO.getContent());
                                }
                            }
                        }
                    }

                }
                detailVOList.sort(Comparator.comparing(AppQuestionDetailVO::getSort));
                detailVO.setQuestionDetailList(detailVOList);
                detailVO.setQuestionRecordId(questionRecordId);
            }
        }

        // 按照原始入参 questionIds 的顺序对结果进行排序
        return sortQuestionsByInputOrder(questionVos, originalQuestionIds);
    }

    public AppUserPracticeRecordRespVO getStudyReport(QuestionSearchReqVO reqVO) {
        // 获取报告
        if (reqVO.getPracticeRecordId() == null) {
            throw new ServiceException(DATA_NOT_EXIST);
        }
        UserPracticeRecordDO recordDO = userPracticeRecordService.getById(reqVO.getPracticeRecordId());
        if (recordDO == null) {
            throw new ServiceException(DATA_NOT_EXIST);
        }
        AppUserPracticeRecordRespVO respVO = BeanUtils.toBean(recordDO, AppUserPracticeRecordRespVO.class);

        // 获取作答记录数据
        LambdaQueryWrapper<UserQuestionAnswerRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserQuestionAnswerRecordDO::getPracticeId, reqVO.getPracticeRecordId());
        queryWrapper.eq(UserQuestionAnswerRecordDO::getUserId, StpUtil.getLoginIdAsLong());
        queryWrapper.orderByAsc(UserQuestionAnswerRecordDO::getQuestionId);
        List<UserQuestionAnswerRecordDO> answerRecordDOS = userQuestionAnswerRecordService.list(queryWrapper);
        List<QuestionTypeEnum> writingQuestionTypeList = QuestionTypeEnum.getWritingQuestionTypeList();
        if (answerRecordDOS == null || answerRecordDOS.isEmpty()) {
            respVO.setUserQuestionStatusList(Collections.emptyList());
        } else {
            int totalScore = 0;
            int count = 0;
            for (UserQuestionAnswerRecordDO answerRecordDO : answerRecordDOS) {
                if (writingQuestionTypeList.contains(QuestionTypeEnum.getByCode(answerRecordDO.getQuestionTypeId()))
                        && answerRecordDO.getUserScore() != null && Objects.equals(answerRecordDO.getRecordStatus(), QuestionRecordStatusEnum.AI_CORRECTION_COMPLETED.getCode())) {
                    totalScore += answerRecordDO.getUserScore();
                    count++;
                }
            }
            if (count > 0 && count == answerRecordDOS.size()) {
                // 算平均分并向上取整
                double avg = (double) totalScore / count;
                respVO.setAvgScore((int) Math.ceil(avg));
            } else {
                respVO.setAvgScore(-1);
            }
            // 获取作答数据
            LambdaQueryWrapper<UserQuestionAnswerDataDO> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.in(UserQuestionAnswerDataDO::getRecordId, answerRecordDOS.stream().map(UserQuestionAnswerRecordDO::getId).collect(Collectors.toList()));
            queryWrapper1.orderByAsc(UserQuestionAnswerDataDO::getQuestionDetailId);
            List<UserQuestionAnswerDataDO> answerDataDOS = userQuestionAnswerDataService.list(queryWrapper1);
            Map<Long, List<UserQuestionAnswerDataDO>> userAnswerDataMap = answerDataDOS.stream().collect(Collectors.groupingBy(UserQuestionAnswerDataDO::getRecordId));

            List<AppUserQuestionStatusVO> userQuestionStatusList = new ArrayList<>();
            int sort = 1;
            for (UserQuestionAnswerRecordDO answerRecordDO : answerRecordDOS) {
                List<UserQuestionAnswerDataDO> answerDataDOList = userAnswerDataMap.get(answerRecordDO.getId());
                for (UserQuestionAnswerDataDO answerDataDO : answerDataDOList) {
                    AppUserQuestionStatusVO userQuestionStatusVO = new AppUserQuestionStatusVO();
                    userQuestionStatusVO.setRecordId(answerRecordDO.getId());
                    userQuestionStatusVO.setQuestionId(answerRecordDO.getQuestionId());
                    userQuestionStatusVO.setRecordStatus(answerRecordDO.getRecordStatus());
                    userQuestionStatusVO.setVersion(answerRecordDO.getVersion());

                    userQuestionStatusVO.setQuestionDetailId(answerDataDO.getQuestionDetailId());
                    userQuestionStatusVO.setIsCorrect(answerDataDO.getIsCorrect());
                    userQuestionStatusVO.setAiCorrectStatus(answerDataDO.getAiCorrectStatus());

                    userQuestionStatusVO.setSort(sort++);

                    userQuestionStatusList.add(userQuestionStatusVO);
                }
            }

//            userQuestionStatusList 根据respVO.getQuestionIds() 排序
            String questionIds = recordDO.getQuestionIds();
            if (questionIds == null || questionIds.isEmpty()) {
                return respVO;
            }
            String[] strings = questionIds.split(",");
            // 转化为List<Long> 数组
            List<Long> questionIdList = Arrays.stream(strings).map(Long::valueOf).collect(Collectors.toList());
            respVO.setUserQuestionStatusList(sortQuestionsByInputOrder2(userQuestionStatusList, questionIdList));
        }

        // 练习来源
        List<String> practiceSource = new ArrayList<>();
        if (recordDO.getHskLevel() != null) {
            practiceSource.add(Objects.requireNonNull(HskEnum.getDescByCode(recordDO.getHskLevel())));
        }
        if (recordDO.getSubject() != null) {
            practiceSource.add(Objects.requireNonNull(SubjectEnum.getByCode(recordDO.getSubject())).desc);
        }
        TextbookDO textbookDO = textbookService.getById(recordDO.getTextbookId());
        if (textbookDO != null) {
            practiceSource.add(LanguageUtils.getLocalizedValue(textbookDO.getNameCn(), textbookDO.getNameEn(), textbookDO.getNameOt()));
        }
        ChapterDO chapterDO = chapterService.getById(recordDO.getChapterId());
        if (chapterDO != null) {
            practiceSource.add(LanguageUtils.getLocalizedValue(chapterDO.getChapterNameCn(), chapterDO.getChapterNameEn(), chapterDO.getChapterNameOt()));
        }
        respVO.setPracticeSource(String.join("-", practiceSource));

        // 练习类型
        QuestionTypeDO typeDO = questionTypeService.getById(recordDO.getQuestionTypeId());
        if (typeDO != null) {
            respVO.setQuestionTypeDesc(LanguageUtils.getLocalizedValue(typeDO.getNameCn(), typeDO.getNameEn(), typeDO.getNameOt()));
        }
        // 判断是否有下一单元,下一章节,下一教材
        QuestionSearchReqVO req = new QuestionSearchReqVO();
        req.setHskLevel(recordDO.getHskLevel());
        req.setSubject(recordDO.getSubject());
        req.setUnitSort(recordDO.getUnitSort());
        req.setTypeId(recordDO.getQuestionTypeId());
        List<TextbookChapterQuestionRespVO> respVOS = questionService.getTextbookChapterQuestions(req);
        Map<Long, List<TextbookChapterQuestionRespVO>> textBookMap = respVOS.stream().collect(Collectors.groupingBy(TextbookChapterQuestionRespVO::getTextbookId));
        List<TextbookChapterQuestionRespVO> questionRespVOS = textBookMap.get(recordDO.getTextbookId());
        if (CollUtil.isNotEmpty(questionRespVOS)) {
            Set<Long> chapterIds = questionRespVOS.stream().map(TextbookChapterQuestionRespVO::getChapterId).collect(Collectors.toSet());
            LambdaQueryWrapper<ChapterDO> chapterDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            chapterDOLambdaQueryWrapper.in(ChapterDO::getId, chapterIds);
            chapterDOLambdaQueryWrapper.orderByAsc(ChapterDO::getChapterOrder);
            List<ChapterDO> chapterDOS = chapterService.list(chapterDOLambdaQueryWrapper);
            boolean hasNextChapter = false;
            for (int i = 0; i < chapterDOS.size(); i++) {
                if (chapterDOS.get(i).getId().equals(recordDO.getChapterId()) && i < chapterDOS.size() - 1) {
                    respVO.setHasNextChapter(true);
                    respVO.setNextChapterId(chapterDOS.get(i + 1).getId());
                    respVO.setHasNextTextbook(true);
                    respVO.setNextTextbookId(chapterDOS.get(i + 1).getTextbookId());
                    hasNextChapter = true;
                    break;
                }
            }
            if (!hasNextChapter) {
                Set<Long> textBookIds = textBookMap.keySet();
                LambdaQueryWrapper<TextbookDO> textbookDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                textbookDOLambdaQueryWrapper.in(TextbookDO::getId, textBookIds);
                textbookDOLambdaQueryWrapper.orderByAsc(TextbookDO::getSort);
                List<TextbookDO> textbookDOS = textbookService.list(textbookDOLambdaQueryWrapper);
                boolean hasNextTextbook = false;
                for (int i = 0; i < textbookDOS.size(); i++) {
                    if (textbookDOS.get(i).getId().equals(recordDO.getTextbookId()) && i < textbookDOS.size() - 1) {
                        respVO.setHasNextTextbook(true);
                        respVO.setNextTextbookId(textbookDOS.get(i + 1).getId());
                        hasNextTextbook = true;
                        break;
                    }
                }
                if (hasNextTextbook) {
                    // 查找符合的章节
                    List<TextbookChapterQuestionRespVO> questionRespVOS1 = textBookMap.get(respVO.getNextTextbookId());
                    if (questionRespVOS1 == null || questionRespVOS1.isEmpty()) {
                        respVO.setHasNextChapter(false);
                    } else {
                        Set<Long> chapterIds1 = questionRespVOS1.stream().map(TextbookChapterQuestionRespVO::getChapterId).collect(Collectors.toSet());
                        LambdaQueryWrapper<ChapterDO> chapterDOLambdaQueryWrapper1 = new LambdaQueryWrapper<>();
                        chapterDOLambdaQueryWrapper1.in(ChapterDO::getId, chapterIds1);
                        chapterDOLambdaQueryWrapper1.orderByAsc(ChapterDO::getChapterOrder);
                        List<ChapterDO> chapterDOS1 = chapterService.list(chapterDOLambdaQueryWrapper1);
                        respVO.setHasNextChapter(true);
                        respVO.setNextChapterId(chapterDOS1.get(0).getId());
                    }
                }
            }
        }

        // 处理互动课科目
        if (PracticeModeEnum.INTERACTIVE_COURSE.getCode().equals(recordDO.getPracticeMode())) {
            respVO.setInteractiveCourseUnitId(recordDO.getInteractiveCourseUnitId());
            // hsk等级
            Integer userHskLevel = appUserApi.getUserHskLevel(StpUtil.getLoginIdAsLong());
            // hsk 1级和2级对应的科目是听力阅读 3级和4级对应的科目是全科
            List<Integer> interactiveCourseSubjects = new ArrayList<>();
            if (Objects.equals(userHskLevel, HskEnum.HSK_1.getCode()) || Objects.equals(userHskLevel,
                HskEnum.HSK_2.getCode())) {
                interactiveCourseSubjects.add(SubjectEnum.LISTENING.getCode());
                interactiveCourseSubjects.add(SubjectEnum.READING.getCode());
            } else {
                interactiveCourseSubjects.add(SubjectEnum.WRITING.getCode());
            }
            // 从科目中随机取一个
            int interactiveCourseSubject = interactiveCourseSubjects.get(new Random().nextInt(interactiveCourseSubjects.size()));
            respVO.setInteractiveCourseSubject(interactiveCourseSubject);
        }

        return respVO;
    }

    public AppQuestionVo getReportQuestionDetails(QuestionSearchReqVO reqVO) {
        // 获取报告
        if (reqVO.getPracticeRecordId() == null) {
            throw new ServiceException(DATA_NOT_EXIST);
        }
        UserPracticeRecordDO recordDO = userPracticeRecordService.getById(reqVO.getPracticeRecordId());
        if (recordDO == null) {
            throw new ServiceException(DATA_NOT_EXIST);
        }
        // 获取作答记录数据
        LambdaQueryWrapper<UserQuestionAnswerRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserQuestionAnswerRecordDO::getPracticeId, reqVO.getPracticeRecordId());
        queryWrapper.eq(UserQuestionAnswerRecordDO::getPracticeMode, reqVO.getPracticeMode());
        queryWrapper.eq(UserQuestionAnswerRecordDO::getUserId, StpUtil.getLoginIdAsLong());
        queryWrapper.orderByAsc(UserQuestionAnswerRecordDO::getId);
        List<UserQuestionAnswerRecordDO> answerRecordDOS = userQuestionAnswerRecordService.list(queryWrapper);
        if (answerRecordDOS.isEmpty()) {
            throw new ServiceException(DATA_NOT_EXIST);
        }
        int version = 0;
        Long questionId = reqVO.getQuestionId();
        Long nextQuestionId = null;
        for (int i = 0; i < answerRecordDOS.size(); i++) {
            UserQuestionAnswerRecordDO answerRecordDO = answerRecordDOS.get(i);
            if (answerRecordDO.getQuestionId().equals(questionId)) {
                version = answerRecordDO.getVersion();
            }
            if (i < answerRecordDOS.size() - 1) {
                UserQuestionAnswerRecordDO nextRecordDO = answerRecordDOS.get(i + 1);
                if (nextRecordDO.getQuestionId().equals(questionId)) {
                    nextQuestionId = nextRecordDO.getQuestionId();
                }
            }
        }
        // 获取题目详情
        LambdaQueryWrapper<QuestionVersionDO> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(QuestionVersionDO::getQuestionId, questionId);
        queryWrapper1.eq(QuestionVersionDO::getVersion, version);
        QuestionVersionDO questionVersionDO = questionVersionService.getOne(queryWrapper1);
        AppQuestionVo questionVO = new AppQuestionVo();
        questionVO.setId(questionVersionDO.getQuestionId());
        questionVO.setQuestionCode(questionVersionDO.getQuestionCode());
        questionVO.setQuestionNum(questionVersionDO.getQuestionNum());
        questionVO.setHskLevel(questionVersionDO.getHskLevel());
        questionVO.setHskLevelDesc(Objects.requireNonNull(HskEnum.getDescByCode(questionVersionDO.getHskLevel())));
        questionVO.setTypeId(questionVersionDO.getTypeId());
        questionVO.setSubject(questionVersionDO.getSubject());
        questionVO.setSubjectDesc(Objects.requireNonNull(SubjectEnum.getByCode(questionVersionDO.getSubject())).desc);
        questionVO.setMaterialAudio(questionVersionDO.getMaterialAudio());
        questionVO.setMaterialImage(questionVersionDO.getMaterialImage());
        questionVO.setMaterialContent(questionVersionDO.getMaterialContent());
        questionVO.setOptions(questionVersionDO.getOptions());
        questionVO.setOptionContents(JSON.parseArray(questionVersionDO.getOptions(), AppOptionContentVO.class));

        questionVO.setNextQuestionId(nextQuestionId);

        // 获取题目详情
        LambdaQueryWrapper<QuestionDetailVersionDO> queryWrapper2 = new LambdaQueryWrapper<>();
        queryWrapper2.eq(QuestionDetailVersionDO::getQuestionId, questionId);
        queryWrapper2.eq(QuestionDetailVersionDO::getVersion, version);
        List<QuestionDetailVersionDO> questionDetailVersionDOS = questionDetailVersionService.list(queryWrapper2);

        // 这儿只会有一条数据
        UserQuestionAnswerRecordDO answerRecordDO = answerRecordDOS.stream().filter(q -> q.getQuestionId().equals(questionId)).findFirst().get();
        // 获取用户作答数据
        LambdaQueryWrapper<UserQuestionAnswerDataDO> queryWrapper4 = new LambdaQueryWrapper<>();
        queryWrapper4.eq(UserQuestionAnswerDataDO::getQuestionId, questionId);
        queryWrapper4.eq(UserQuestionAnswerDataDO::getRecordId, answerRecordDO.getId());
        List<UserQuestionAnswerDataDO> answerDataDOS = userQuestionAnswerDataService.list(queryWrapper4);
        Map<Long, UserQuestionAnswerDataDO> dataDOMap = answerDataDOS.stream().collect(Collectors.toMap(UserQuestionAnswerDataDO::getQuestionDetailId, Function.identity(), (v1, v2) -> v1));

        List<AppQuestionDetailVO> questionDetailList = new ArrayList<>();
        for (QuestionDetailVersionDO questionDetailVersionDO : questionDetailVersionDOS) {
            AppQuestionDetailVO questionDetailVO = new AppQuestionDetailVO();
            questionDetailVO.setQuestionId(questionDetailVersionDO.getQuestionId());
            questionDetailVO.setId(questionDetailVersionDO.getQuestionDetailId());
            questionDetailVO.setSort(questionDetailVersionDO.getSort());
            questionDetailVO.setAttachmentAudio(questionDetailVersionDO.getAttachmentAudio());
            questionDetailVO.setAttachmentAudioTime(questionDetailVersionDO.getAttachmentAudioTime());
            questionDetailVO.setAttachmentImage(questionDetailVersionDO.getAttachmentImage());
            questionDetailVO.setAttachmentContent(questionDetailVersionDO.getAttachmentContent());
            questionDetailVO.setAnswer(questionDetailVersionDO.getAnswer());
            questionDetailVO.setOptions(questionDetailVersionDO.getOptions());
            questionDetailVO.setOptionContents(JSON.parseArray(questionDetailVersionDO.getOptions(), AppOptionContentVO.class));
            questionDetailVO.setVersion(questionDetailVersionDO.getVersion());
            questionDetailVO.setExplainText(LanguageUtils.getLocalizedValue(questionDetailVersionDO.getExplainTextCn(), questionDetailVersionDO.getExplainTextEn(), questionDetailVersionDO.getExplainTextOt()));
            questionDetailVO.setExplainVideo(questionDetailVersionDO.getExplainVideo());
            questionDetailVO.setExplainAudio(questionDetailVersionDO.getExplainAudio());
            UserQuestionAnswerDataDO dataDO = dataDOMap.get(questionDetailVersionDO.getQuestionDetailId());

            questionDetailVO.setUserQuestionAnswerData(BeanUtils.toBean(dataDO, AppUserQuestionAnswerData.class));

            questionDetailList.add(questionDetailVO);

        }

        questionVO.setQuestionDetailList(questionDetailList);
        return questionVO;
    }

//    @Transactional(rollbackFor = Exception.class)
//    @Lock4j(name = "saveUserQuestionAnswerRecord2", keys = {"#questionId", "#version", "#userId"})
//    public Long saveUserQuestionAnswerRecord2(AppUserAnswerSaveVO reqVO, long userId) {
//        Long questionId = reqVO.getQuestionId();
//        Integer version = reqVO.getVersion();
//        QuestionRespVO question = questionService.getQuestionById(questionId);
//        if (QuestionTypeEnum.LISTENING_PICTURE_JUDGE.getCode() == question.getTypeId()) {

    /// /            saveLISTENING_PICTURE_JUDGE
//        }
//        return null;
//    }

    @Transactional(rollbackFor = Exception.class)
    @Lock4j(name = "saveUserQuestionAnswerRecord", keys = {"#reqVO.practiceId", "#reqVO.questionId", "#reqVO.version", "#userId", "#questionDetailId"})
    public Long saveUserQuestionAnswerRecord(AppUserAnswerSaveVO reqVO, long userId, Long questionDetailId) {
        // 1. 参数验证和初始化
        validateAndInitialize(reqVO);

        // 2. 获取题目信息
        QuestionContext questionContext = buildQuestionContext(reqVO);

        // 3. 处理答题记录
        Long recordId = processAnswerRecord(reqVO, userId, questionContext);

        // 4. 保存答题数据
        saveAnswerData(reqVO, recordId, questionContext);


        // 5. 更新统计信息
        if (Objects.equals(reqVO.getPracticeMode(), PracticeModeEnum.TYPE_PRACTICE.getCode())
                || Objects.equals(reqVO.getPracticeMode(), PracticeModeEnum.INTERACTIVE_COURSE.getCode())) {
            updateStatistics(reqVO, questionContext.getExerciseCount(), questionContext.getCorrectNum(), questionContext.getAnswerTime());
        }
        return recordId;
    }

    /**
     * 参数验证和初始化
     */
    private void validateAndInitialize(AppUserAnswerSaveVO reqVO) {
        if (reqVO.getQuestionId() == null) {
            throw new ServiceException(DATA_NOT_EXIST);
        }
        if (reqVO.getUserAnswerDetailList() == null || reqVO.getUserAnswerDetailList().isEmpty()) {
            log.error("用户作答数据为空");
            throw exception(LOCKED);
        }
    }

    /**
     * 构建题目上下文信息
     */
    private QuestionContext buildQuestionContext(AppUserAnswerSaveVO reqVO) {
        Long questionId = reqVO.getQuestionId();
        Integer version = reqVO.getVersion();

        // 获取题目版本信息
        QuestionVersionDO questionVersionDO = questionVersionService.getQuestionByIdAndVersion(questionId, version);
        if (questionVersionDO == null || questionVersionDO.getDeleted()) {
            log.error("这道题已经被删除或者版本库中不存在 {}", JSON.toJSONString(questionVersionDO));
            throw new ServiceException(DATA_NOT_EXIST);
        }

        QuestionRespVO question = BeanUtils.toBean(questionVersionDO, QuestionRespVO.class);
        question.setId(questionId);

        // 获取题目详情版本信息
        List<QuestionDetailVersionDO> questionDetailVersionDOS = questionDetailVersionService.lambdaQuery()
                .eq(QuestionDetailVersionDO::getQuestionId, questionId)
                .eq(QuestionDetailVersionDO::getVersion, version)
                .list();

        // 确定记录状态
        Integer recordStatus = reqVO.getRecordStatus();
        if (Objects.equals(reqVO.getPracticeMode(), PracticeModeEnum.EXAM.getCode())
                || Objects.equals(reqVO.getPracticeMode(), PracticeModeEnum.THIRTY_MINUTE_EXAM.getCode())
                || Objects.equals(reqVO.getPracticeMode(), PracticeModeEnum.FIFTEEN_MINUTE_EXAM.getCode())) {
            recordStatus = 1; // 模考练习默认为进行中，在提交以后会更新为已完成
        }
        return QuestionContext.builder()
                .questionVersionDO(questionVersionDO)
                .question(question)
                .questionDetailVersionDOS(questionDetailVersionDOS)
                .recordStatus(recordStatus)
                .answerTime(reqVO.getAnswerTime())
                .build();
    }

    @Lock4j(name = "commitUserPracticeEnd", keys = {"#practiceId", "#userId"})
    @Transactional(rollbackFor = Exception.class)
    public Long commitUserPracticeEnd(Long practiceId, long userId, Long interactiveCourseUnitId) {
        UserPracticeRecordDO recordDO = userPracticeRecordService.getById(practiceId);
        if (recordDO == null || recordDO.getUserId() != userId) {
            throw exception(DATA_NOT_EXIST);
        }
        if (recordDO.getRecordStatus() == 2) {
            throw new ServiceException(TOO_MANY_REQUESTS);
        }
        // 获取题目总数 这儿保存用户实际做的题目数
        LambdaQueryWrapper<UserQuestionAnswerRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserQuestionAnswerRecordDO::getPracticeId, practiceId);
        queryWrapper.eq(UserQuestionAnswerRecordDO::getPracticeMode, recordDO.getPracticeMode());
        queryWrapper.orderByAsc(UserQuestionAnswerRecordDO::getQuestionId);
        List<UserQuestionAnswerRecordDO> answerRecordDOS = userQuestionAnswerRecordService.list(queryWrapper);
        if (answerRecordDOS.isEmpty()) {
            throw new ServiceException(500, "该记录下无做题记录,不能提交！");
        }
        List<Long> questionIds = answerRecordDOS.stream().map(UserQuestionAnswerRecordDO::getQuestionId).distinct().collect(Collectors.toList());
        Optional<Integer> count = answerRecordDOS.stream().map(UserQuestionAnswerRecordDO::getQuestionNum).reduce(Integer::sum);
        String questionIdsStr = questionIds.stream().map(String::valueOf).collect(Collectors.joining(","));

        recordDO.setQuestionIds(questionIdsStr);
        recordDO.setQuestionNum(count.get());
        recordDO.setEndTime(LocalDateTime.now());
        recordDO.setRecordStatus(2);
        recordDO.setUpdateTime(LocalDateTime.now());
        userPracticeRecordService.updateById(recordDO);

        // 如果来自互动课，发送完成事件
        if (interactiveCourseUnitId != null) {

            long correctCount = recordDO.getCorrectNum();

            // 获取总数
            long totalCount = recordDO.getQuestionNum();

            // 正确率计算
            double v = totalCount > 0 ? NumberUtil.div(correctCount, totalCount, 4, RoundingMode.CEILING) * 100 : 0.0;
            // 正确率向上取整
            BigDecimal accuracy = BigDecimal.valueOf((int) Math.ceil(v));

            // 发送互动课练习完成事件
            try {
                List<Long> questionIdList = Arrays.stream(questionIdsStr.split(","))
                        .map(Long::parseLong)
                        .toList();

                boolean needAICorrect = QuestionTypeEnum.isNeedAICorrect(
                    recordDO.getQuestionTypeId());

                questionProducer.sendPracticeCompletedEvent(
                        userId,
                        interactiveCourseUnitId,
                        questionIdList,
                        // 正确率取整 如果是不需要批改的题目,正确率传null
                        needAICorrect ? null : BigDecimal.valueOf(accuracy.intValue()),
                        practiceId
                );
            } catch (Exception e) {
                log.error("发送互动课练习完成事件失败: userId={}, unitId={}, practiceId={}",
                        userId, interactiveCourseUnitId, practiceId, e);
            }
        }

        return practiceId;
    }


    public String oneClickPolishing(QuestionSearchReqVO reqVO) {
        // 查询用户作答数据
        LambdaQueryWrapper<UserQuestionAnswerRecordDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserQuestionAnswerRecordDO::getUserId, reqVO.getUserId());
        wrapper.eq(UserQuestionAnswerRecordDO::getPracticeMode, reqVO.getPracticeMode());
        wrapper.eq(UserQuestionAnswerRecordDO::getPracticeId, reqVO.getPracticeRecordId());
        wrapper.eq(UserQuestionAnswerRecordDO::getQuestionId, reqVO.getQuestionId());
        UserQuestionAnswerRecordDO answerRecordDO = userQuestionAnswerRecordService.getOne(wrapper);
        LambdaQueryWrapper<UserQuestionAnswerDataDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserQuestionAnswerDataDO::getQuestionId, reqVO.getQuestionId());
        queryWrapper.eq(UserQuestionAnswerDataDO::getRecordId, answerRecordDO.getId());
        List<UserQuestionAnswerDataDO> answerDataDOList = userQuestionAnswerDataService.list(queryWrapper);
        // 书写题目前只会有一个大题数据，所以直接取第一个
        UserQuestionAnswerDataDO answerDataDO = answerDataDOList.get(0);
        String userAnswer = answerDataDO.getUserAnswer();
        // 查询题目数据
        QuestionDetailDO questionDetailDO = questionDetailService.getById(reqVO.getQuestionDetailId());
        // 调起ai批改
//        String string = cozeApi.nonStreamingDialogue("请将下面内容进行润色，使其更加专业、准确、简洁。", "请将下面内容进行润色，使其更加专业、准确、简洁。", 1);
//        System.out.println(string);
        return null;
    }

    public Integer getUserQuestionAiCorrectionCount(long userId) {
        // 获取配置值并安全转换
        String configValue = configApi.getConfigValueByKey(USER_AI_CORRECTION_COUNT_KEY);
        int maxCount = configValue == null ? 0 : Integer.parseInt(configValue);

        // 统一时间基准
        Date now = new Date();
        DateTime beginOfDay = DateUtil.beginOfDay(now);
        DateTime endOfDay = DateUtil.endOfDay(now);

        // 查询用户已使用次数
        int usedCount = writingAiCorrectionRecordApi.getUserWritingAiCorrectionCount(userId, beginOfDay, endOfDay);

        // 保证非负返回
        return Math.max(0, maxCount - usedCount);
    }

    /**
     * 真题调用Ai批改
     *
     * @param reqVO
     * @return 1 已达上限 2 批改中 3批改成功  4批改失败
     */
    @Lock4j(name = "questionCallAiCorrection", keys = {"#reqVO.recordId", "#reqVO.userId"}, acquireTimeout = 0)
    @Transactional(rollbackFor = Exception.class)
    public Integer questionCallAiCorrection(CallAiCorrectionReqVO reqVO) {
        Integer result = 2;
        Date now = new Date();

        // 查询用户作答记录
        LambdaQueryWrapper<UserQuestionAnswerRecordDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserQuestionAnswerRecordDO::getUserId, reqVO.getUserId());
        wrapper.eq(UserQuestionAnswerRecordDO::getId, reqVO.getRecordId());
        UserQuestionAnswerRecordDO answerRecordDO = userQuestionAnswerRecordService.getOne(wrapper);
        if (answerRecordDO == null) {
            throw new ServiceException(DATA_NOT_EXIST);
        }
        List<QuestionTypeEnum> writingQuestionTypeList = QuestionTypeEnum.getWritingQuestionTypeList();
        if (!writingQuestionTypeList.contains(QuestionTypeEnum.getByCode(answerRecordDO.getQuestionTypeId()))) {
            throw new ServiceException(TOO_MANY_REQUESTS);
        }
        if (answerRecordDO.getRecordStatus() != 2 && answerRecordDO.getRecordStatus() != 3 && answerRecordDO.getRecordStatus() != 5) {
            throw new ServiceException(TOO_MANY_REQUESTS);
        }
        // 查询用户作答数据
        LambdaQueryWrapper<UserQuestionAnswerDataDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserQuestionAnswerDataDO::getRecordId, answerRecordDO.getId());
        List<UserQuestionAnswerDataDO> answerDataDOList = userQuestionAnswerDataService.list(queryWrapper);
        if (answerDataDOList.isEmpty()) {
            throw new ServiceException(DATA_NOT_EXIST);
        }

        // 查询用户剩余次数
        if (reqVO.getBizType().equals(AiCorrectBizTypeEnum.REAL_EXAM.getCode())) {
            int userAiCorrectionTimes = aiCorrectionTimesApi.getUserRemainingCount(
                    reqVO.getUserId(), USER_AI_CORRECTION_COUNT_KEY, WRITING_AI_CORRECTION_COUNT, now, AiCorrectBizTypeEnum.REAL_EXAM);

            if (userAiCorrectionTimes <= 0) {
                result = 1;
                return result;
            }
        }


        // 更新作答记录为未完成状态
        answerRecordDO.setRecordStatus(3);
        userQuestionAnswerRecordService.updateById(answerRecordDO);

        // 目前书写题只会有一个做题数据，所以直接取第一个
        UserQuestionAnswerDataDO answerDataDO = answerDataDOList.get(0);

        // 图片描述
        String imageDescription = "";
        boolean needImageDescription = false;
//        QuestionDetailDO questionDetailDO = questionDetailService.getById(answerDataDO.getQuestionDetailId());
        LambdaQueryWrapper<QuestionDetailVersionDO> questionDetailVersionWrapper = new LambdaQueryWrapper<>();
        questionDetailVersionWrapper.eq(QuestionDetailVersionDO::getQuestionDetailId, answerDataDO.getQuestionDetailId());
        questionDetailVersionWrapper.eq(QuestionDetailVersionDO::getVersion, answerDataDO.getVersion());
        QuestionDetailVersionDO detailVersionServiceOne = questionDetailVersionService.getOne(questionDetailVersionWrapper);

        if (detailVersionServiceOne.getAttachmentImage() != null && !detailVersionServiceOne.getAttachmentImage().isEmpty()) {
            needImageDescription = true;
            imageDescription = detailVersionServiceOne.getAttachmentImageDesc();
        }
        // 获取题目信息
        String questionContent = detailVersionServiceOne.getAttachmentContent();

        // 记录次数
        Long aiCorrectionTimesId = getAiCorrectionTimesId(reqVO, now);

        // 调用ai批改 同时调用一键润色
        CozeCallDto cozeCallDto = new CozeCallDto();
        cozeCallDto.setQuestionId(answerRecordDO.getQuestionId());
        cozeCallDto.setQuestionDetailId(answerDataDO.getQuestionDetailId());
        cozeCallDto.setQuestionVersion(answerDataDO.getVersion());
        cozeCallDto.setUserId(reqVO.getUserId());
        cozeCallDto.setRecordId(answerRecordDO.getId());
        cozeCallDto.setAnswerDataId(answerDataDO.getId());
        cozeCallDto.setQuestionContent(questionContent);
        cozeCallDto.setAnswerData(answerDataDO.getUserAnswer());
        cozeCallDto.setQuestionTypeId(answerRecordDO.getQuestionTypeId());
        cozeCallDto.setOneClickPolishing(true);
        cozeCallDto.setImageDescription(imageDescription);
        cozeCallDto.setAiCorrectionDate(now);
        cozeCallDto.setNeedImageDescription(needImageDescription);
        cozeCallDto.setAiCorrectionTimesId(aiCorrectionTimesId);
        cozeCallDto.setBizType(reqVO.getBizType());
        cozeApi.questionWritingCorrection(cozeCallDto);
        return result;
    }

    private Long getAiCorrectionTimesId(CallAiCorrectionReqVO reqVO, Date now) {
        AiCorrectionTimesSaveReqDto createReqDto = new AiCorrectionTimesSaveReqDto();
        // 增加使用次数
        createReqDto.setUserId(reqVO.getUserId());
        createReqDto.setBizType(reqVO.getBizType());
        createReqDto.setBizId(reqVO.getBizId());
        createReqDto.setRecordId(reqVO.getRecordId());
        createReqDto.setCallTime(now);
        createReqDto.setCallCount(1);
        return aiCorrectionTimesApi.createAiCorrectionTimes(createReqDto);
    }

    /**
     * 获取Ai批改结果
     *
     * @param reqVO
     * @return
     */
    public CozeRespDto getQuestionAiCorrectionResult(CallAiCorrectionReqVO reqVO) {
        long userId = StpUtil.getLoginIdAsLong();
        // 查询用户作答记录
        LambdaQueryWrapper<UserQuestionAnswerRecordDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserQuestionAnswerRecordDO::getUserId, userId);
        wrapper.eq(UserQuestionAnswerRecordDO::getId, reqVO.getRecordId());
        UserQuestionAnswerRecordDO answerRecordDO = userQuestionAnswerRecordService.getOne(wrapper);
        if (answerRecordDO == null) {
            throw new ServiceException(DATA_NOT_EXIST);
        }
        List<QuestionTypeEnum> writingQuestionTypeList = QuestionTypeEnum.getWritingQuestionTypeList();
        if (!writingQuestionTypeList.contains(QuestionTypeEnum.getByCode(answerRecordDO.getQuestionTypeId()))) {
            throw new ServiceException(TOO_MANY_REQUESTS);
        }
//        if (answerRecordDO.getRecordStatus() != 2) {
//            throw new ServiceException(TOO_MANY_REQUESTS);
//        }
        // 查询用户作答数据
        LambdaQueryWrapper<UserQuestionAnswerDataDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserQuestionAnswerDataDO::getRecordId, answerRecordDO.getId());
        List<UserQuestionAnswerDataDO> answerDataDOList = userQuestionAnswerDataService.list(queryWrapper);
        if (answerDataDOList.isEmpty()) {
            throw new ServiceException(DATA_NOT_EXIST);
        }
        // 目前写作题只会有一个做题数据，所以直接取第一个
        UserQuestionAnswerDataDO answerDataDO = answerDataDOList.get(0);
        CozeCallDto cozeCallDto = new CozeCallDto();
        cozeCallDto.setUserId(userId);
        cozeCallDto.setRecordId(reqVO.getRecordId());
        cozeCallDto.setQuestionId(answerRecordDO.getQuestionId());
        cozeCallDto.setAnswerDataId(answerDataDO.getId());
        cozeCallDto.setQuestionDetailId(answerDataDO.getQuestionDetailId());
        CozeRespDto result = cozeApi.getQuestionAiCorrectionResult(cozeCallDto);
        if (result == null) {
            CozeRespDto cozeRespDto = new CozeRespDto();
            cozeRespDto.setStatus(2);
            cozeRespDto.setErrorMessage("Ai批改失败");
            log.error("未获取到AI批改数据");
            return cozeRespDto;
        }
//        if (result.getCozeDataRespDtos() == null
//                || result.getCozeDataRespDtos().isEmpty()
//                || !Objects.equals(result.getQuestionDetailId(), answerDataDO.getQuestionDetailId())) {
//            CozeRespDto cozeRespDto = new CozeRespDto();
////            cozeRespDto.setStatus(2);
//            cozeRespDto.setErrorMessage("Ai批改中---");
//            log.error("未获取到AI批改数据");
//            return cozeRespDto;
//        }
        // 构造返回数据
        result.setAnswerData(answerDataDO.getUserAnswer());
        result.setReferenceAnswer(answerDataDO.getAnswer());


        return result;
    }

    public List<TextbookChapterQuestionCountVO> getUserPracticeTextbookChapterList(@Valid QuestionSearchReqVO pageReqVO) {
//        List<TextbookChapterQuestionRespVO> respVOS = questionService.getTextbookChapterQuestions(pageReqVO);
        List<TextbookChapterQuestionRespVO> respVOS = userPracticeRecordService.getUserPracticeTextbookChapterList(pageReqVO);
        if (respVOS == null || respVOS.isEmpty()) {
            return new ArrayList<>();
        }
        Map<Long, List<TextbookChapterQuestionRespVO>> textbookMap = respVOS.stream().collect(Collectors.groupingBy(TextbookChapterQuestionRespVO::getTextbookId));
        Set<Long> textbookIds = respVOS.stream().map(TextbookChapterQuestionRespVO::getTextbookId).collect(Collectors.toSet());
        Set<Long> chapterIds = respVOS.stream().map(TextbookChapterQuestionRespVO::getChapterId).collect(Collectors.toSet());
        List<TextbookDO> textbookDOS = textbookService.listByIds(textbookIds);
        Map<Long, TextbookDO> textbookDOMap = textbookDOS.stream().collect(Collectors.toMap(TextbookDO::getId, textbookDO -> textbookDO, (textbookDO, textbookDO2) -> textbookDO));
        List<ChapterDO> chapterDOS = chapterService.listByIds(chapterIds);
        Map<Long, ChapterDO> chapterDOMap = chapterDOS.stream().collect(Collectors.toMap(ChapterDO::getId, chapterDO -> chapterDO, (chapterDO, chapterDO2) -> chapterDO));

        List<TextbookChapterQuestionCountVO> result = new ArrayList<>();
        for (Map.Entry<Long, List<TextbookChapterQuestionRespVO>> entry : textbookMap.entrySet()) {
            List<TextbookChapterQuestionRespVO> value = entry.getValue();
            TextbookDO textbookDO = textbookDOMap.get(entry.getKey());
            TextbookChapterQuestionCountVO textbookChapterQuestionCountVO = new TextbookChapterQuestionCountVO();
            textbookChapterQuestionCountVO.setTextbookId(textbookDO.getId());
            textbookChapterQuestionCountVO.setTextbookName(LanguageUtils.getLocalizedValue(textbookDO.getNameCn(), textbookDO.getNameEn(), textbookDO.getNameOt()));
            textbookChapterQuestionCountVO.setSort(textbookDO.getSort());
            List<ChapterQuestionCountVO> chapterQuestionCountList = new ArrayList<>();
            for (TextbookChapterQuestionRespVO chapterQuestionRespVO : value) {
                ChapterQuestionCountVO chapterQuestionCountVO = new ChapterQuestionCountVO();

                chapterQuestionCountVO.setChapterId(chapterQuestionRespVO.getChapterId());
                ChapterDO chapterDO = chapterDOMap.get(chapterQuestionRespVO.getChapterId());
                chapterQuestionCountVO.setChapterName(LanguageUtils.getLocalizedValue(chapterDO.getChapterNameCn(), chapterDO.getChapterNameEn(), chapterDO.getChapterNameOt()));
                chapterQuestionCountVO.setSort(chapterDO.getChapterOrder());
                chapterQuestionCountVO.setQuestionCount(chapterQuestionRespVO.getQuestionCount());
                chapterQuestionCountVO.setCorrectCount(chapterQuestionRespVO.getCorrectCount());
                chapterQuestionCountVO.setExerciseCount(chapterQuestionRespVO.getExerciseCount());
                chapterQuestionCountVO.setErrorCount(Math.max((chapterQuestionCountVO.getExerciseCount() != null ? chapterQuestionCountVO.getExerciseCount() : 0) - (chapterQuestionCountVO.getCorrectCount() != null ? chapterQuestionCountVO.getCorrectCount() : 0), 0));
                chapterQuestionCountList.add(chapterQuestionCountVO);
            }
            chapterQuestionCountList.sort(Comparator.comparing(ChapterQuestionCountVO::getSort));
            textbookChapterQuestionCountVO.setChapterQuestionCountList(chapterQuestionCountList);
            result.add(textbookChapterQuestionCountVO);

        }
        result.sort(Comparator.comparing(TextbookChapterQuestionCountVO::getSort));
        return result;
    }

    public QuestionStatisticsRespVO getUserPracticeUnitQuestionTypeList(@Valid QuestionSearchReqVO pageReqVO) {
        QuestionStatisticsRespVO statistics = new QuestionStatisticsRespVO();

        // 获取用户最新练习记录的统计信息（答题时长、答题总数、正确数）
        if (pageReqVO.getUserId() != null && pageReqVO.getUserId() != -1) {
            QuestionStatisticsRespVO userStats = userPracticeRecordService.getQuestionNewestHaveReportPracticeRecord(pageReqVO);
            if (userStats != null) {
                statistics.setAnswerTimeCount(userStats.getAnswerTimeCount());
                statistics.setAnswerCount(userStats.getAnswerCount());
                statistics.setRightCount(userStats.getRightCount());
            }
        }

        // 获取用户作答已生成报告的数据
        List<QuestionTypeCountRespVO> userAnswerCount = userPracticeRecordService.getHistoryUserUnitSortQuestionTypeCount(pageReqVO);

        Map<Integer, List<QuestionTypeCountRespVO>> userAnswerMap = userAnswerCount.stream()
                .collect(Collectors.groupingBy(QuestionTypeCountRespVO::getUnitSort));

        // 获取题型名称
        List<QuestionTypeDO> questionTypeDOList = questionTypeService.list();
        Map<Long, QuestionTypeDO> questionTypeMap = questionTypeDOList.stream()
                .collect(Collectors.toMap(QuestionTypeDO::getId, Function.identity(), (t1, t2) -> t1));

        // 构建单元题目统计列表
        List<UnitQuestionCountVO> unitQuestionCountVOList = new ArrayList<>();
        for (Map.Entry<Integer, List<QuestionTypeCountRespVO>> entry : userAnswerMap.entrySet()) {
            Integer unitSort = entry.getKey();
            List<QuestionTypeCountRespVO> questionTypeList = entry.getValue();

            UnitQuestionCountVO unitQuestionCountVO = new UnitQuestionCountVO();
            unitQuestionCountVO.setUnitSort(unitSort);

            // 构建题型统计列表
            List<QuestionTypeCountVO> questionTypeCountVOList = new ArrayList<>();
            for (QuestionTypeCountRespVO questionTypeCountRespVO : questionTypeList) {
                QuestionTypeCountVO questionTypeCountVO = new QuestionTypeCountVO();
                questionTypeCountVO.setTypeId(questionTypeCountRespVO.getTypeId());

                // 设置题型名称（支持国际化）
                QuestionTypeDO questionTypeDO = questionTypeMap.get(questionTypeCountRespVO.getTypeId());
                if (questionTypeDO != null) {
                    questionTypeCountVO.setTypeName(LanguageUtils.getLocalizedValue(
                            questionTypeDO.getNameCn(), questionTypeDO.getNameEn(), questionTypeDO.getNameOt()));
                }

                questionTypeCountVO.setCompletedCount(questionTypeCountRespVO.getExerciseCount());
                questionTypeCountVO.setCorrectCount(questionTypeCountRespVO.getCorrectCount());
                questionTypeCountVO.setPracticeRecordId(questionTypeCountRespVO.getPracticeRecordId());
                questionTypeCountVO.setQuestionCount(questionTypeCountRespVO.getQuestionCount());
                questionTypeCountVO.setPracticeRecordId(questionTypeCountRespVO.getPracticeRecordId());
                questionTypeCountVOList.add(questionTypeCountVO);
            }

            // 按题型ID排序
            questionTypeCountVOList.sort(Comparator.comparing(QuestionTypeCountVO::getTypeId));
            unitQuestionCountVO.setQuestionTypeCountVOList(questionTypeCountVOList);
            unitQuestionCountVOList.add(unitQuestionCountVO);
        }

        // 按单元排序排序
        unitQuestionCountVOList.sort(Comparator.comparing(UnitQuestionCountVO::getUnitSort));
        statistics.setUnitQuestionCountVOList(unitQuestionCountVOList);

        return statistics;
    }


    /**
     * 处理答题记录
     */
    private Long processAnswerRecord(AppUserAnswerSaveVO reqVO, long userId, QuestionContext context) {
        Long recordId = reqVO.getRecordId();

        // 如果没有传入recordId，先尝试查找现有记录
        if (recordId == null) {
            recordId = findExistingRecordId(reqVO, userId, context);
        }

        if (recordId != null) {
            return updateExistingRecord(reqVO, userId, context, recordId);
        } else {
            return createNewRecord(reqVO, userId, context);
        }
    }

    /**
     * 查找现有的答题记录ID
     */
    private Long findExistingRecordId(AppUserAnswerSaveVO reqVO, long userId, QuestionContext context) {
        UserQuestionAnswerRecordDO existingRecord = userQuestionAnswerRecordService.lambdaQuery()
                .eq(UserQuestionAnswerRecordDO::getUserId, userId)
                .eq(UserQuestionAnswerRecordDO::getQuestionId, reqVO.getQuestionId())
                .eq(UserQuestionAnswerRecordDO::getVersion, reqVO.getVersion())
                .eq(UserQuestionAnswerRecordDO::getPracticeMode, reqVO.getPracticeMode())
                .eq(UserQuestionAnswerRecordDO::getPracticeId, reqVO.getPracticeId())
                .one();

        if (existingRecord != null) {
            if (Objects.equals(existingRecord.getRecordStatus(), QuestionRecordStatusEnum.IN_PROGRESS.getCode())) {
                // 找到进行中的记录，可以继续使用
                log.warn("寻找到历史提交记录中 {}", JSON.toJSONString(existingRecord));
                return existingRecord.getId();
            } else if (Objects.equals(existingRecord.getRecordStatus(), QuestionRecordStatusEnum.SUBMITTED.getCode()) && Objects.equals(context.getRecordStatus(), QuestionRecordStatusEnum.SUBMITTED.getCode())) {
                // 记录已完成，但又要提交完成状态，抛出异常
                log.error("recordId={}的记录已结束", existingRecord.getId());
                throw new ServiceException(QUESTION_ANSWERED_ERROR_TWO);
            } else if (Objects.equals(context.getRecordStatus(), QuestionRecordStatusEnum.IN_PROGRESS.getCode())) {
                // 记录已完成，但要提交进行中状态，直接返回现有记录ID
                return existingRecord.getId();
            }
        }

        return null; // 没有找到现有记录
    }

    /**
     * 更新现有记录
     */
    private Long updateExistingRecord(AppUserAnswerSaveVO reqVO, long userId, QuestionContext context, Long recordId) {
        UserQuestionAnswerRecordDO recordDO = userQuestionAnswerRecordService.getById(recordId);
        if (recordDO == null) {
            log.error("未找到recordId={}的记录", recordId);
            throw new ServiceException(DATA_NOT_EXIST);
        }

        // 验证记录状态
        List<QuestionTypeEnum> writingQuestionTypeList = QuestionTypeEnum.getWritingQuestionTypeList();
        // 真题练习已提交的数据不允许再提交
        if (Objects.equals(recordDO.getPracticeMode(), PracticeModeEnum.TYPE_PRACTICE.getCode())) {
            if (!Objects.equals(recordDO.getRecordStatus(), QuestionRecordStatusEnum.IN_PROGRESS.getCode()) && Objects.equals(reqVO.getRecordStatus(), QuestionRecordStatusEnum.SUBMITTED.getCode())) {
                log.error("recordId={}的记录已结束", recordDO.getId());
                if (writingQuestionTypeList.contains(QuestionTypeEnum.getByCode(context.getQuestion().getTypeId()))) {
                    throw new ServiceException(QUESTION_ANSWERED_ERROR_TWO);
                } else {
                    throw new ServiceException(QUESTION_ANSWERED_ERROR);
                }
            }
            if (!Objects.equals(recordDO.getRecordStatus(), QuestionRecordStatusEnum.IN_PROGRESS.getCode()) && Objects.equals(reqVO.getRecordStatus(), QuestionRecordStatusEnum.IN_PROGRESS.getCode())) {
                log.error("recordId={}的记录已结束", recordDO.getId());
                if (writingQuestionTypeList.contains(QuestionTypeEnum.getByCode(context.getQuestion().getTypeId()))) {
                    return recordId;
                } else {
                    throw new ServiceException(QUESTION_ANSWERED_ERROR);
                }
            }
        }

        // 计算答题统计
        AnswerStatistics statistics = calculateAnswerStatistics(reqVO, context, recordId);
        context.setExerciseCount(statistics.getExerciseCount());
        context.setCorrectNum(statistics.getCorrectNum());

        // 处理单题提交逻辑
        Integer finalRecordStatus = handleSingleQuestionSubmission(reqVO, context, recordDO, recordId);

        // 更新记录
        updateRecordData(recordDO, reqVO, context, statistics, finalRecordStatus);
        userQuestionAnswerRecordService.updateById(recordDO);

        return recordId;
    }

    /**
     * 创建新记录
     */
    private Long createNewRecord(AppUserAnswerSaveVO reqVO, long userId, QuestionContext context) {
        // 最后一次检查是否已存在记录（双重检查）
        checkExistingAnswerForNewRecord(reqVO, userId, context.getQuestion());

        // 计算答题统计
        AnswerStatistics statistics = calculateAnswerStatistics(reqVO, context, null);
        context.setExerciseCount(statistics.getExerciseCount());
        context.setCorrectNum(statistics.getCorrectNum());

        // 确定最终状态
        Integer finalRecordStatus = determineFinalRecordStatus(reqVO, context);

        // 创建新记录
        UserQuestionAnswerRecordDO recordDO = buildNewRecord(reqVO, userId, context, statistics, finalRecordStatus);
        userQuestionAnswerRecordService.save(recordDO);

        return recordDO.getId();
    }


    /**
     * 检查是否已存在答题记录（用于创建新记录时的最终检查）
     */
    private void checkExistingAnswerForNewRecord(AppUserAnswerSaveVO reqVO, long userId, QuestionRespVO question) {
        List<UserQuestionAnswerRecordDO> existingRecords = userQuestionAnswerRecordService.lambdaQuery()
                .eq(UserQuestionAnswerRecordDO::getUserId, userId)
                .eq(UserQuestionAnswerRecordDO::getQuestionId, reqVO.getQuestionId())
                .eq(UserQuestionAnswerRecordDO::getVersion, reqVO.getVersion())
                .eq(UserQuestionAnswerRecordDO::getPracticeMode, reqVO.getPracticeMode())
                .eq(UserQuestionAnswerRecordDO::getPracticeId, reqVO.getPracticeId())
                .list();

        if (existingRecords != null && !existingRecords.isEmpty()) {
            log.error("用户已作答该题，recordId={} 本次作答数据:{}", existingRecords.get(0).getId(), JSON.toJSON(reqVO));
            List<QuestionTypeEnum> writingQuestionTypeList = QuestionTypeEnum.getWritingQuestionTypeList();
            if (writingQuestionTypeList.contains(QuestionTypeEnum.getByCode(question.getTypeId()))) {
                throw new ServiceException(QUESTION_ANSWERED_ERROR_TWO);
            } else {
                throw new ServiceException(QUESTION_ANSWERED_ERROR);
            }
        }
    }

    /**
     * 计算答题统计
     */
    private AnswerStatistics calculateAnswerStatistics(AppUserAnswerSaveVO reqVO, QuestionContext context, Long recordId) {
        int exerciseCount = 0;
        int correctNum = 0;
        int wordCount = 0;
        int userCorrectWord = 0;

        List<QuestionTypeEnum> writingQuestionTypeList = QuestionTypeEnum.getWritingQuestionTypeList();
        List<AppUserAnswerDetailSaveVO> userAnswerDetailList = reqVO.getUserAnswerDetailList();

        // 获取旧答题数据
        Map<Long, UserQuestionAnswerDataDO> oldDataMap = getOldAnswerDataMap(recordId, userAnswerDetailList);

        for (AppUserAnswerDetailSaveVO userAnswerDetail : userAnswerDetailList) {
            if (!Objects.equals(userAnswerDetail.getQuestionId(), reqVO.getQuestionId())) {
                log.error("用户作答数据中的questionId与请求中的questionId不一致, userAnswerDetail: {}, reqVO: {}", JSON.toJSON(userAnswerDetail), JSON.toJSON(reqVO));
                throw exception(LOCKED);
            }

            // 计算正确数
            correctNum += calculateCorrectCount(userAnswerDetail, oldDataMap);

            // 计算练习数
            if (writingQuestionTypeList.contains(QuestionTypeEnum.getByCode(context.getQuestion().getTypeId())) && !Objects.equals(context.getRecordStatus(), QuestionRecordStatusEnum.SUBMITTED.getCode())) {
                // 书写题未完成不计入练习数
            } else if (writingQuestionTypeList.contains(QuestionTypeEnum.getByCode(context.getQuestion().getTypeId())) && Objects.equals(context.getRecordStatus(), QuestionRecordStatusEnum.SUBMITTED.getCode())) {
                exerciseCount++;
            } else if (!oldDataMap.containsKey(userAnswerDetail.getQuestionDetailId())) {
                exerciseCount++;
            }

            // 连词成句特殊处理
            if (QuestionTypeEnum.WRITING_CONSTRUCT_SENTENCE.getCode() == context.getQuestion().getTypeId()) {
                int[] wordStats = calculateWordStatistics(userAnswerDetail.getUserAnswer());
                wordCount = wordStats[0];
                userCorrectWord = wordStats[1];
            }
        }

        return AnswerStatistics.builder()
                .exerciseCount(exerciseCount)
                .correctNum(correctNum)
                .wordCount(wordCount)
                .userCorrectWord(userCorrectWord)
                .build();
    }

    /**
     * 获取旧答题数据映射
     */
    private Map<Long, UserQuestionAnswerDataDO> getOldAnswerDataMap(Long recordId, List<AppUserAnswerDetailSaveVO> userAnswerDetailList) {
        if (recordId == null) {
            return new HashMap<>();
        }

        Set<Long> questionDetailIds = userAnswerDetailList.stream()
                .map(AppUserAnswerDetailSaveVO::getQuestionDetailId)
                .collect(Collectors.toSet());

        List<UserQuestionAnswerDataDO> oldAnswerDatas = userQuestionAnswerDataService.lambdaQuery()
                .eq(UserQuestionAnswerDataDO::getRecordId, recordId)
                .in(UserQuestionAnswerDataDO::getQuestionDetailId, questionDetailIds)
                .list();

        return oldAnswerDatas.stream()
                .collect(Collectors.toMap(UserQuestionAnswerDataDO::getQuestionDetailId, Function.identity(), (t1, t2) -> t1));
    }

    /**
     * 计算正确数变化
     */
    private int calculateCorrectCount(AppUserAnswerDetailSaveVO userAnswerDetail, Map<Long, UserQuestionAnswerDataDO> oldDataMap) {
        Boolean isCorrect = userAnswerDetail.getIsCorrect();
        Long questionDetailId = userAnswerDetail.getQuestionDetailId();

        if (isCorrect != null && isCorrect && !oldDataMap.containsKey(questionDetailId)) {
            return 1; // 新增正确答案
        }

        if (oldDataMap.containsKey(questionDetailId)) {
            UserQuestionAnswerDataDO oldData = oldDataMap.get(questionDetailId);
            Boolean oldCorrect = oldData.getIsCorrect();

            if (Boolean.TRUE.equals(oldCorrect)) {
                // 旧数据正确但新数据错误时减少正确数
                if (isCorrect != null && !isCorrect) {
                    return -1;
                }
            } else {
                // 旧数据错误但新数据正确时增加正确数
                if (isCorrect != null && isCorrect) {
                    return 1;
                }
            }
        }

        return 0;
    }

    /**
     * 计算连词成句的词汇统计
     */
    private int[] calculateWordStatistics(String userAnswer) {
        SortQuestionAnswerVo answerVo = JSON.parseObject(userAnswer, SortQuestionAnswerVo.class);
        String questionSort = answerVo.getQuestionSort();
        String userAnswerSort = answerVo.getUserAnswerSort();

        int wordCount = questionSort.length();
        int userCorrectWord = 0;

        for (int i = 0; i < questionSort.length(); i++) {
            if (userAnswerSort.charAt(i) == questionSort.charAt(i)) {
                userCorrectWord++;
            }
        }

        return new int[]{wordCount, userCorrectWord};
    }

    /**
     * 处理单题提交逻辑
     */
    private Integer handleSingleQuestionSubmission(AppUserAnswerSaveVO reqVO, QuestionContext context,
                                                   UserQuestionAnswerRecordDO recordDO, Long recordId) {
        List<QuestionTypeEnum> singleQuestionTypeList = QuestionTypeEnum.getSingleQuestionTypeList();
        Integer recordStatus = context.getRecordStatus();

        if (singleQuestionTypeList.contains(QuestionTypeEnum.getByCode(recordDO.getQuestionTypeId()))) {
            recordStatus = 1; // 默认未完成

            // 获取现有答题数据
            List<UserQuestionAnswerDataDO> existingAnswers = userQuestionAnswerDataService.lambdaQuery()
                    .eq(UserQuestionAnswerDataDO::getRecordId, recordId)
                    .eq(UserQuestionAnswerDataDO::getQuestionId, reqVO.getQuestionId())
                    .list();

            if (!existingAnswers.isEmpty()) {
                // 检查重复作答
                Set<Long> existingDetailIds = existingAnswers.stream()
                        .map(UserQuestionAnswerDataDO::getQuestionDetailId)
                        .collect(Collectors.toSet());
                Set<Long> currentDetailIds = reqVO.getUserAnswerDetailList().stream()
                        .map(AppUserAnswerDetailSaveVO::getQuestionDetailId)
                        .collect(Collectors.toSet());

                for (Long existingDetailId : existingDetailIds) {
                    if (currentDetailIds.contains(existingDetailId)) {
                        log.error("用户已作答该题，recordId={} 本次作答数据:{}", recordId, JSON.toJSON(reqVO));
                        throw exception(LOCKED);
                    }
                }

                // 检查是否完成所有小题
                int totalAnswered = reqVO.getUserAnswerDetailList().size() + existingAnswers.size();
                if (context.getQuestionDetailVersionDOS().size() == totalAnswered) {
                    recordStatus = 2; // 已完成
                }
            } else {
                // 检查是否一次性完成所有小题
                if (context.getQuestionDetailVersionDOS().size() == reqVO.getUserAnswerDetailList().size()) {
                    recordStatus = 2; // 已完成
                }
            }
        }

        return recordStatus;
    }

    /**
     * 确定最终记录状态
     */
    private Integer determineFinalRecordStatus(AppUserAnswerSaveVO reqVO, QuestionContext context) {
        Integer recordStatus = context.getRecordStatus();

        if (recordStatus == 2 && context.getQuestionDetailVersionDOS() != null
                && !context.getQuestionDetailVersionDOS().isEmpty()
                && context.getQuestionDetailVersionDOS().size() == reqVO.getUserAnswerDetailList().size()) {
            return 2; // 已完成
        }

        return 1; // 进行中
    }

    /**
     * 更新记录数据
     */
    private void updateRecordData(UserQuestionAnswerRecordDO recordDO, AppUserAnswerSaveVO reqVO,
                                  QuestionContext context, AnswerStatistics statistics, Integer finalRecordStatus) {
        Integer totalAnswerTime = context.getAnswerTime() + recordDO.getAnswerTime();

        recordDO.setAnswerDate(reqVO.getAnswerDate());
        recordDO.setStartTime(reqVO.getStartTime());
        recordDO.setEndTime(reqVO.getEndTime());
        recordDO.setQuestionNum(Optional.ofNullable(recordDO.getQuestionNum()).orElse(0) + statistics.getExerciseCount());
        recordDO.setCorrectNum(Optional.ofNullable(recordDO.getCorrectNum()).orElse(0) + statistics.getCorrectNum());
        recordDO.setAnswerTime(totalAnswerTime);
        recordDO.setRecordStatus(finalRecordStatus);

        // 连词成句特殊处理
        if (QuestionTypeEnum.WRITING_CONSTRUCT_SENTENCE.getCode() == context.getQuestion().getTypeId()) {
            recordDO.setTotalScore(statistics.getWordCount());
            recordDO.setUserScore(statistics.getUserCorrectWord());
        }

        context.setAnswerTime(totalAnswerTime);
    }

    /**
     * 构建新记录
     */
    private UserQuestionAnswerRecordDO buildNewRecord(AppUserAnswerSaveVO reqVO, long userId,
                                                      QuestionContext context, AnswerStatistics statistics, Integer finalRecordStatus) {
        UserQuestionAnswerRecordDO recordDO = new UserQuestionAnswerRecordDO();
        QuestionRespVO question = context.getQuestion();

        recordDO.setUserId(userId);
        recordDO.setHskLevel(question.getHskLevel());
        recordDO.setTextbookId(question.getTextbookId());
        recordDO.setChapterId(question.getChapterId());
        recordDO.setUnitId(question.getUnitId());
        recordDO.setQuestionTypeId(question.getTypeId());
        recordDO.setSubject(question.getSubject());
        recordDO.setVersion(reqVO.getVersion());
        recordDO.setPracticeMode(reqVO.getPracticeMode());
        recordDO.setPracticeId(reqVO.getPracticeId());
        recordDO.setAnswerTime(reqVO.getAnswerTime());
        recordDO.setAnswerDate(reqVO.getAnswerDate());
        recordDO.setStartTime(reqVO.getStartTime());
        recordDO.setEndTime(reqVO.getEndTime());
        recordDO.setRecordStatus(finalRecordStatus);
        recordDO.setQuestionNum(statistics.getExerciseCount());
        recordDO.setCorrectNum(statistics.getCorrectNum());
        recordDO.setQuestionVersionId(context.getQuestionVersionDO().getId());
        recordDO.setQuestionId(reqVO.getQuestionId());

        // 连词成句特殊处理
        if (QuestionTypeEnum.WRITING_CONSTRUCT_SENTENCE.getCode() == question.getTypeId()) {
            recordDO.setTotalScore(statistics.getWordCount());
            recordDO.setUserScore(statistics.getUserCorrectWord());
        }

        return recordDO;
    }

    /**
     * 保存答题数据
     */
    private void saveAnswerData(AppUserAnswerSaveVO reqVO, Long recordId, QuestionContext context) {
        List<AppUserAnswerDetailSaveVO> userAnswerDetailList = reqVO.getUserAnswerDetailList();

        // 删除旧数据
        removeOldAnswerData(recordId, userAnswerDetailList);

        // 构建新数据
        List<UserQuestionAnswerDataDO> newAnswerData = buildNewAnswerData(userAnswerDetailList, recordId, context);

        // 保存新数据
        userQuestionAnswerDataService.saveBatch(newAnswerData);


        // 错题收藏
        if (Objects.equals(reqVO.getPracticeMode(), PracticeModeEnum.TYPE_PRACTICE.getCode())) {
            favoriteWrongQuestion(newAnswerData);
        }
    }

    /**
     * 错题收藏
     *
     * @param newAnswerData
     */
    private void favoriteWrongQuestion(List<UserQuestionAnswerDataDO> newAnswerData) {
        List<QuestionTypeEnum> singleQuestionTypeList = QuestionTypeEnum.getSingleQuestionTypeList();
        Set<Long> questionIds = newAnswerData.stream().map(UserQuestionAnswerDataDO::getQuestionId).collect(Collectors.toSet());
        List<QuestionDO> questionDOS = questionService.listByIds(questionIds);
        Map<Long, QuestionDO> questionDOMap = questionDOS.stream().collect(Collectors.toMap(QuestionDO::getId, Function.identity(), (k1, k2) -> k1));

        List<UserFavoriteTargetDto> targetList = new ArrayList<>();
        newAnswerData.forEach(answerDataDO -> {
            if (answerDataDO.getIsCorrect() != null && !answerDataDO.getIsCorrect()) {
                UserFavoriteTargetDto targetDto = new UserFavoriteTargetDto();
                QuestionDO questionDO = questionDOMap.getOrDefault(answerDataDO.getQuestionId(), null);
                if (questionDO != null && singleQuestionTypeList.contains(QuestionTypeEnum.getByCode(questionDO.getTypeId()))) {
                    targetDto.setTargetId(answerDataDO.getQuestionId());
                    targetDto.setTargetDetailId(answerDataDO.getQuestionDetailId());
                } else {
                    targetDto.setTargetId(answerDataDO.getQuestionId());
                }
                targetList.add(targetDto);
            }
        });

        userFavoriteApi.addRealQuestionBatch(StpUtil.getLoginIdAsLong(), FavoriteSourceEnum.REAL_EXAM_PRACTICE.getCode(), targetList, true);

    }

    /**
     * 删除旧答题数据
     */
    private void removeOldAnswerData(Long recordId, List<AppUserAnswerDetailSaveVO> userAnswerDetailList) {
        if (recordId == null) {
            return;
        }

        Set<Long> questionDetailIds = userAnswerDetailList.stream()
                .map(AppUserAnswerDetailSaveVO::getQuestionDetailId)
                .collect(Collectors.toSet());

        List<UserQuestionAnswerDataDO> oldAnswerDatas = userQuestionAnswerDataService.lambdaQuery()
                .eq(UserQuestionAnswerDataDO::getRecordId, recordId)
                .in(UserQuestionAnswerDataDO::getQuestionDetailId, questionDetailIds)
                .list();

        if (!oldAnswerDatas.isEmpty()) {
            Set<Long> oldDataIds = oldAnswerDatas.stream()
                    .map(UserQuestionAnswerDataDO::getId)
                    .collect(Collectors.toSet());
            userQuestionAnswerDataService.removeByIds(oldDataIds);
        }
    }

    /**
     * 构建新答题数据
     */
    private List<UserQuestionAnswerDataDO> buildNewAnswerData(List<AppUserAnswerDetailSaveVO> userAnswerDetailList,
                                                              Long recordId, QuestionContext context) {
        Map<Long, QuestionDetailVersionDO> versionDOMap = context.getQuestionDetailVersionDOS().stream()
                .collect(Collectors.toMap(QuestionDetailVersionDO::getQuestionDetailId, Function.identity(), (k1, k2) -> k1));

        List<UserQuestionAnswerDataDO> answerDataList = new ArrayList<>();
        for (AppUserAnswerDetailSaveVO userAnswerDetail : userAnswerDetailList) {
            UserQuestionAnswerDataDO answerData = BeanUtils.toBean(userAnswerDetail, UserQuestionAnswerDataDO.class);
            answerData.setRecordId(recordId);
            answerData.setAiCorrectStatus(0);

            if (versionDOMap.containsKey(userAnswerDetail.getQuestionDetailId())) {
                answerData.setQuestionDetailVersionId(versionDOMap.get(userAnswerDetail.getQuestionDetailId()).getId());
            }

            answerDataList.add(answerData);
        }

        return answerDataList;
    }

    /**
     * 更新统计信息
     */
    private void updateStatistics(AppUserAnswerSaveVO reqVO, int exerciseCount, int correctNum, Integer answerTime) {
        // 更新Redis统计
        updateRedisStatistics(reqVO.getQuestionId(), exerciseCount, correctNum);

        // 更新练习记录统计
        updatePracticeRecordStatistics(reqVO, exerciseCount, correctNum, answerTime);
    }

    /**
     * 更新Redis统计信息
     */
    private void updateRedisStatistics(Long questionId, int exerciseCount, int correctNum) {
        try {
            String dateStr = DateUtil.format(new Date(), "yyyy-MM-dd");
            String answerCountKey = String.format(RedisKeyPrefix.QUESTION_ANSWER_COUNT, dateStr, questionId);
            String correctCountKey = String.format(RedisKeyPrefix.QUESTION_ANSWER_CORRECT_COUNT, dateStr, questionId);

            if (exerciseCount > 0) {
                redisUtil.increment(answerCountKey, exerciseCount);
            }
            if (correctNum > 0) {
                redisUtil.increment(correctCountKey, correctNum);
            }
        } catch (RedisException e) {
            log.error("更新Redis统计失败: questionId={}", questionId, e);
        }
    }

    /**
     * 更新练习记录统计
     */
    private void updatePracticeRecordStatistics(AppUserAnswerSaveVO reqVO, int exerciseCount, int correctNum, Integer answerTime) {
        UserPracticeRecordDO practiceRecord = userPracticeRecordService.getById(reqVO.getPracticeId());
        if (practiceRecord != null) {
            practiceRecord.setAnswerNum(Optional.ofNullable(practiceRecord.getAnswerNum()).orElse(0) + exerciseCount);
            practiceRecord.setCorrectNum(Optional.ofNullable(practiceRecord.getCorrectNum()).orElse(0) + correctNum);
            practiceRecord.setAnswerTime(Optional.ofNullable(practiceRecord.getAnswerTime()).orElse(0) + answerTime);
            userPracticeRecordService.updateById(practiceRecord);
        }
    }

    /**
     * 题目上下文信息
     */
    @Data
    @Builder
    private static class QuestionContext {
        private QuestionVersionDO questionVersionDO;
        private QuestionRespVO question;
        private List<QuestionDetailVersionDO> questionDetailVersionDOS;
        private Integer recordStatus;
        private Integer answerTime;
        private int exerciseCount;
        private int correctNum;
    }

    /**
     * 答题统计信息
     */
    @Data
    @Builder
    private static class AnswerStatistics {
        private int exerciseCount;
        private int correctNum;
        private int wordCount;
        private int userCorrectWord;
    }

    /**
     * 按照输入的questionIds顺序对题目列表进行排序
     *
     * @param questionVos 题目列表
     * @param questionIds 输入的题目ID顺序
     * @return 排序后的题目列表
     */
    private List<AppQuestionVo> sortQuestionsByInputOrder(List<AppQuestionVo> questionVos, List<Long> questionIds) {
        if (questionIds == null || questionIds.isEmpty() || questionVos == null || questionVos.isEmpty()) {
            return questionVos;
        }

        // 创建ID到索引的映射，用于确定排序顺序
        Map<Long, Integer> orderMap = new HashMap<>();
        for (int i = 0; i < questionIds.size(); i++) {
            orderMap.put(questionIds.get(i), i);
        }

        // 按照输入顺序排序
        return questionVos.stream()
                .sorted((q1, q2) -> {
                    Integer order1 = orderMap.get(q1.getId());
                    Integer order2 = orderMap.get(q2.getId());

                    // 如果某个ID不在输入列表中，放到最后
                    if (order1 == null && order2 == null) {
                        return 0;
                    }
                    if (order1 == null) {
                        return 1;
                    }
                    if (order2 == null) {
                        return -1;
                    }

                    return order1.compareTo(order2);
                })
                .collect(Collectors.toList());
    }


    /**
     * 按照输入的questionIds顺序对题目列表进行排序
     *
     * @param questionVos 题目列表
     * @param questionIds 输入的题目ID顺序
     * @return 排序后的题目列表
     */
    private List<AppUserQuestionStatusVO> sortQuestionsByInputOrder2(List<AppUserQuestionStatusVO> questionVos, List<Long> questionIds) {
        if (questionIds == null || questionIds.isEmpty() || questionVos == null || questionVos.isEmpty()) {
            return questionVos;
        }

        // 创建ID到索引的映射，用于确定排序顺序
        Map<Long, Integer> orderMap = new HashMap<>();
        for (int i = 0; i < questionIds.size(); i++) {
            orderMap.put(questionIds.get(i), i);
        }

        // 按照输入顺序排序
        return questionVos.stream()
                .sorted((q1, q2) -> {
                    Integer order1 = orderMap.get(q1.getQuestionId());
                    Integer order2 = orderMap.get(q2.getQuestionId());

                    // 如果某个ID不在输入列表中，放到最后
                    if (order1 == null && order2 == null) {
                        return 0;
                    }
                    if (order1 == null) {
                        return 1;
                    }
                    if (order2 == null) {
                        return -1;
                    }

                    return order1.compareTo(order2);
                })
                .collect(Collectors.toList());
    }
}