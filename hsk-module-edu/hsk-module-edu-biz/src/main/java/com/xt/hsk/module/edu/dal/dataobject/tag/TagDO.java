package com.xt.hsk.module.edu.dal.dataobject.tag;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.*;

import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.experimental.Accessors;

/**
 * 标签 DO
 *
 * <AUTHOR>
 */
@TableName("edu_tag")
@KeySequence("edu_tag_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = false)
public class TagDO extends BaseDO {

    /**
     * 唯一ID（自增）
     */
    @TableId
    private Long id;
    /**
     * 标签名
     */
    @ExcelProperty("*标签名称")
    private String tagName;
    /**
     * 备注
     */
    @ExcelProperty("备注信息")
    private String remark;
    /**
     * 状态 0开启 1关闭
     */
    private Integer status;

}