package com.xt.hsk.module.edu.enums.exam;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 模考答题模式枚举
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Getter
@AllArgsConstructor
public enum ExamAnswerModeEnum implements BasicEnum<Integer>, VO, ArrayValuable<Integer> {
    /**
     * 答题模式
     */
    REDO(1, "重新作答"),
    CONTINUE(2, "继续作答"),
    NEW(3, "新练习"),
    ;

    private final Integer code;
    private final String desc;

    private static final Integer[] ARRAYS = Arrays.stream(values())
            .map(ExamAnswerModeEnum::getCode)
            .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}
