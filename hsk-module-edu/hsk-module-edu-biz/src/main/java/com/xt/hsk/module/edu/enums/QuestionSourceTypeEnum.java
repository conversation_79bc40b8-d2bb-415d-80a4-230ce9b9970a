package com.xt.hsk.module.edu.enums;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 题目来源类型枚举
 *
 * <AUTHOR>
 * @since 2025/06/12
 */
@Getter
@AllArgsConstructor
public enum QuestionSourceTypeEnum implements BasicEnum<Integer> {

    /**
     * 专项练习
     */
    SPECIAL_PRACTICE(1, "专项练习"),

    /**
     * 真题练习
     */
    REAL_EXAM(2, "真题练习"),

    /**
     * 本地视频
     */
    LOCAL_VIDEO(3, "本地视频");

    /**
     * 类型
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

} 