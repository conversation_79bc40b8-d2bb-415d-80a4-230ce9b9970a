package com.xt.hsk.module.edu.manager.course;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_COURSE_NOT_EXISTS;

import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.course.vo.RecommendedCoursePageReqVO;
import com.xt.hsk.module.edu.controller.admin.course.vo.RecommendedCourseRespVO;
import com.xt.hsk.module.edu.controller.admin.course.vo.RecommendedCourseSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.course.vo.RecommendedCourseUpdateSortReqVO;
import com.xt.hsk.module.edu.dal.dataobject.course.RecommendedCourseDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseDO;
import com.xt.hsk.module.edu.service.course.RecommendedCourseService;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseService;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 课程推荐 Manager，负责业务逻辑的编排
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@Component
public class RecommendedCourseManager {

    @Resource
    private RecommendedCourseService recommendedCourseService;

    @Resource
    private EliteCourseService eliteCourseService;


    /**
     * 创建课程推荐
     *
     * @param saveReqVO 创建信息
     * @return 创建的课程推荐ID列表
     */
    @LogRecord(type = LogRecordType.RECOMMENDED_COURSE, success = "创建推荐课程", bizNo = "{{#ids}}")
    @Transactional(rollbackFor = Exception.class)
    public List<Long> createRecommendedCourse(RecommendedCourseSaveReqVO saveReqVO) {
        List<Long> ids = new ArrayList<>();

        // 获取课程信息，提取HSK等级
        for (Long courseId : saveReqVO.getCourseIds()) {
            // 查询课程信息
            EliteCourseDO course = eliteCourseService.getEliteCourse(courseId);
            if (course == null) {
                throw exception(ELITE_COURSE_NOT_EXISTS);
            }

            // 获取该HSK等级下的最大排序序号
            Integer maxSort = recommendedCourseService.getMaxSortByHskLevel(course.getHskLevel());

            // 创建推荐课程记录
            RecommendedCourseDO recommendedCourse = new RecommendedCourseDO();
            recommendedCourse.setCourseId(courseId);
            // 排序序号递增
            recommendedCourse.setSort(maxSort + 1);

            recommendedCourseService.save(recommendedCourse);
            ids.add(recommendedCourse.getId());
        }

        return ids;
    }

    /**
     * 更新课程推荐排序
     * <p>
     * 排序规则：同一HSK等级下排序不重复，采用区间调整法确保序号连续性
     * </p>
     *
     * @param updateSortReqVO 更新排序信息
     */
    @LogRecord(type = LogRecordType.RECOMMENDED_COURSE,
        success = "将推荐ID为【{{#recommendedCourse.id}}的排序从{{#oldSort}}修改为{{#recommendedCourse.sort}}】",
        bizNo = "{{#recommendedCourse.id}}")
    @Transactional(rollbackFor = Exception.class)
    public void updateRecommendedCourseSort(RecommendedCourseUpdateSortReqVO updateSortReqVO) {
        // 查询推荐课程是否存在
        RecommendedCourseDO recommendedCourse = recommendedCourseService.getById(
            updateSortReqVO.getId());
        if (recommendedCourse == null) {
            return;
        }
        
        Integer oldSort = recommendedCourse.getSort();
        Integer newSort = updateSortReqVO.getSort();
        
        // 不需要调整
        if (oldSort.equals(newSort)) {
            return;
        }
        
        // 获取课程信息，用于获取HSK等级
        EliteCourseDO course = eliteCourseService.getEliteCourse(recommendedCourse.getCourseId());
        if (course == null) {
            throw exception(ELITE_COURSE_NOT_EXISTS);
        }
        
        Integer hskLevel = course.getHskLevel();
        
        // 在同一HSK等级下调整序号
        if (newSort < oldSort) {
            // 向前移动：将[newSort, oldSort]范围内的推荐课程序号+1
            recommendedCourseService.updateSortInRange(hskLevel, newSort, oldSort, 1);
        } else {
            // 向后移动：将[oldSort, newSort]范围内的推荐课程序号-1
            recommendedCourseService.updateSortInRange(hskLevel, oldSort, newSort, -1);
        }

        // 更新当前推荐课程的排序
        RecommendedCourseDO recommendedCourseDO = new RecommendedCourseDO();
        recommendedCourseDO.setId(recommendedCourse.getId());
        recommendedCourseDO.setSort(newSort);
        recommendedCourseService.updateById(recommendedCourseDO);

        LogRecordContext.putVariable("recommendedCourse", recommendedCourse);
        LogRecordContext.putVariable("oldSort", oldSort);
    }

    /**
     * 删除课程推荐
     *
     * @param id 编号
     */
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = LogRecordType.RECOMMENDED_COURSE,
        subType = "删除推荐课程",
        success = "删除推荐课程",
        bizNo = "{{#id}}")
    public void deleteRecommendedCourse(Long id) {
        recommendedCourseService.removeById(id);
    }

    /**
     * 获取推荐课程分页数据，包含课程详细信息
     *
     * @param pageVO 分页查询参数
     * @return 推荐课程分页列表
     */
    public PageResult<RecommendedCourseRespVO> getRecommendedCoursePage(
        RecommendedCoursePageReqVO pageVO) {
        // 直接使用连接查询，包含HSK等级和课程名称的过滤
        return recommendedCourseService.selectJoinPage(pageVO);
    }
} 