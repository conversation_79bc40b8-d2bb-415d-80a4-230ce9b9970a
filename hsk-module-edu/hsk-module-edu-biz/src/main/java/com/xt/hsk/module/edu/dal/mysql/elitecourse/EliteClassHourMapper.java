package com.xt.hsk.module.edu.dal.mysql.elitecourse;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteClassHourPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteClassHourDO;
import com.xt.hsk.module.edu.manager.elitecourse.dto.CourseHourCountDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 精品课课时 Mapper
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface EliteClassHourMapper extends BaseMapperX<EliteClassHourDO> {

    default PageResult<EliteClassHourDO> selectPage(EliteClassHourPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<EliteClassHourDO>()
                .eqIfPresent(EliteClassHourDO::getCourseId, reqVO.getCourseId())
                .eqIfPresent(EliteClassHourDO::getChapterId, reqVO.getChapterId())
                .eqIfPresent(EliteClassHourDO::getReuseId, reqVO.getReuseId())
                .eqIfPresent(EliteClassHourDO::getClassHourNameCn, reqVO.getClassHourNameCn())
                .eqIfPresent(EliteClassHourDO::getClassHourNameEn, reqVO.getClassHourNameEn())
                .eqIfPresent(EliteClassHourDO::getClassHourNameOt, reqVO.getClassHourNameOt())
                .eqIfPresent(EliteClassHourDO::getClassHourType, reqVO.getClassHourType())
                .eqIfPresent(EliteClassHourDO::getVideoId, reqVO.getVideoId())
                .eqIfPresent(EliteClassHourDO::getSort, reqVO.getSort())
                .betweenIfPresent(EliteClassHourDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(EliteClassHourDO::getId));
    }

    List<CourseHourCountDto> getEliteClassHourCount(List<Long> courseIds);
}