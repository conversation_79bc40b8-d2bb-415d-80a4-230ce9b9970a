package com.xt.hsk.module.edu.dal.mysql.course;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.course.vo.RecommendedCoursePageReqVO;
import com.xt.hsk.module.edu.controller.admin.course.vo.RecommendedCourseRespVO;
import com.xt.hsk.module.edu.controller.app.elitecourse.vo.EliteCourseDetailRespVO;
import com.xt.hsk.module.edu.dal.dataobject.course.RecommendedCourseDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 课程推荐 Mapper
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@Mapper
public interface RecommendedCourseMapper extends BaseMapperX<RecommendedCourseDO> {

    /**
     * 分页查询推荐课程
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    default PageResult<RecommendedCourseDO> selectPage(RecommendedCoursePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<RecommendedCourseDO>()
            .orderByAsc(RecommendedCourseDO::getSort)
            .orderByDesc(RecommendedCourseDO::getId));
    }

    /**
     * 分页查询推荐课程，包含课程信息，支持HSK等级和课程名称筛选
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    default PageResult<RecommendedCourseRespVO> selectJoinPage(RecommendedCoursePageReqVO reqVO) {
        // 使用MyBatis Plus Join进行连表查询
        MPJLambdaWrapper<RecommendedCourseDO> wrapper = new MPJLambdaWrapper<RecommendedCourseDO>()
            // 选择RecommendedCourseDO的所有字段
            .selectAll(RecommendedCourseDO.class)
            // 选择EliteCourseDO的需要字段
            .select(EliteCourseDO::getCourseNameCn)
            .select(EliteCourseDO::getCoverUrlLarge)
            .select(EliteCourseDO::getSellingPriceCn)
            .select(EliteCourseDO::getHskLevel)
            .select(EliteCourseDO::getListingStatus)
            .select(EliteCourseDO::getIsShow)
            // 左连接课程表
            .leftJoin(EliteCourseDO.class, EliteCourseDO::getId, RecommendedCourseDO::getCourseId);

        // 添加过滤条件
        if (reqVO.getHskLevel() != null) {
            wrapper.eq(EliteCourseDO::getHskLevel, reqVO.getHskLevel());
        }
        if (reqVO.getCourseName() != null && !reqVO.getCourseName().isEmpty()) {
            wrapper.like(EliteCourseDO::getCourseNameCn, reqVO.getCourseName());
        }

        // 添加排序
        wrapper.orderByAsc(EliteCourseDO::getHskLevel)
            .orderByAsc(RecommendedCourseDO::getSort);

        // 执行分页查询
        return selectJoinPage(reqVO, RecommendedCourseRespVO.class, wrapper);
    }

    /**
     * 查询特定HSK等级下所有推荐课程，包含课程信息
     *
     * @param hskLevel HSK等级
     * @return 推荐课程列表
     */
    default List<EliteCourseDetailRespVO.RecommendedCourseVO> selectRecommendedCoursesByHskLevel(
        Integer hskLevel, Long notCourseId) {
        // 使用MyBatis Plus Join进行连表查询
        MPJLambdaWrapper<RecommendedCourseDO> wrapper = new MPJLambdaWrapper<RecommendedCourseDO>()
            // 选择EliteCourseDO的需要字段
            .select(EliteCourseDO::getId)
            .select(EliteCourseDO::getCourseNameCn)
            .select(EliteCourseDO::getCourseNameEn)
            .select(EliteCourseDO::getCourseNameOt)
            .select(EliteCourseDO::getCoverUrlSmall)
            .select(EliteCourseDO::getSellingPriceCn)
            .select(EliteCourseDO::getSellingPriceEn)
            .select(EliteCourseDO::getSellingPriceOt)
            .select(EliteCourseDO::getSalesBase)
            .select(EliteCourseDO::getEnrollmentCount)
            // 选择RecommendedCourseDO的排序字段
            .select(RecommendedCourseDO::getSort)
            // 左连接课程表
            .leftJoin(EliteCourseDO.class, EliteCourseDO::getId, RecommendedCourseDO::getCourseId)
            // 添加过滤条件
            .eq(EliteCourseDO::getHskLevel, hskLevel)
            // 上架状态：1-上架
            .eq(EliteCourseDO::getListingStatus, 1)
            // 展示状态：1-显示
            .eq(EliteCourseDO::getIsShow, 1)
            // 需要过滤的课程ID
            .neIfExists(EliteCourseDO::getId, notCourseId)
            // 添加排序
            .orderByDesc(EliteCourseDO::getHskLevel)
            .orderByAsc(RecommendedCourseDO::getSort);

        // 执行查询
        return selectJoinList(EliteCourseDetailRespVO.RecommendedCourseVO.class, wrapper);
    }

    /**
     * 查询特定HSK等级下最大的排序序号
     *
     * @param hskLevel HSK等级
     * @return 最大排序序号
     */
    default Integer selectMaxSortByHskLevel(Integer hskLevel) {
        // 使用MyBatis Plus Join进行连表查询
        MPJLambdaWrapper<RecommendedCourseDO> wrapper = new MPJLambdaWrapper<RecommendedCourseDO>()
            .selectMax(RecommendedCourseDO::getSort)
            .leftJoin(EliteCourseDO.class, EliteCourseDO::getId, RecommendedCourseDO::getCourseId)
            .eq(EliteCourseDO::getHskLevel, hskLevel)
            .eq(RecommendedCourseDO::getDeleted, false);

        // 执行查询并获取结果
        Object result = selectJoinOne(Object.class, wrapper);
        return result != null ? (Integer) result : 0;
    }
    
    /**
     * 在指定HSK等级下更新排序区间内的推荐课程排序
     *
     * @param hskLevel HSK等级
     * @param startSort 开始排序值
     * @param endSort 结束排序值
     * @param increment 增量值（可为正数或负数）
     * @return 更新的记录数
     */
    @Update("UPDATE edu_recommended_course rc " +
            "JOIN edu_elite_course ec ON rc.course_id = ec.id " +
            "SET rc.sort = rc.sort + #{increment},rc.update_time = now()" +
            "WHERE ec.hsk_level = #{hskLevel} " +
            "AND rc.sort >= #{startSort} " +
            "AND rc.sort <= #{endSort} " +
            "AND rc.deleted = 0")
    int updateSortInRange(@Param("hskLevel") Integer hskLevel,
                          @Param("startSort") Integer startSort,
                          @Param("endSort") Integer endSort,
                          @Param("increment") Integer increment);
} 