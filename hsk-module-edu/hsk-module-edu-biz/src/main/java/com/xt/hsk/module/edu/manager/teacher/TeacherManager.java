package com.xt.hsk.module.edu.manager.teacher;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.teacher.vo.TeacherPageReqVO;
import com.xt.hsk.module.edu.controller.admin.teacher.vo.TeacherRespVO;
import com.xt.hsk.module.edu.controller.admin.teacher.vo.TeacherSaveReqVO;
import com.xt.hsk.module.edu.convert.course.TeacherConvert;
import com.xt.hsk.module.edu.dal.dataobject.teacher.TeacherDO;
import com.xt.hsk.module.edu.enums.ErrorCodeConstants;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseTeacherService;
import com.xt.hsk.module.edu.service.teacher.TeacherService;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 讲师管理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TeacherManager {

    @Resource
    private TeacherService teacherService;

    @Resource
    private EliteCourseTeacherService eliteCourseTeacherService;

    /**
     * 创建讲师 新增时序号默认为1，其他历史讲师序号自动+1
     *
     * @param createReqVO create req vo
     * @return {@code Long }
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createTeacher(TeacherSaveReqVO createReqVO) {
        // 校验手机号和区号唯一性
        validateCountryCodeAndMobileUnique(null, createReqVO.getCountryCode(),
            createReqVO.getMobile());

        // 校验昵称唯一性
        validateNicknameUnique(createReqVO.getTeacherNameCn(), null);

        // 插入
        TeacherDO teacher = TeacherConvert.INSTANCE.createReqVOToDO(createReqVO);

        // 只将序号大于等于1的讲师序号+1，为新讲师腾出位置
        teacherService.update()
            .setSql("sort = sort + 1")
            .ge("sort", 1)
            .update();
        teacher.setSort(1);

        teacherService.save(teacher);

        // 记录操作日志上下文
        LogRecordContext.putVariable("teacherId", teacher.getId());
        LogRecordContext.putVariable("teacher", teacher);
        // 返回
        return teacher.getId();
    }

    /**
     * 校验昵称唯一性
     */
    private void validateNicknameUnique(String nickname, Long id) {
        TeacherDO existTeacher = teacherService.lambdaQuery()
            .eq(TeacherDO::getTeacherNameCn, nickname)
            .one();
        if (existTeacher != null && !existTeacher.getId().equals(id)) {
            throw exception(ErrorCodeConstants.TEACHER_NICKNAME_EXISTS);
        }
    }

    /**
     * 更新讲师
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTeacher(TeacherSaveReqVO updateReqVO) {
        // 校验存在
        validateTeacherExists(updateReqVO.getId());
        // 校验手机号和区号唯一性
        validateCountryCodeAndMobileUnique(updateReqVO.getId(), updateReqVO.getCountryCode(),
            updateReqVO.getMobile());
        // 校验昵称唯一性
        validateNicknameUnique(updateReqVO.getTeacherNameCn(), updateReqVO.getId());
        // 更新
        TeacherDO updateObj = TeacherConvert.INSTANCE.createReqVOToDO(updateReqVO);
        teacherService.updateById(updateObj);

        // 记录操作日志上下文
        LogRecordContext.putVariable("teacher", updateObj);
    }

    /**
     * 删除讲师
     *
     * @param id 主键id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteTeacher(Long id) {
        // 校验存在
        TeacherDO teacherDO = validateTeacherExists(id);

        // 设置日志上下文变量
        LogRecordContext.putVariable("teacher", teacherDO);

        // 修改删除时间 防止后面新增时破坏索引
        teacherDO.setDeleteTime(DateUtil.currentSeconds());
        teacherService.updateById(teacherDO);
        // 删除
        teacherService.removeById(id);
    }

    private TeacherDO validateTeacherExists(Long id) {
        TeacherDO teacherDO = teacherService.getById(id);
        if (teacherDO == null) {
            throw exception(ErrorCodeConstants.TEACHER_NOT_EXISTS);
        }
        return teacherDO;
    }

    /**
     * 校验手机号和区号唯一性
     */
    private void validateCountryCodeAndMobileUnique(Long id, String countryCode, String mobile) {
        TeacherDO existTeacher = teacherService.getByCountryCodeAndMobile(countryCode, mobile);
        if (existTeacher != null && !existTeacher.getId().equals(id)) {
            throw exception(ErrorCodeConstants.TEACHER_COUNTRY_CODE_MOBILE_EXISTS);
        }
    }

    /**
     * 获取讲师
     *
     * @param id 主键id
     * @return {@code TeacherDO }
     */
    public TeacherDO getTeacher(Long id) {
        return teacherService.getById(id);
    }

    public PageResult<TeacherRespVO> getTeacherVoPage(TeacherPageReqVO pageReqVO) {
        // 查询讲师分页数据
        PageResult<TeacherDO> teacherPage = teacherService.getTeacherPage(pageReqVO);
        List<TeacherDO> list = teacherPage.getList();

        // 如果列表为空，直接返回空结果
        if (CollUtil.isEmpty(list)) {
            return new PageResult<>(CollUtil.newArrayList(), teacherPage.getTotal());
        }

        // 转换为VO对象列表
        List<TeacherRespVO> teacherRespVOList = TeacherConvert.INSTANCE.doListToRespVOList(list);

        // 获取所有讲师ID
        List<Long> teacherIds = list.stream().map(TeacherDO::getId).toList();

        // 查询讲师关联的未删除课程数量
        Map<Long, Integer> teacherCourseCountMap = eliteCourseTeacherService.countNotDeletedCourseByTeacherIds(
            teacherIds);

        // 设置每个讲师关联的课程数量
        teacherRespVOList.forEach(teacherRespVO -> {
            Integer courseCount = teacherCourseCountMap.getOrDefault(teacherRespVO.getId(), 0);
            teacherRespVO.setCourseCount(courseCount);
        });
        
        return new PageResult<>(teacherRespVOList, teacherPage.getTotal());
    }

    /**
     * 更新状态
     *
     * @param id id
     */
    public void updateStatus(Long id) {
        // 校验存在
        TeacherDO teacherDO = validateTeacherExists(id);
        teacherDO.setDisplayStatus(!teacherDO.getDisplayStatus());
        teacherService.updateById(teacherDO);

        // 记录操作日志上下文
        String statusText = teacherDO.getDisplayStatus() ? "显示" : "隐藏";
        LogRecordContext.putVariable("statusText", statusText);
        LogRecordContext.putVariable("teacher", teacherDO);
    }

    /**
     * 修改排序
     *
     * @param id   讲师ID
     */
    public void updateSort(Long id, Integer newSort) {
        // 校验存在
        TeacherDO teacher = validateTeacherExists(id);
        Integer oldSort = teacher.getSort();

        // 设置日志上下文变量
        LogRecordContext.putVariable("oldSort", oldSort);
        LogRecordContext.putVariable("teacher", teacher);

        // 不需要调整
        if (oldSort.equals(newSort)) {
            return;
        }

        if (newSort < oldSort) {
            // 向前移动：将[newSort, oldSort-1]范围内的讲师序号+1
            teacherService.update()
                .setSql("sort = sort + 1")
                .between("sort", newSort, oldSort - 1)
                .update();
        } else {
            // 向后移动：将[oldSort+1, newSort]范围内的讲师序号-1
            teacherService.update()
                .setSql("sort = sort - 1")
                .between("sort", oldSort + 1, newSort)
                .update();
        }

        // 设置该讲师的新序号
        teacher.setSort(newSort);
        teacher.setUpdateTime(LocalDateTime.now());
        teacherService.updateById(teacher);
    }

} 