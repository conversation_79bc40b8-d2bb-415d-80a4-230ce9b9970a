package com.xt.hsk.module.edu.dal.dataobject.interactivecourse;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 互动课课件 DO
 *
 * <AUTHOR>
 */
@TableName("edu_interactive_course_courseware")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InteractiveCourseCoursewareDO extends BaseDO {

    /**
     * 课件ID
     */
    @TableId
    private Long id;

    /**
     * 资料名称
     */
    private String materialName;

    /**
     * 资料URL
     */
    private String materialUrl;

    /**
     * 资料大小（字节）
     */
    private Long materialSize;

    /**
     * 单元id
     */
    private Long unitId;

    /**
     * 排序
     */
    private Integer sort;
} 