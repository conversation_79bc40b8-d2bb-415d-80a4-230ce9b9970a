package com.xt.hsk.module.edu.convert.elitecourse;

import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseRegisterUserPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseRegisterUserRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyStatsPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyStatsRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 精品课程学习记录 转换
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Mapper
public interface EliteCourseStudyRecordConvert {

    EliteCourseStudyRecordConvert INSTANCE = Mappers.getMapper(EliteCourseStudyRecordConvert.class);

    EliteCourseRegisterUserPageReqVO studyReqVoToRegisterUserReqVo(EliteCourseStudyStatsPageReqVO pageReqVO);

    EliteCourseStudyStatsRespVO registerUserRespVoToStudyStatsRespVo(EliteCourseRegisterUserRespVO pageReqVO);

}
