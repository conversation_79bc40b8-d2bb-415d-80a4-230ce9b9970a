package com.xt.hsk.module.edu.enums.exam;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 模考题型单元枚举
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Getter
@AllArgsConstructor
public enum ExamQuestionTypeUnitEnum implements BasicEnum<Integer>, VO, ArrayValuable<Integer> {
    /**
     * 模考题型单元枚举
     */
    PART_ONE(1, "第一部分"),
    PART_TWO(2, "第二部分"),
    PART_THREE(3, "第三部分"),
    PART_FOUR(4, "第四部分"),
    ;

    private static final Integer[] ARRAYS = Arrays.stream(values()).map(ExamQuestionTypeUnitEnum::getCode).toArray(Integer[]::new);
    private final Integer code;
    private final String desc;

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

    /**
     * 根据模考题型单元码获取对应的描述信息
     *
     * @param code 模考题型单元码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (ExamQuestionTypeUnitEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据模考题型单元码获取对应的枚举实例
     *
     * @param code 模考题型单元码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static ExamQuestionTypeUnitEnum getByCode(Integer code) {
        for (ExamQuestionTypeUnitEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
} 