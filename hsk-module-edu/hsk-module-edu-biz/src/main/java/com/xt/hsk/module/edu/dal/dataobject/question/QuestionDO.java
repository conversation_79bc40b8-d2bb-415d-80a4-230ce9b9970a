package com.xt.hsk.module.edu.dal.dataobject.question;

import lombok.*;


import com.baomidou.mybatisplus.annotation.*;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;

/**
 * 题目 DO
 *
 * <AUTHOR>
 */
@TableName("edu_question")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * HSK等级
     */
    private Integer hskLevel;
    /**
     * 教材ID
     */
    private Long textbookId;
    /**
     * 章节ID
     */
    private Long chapterId;
    /**
     * 单元ID
     */
    private Long unitId;
    /**
     * 题型ID
     */
    private Long typeId;
    /**
     * 科目
     */
    private Integer subject;
    /**
     * 材料音频
     */
    private String materialAudio;
    /**
     * 材料音频
     */
    private String materialAudioContent;
    /**
     * 材料图片
     */
    private String materialImage;
    /**
     * 材料文字
     */
    private String materialContent;
    /**
     * 题目选项 json
     */
    private String options;
    /**
     * 小题数量
     */
    private Integer questionNum;
    /**
     * 状态 0启用 1禁用
     */
    private Integer status;
    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;
    /**
     * 版本号
     */
    private Integer version;
    /**
     * 题目编码 #100000开始
     */
    private String questionCode;
    /**
     * 正确作答次数
     */
    private Integer correctAnswerCount;
    /**
     * 总作答次数
     */
    private Integer totalAnswerCount;

}