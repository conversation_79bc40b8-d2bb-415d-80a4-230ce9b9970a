package com.xt.hsk.module.edu.manager.exam.admin;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_QUESTION_TYPE_EXISTS_CANNOT_CREATE;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_QUESTION_TYPE_IN_USE_CANNOT_DELETE;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_QUESTION_TYPE_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.web.core.util.WebFrameworkUtils;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamQuestionTypeDeleteCheckRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamQuestionTypeDuplicateCheckReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamQuestionTypeDuplicateCheckRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamQuestionTypePageReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamQuestionTypeRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamQuestionTypeSaveReqVO;
import com.xt.hsk.module.edu.convert.exam.ExamQuestionTypeConvert;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamPaperRuleDetailDO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamQuestionTypeDO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiontype.QuestionTypeDO;
import com.xt.hsk.module.edu.service.exam.ExamPaperRuleDetailService;
import com.xt.hsk.module.edu.service.exam.ExamQuestionTypeService;
import com.xt.hsk.module.edu.service.question.questiontype.QuestionTypeService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 模考题型 后台 Manager
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Slf4j
@Component
public class ExamQuestionTypeAdminManager {

    @Resource
    private ExamQuestionTypeService examQuestionTypeService;

    @Resource
    private QuestionTypeService questionTypeService;

    @Resource
    private ExamPaperRuleDetailService examPaperRuleDetailService;

    /**
     * 创建模考题型
     */
    public Long createExamQuestionType(ExamQuestionTypeSaveReqVO createReqVO) {

        // 查询是否已经存在
        boolean exists = examQuestionTypeService.lambdaQuery()
                .eq(ExamQuestionTypeDO::getHskLevel, createReqVO.getHskLevel())
                .eq(ExamQuestionTypeDO::getSubject, createReqVO.getSubject())
                .eq(ExamQuestionTypeDO::getUnit, createReqVO.getUnit())
                .exists();
        if (exists) {
            throw exception(EXAM_QUESTION_TYPE_EXISTS_CANNOT_CREATE);
        }

        ExamQuestionTypeDO examQuestionType = ExamQuestionTypeConvert.INSTANCE.saveReqVoToDo(createReqVO);

        String questionTypeIds = JSONUtil.toJsonStr(createReqVO.getQuestionTypeIdList());
        examQuestionType.setQuestionTypeIds(questionTypeIds);

        examQuestionTypeService.save(examQuestionType);

        // 记录操作日志上下文
        LogRecordContext.putVariable("questionTypeId", examQuestionType.getId());

        return examQuestionType.getId();
    }

    /**
     * 更新模考题型
     */
    public void updateExamQuestionType(ExamQuestionTypeSaveReqVO updateReqVO) {
        // 校验存在
        validateExamQuestionTypeExists(updateReqVO.getId());

        String questionTypeIds = JSONUtil.toJsonStr(updateReqVO.getQuestionTypeIdList());

        examQuestionTypeService.lambdaUpdate()
                .eq(ExamQuestionTypeDO::getId, updateReqVO.getId())
                .set(ExamQuestionTypeDO::getQuestionTypeIds, questionTypeIds)
                .set(ExamQuestionTypeDO::getUpdateTime, LocalDateTime.now())
                .set(ExamQuestionTypeDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                .update();
    }

    /**
     * 删除模考题型
     */
    public void deleteExamQuestionType(Long id) {
        // 校验存在
        validateExamQuestionTypeExists(id);

        // 检查是否被组卷规则引用
        validateQuestionTypeNotInUse(id);

        // 删除
        examQuestionTypeService.removeById(id);
    }

    /**
     * 校验题型是否被组卷规则使用
     */
    private void validateQuestionTypeNotInUse(Long questionTypeId) {
        boolean inUse = examPaperRuleDetailService.lambdaQuery()
                .eq(ExamPaperRuleDetailDO::getExamQuestionTypeId, questionTypeId)
                .exists();

        if (inUse) {
            throw exception(EXAM_QUESTION_TYPE_IN_USE_CANNOT_DELETE);
        }
    }

    private void validateExamQuestionTypeExists(Long id) {
        if (examQuestionTypeService.getById(id) == null) {
            throw exception(EXAM_QUESTION_TYPE_NOT_EXISTS);
        }
    }

    /**
     * 根据id获取模考题型
     */
    public ExamQuestionTypeDO getExamQuestionType(Long id) {
        return examQuestionTypeService.getById(id);
    }

    /**
     * 分页获取模考题型
     */
    public PageResult<ExamQuestionTypeRespVO> getExamQuestionTypePage(@Valid ExamQuestionTypePageReqVO pageReqVO) {
        PageResult<ExamQuestionTypeDO> page = examQuestionTypeService.selectPage(pageReqVO);

        List<ExamQuestionTypeDO> list = page.getList();

        List<ExamQuestionTypeRespVO> voList = ExamQuestionTypeConvert.INSTANCE.doListToRespVoList(list);

        // 设置题型名称
        setQuestionName(voList);

        return new PageResult<>(voList, page.getTotal());
    }

    /**
     * 设置题型名称
     */
    private void setQuestionName(List<ExamQuestionTypeRespVO> voList) {
        // 如果传入的列表为空，直接返回
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        // 从所有 VO 中提取出所有非空的题型 ID 字符串，并解析成 Long 类型的 ID 集合
        List<Long> questionTypeIdList = voList.stream()
                .filter(vo -> CharSequenceUtil.isNotBlank(vo.getQuestionTypeIds()))
                .map(vo -> JSONUtil.toList(vo.getQuestionTypeIds(), Long.class))
                .filter(CollUtil::isNotEmpty)
                .flatMap(List::stream)
                .distinct()
                .toList();

        // 如果没有有效的题型 ID，则直接返回
        if (CollUtil.isEmpty(questionTypeIdList)) {
            return;
        }

        // 根据题型 ID 从数据库中查询题型名称，并构建 ID -> 名称 的映射 Map
        Map<Long, String> questionTypeNameMap = questionTypeService.lambdaQuery()
                .in(QuestionTypeDO::getId, questionTypeIdList)
                .list()
                .stream()
                .collect(Collectors.toMap(
                        QuestionTypeDO::getId,
                        QuestionTypeDO::getNameCn,
                        (v1, v2) -> v1
                ));

        // 遍历原始 VO 列表，设置每一项对应的题型名称列表
        for (ExamQuestionTypeRespVO vo : voList) {
            if (CharSequenceUtil.isBlank(vo.getQuestionTypeIds())) {
                continue;
            }

            // 将题型 ID 字符串转为 Long 类型列表，然后通过 ID 从 Map 中取出对应的名称
            List<String> questionTypeNameList = JSONUtil.toList(vo.getQuestionTypeIds(), Long.class)
                    .stream()
                    .map(questionTypeNameMap::get)
                    .filter(CharSequenceUtil::isNotBlank)
                    .toList();

            // 设置到响应对象中
            vo.setQuestionTypeNameList(questionTypeNameList);
        }
    }

    /**
     * 检查模考题型是否可以删除
     *
     * @param id 模考组卷规则id
     * @return 删除检查结果
     */
    public ExamQuestionTypeDeleteCheckRespVO checkExamQuestionTypeCanDelete(Long id) {
        // 校验模考组卷规则是否存在
        validateExamQuestionTypeExists(id);

        // 查询该模考组卷规则下的详情数量
        long detailCount = examPaperRuleDetailService.lambdaQuery()
                .eq(ExamPaperRuleDetailDO::getExamQuestionTypeId, id)
                .count();

        return new ExamQuestionTypeDeleteCheckRespVO(detailCount <= 0, detailCount);
    }

    /**
     * 检查模考题型是否存在重复数据
     *
     * @param checkReqVO 重复检查请求对象
     * @return 重复检查结果
     */
    public ExamQuestionTypeDuplicateCheckRespVO checkExamQuestionTypeDuplicate(ExamQuestionTypeDuplicateCheckReqVO checkReqVO) {
        Long duplicateCount = examQuestionTypeService.lambdaQuery()
                .eq(Objects.nonNull(checkReqVO.getExcludeId()), ExamQuestionTypeDO::getId, checkReqVO.getExcludeId())
                .eq(ExamQuestionTypeDO::getHskLevel, checkReqVO.getHskLevel())
                .eq(ExamQuestionTypeDO::getSubject, checkReqVO.getSubject())
                .eq(ExamQuestionTypeDO::getUnit, checkReqVO.getUnit())
                .count();

        return new ExamQuestionTypeDuplicateCheckRespVO(duplicateCount > 0, duplicateCount);
    }
}