package com.xt.hsk.module.edu.manager.elitecourse;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.web.core.util.WebFrameworkUtils;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteClassHourPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteClassHourReuseRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteClassHourSaveReqVO;
import com.xt.hsk.module.edu.convert.elitecourse.EliteClassHourConvert;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteChapterDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteClassHourDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseVideoDO;
import com.xt.hsk.module.edu.enums.elitecourse.EliteClassHourReuseStatusEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteClassHourTypeEnum;
import com.xt.hsk.module.edu.service.elitecourse.EliteChapterService;
import com.xt.hsk.module.edu.service.elitecourse.EliteClassHourService;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseService;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseVideoService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_CLASS_HOUR_NOT_EXISTS;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_CLASS_HOUR_VIDEO_URL_NOT_EMPTY;


/**
 * 精品课课时 Manager
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Slf4j
@Component
public class EliteClassHourManager {

    @Resource
    private EliteClassHourService eliteClassHourService;

    @Resource
    private EliteCourseService eliteCourseService;

    @Resource
    private EliteChapterService eliteChapterService;

    @Resource
    private EliteCourseVideoService eliteCourseVideoService;

    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 创建精品课课时
     */
    @Transactional(rollbackFor = Exception.class)
    public void createEliteClassHour(EliteClassHourSaveReqVO createReqVO) {

        // 创建录播课
        if (EliteClassHourTypeEnum.RECORDED.getCode().equals(createReqVO.getClassHourType())) {
            transactionTemplate.execute(status -> {
                EliteClassHourDO recordedCourse = createRecordedCourse(createReqVO);
                // 获取课程信息
                EliteCourseDO course = eliteCourseService.getById(createReqVO.getCourseId());

                // 设置日志上下文变量
                LogRecordContext.putVariable("classHourId", recordedCourse.getId());
                LogRecordContext.putVariable("classHour", recordedCourse);
                LogRecordContext.putVariable("courseName", course != null ? course.getCourseNameCn() : "未知课程");
                return null;
            });
        }
    }

    /**
     * 创建录播课
     */
    @Transactional(rollbackFor = Exception.class)
    public EliteClassHourDO createRecordedCourse(EliteClassHourSaveReqVO createReqVO) {

        if (CharSequenceUtil.isBlank(createReqVO.getVideoUrl())) {
            throw exception(ELITE_CLASS_HOUR_VIDEO_URL_NOT_EMPTY);
        }

        // 插入
        EliteClassHourDO eliteClassHour = EliteClassHourConvert.INSTANCE.saveReqVOToDO(createReqVO);

        // 计算课时序号：该章节下现有最大课时序号+1
        Integer maxSort = eliteClassHourService.lambdaQuery()
                .eq(EliteClassHourDO::getChapterId, createReqVO.getChapterId())
                .select(EliteClassHourDO::getSort)
                .orderByDesc(EliteClassHourDO::getSort)
                .last("LIMIT 1")
                .oneOpt()
                .map(EliteClassHourDO::getSort)
                .orElse(0);

        eliteClassHour.setSort(maxSort + 1);

        eliteClassHourService.save(eliteClassHour);

        // 保存录播课视频信息
        if (CharSequenceUtil.isNotBlank(createReqVO.getVideoUrl())) {
            Long classHourId = eliteClassHour.getId();
            EliteCourseVideoDO courseVideo = new EliteCourseVideoDO();
            courseVideo.setVideoUrl(createReqVO.getVideoUrl());
            courseVideo.setVideoId(generateVideo(classHourId));
            courseVideo.setNameCn(eliteClassHour.getClassHourNameCn());
            courseVideo.setNameEn(eliteClassHour.getClassHourNameEn());
            courseVideo.setNameOt(eliteClassHour.getClassHourNameOt());
            courseVideo.setVideoCreateTime(LocalDateTime.now());
            courseVideo.setType(1);
            eliteCourseVideoService.save(courseVideo);

            eliteClassHourService.
                    lambdaUpdate()
                    .eq(EliteClassHourDO::getId, classHourId)
                    .set(EliteClassHourDO::getVideoId, courseVideo.getVideoId())
                    .set(EliteClassHourDO::getUpdateTime, LocalDateTime.now())
                    .set(EliteClassHourDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                    .update();
        }

        return eliteClassHour;

    }

    /**
     * 生成一个长度为8位的Long类型视频ID
     *
     * @param number 原始数字
     * @return 视频ID
     */
    public Long generateVideo(Long number) {

        // 如果传入的数字等于空或者1，则将其设为1
        number = number == null || number < 1 ? 1 : number;

        // 将数字转为字符串
        String numberStr = String.valueOf(number);
        int numberStrLength = numberStr.length();

        // 如果数字已经是 8 位或以上，则无需处理，直接返回
        if (numberStrLength >= 8) {
            return number;
        }

        // 获取当前时间的秒数
        String secondsStr = String.valueOf(DateUtil.currentSeconds());

        // 计算需要补充的位数，使总长度为8
        int timeLen = 8 - numberStrLength;

        // 从当前时间的秒数末尾截取 timeLen 位
        String timePart = secondsStr.substring(secondsStr.length() - timeLen);

        // 拼接 number 和时间部分，得到新的8位字符串
        String combined = numberStr + timePart;

        // 返回
        return Long.parseLong(combined);
    }




    public void updateEliteClassHour(EliteClassHourSaveReqVO updateReqVO) {

        // 更新录播课
        if (EliteClassHourTypeEnum.RECORDED.getCode().equals(updateReqVO.getClassHourType())) {

            transactionTemplate.execute(status -> {
                updateRecordedCourse(updateReqVO);

                // 记录操作日志上下文
                // 获取课程信息
                EliteCourseDO course = eliteCourseService.getById(updateReqVO.getCourseId());

                LogRecordContext.putVariable("courseName", course != null ? course.getCourseNameCn() : "未知课程");
                LogRecordContext.putVariable("classHourNameCn", updateReqVO.getClassHourNameCn());
                return null;
            });
        }
    }

    /**
     * 更新录播课
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateRecordedCourse(EliteClassHourSaveReqVO updateReqVO) {

        EliteClassHourDO eliteClassHour = validateEliteClassHourExists(updateReqVO.getId());

        if (CharSequenceUtil.isBlank(updateReqVO.getVideoUrl())) {
            throw exception(ELITE_CLASS_HOUR_VIDEO_URL_NOT_EMPTY);
        }

        // 更新
        EliteClassHourDO updateObj = EliteClassHourConvert.INSTANCE.saveReqVOToDO(updateReqVO);
        updateObj.setUpdateTime(LocalDateTime.now());
        updateObj.setUpdater(String.valueOf(WebFrameworkUtils.getLoginUserId()));
        eliteClassHourService.updateById(updateObj);

        // 更新录播课视频信息
        if (CharSequenceUtil.isNotBlank(updateReqVO.getVideoUrl())) {
            EliteCourseVideoDO courseVideoDO = eliteCourseVideoService.lambdaQuery()
                    .eq(EliteCourseVideoDO::getVideoId, eliteClassHour.getVideoId())
                    .last("LIMIT 1")
                    .one();

            if (courseVideoDO != null) {
                eliteCourseVideoService.lambdaUpdate()
                        .eq(EliteCourseVideoDO::getVideoId, courseVideoDO.getVideoId())
                        .set(EliteCourseVideoDO::getVideoUrl, updateReqVO.getVideoUrl())
                        .set(EliteCourseVideoDO::getNameCn, updateReqVO.getClassHourNameCn())
                        .set(EliteCourseVideoDO::getNameEn, updateReqVO.getClassHourNameEn())
                        .set(EliteCourseVideoDO::getNameOt, updateReqVO.getClassHourNameOt())
                        .set(EliteCourseVideoDO::getUpdateTime, LocalDateTime.now())
                        .set(EliteCourseVideoDO::getVideoCreateTime, LocalDateTime.now())
                        .set(EliteCourseVideoDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                        .update();

            } else {
                Long classHourId = eliteClassHour.getId();
                EliteCourseVideoDO courseVideo = new EliteCourseVideoDO();
                courseVideo.setVideoUrl(updateReqVO.getVideoUrl());
                Long videoId = eliteClassHour.getVideoId() == null ? generateVideo(classHourId) : eliteClassHour.getVideoId();
                courseVideo.setVideoId(videoId);
                courseVideo.setNameCn(eliteClassHour.getClassHourNameCn());
                courseVideo.setNameEn(eliteClassHour.getClassHourNameEn());
                courseVideo.setNameOt(eliteClassHour.getClassHourNameOt());
                courseVideo.setVideoCreateTime(LocalDateTime.now());
                courseVideo.setType(1);
                eliteCourseVideoService.save(courseVideo);

                eliteClassHourService.
                        lambdaUpdate()
                        .eq(EliteClassHourDO::getId, classHourId)
                        .set(EliteClassHourDO::getVideoId, courseVideo.getVideoId())
                        .set(EliteClassHourDO::getUpdateTime, LocalDateTime.now())
                        .set(EliteClassHourDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                        .update();
            }
        }
    }


    public void deleteEliteClassHour(EliteClassHourSaveReqVO reqVO) {

        List<Long> classHourIds = reqVO.getClassHourIds();

        if (CollUtil.isNotEmpty(classHourIds)) {
            eliteClassHourService.removeBatchByIds(classHourIds);
        }

        Long reuseId = reqVO.getReuseId();

        if (reuseId != null) {
            eliteClassHourService.lambdaUpdate()
                    .eq(EliteClassHourDO::getReuseId, reuseId)
                    .or()
                    .eq(EliteClassHourDO::getId, reuseId)
                    .set(EliteClassHourDO::getDeleted, true)
                    .set(EliteClassHourDO::getUpdateTime, LocalDateTime.now())
                    .set(EliteClassHourDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                    .update();

        }
    }

    private EliteClassHourDO validateEliteClassHourExists(Long id) {
        EliteClassHourDO eliteClassHourDO = eliteClassHourService.getById(id);
        if (eliteClassHourDO == null) {
            throw exception(ELITE_CLASS_HOUR_NOT_EXISTS);
        }
        return eliteClassHourDO;
    }

    /**
     * 修改精品课课时排序
     * <p>
     * 1.校验课时是否存在；
     * 2.根据新旧序号大小关系调整同章节下其他课时的序号；
     * 3.更新当前课时序号
     *
     * @param id      课时id
     * @param newSort 新排序
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateEliteClassHourSort(Long id, Integer newSort) {
        // 1. 校验课时是否存在
        EliteClassHourDO originalClassHour = validateEliteClassHourExists(id);

        Integer oldSort = originalClassHour.getSort();
        Long chapterId = originalClassHour.getChapterId();

        // 2. 如果新旧序号相同，直接返回
        if (oldSort.equals(newSort)) {
            return;
        }

        // 3. 调整同章节下其他课时的序号
        if (oldSort < newSort) {
            // 旧序号小于新序号：将(旧序号+1)到新序号范围内的课时序号-1
            eliteClassHourService.lambdaUpdate()
                    .eq(EliteClassHourDO::getChapterId, chapterId)
                    .between(EliteClassHourDO::getSort, oldSort + 1, newSort)
                    .setSql("sort = sort - 1")
                    .set(EliteClassHourDO::getUpdateTime, LocalDateTime.now())
                    .set(EliteClassHourDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                    .update();
        } else {
            // 旧序号大于新序号：将新序号到(旧序号-1)范围内的课时序号+1
            eliteClassHourService.lambdaUpdate()
                    .eq(EliteClassHourDO::getChapterId, chapterId)
                    .between(EliteClassHourDO::getSort, newSort, oldSort - 1)
                    .setSql("sort = sort + 1")
                    .set(EliteClassHourDO::getUpdateTime, LocalDateTime.now())
                    .set(EliteClassHourDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                    .update();
        }

        // 4. 更新当前课时的序号
        eliteClassHourService.lambdaUpdate()
                .eq(EliteClassHourDO::getId, id)
                .set(EliteClassHourDO::getSort, newSort)
                .set(EliteClassHourDO::getUpdateTime, LocalDateTime.now())
                .set(EliteClassHourDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                .update();

        LogRecordContext.putVariable("classHour", originalClassHour);
        LogRecordContext.putVariable("oldSort", oldSort);
    }

    public EliteClassHourDO getEliteClassHour(Long id) {
        return eliteClassHourService.getById(id);
    }

    public PageResult<EliteClassHourDO> getEliteClassHourPage(@Valid EliteClassHourPageReqVO pageReqVO) {
        return eliteClassHourService.selectPage(pageReqVO);
    }

    /**
     * 复用课时到指定课程。
     *
     * <p>该方法支持两种复用方式：
     * <ul>
     *     <li>如果请求中指定了章节ID，则直接将指定课时复制到该章节。</li>
     *     <li>如果未指定章节ID，则复制课时所属的原始章节及其课时，自动在目标课程中生成对应章节。</li>
     * </ul>
     *
     * @param saveReqVO 包含课时ID列表、目标课程ID、可选的目标章节ID等信息的请求对象
     */
    @Transactional(rollbackFor = Exception.class)
    public void copyClassHoursToTargetCourse(EliteClassHourSaveReqVO saveReqVO) {
        // 获取请求中的课时 ID 列表
        List<Long> classHourIdList = saveReqVO.getClassHourIds();

        // 如果没有课时 ID，直接返回
        if (CollUtil.isEmpty(classHourIdList)) {
            return;
        }

        // 获取目标课程信息
        EliteCourseDO targetCourse = eliteCourseService.getEliteCourse(saveReqVO.getCourseId());
        Long targetCourseId = targetCourse.getId();

        // 如果请求中指定了目标章节 ID，直接将课时复制到该章节
        if (saveReqVO.getChapterId() != null) {
            copyClassHoursToChapter(classHourIdList, saveReqVO.getChapterId(), targetCourseId);
            return;
        }

        // 获取指定课时的完整信息
        List<EliteClassHourDO> sourceClassHours = eliteClassHourService.listByIds(classHourIdList);
        // 如果未找到课时信息，返回
        if (CollUtil.isEmpty(sourceClassHours)) {
            return;
        }

        // 将课时按章节分组（key 是章节 ID，value 是该章节下的课时列表）
        Map<Long, List<EliteClassHourDO>> chapterIdToClassHoursMap =
                sourceClassHours.stream().collect(Collectors.groupingBy(EliteClassHourDO::getChapterId));

        // 获取所有原始章节的 ID
        List<Long> sourceChapterIdList = new ArrayList<>(chapterIdToClassHoursMap.keySet());

        // 获取原始章节的完整信息
        List<EliteChapterDO> sourceChapters = eliteChapterService.listByIds(sourceChapterIdList);
        // 如果未找到章节信息，返回
        if (CollUtil.isEmpty(sourceChapters)) {
            return;
        }

        // 获取目标课程中现有章节的最大排序值，用于在复制时递增排序
        int chapterSortIndex = eliteChapterService.getMaxSortByCourseId(targetCourseId);

        // 遍历原始章节，将每个章节及其对应的课时复制到目标课程
        for (EliteChapterDO sourceChapter : sourceChapters) {
            // 克隆原始章节信息到目标课程
            EliteChapterDO newChapter = cloneChapterForCourse(sourceChapter, targetCourseId, ++chapterSortIndex);
            eliteChapterService.save(newChapter);

            // 获取当前章节下的课时列表
            List<EliteClassHourDO> classHoursInChapter = chapterIdToClassHoursMap.get(sourceChapter.getId());
            if (CollUtil.isNotEmpty(classHoursInChapter)) {
                // 获取课时 ID 列表并复制课时到新章节
                List<Long> classHourIdsToCopy = classHoursInChapter.stream().map(EliteClassHourDO::getId).toList();
                copyClassHoursToChapter(classHourIdsToCopy, newChapter.getId(), targetCourseId);
            }
        }
    }

    /**
     * 克隆原始章节并构建一个用于目标课程的新章节对象。
     *
     * @param source         原始章节对象
     * @param targetCourseId 目标课程 ID
     * @param sortIndex      排序值，用于控制新章节在课程中的顺序
     * @return 克隆后的章节对象
     */
    private EliteChapterDO cloneChapterForCourse(EliteChapterDO source, Long targetCourseId, int sortIndex) {
        EliteChapterDO chapter = new EliteChapterDO();
        chapter.setChapterNameCn(source.getChapterNameCn());
        chapter.setChapterNameEn(source.getChapterNameEn());
        chapter.setChapterNameOt(source.getChapterNameOt());
        chapter.setCourseId(targetCourseId);
        chapter.setSort(sortIndex);
        return chapter;
    }


    /**
     * 将一组课时复用到目标章节中
     * 复用的课时会创建为新对象，避免直接修改原课时，并处理名称重复问题。
     *
     * @param sourceClassHourIds 要复用的课时 ID 列表
     * @param targetChapterId    目标章节 ID
     * @param courseId           课程 ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void copyClassHoursToChapter(List<Long> sourceClassHourIds, Long targetChapterId, Long courseId) {
        // 根据课时 ID 获取原始课时数据
        List<EliteClassHourDO> sourceClassHours = eliteClassHourService.listByIds(sourceClassHourIds);

        // 如果原始课时为空，则不进行任何操作
        if (CollUtil.isEmpty(sourceClassHours)) {
            return;
        }

        // 获取目标章节下已有的课时
        List<EliteClassHourDO> targetChapterClassHours = eliteClassHourService.lambdaQuery()
                .eq(EliteClassHourDO::getChapterId, targetChapterId)
                .list();

        List<EliteClassHourDO> newClassHoursToInsert = new ArrayList<>();

        // 获取当前章节下已有课时中最大的 sort 值，用于排序
        int nextSortValue = targetChapterClassHours.stream()
                .map(EliteClassHourDO::getSort)
                .max(Integer::compareTo)
                .orElse(0);

        // 获取当前章节下已有的课时名称，用于后续重复名检测
        List<String> existingClassHourNames = targetChapterClassHours.stream()
                .map(EliteClassHourDO::getClassHourNameCn)
                .collect(Collectors.toList());

        // 遍历需要复用的课时
        for (EliteClassHourDO sourceClassHour : sourceClassHours) {
            String baseName = sourceClassHour.getClassHourNameCn();
            // 获取当前名称的下一个可用索引
            int duplicateIndex = getNextIndexForName(existingClassHourNames, baseName);

            // 构建新的课时对象
            EliteClassHourDO newClassHour = buildNewClassHour(
                    sourceClassHour,
                    courseId,
                    targetChapterId,
                    ++nextSortValue,
                    duplicateIndex
            );
            newClassHoursToInsert.add(newClassHour);

            // 将新课时名称添加到已有名称列表，避免后续名称冲突
            existingClassHourNames.add(newClassHour.getClassHourNameCn());
        }

        // 批量保存新课时
        if (CollUtil.isNotEmpty(newClassHoursToInsert)) {
            eliteClassHourService.saveBatch(newClassHoursToInsert);
        }
    }

    /**
     * 根据已有名称列表，获取某个基础名称可用的下一个索引值（用于避免名称重复）。
     * <p>
     * 例如，如果已有名称为 "课时1", "课时1(1)", "课时1(2)"，则返回 3。
     *
     * @param existingNames 已有的名称列表
     * @param baseName      需要检测的基础名称
     * @return 可用的索引值，如果为 0 表示无重复
     */
    private int getNextIndexForName(List<String> existingNames, String baseName) {
        int maxIndex = 0;
        boolean hasExactMatch = existingNames.contains(baseName);

        // 正则匹配形如 "baseName(数字)" 的名称
        Pattern pattern = Pattern.compile(Pattern.quote(baseName) + "\\((\\d+)\\)");

        for (String existingName : existingNames) {
            Matcher matcher = pattern.matcher(existingName);
            if (matcher.matches()) {
                try {
                    int index = Integer.parseInt(matcher.group(1));
                    if (index >= maxIndex) {
                        maxIndex = index + 1;
                    }
                } catch (NumberFormatException ignored) {
                    // 忽略解析失败的名称
                }
            }
        }

        // 如果存在精确重复的名称但没有索引，则返回 1
        if (hasExactMatch && maxIndex == 0) {
            maxIndex = 1;
        }

        return maxIndex;
    }

    /**
     * 构建新的课时对象，用于复用。会设置课程、章节、排序、复用ID、视频ID及名称等字段。
     * 如果 index > 0，则会在名称后加上索引以避免重复。
     *
     * @param source    原始课时对象
     * @param courseId  目标课程 ID
     * @param chapterId 目标章节 ID
     * @param sort      新课时排序值
     * @param index     重名索引，0 表示无重复
     * @return 构建好的新课时对象
     */
    private EliteClassHourDO buildNewClassHour(EliteClassHourDO source, Long courseId, Long chapterId, int sort, int index) {
        EliteClassHourDO newClassHour = new EliteClassHourDO();
        newClassHour.setCourseId(courseId);
        newClassHour.setChapterId(chapterId);
        newClassHour.setSort(sort);

        // 设置复用 ID（优先使用已有 reuseId，否则使用 source 的 ID）
        Long reuseId = source.getReuseId() == null ? source.getId() : source.getReuseId();
        newClassHour.setReuseId(reuseId);

        newClassHour.setClassHourType(source.getClassHourType());
        newClassHour.setVideoId(source.getVideoId());

        // 设置名称，如果有重复索引则加上 "(index)"
        if (index == 0) {
            newClassHour.setClassHourNameCn(source.getClassHourNameCn());
            newClassHour.setClassHourNameEn(source.getClassHourNameEn());
            newClassHour.setClassHourNameOt(source.getClassHourNameOt());
        } else {
            newClassHour.setClassHourNameCn(source.getClassHourNameCn() + "(" + index + ")");
            if (CharSequenceUtil.isNotBlank(source.getClassHourNameEn())) {
                newClassHour.setClassHourNameEn(source.getClassHourNameEn() + "(" + index + ")");
            }
            if (CharSequenceUtil.isNotBlank(source.getClassHourNameOt())) {
                newClassHour.setClassHourNameOt(source.getClassHourNameOt() + "(" + index + ")");
            }
        }

        return newClassHour;
    }


    /**
     * 获取课时复用关系信息
     *
     * @param classHourId 课时ID
     * @return 课时复用关系信息，如果课时不存在则返回null
     */
    public EliteClassHourReuseRespVO getReuse(Long classHourId) {
        // 检查课时是否存在
        EliteClassHourDO classHour = eliteClassHourService.getEliteClassHour(classHourId);

        EliteClassHourReuseRespVO result = new EliteClassHourReuseRespVO();

        // 情况一：当前课时是复用的课时
        if (classHour.getReuseId() != null) {
            // 获取原始课时
            EliteClassHourDO originalClassHour = eliteClassHourService.getById(classHour.getReuseId());
            if (originalClassHour != null) {
                // 获取当前课时所属课程名称
                String courseName = Optional.ofNullable(eliteCourseService.getEliteCourse(classHour.getCourseId()))
                        .map(EliteCourseDO::getCourseNameCn)
                        .orElse("");

                // 构建复用信息
                EliteClassHourReuseRespVO.ReuseRelationItem reuseInfo = new EliteClassHourReuseRespVO.ReuseRelationItem();
                reuseInfo.setClassHourId(originalClassHour.getId().toString());
                reuseInfo.setCourseClassHourName(courseName + "-" + originalClassHour.getClassHourNameCn());

                result.setReuseList(Collections.singletonList(reuseInfo));
                result.setReuseStatus(EliteClassHourReuseStatusEnum.REUSED.getCode());

                return result;
            }
        }

        // 情况二：当前课时被其他课时复用
        List<EliteClassHourDO> reusedClassHours = eliteClassHourService.lambdaQuery()
                .eq(EliteClassHourDO::getReuseId, classHourId)
                .list();

        if (CollUtil.isNotEmpty(reusedClassHours)) {
            // 获取所有相关课程，批量查询提高性能
            Set<Long> courseIds = reusedClassHours.stream()
                    .map(EliteClassHourDO::getCourseId)
                    .collect(Collectors.toSet());

            Map<Long, String> courseNameMap = eliteCourseService.listByIds(courseIds).stream()
                    .collect(Collectors.toMap(
                            EliteCourseDO::getId,
                            EliteCourseDO::getCourseNameCn
                    ));

            // 构建被复用课时列表
            List<EliteClassHourReuseRespVO.ReuseRelationItem> reuseList = reusedClassHours.stream()
                    .map(item -> {
                        String courseName = courseNameMap.getOrDefault(item.getCourseId(), "");
                        EliteClassHourReuseRespVO.ReuseRelationItem reuseItem = new EliteClassHourReuseRespVO.ReuseRelationItem();
                        reuseItem.setClassHourId(item.getId().toString());
                        reuseItem.setCourseClassHourName(courseName + "-" + item.getClassHourNameCn());
                        return reuseItem;
                    })
                    .collect(Collectors.toList());

            result.setReuseList(reuseList);
            result.setReuseStatus(EliteClassHourReuseStatusEnum.BE_REUSED.getCode());

            return result;
        }

        // 情况三：没有复用关系
        result.setReuseStatus(EliteClassHourReuseStatusEnum.NO_REUSE.getCode());
        result.setReuseList(Collections.emptyList());
        return result;
    }
}