package com.xt.hsk.module.edu.service.userselfassessmentrecord;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.app.userselfassessmentrecord.vo.UserSelfAssessmentRecordPageReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.xt.hsk.module.edu.dal.dataobject.userselfassessmentrecord.UserSelfAssessmentRecordDO;

import com.xt.hsk.module.edu.dal.mysql.userselfassessmentrecord.UserSelfAssessmentRecordMapper;


/**
 * 用户等级自测记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserSelfAssessmentRecordServiceImpl extends ServiceImpl<UserSelfAssessmentRecordMapper,UserSelfAssessmentRecordDO> implements UserSelfAssessmentRecordService {

    @Resource
    private UserSelfAssessmentRecordMapper userSelfAssessmentRecordMapper;

    @Override
    public PageResult<UserSelfAssessmentRecordDO> selectPage(UserSelfAssessmentRecordPageReqVO pageReqVO) {

        return userSelfAssessmentRecordMapper.selectPage(pageReqVO);
    }

}