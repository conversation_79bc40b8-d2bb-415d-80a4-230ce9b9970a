package com.xt.hsk.module.edu.dal.mysql.interactivecourse;

import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseUnitResourceRelDO;
import com.xt.hsk.module.edu.enums.interactivecourse.UnitResourceTypeEnum;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.ibatis.annotations.Mapper;

/**
 * 互动课单元资源关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InteractiveCourseUnitResourceRelMapper extends
    BaseMapperX<InteractiveCourseUnitResourceRelDO> {

    /**
     * 根据单元ID查询资源关联列表
     *
     * @param unitId 单元ID
     * @return 资源关联列表
     */
    default List<InteractiveCourseUnitResourceRelDO> selectListByUnitId(Long unitId) {
        return selectList(InteractiveCourseUnitResourceRelDO::getUnitId, unitId);
    }

    /**
     * 根据单元ID和资源类型查询资源关联列表
     *
     * @param unitId       单元ID
     * @param resourceType 资源类型
     * @return 资源关联列表
     */
    default List<InteractiveCourseUnitResourceRelDO> selectListByUnitIdAndResourceType(Long unitId,
        Integer resourceType) {
        return selectList(new LambdaQueryWrapperX<InteractiveCourseUnitResourceRelDO>()
            .eq(InteractiveCourseUnitResourceRelDO::getUnitId, unitId)
            .eq(InteractiveCourseUnitResourceRelDO::getResourceType, resourceType));
    }

    /**
     * 根据单元ID列表查询专项练习ID映射
     *
     * @param unitIds 单元ID列表
     * @return 单元ID到专项练习ID的映射
     */
    default Map<Long, Long> selectSpecialExerciseIdsByUnitIds(List<Long> unitIds) {
        if (unitIds == null || unitIds.isEmpty()) {
            return new HashMap<>();
        }
        
        // 资源类型 3-专项练习
        List<InteractiveCourseUnitResourceRelDO> relList = selectList(
            new LambdaQueryWrapperX<InteractiveCourseUnitResourceRelDO>()
                .in(InteractiveCourseUnitResourceRelDO::getUnitId, unitIds)
                .eq(InteractiveCourseUnitResourceRelDO::getResourceType, 
                    UnitResourceTypeEnum.SPECIAL_PRACTICE.getCode()));
        
        // 转换为Map
        return relList.stream()
            .collect(Collectors.toMap(
                InteractiveCourseUnitResourceRelDO::getUnitId,
                InteractiveCourseUnitResourceRelDO::getResourceId,
                // 如果有重复的单元ID，保留第一个
                (v1, v2) -> v1
            ));
    }

    /**
     * 根据单元ID列表查询真题练习题型ID映射
     *
     * @param unitIds 单元ID列表
     * @return 单元ID到题型ID的映射
     */
    default Map<Long, Long> selectQuestionTypeIdsByUnitIds(List<Long> unitIds) {
        if (unitIds == null || unitIds.isEmpty()) {
            return new HashMap<>();
        }

        // 资源类型 4-真题练习
        List<InteractiveCourseUnitResourceRelDO> relList = selectList(
            new LambdaQueryWrapperX<InteractiveCourseUnitResourceRelDO>()
                .in(InteractiveCourseUnitResourceRelDO::getUnitId, unitIds)
                .eq(InteractiveCourseUnitResourceRelDO::getResourceType,
                    UnitResourceTypeEnum.QUESTION.getCode()));

        // 转换为Map
        return relList.stream()
            .collect(Collectors.toMap(
                InteractiveCourseUnitResourceRelDO::getUnitId,
                InteractiveCourseUnitResourceRelDO::getResourceId,
                (v1, v2) -> v1 // 如果有重复的单元ID，保留第一个
            ));
    }


} 