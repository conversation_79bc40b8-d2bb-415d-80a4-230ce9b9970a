package com.xt.hsk.module.edu.manager.exam.admin;

import cn.hutool.core.collection.CollUtil;
import com.xt.hsk.framework.common.enums.QuestionTypeEnum;
import com.xt.hsk.module.edu.dal.dataobject.exam.UserExamRecordDO;
import com.xt.hsk.module.edu.dal.dataobject.userquestionanswerrecord.UserQuestionAnswerRecordDO;
import com.xt.hsk.module.edu.enums.exam.ExamCorrectionStatusEnum;
import com.xt.hsk.module.edu.service.exam.UserExamRecordService;
import com.xt.hsk.module.edu.service.userquestionanswerrecord.UserQuestionAnswerRecordService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 模考批改 app Manager
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Component
public class ExamCorrectionManager {

    @Resource
    private UserExamRecordService userExamRecordService;

    @Resource
    private UserQuestionAnswerRecordService userQuestionAnswerRecordService;

    /**
     * 更新模考批改状态
     *
     * @param recordId 答题记录ID
     * @param success 是否批改成功
     */
    public void updateExamCorrectionStatus(Long recordId, Boolean success) {

        log.info("模考AI批改完成,更新模考批改状态,记录ID: {},是否批改成功: {}", recordId, success);

        if (Boolean.FALSE.equals(success)) {
            userExamRecordService.lambdaUpdate()
                    .set(UserExamRecordDO::getCorrectionStatus, ExamCorrectionStatusEnum.PENDING_REVIEW.getCode())
                    .set(UserExamRecordDO::getUpdateTime, LocalDateTime.now())
                    .eq(UserExamRecordDO::getId, recordId)
                    .update();
            return;
        }
        UserQuestionAnswerRecordDO userQuestionAnswerRecord = userQuestionAnswerRecordService.getById(recordId);
        if (userQuestionAnswerRecord == null) {
            log.error("更新模考批改状态,用户作答记录不存在,记录ID: {}", recordId);
            return;
        }
        // 获取所有书写题类型ID列表
        List<Long> writingQuestionTypeIds = QuestionTypeEnum.getWritingQuestionTypeList()
                .stream()
                .map(QuestionTypeEnum::getCode)
                .toList();

        // 检查是否存在未完成批改的书写题
        List<UserQuestionAnswerRecordDO> answerRecordList = userQuestionAnswerRecordService.lambdaQuery()
                .eq(UserQuestionAnswerRecordDO::getPracticeId, userQuestionAnswerRecord.getPracticeId())
                .in(UserQuestionAnswerRecordDO::getQuestionTypeId, writingQuestionTypeIds)
                .list();

        if (CollUtil.isNotEmpty(answerRecordList)) {
            Set<Integer> recordStatusList = answerRecordList.stream()
                    .map(UserQuestionAnswerRecordDO::getRecordStatus)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            if (recordStatusList.containsAll(List.of(1, 2, 3))) {
                log.info("模考AI批改进行中,记录ID: {}", recordId);
                return;
            }

            if (recordStatusList.contains(5)) {
                userExamRecordService.lambdaUpdate()
                        .set(UserExamRecordDO::getCorrectionStatus, ExamCorrectionStatusEnum.PENDING_REVIEW.getCode())
                        .set(UserExamRecordDO::getUpdateTime, LocalDateTime.now())
                        .eq(UserExamRecordDO::getId, userQuestionAnswerRecord.getPracticeId())
                        .update();
            }
            if (recordStatusList.contains(4) && recordStatusList.size() == 1) {
                log.info("模考AI批改完成,更新模考批改状态,记录ID: {},所有书写题已批改完成", recordId);
                userExamRecordService.lambdaUpdate()
                        .set(UserExamRecordDO::getCorrectionStatus, ExamCorrectionStatusEnum.COMPLETED.getCode())
                        .set(UserExamRecordDO::getUpdateTime, LocalDateTime.now())
                        .eq(UserExamRecordDO::getId, userQuestionAnswerRecord.getPracticeId())
                        .update();
            }
        }
    }
}