package com.xt.hsk.module.edu.manager.elitecourse;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.web.core.util.WebFrameworkUtils;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteChapterPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteChapterRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteChapterSaveReqVO;
import com.xt.hsk.module.edu.convert.elitecourse.EliteChapterConvert;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteChapterDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteClassHourDO;
import com.xt.hsk.module.edu.service.elitecourse.EliteChapterService;
import com.xt.hsk.module.edu.service.elitecourse.EliteClassHourService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.IntStream;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_CHAPTER_HAS_CLASS_HOURS;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_CHAPTER_NOT_EXISTS;


/**
 * 精品课章节 Manager
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Slf4j
@Component
public class EliteChapterManager {

    @Resource
    private EliteChapterService eliteChapterService;

    @Resource
    private EliteClassHourService eliteClassHourService;

    /**
     * 创建精品课章节
     */
    public void createEliteChapter(EliteChapterSaveReqVO createReqVO) {
        List<EliteChapterSaveReqVO> chapterList = createReqVO.getChapterList();
        Long courseId = createReqVO.getCourseId();

        // 查询该课程下现有章节的最大序号
        Integer maxSort = eliteChapterService.lambdaQuery()
                .eq(EliteChapterDO::getCourseId, courseId)
                .select(EliteChapterDO::getSort)
                .orderByDesc(EliteChapterDO::getSort)
                .last("LIMIT 1")
                .oneOpt()
                .map(EliteChapterDO::getSort)
                .orElse(0);

        // 使用Stream同时设置序号并转换为DO对象
        List<EliteChapterDO> doList = IntStream.range(0, chapterList.size())
                .mapToObj(i -> {
                    EliteChapterSaveReqVO chapter = chapterList.get(i);
                    chapter.setCourseId(courseId);
                    chapter.setSort(maxSort + i + 1);
                    return EliteChapterConvert.INSTANCE.saveReqVOToDO(chapter);
                })
                .toList();

        // 插入
        eliteChapterService.saveBatch(doList);
    }

    /**
     * 更新精品课章节
     */
    public void updateEliteChapter(EliteChapterSaveReqVO updateReqVO) {
        // 校验存在
        validateEliteChapterExists(updateReqVO.getId());
        // 更新
        EliteChapterDO updateObj = EliteChapterConvert.INSTANCE.saveReqVOToDO(updateReqVO);
        updateObj.setUpdateTime(LocalDateTime.now());
        updateObj.setUpdater(String.valueOf(WebFrameworkUtils.getLoginUserId()));
        eliteChapterService.updateById(updateObj);
    }

    /**
     * 删除精品课章节
     */
    public void deleteEliteChapter(Long id) {
        // 校验存在
        validateEliteChapterExists(id);
        
        // 校验该章节下是否有课时
        validateChapterHasNoClassHours(id);
        
        // 删除
        eliteChapterService.removeById(id);
    }

    /**
     * 验证精品课章节是否存在
     *
     * @param id 精品课章节id
     * @return 精品课章节DO
     */
    private EliteChapterDO validateEliteChapterExists(Long id) {
        EliteChapterDO chapterDO = eliteChapterService.getById(id);
        if (chapterDO == null) {
            throw exception(ELITE_CHAPTER_NOT_EXISTS);
        }
        return chapterDO;
    }

    /**
     * 校验章节下是否有课时
     */
    private void validateChapterHasNoClassHours(Long chapterId) {
        boolean exists = eliteClassHourService.lambdaQuery()
                .eq(EliteClassHourDO::getChapterId, chapterId)
                .exists();

        if (exists) {
            throw exception(ELITE_CHAPTER_HAS_CLASS_HOURS);
        }
    }

    public EliteChapterDO getEliteChapter(Long id) {
        return eliteChapterService.getById(id);
    }

    public PageResult<EliteChapterDO> getEliteChapterPage(@Valid EliteChapterPageReqVO pageReqVO) {
        return eliteChapterService.selectPage(pageReqVO);
    }

    /**
     * 修改精品课章节排序
     * <p>
     * 1.校验章节是否存在；
     * 2.根据新旧序号大小关系调整同课程下其他章节的序号；
     * 3.更新当前章节序号
     *
     * @param id      章节id
     * @param newSort 新排序
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateEliteChapterSort(Long id, Integer newSort) {
        // 1. 校验章节是否存在
        EliteChapterDO originalChapter = validateEliteChapterExists(id);

        Integer oldSort = originalChapter.getSort();
        Long courseId = originalChapter.getCourseId();

        // 2. 如果新旧序号相同，直接返回
        if (oldSort.equals(newSort)) {
            return;
        }

        // 3. 调整同课程下其他章节的序号
        if (oldSort < newSort) {
            // 旧序号小于新序号：将(旧序号+1)到新序号范围内的章节序号-1
            eliteChapterService.lambdaUpdate()
                    .eq(EliteChapterDO::getCourseId, courseId)
                    .between(EliteChapterDO::getSort, oldSort + 1, newSort)
                    .setSql("sort = sort - 1")
                    .set(EliteChapterDO::getSort, LocalDateTime.now())
                    .set(EliteChapterDO::getSort, WebFrameworkUtils.getLoginUserId())
                    .update();
        } else {
            // 旧序号大于新序号：将新序号到(旧序号-1)范围内的章节序号+1
            eliteChapterService.lambdaUpdate()
                    .eq(EliteChapterDO::getCourseId, courseId)
                    .between(EliteChapterDO::getSort, newSort, oldSort - 1)
                    .set(EliteChapterDO::getSort, LocalDateTime.now())
                    .set(EliteChapterDO::getSort, WebFrameworkUtils.getLoginUserId())
                    .setSql("sort = sort + 1")
                    .update();
        }

        // 4. 更新当前章节的序号
        eliteChapterService.lambdaUpdate()
                .eq(EliteChapterDO::getId, id)
                .set(EliteChapterDO::getSort, newSort)
                .set(EliteChapterDO::getSort, LocalDateTime.now())
                .set(EliteChapterDO::getSort, WebFrameworkUtils.getLoginUserId())
                .update();
    }

    /**
     * 根据精品课id获取章节列表
     * 
     * @param courseId 精品课id
     * @return 章节列表，按sort排序
     */
    public List<EliteChapterRespVO> listByCourseId(Long courseId) {
        List<EliteChapterDO> chapterList = eliteChapterService.lambdaQuery()
                .eq(EliteChapterDO::getCourseId, courseId)
                .orderByAsc(EliteChapterDO::getSort)
                .list();

        return EliteChapterConvert.INSTANCE.doListToCreateReqVOList(chapterList);
    }
}