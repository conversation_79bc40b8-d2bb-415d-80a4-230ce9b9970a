package com.xt.hsk.module.edu.service.userselfassessmentrecord;

import cn.dev33.satoken.stp.StpUtil;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.app.question.vo.AppQuestionVo;
import com.xt.hsk.module.edu.controller.app.userselfassessmentrecord.vo.UserSelfAssessmentRecordPageReqVO;
import com.xt.hsk.module.edu.controller.app.userselfassessmentrecord.vo.UserSelfAssessmentRecordRespVO;
import com.xt.hsk.module.edu.controller.app.userselfassessmentrecord.vo.UserSelfAssessmentRecordSaveReqVO;
import com.xt.hsk.module.edu.dal.dataobject.userselfassessmentrecord.UserSelfAssessmentRecordDO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.DATA_NOT_EXIST;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;


/**
 * 用户等级自测记录 Manager
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserSelfAssessmentRecordManager {

    @Resource
    private UserSelfAssessmentRecordService userSelfAssessmentRecordService;


    public UserSelfAssessmentRecordRespVO createUserSelfAssessmentRecord() {
        // 1.寻找题目信息
        List<AppQuestionVo> appQuestionVos = getAppQuestionVos();
        List<Long> questionIds = appQuestionVos.stream().map(AppQuestionVo::getId).toList();
        String questionIdsStr = questionIds.stream().map(String::valueOf).collect(Collectors.joining(","));
        // 2.生成自测记录
        long userId = StpUtil.getLoginIdAsLong();
        UserSelfAssessmentRecordDO userSelfAssessmentRecordDO = new UserSelfAssessmentRecordDO();
        userSelfAssessmentRecordDO.setUserId(userId);
        userSelfAssessmentRecordDO.setStartTime(LocalDateTime.now());
        userSelfAssessmentRecordDO.setRecordStatus(1);
        userSelfAssessmentRecordDO.setQuestionIds(questionIdsStr);
        userSelfAssessmentRecordService.save(userSelfAssessmentRecordDO);

        Long recordDOId = userSelfAssessmentRecordDO.getId();
        // 3.生成返回数据
        UserSelfAssessmentRecordRespVO userSelfAssessmentRecordRespVO = new UserSelfAssessmentRecordRespVO();
        userSelfAssessmentRecordRespVO.setId(recordDOId);
        userSelfAssessmentRecordRespVO.setQuestions(appQuestionVos);
        userSelfAssessmentRecordRespVO.setQuestionIds(questionIdsStr);
        return userSelfAssessmentRecordRespVO;
    }

    private List<AppQuestionVo> getAppQuestionVos() {
        // 1.每个等级+科目 随机取5条数据id
        // 2.查询取出来的数据是否都是未删除,且状态为启用的
        // 3.存在等级+科目的数据不足5条,则重新随机取5条数据id,直到取出来的数据有效条数大于等于5条,最多重新取数据3次
        // 4.一共会有15*5道题，再从这些题目中按照5和6随机取18道题
        // 5.每个等级设置3题，6个等级一共设置18题
        // 6.每个等级下，确保每个科目至少1题,若不足3个科目，剩下的题目在该等级的所有可选题型范围内任意添加。
        return null;
    }


    public void submitUserSelfAssessmentRecord(UserSelfAssessmentRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateUserSelfAssessmentRecordExists(updateReqVO.getId());
        // 更新
        UserSelfAssessmentRecordDO updateObj = BeanUtils.toBean(updateReqVO, UserSelfAssessmentRecordDO.class);
        userSelfAssessmentRecordService.updateById(updateObj);
    }


    public void deleteUserSelfAssessmentRecord(Long id) {
        // 校验存在
        validateUserSelfAssessmentRecordExists(id);
        // 删除
        userSelfAssessmentRecordService.removeById(id);
    }

    private void validateUserSelfAssessmentRecordExists(Long id) {
        if (userSelfAssessmentRecordService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }


    public UserSelfAssessmentRecordDO getUserSelfAssessmentRecord(Long id) {
        return userSelfAssessmentRecordService.getById(id);
    }

    public PageResult<UserSelfAssessmentRecordDO> getUserSelfAssessmentRecordPage(@Valid UserSelfAssessmentRecordPageReqVO pageReqVO) {
        return userSelfAssessmentRecordService.selectPage(pageReqVO);
    }

}