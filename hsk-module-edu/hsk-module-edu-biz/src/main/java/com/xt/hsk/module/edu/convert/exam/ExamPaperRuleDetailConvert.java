package com.xt.hsk.module.edu.convert.exam;

import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleDetailRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleDetailSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleQuestionTypeSaveReqVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamPaperRuleDetailDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 模考组卷规则明细 转换
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Mapper
public interface ExamPaperRuleDetailConvert {

    ExamPaperRuleDetailConvert INSTANCE = Mappers.getMapper(ExamPaperRuleDetailConvert.class);

    /**
     * 创建VO类转DO类
     */
    ExamPaperRuleDetailDO saveReqVoToDo(ExamPaperRuleDetailSaveReqVO createReqVO);

    /**
     * 模考规则题型创建VO类转DO类
     */
    ExamPaperRuleDetailDO saveReqVoToDo(ExamPaperRuleQuestionTypeSaveReqVO createReqVO);

    /**
     * DO列表转响应VO列表
     */
    List<ExamPaperRuleDetailRespVO> doListToRespVoList(List<ExamPaperRuleDetailDO> list);
}
