package com.xt.hsk.module.edu.job.question;

import com.xt.hsk.framework.quartz.core.handler.JobHandler;
import com.xt.hsk.framework.redis.utils.RedisUtil;
import com.xt.hsk.module.edu.service.question.QuestionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 题目分类缓存处理任务
 */
@Component
@Slf4j
public class QuestionClassifyCacheHandleJob implements JobHandler {
    String classifyCacheKey = "HSK:question:classify:s%:s%";
    /**
     * 每次处理的题目数量
     */
    private final int BATCH_SIZE = 200;
    @Resource
    private QuestionService questionService;
    @Resource
    private RedisUtil redisUtil;
    @Override
    public String execute(String param) throws Exception {
        // 每个等级和科目随机取BATCH_SIZE道题目放入缓存中
        // 等级 1 2 4 8 16 32   科目 1 2 4
        Map<String, List<Long>> questionMap = new HashMap<>();
        int level = 1;
        int subject = 1;
        for (int i = 0; i < 6; i++) {
            for (int j = 0; j < 3; j++) {
                // 1 2 6 没有书写题
                if ((i==0 || i == 1 || i==5) && j == 2 ){
                    continue;
                }
                log.info("处理等级{}科目{}的题目", level<<i, subject<<j);
                List<Long> questionIds = questionService.randomQuestionIds(level, subject, BATCH_SIZE);
                if (questionIds != null && !questionIds.isEmpty()){
                    questionMap.put(String.format(classifyCacheKey, i, j), questionIds);
                }
            }
        }
        log.info("题目分类缓存处理任务取数结束，开始放入redis中");

        for (Map.Entry<String, List<Long>> entry : questionMap.entrySet()) {
            String key = entry.getKey();
            log.info("题目分类缓存处理任务取数结束,放入redis中，key:{}", key);
            redisUtil.delete(key);
            redisUtil.sAdd(key, entry.getValue());
            redisUtil.expire(key,1,TimeUnit.DAYS);
        }


        return "题目分类缓存处理任务取数结束";
    }

}
