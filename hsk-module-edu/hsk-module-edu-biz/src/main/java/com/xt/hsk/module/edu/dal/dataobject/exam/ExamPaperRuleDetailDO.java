package com.xt.hsk.module.edu.dal.dataobject.exam;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import com.xt.hsk.module.edu.enums.exam.ExamQuestionTypeUnitEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import lombok.*;

/**
 * 模考组卷规则明细 DO
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@TableName("edu_exam_paper_rule_detail")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExamPaperRuleDetailDO extends BaseDO {

    /**
     * 组卷规则明细ID
     */
    @TableId
    private Long id;
    /**
     * 组卷规则ID
     */
    private Long paperRuleId;
    /**
     * 模考题型ID
     */
    private Long examQuestionTypeId;
    /**
     * HSK等级
     *
     * @see HskEnum
     */
    private Integer hskLevel;
    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    private Integer examType;
    /**
     * 科目
     *
     * @see SubjectEnum
     */
    private Integer subject;
    /**
     * 单元部分
     *
     * @see ExamQuestionTypeUnitEnum
     */
    private Integer unit;
    /**
     * 题数
     */
    private Integer questionCount;

}