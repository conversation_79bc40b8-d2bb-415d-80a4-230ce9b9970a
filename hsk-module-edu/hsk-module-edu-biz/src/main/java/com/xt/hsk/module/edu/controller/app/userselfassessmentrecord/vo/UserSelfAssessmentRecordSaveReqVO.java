package com.xt.hsk.module.edu.controller.app.userselfassessmentrecord.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UserSelfAssessmentRecordSaveReqVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 已作答数量
     */
    private Integer answerNum;

    /**
     * 已正确数量
     */
    private Integer correctNum;

    /**
     * 题目数量
     */
    private Integer questionNum;

    /**
     * 开始作答时间
     */
    private LocalDateTime startTime;

    /**
     * 结束作答时间
     */
    private LocalDateTime endTime;

    /**
     * 记录状态 1 进行中 2 生成报告
     */
    private Integer recordStatus;

    /**
     * 本次练习的全部题目id(英文逗号拼接)
     */
    private String questionIds;

    /**
     * 预测水平 hskLevel 1 2 4 8 16 32
     */
    private Integer predictionLevel;

}