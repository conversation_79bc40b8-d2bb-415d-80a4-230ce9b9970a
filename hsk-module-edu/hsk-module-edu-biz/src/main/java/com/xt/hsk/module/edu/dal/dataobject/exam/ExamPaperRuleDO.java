package com.xt.hsk.module.edu.dal.dataobject.exam;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import com.xt.hsk.module.edu.enums.exam.ExamPaperRuleStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import lombok.*;

/**
 * 模考组卷规则 DO
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@TableName("edu_exam_paper_rule")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExamPaperRuleDO extends BaseDO {

    /**
     * 组卷规则ID
     */
    @TableId
    private Long id;
    /**
     * 规则名称
     */
    private String name;
    /**
     * HSK等级
     *
     * @see HskEnum
     */
    private Integer hskLevel;
    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    private Integer examType;
    /**
     * 听力考试时长 (秒)
     */
    private Integer listeningDuration;
    /**
     * 阅读考试时长 (秒)
     */
    private Integer readingDuration;
    /**
     * 书写考试时长 (秒)
     */
    private Integer writingDuration;
    /**
     * 题目数量
     */
    private Integer questionCount;
    /**
     * 状态 1启用 0禁用
     *
     * @see ExamPaperRuleStatusEnum
     */
    private Integer status;

}