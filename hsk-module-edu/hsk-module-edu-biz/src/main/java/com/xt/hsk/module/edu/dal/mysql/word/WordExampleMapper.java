package com.xt.hsk.module.edu.dal.mysql.word;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordExamplePageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordExampleDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 释义关联例句 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WordExampleMapper extends BaseMapperX<WordExampleDO> {

    default PageResult<WordExampleDO> selectPage(WordExamplePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WordExampleDO>()
                .eqIfPresent(WordExampleDO::getMeaningId, reqVO.getMeaningId())
                .eqIfPresent(WordExampleDO::getWordId, reqVO.getWordId())
                .eqIfPresent(WordExampleDO::getWord, reqVO.getWord())
                .eqIfPresent(WordExampleDO::getExampleType, reqVO.getExampleType())
                .eqIfPresent(WordExampleDO::getTranslationCn, reqVO.getTranslationCn())
                .eqIfPresent(WordExampleDO::getTranslationEn, reqVO.getTranslationEn())
                .eqIfPresent(WordExampleDO::getTranslationOt, reqVO.getTranslationOt())
                .eqIfPresent(WordExampleDO::getPinyinWithTone, reqVO.getPinyinWithTone())
                .eqIfPresent(WordExampleDO::getPinyinWithoutTone, reqVO.getPinyinWithoutTone())
                .eqIfPresent(WordExampleDO::getAudioId, reqVO.getAudioId())
                .eqIfPresent(WordExampleDO::getAudioUrl, reqVO.getAudioUrl())
                .betweenIfPresent(WordExampleDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WordExampleDO::getId));
    }

}