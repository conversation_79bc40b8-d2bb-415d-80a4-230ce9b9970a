package com.xt.hsk.module.edu.dal.dataobject.userquestionanswerdata;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 用户题目作答数据 DO
 *
 * <AUTHOR>
 */
@TableName("edu_user_question_answer_data")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserQuestionAnswerDataDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 作答id
     */
    private Long recordId;
    /**
     * 用户答案
     */
    private String userAnswer;
    /**
     * 参考答案
     */
    private String answer;
    /**
     * 是否正确 0-错误 1-正确
     */
    private Boolean isCorrect;
    /**
     * ai 批改状态 0-未批改 1-已批改
     */
    private Integer aiCorrectStatus;
    /**
     * 题目id
     */
    private Long questionId;
    /**
     * 题目明细id
     */
    private Long questionDetailId;
    /**
     * 版本
     */
    private Integer version;
    /**
     * 题目明细版本库id
     */
    private Long questionDetailVersionId;

}