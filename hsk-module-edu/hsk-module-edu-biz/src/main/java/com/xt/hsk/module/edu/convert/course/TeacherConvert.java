package com.xt.hsk.module.edu.convert.course;

import com.xt.hsk.module.edu.controller.admin.teacher.vo.TeacherBasicInfoRespVO;
import com.xt.hsk.module.edu.controller.admin.teacher.vo.TeacherRespVO;
import com.xt.hsk.module.edu.controller.admin.teacher.vo.TeacherSaveReqVO;
import com.xt.hsk.module.edu.dal.dataobject.teacher.TeacherDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 讲师 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TeacherConvert {

    TeacherConvert INSTANCE = Mappers.getMapper(TeacherConvert.class);

    TeacherDO createReqVOToDO(TeacherSaveReqVO bean);

    TeacherRespVO doToRespVO(TeacherDO bean);

    List<TeacherRespVO> doListToRespVOList(List<TeacherDO> list);

    /**
     * DO类转换为讲师基本信息VO类
     *
     * @param teacherDO 教师
     * @return 讲师基本信息VO
     */
    @Mapping(source = "id", target = "teacherId")
    TeacherBasicInfoRespVO doToInfoRespVo(TeacherDO teacherDO);

    /**
     * DO列表转换为讲师基本信息VO列表
     *
     * @param teacherList 教师名单
     * @return 讲师基本信息VO列表
     */
    List<TeacherBasicInfoRespVO> doListToInfoRespVoList(List<TeacherDO> teacherList);
}