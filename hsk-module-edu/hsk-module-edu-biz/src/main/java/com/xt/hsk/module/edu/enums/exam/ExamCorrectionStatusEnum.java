package com.xt.hsk.module.edu.enums.exam;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 模考批改状态枚举
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Getter
@AllArgsConstructor
public enum ExamCorrectionStatusEnum implements BasicEnum<Integer>, VO, ArrayValuable<Integer> {
    /**
     * 模考批改状态枚举
     */
    IN_PROGRESS(1, "进行中"),
    PENDING_REVIEW(2, "待批改"),
    COMPLETED(3, "已批改"),
    FAILED(4, "批改失败"),
    ;

    private static final Integer[] ARRAYS = Arrays.stream(values()).map(ExamCorrectionStatusEnum::getCode).toArray(Integer[]::new);
    private final Integer code;
    private final String desc;

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

    /**
     * 根据模考批改状态码获取对应的描述信息
     *
     * @param code 模考批改状态码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (ExamCorrectionStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据模考批改状态码获取对应的枚举实例
     *
     * @param code 模考批改状态码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static ExamCorrectionStatusEnum getByCode(Integer code) {
        for (ExamCorrectionStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
} 