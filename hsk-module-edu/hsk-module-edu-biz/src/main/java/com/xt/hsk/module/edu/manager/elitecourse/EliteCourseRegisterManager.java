package com.xt.hsk.module.edu.manager.elitecourse;

import static com.xt.hsk.module.edu.enums.elitecourse.EliteCourseLearningStatusEnum.EXPIRED;
import static com.xt.hsk.module.edu.enums.elitecourse.EliteCourseLearningStatusEnum.NORMAL;
import static com.xt.hsk.module.edu.enums.elitecourse.LearningValidityPeriodEnum.BY_DAYS;
import static com.xt.hsk.module.edu.enums.elitecourse.LearningValidityPeriodEnum.BY_DEADLINE;
import static com.xt.hsk.module.edu.enums.elitecourse.LearningValidityPeriodEnum.PERMANENT;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.servlet.ServletUtils;
import com.xt.hsk.framework.web.core.util.WebFrameworkUtils;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseRegisterSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseUserRespVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseRegisterDO;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseLearningStatusEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseRegisterTypeEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseTypeEnum;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseRegisterService;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseService;
import com.xt.hsk.module.trade.api.order.TradeOrderApi;
import com.xt.hsk.module.trade.api.order.dto.GiftOrderItemDTO;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;


/**
 * 课程登记 Manager
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Service
@Validated
public class EliteCourseRegisterManager {

    @Resource
    private EliteCourseRegisterService eliteCourseRegisterService;

    @Resource
    private EliteCourseService eliteCourseService;

    @Resource
    private TradeOrderApi tradeOrderApi;

    /**
     * 精品课课程登记添加用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void addUser(EliteCourseRegisterSaveReqVO createReqVO) {
        if (CollUtil.isEmpty(createReqVO.getUserIdList())) {
            return;
        }

        Map<Long, GiftOrderItemDTO> orderItemMap = tradeOrderApi.batchGiftEliteCourse(
            createReqVO.getUserIdList(), createReqVO.getCourseId(),
            WebFrameworkUtils.getLoginUserId(), ServletUtils.getClientIP());

        EliteCourseDO course = eliteCourseService.getEliteCourse(createReqVO.getCourseId());

        List<EliteCourseRegisterDO> doList = createReqVO.getUserIdList()
                .stream()
                .map(userId -> {
                    EliteCourseRegisterDO courseRegisterDO = new EliteCourseRegisterDO();
                    courseRegisterDO.setUserId(userId);
                    courseRegisterDO.setCourseId(course.getId());
                    courseRegisterDO.setCourseType(EliteCourseTypeEnum.REGULAR_COURSE.getCode());
                    courseRegisterDO.setRegisterType(EliteCourseRegisterTypeEnum.OFFLINE.getCode());

                    // 订单信息
                    GiftOrderItemDTO itemDTO = orderItemMap.get(userId);
                    courseRegisterDO.setOrderNumber(itemDTO.getOrderNo());
                    courseRegisterDO.setOrderDetailId(itemDTO.getOrderItemId());
                    courseRegisterDO.setEnrollmentTime(itemDTO.getOrderTime());

                    // 课程信息
                    courseRegisterDO.setLearningValidityPeriod(course.getLearningValidityPeriod());
                    // 计算有效期
                    calculateValidTime(courseRegisterDO, course);
                    return courseRegisterDO;
                })
                .toList();

        if (CollUtil.isNotEmpty(doList)) {
            eliteCourseRegisterService.saveBatch(doList);
        }

        // 更新课程购买人数
        eliteCourseService.updateCourseRegisterCount(course.getId());

    }

    private void calculateValidTime(EliteCourseRegisterDO courseRegisterDO, EliteCourseDO course) {
        Integer validity = course.getLearningValidityPeriod();

        // 情况一：永久有效
        if (PERMANENT.getCode().equals(validity)) {
            // 永久有效的课程始终有效
            courseRegisterDO.setBeginTime(courseRegisterDO.getEnrollmentTime());
            courseRegisterDO.setEndTime(LocalDateTime.of(9999, 12, 31, 23, 59, 59));

            // 情况二：按截止日期计算
        } else if (BY_DEADLINE.getCode().equals(validity)) {
            courseRegisterDO.setBeginTime(courseRegisterDO.getEnrollmentTime());
            courseRegisterDO.setEndTime(course.getDeadline());

            // 情况三：按有效天数计算
        } else if (BY_DAYS.getCode().equals(validity)) {
            // 获取用户的报名时间
            Date enrollmentTime = DateUtil.date(courseRegisterDO.getEnrollmentTime());
            // 计算报名时间加上有效天数后的截止时间
            Date deadline = DateUtil.offsetDay(enrollmentTime, course.getEffectiveDays());
            courseRegisterDO.setBeginTime(LocalDateTime.now());
            courseRegisterDO.setEndTime(DateUtil.toLocalDateTime(deadline));
        }
    }

    /**
     * 删除精品课课程登记
     */
    public void delete(Long id) {
        eliteCourseRegisterService.removeById(id);
    }

    /**
     * 分页获取课程学员列表
     */
    public PageResult<EliteCourseUserRespVO> getCourseUserList(EliteCourseStudyPageReqVO pageReqVO) {

        // 查询课程用户列表
        PageResult<EliteCourseUserRespVO> voPage = eliteCourseRegisterService.getCourseUserPage(pageReqVO);

        // 处理用户信息
        handleUserInfo(voPage.getList());
        return voPage;
    }

    /**
     * 处理用户信息
     *
     * @param voList 课程注册列表
     */
    private void handleUserInfo(List<EliteCourseUserRespVO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        // 获取课程信息
        Long courseId = voList.stream().findFirst().map(EliteCourseUserRespVO::getCourseId).orElse(null);
        EliteCourseDO eliteCourseDO = eliteCourseService.getEliteCourse(courseId);

        for (EliteCourseUserRespVO vo : voList) {
            // 设置课程信息
            setCourseInfo(vo, eliteCourseDO);

            // 设置报名途径
            vo.setRegisterTypeStr(EliteCourseRegisterTypeEnum.getDescByCode(vo.getRegisterType()));

        }
    }

    /**
     * 设置课程用户的学习状态信息
     * 根据课程的有效期类型（永久、截止日期、有效天数）判断当前课程是否仍在有效期内，
     * 并将结果设置到用户响应对象中。
     *
     * @param vo     课程用户响应对象
     * @param course 课程实体对象
     */
    private void setCourseInfo(EliteCourseUserRespVO vo, EliteCourseDO course) {
        // 如果课程为空，直接返回，避免空指针异常
        if (BeanUtil.isEmpty(course)) {
            return;
        }

        // 获取课程的有效期类型
        Integer validity = course.getLearningValidityPeriod();

        // 当前时间，避免多次创建 Date 对象
        Date now = new Date();

        // 标识课程是否处于有效期内
        boolean isValid = false;

        // 情况一：永久有效
        if (PERMANENT.getCode().equals(validity)) {
            // 永久有效的课程始终有效
            isValid = true;

            // 情况二：按截止日期计算
        } else if (BY_DEADLINE.getCode().equals(validity)) {
            // 获取课程的截止时间（转换为 Date 类型）
            Date deadline = DateUtil.date(course.getDeadline());

            // 当前时间在截止时间之前，则为有效
            isValid = now.before(deadline);

            // 情况三：按有效天数计算
        } else if (BY_DAYS.getCode().equals(validity)) {
            // 获取用户的报名时间
            Date enrollmentTime = DateUtil.date(vo.getEnrollmentTime());

            // 计算报名时间加上有效天数后的截止时间
            Date deadline = DateUtil.offsetDay(enrollmentTime, course.getEffectiveDays());

            // 当前时间在截止时间之前，则为有效
            isValid = now.before(deadline);
        }

        // 根据有效性设置学习状态及描述
        if (isValid) {
            vo.setLearningStatus(NORMAL.getCode());
            vo.setLearningStatusStr(NORMAL.getDesc());
        } else {
            vo.setLearningStatus(EXPIRED.getCode());
            vo.setLearningStatusStr(EXPIRED.getDesc());
        }
    }

    /**
     * 课程用户总数
     */
    public Long countCourseUser(EliteCourseStudyPageReqVO pageReqVO) {
        return eliteCourseRegisterService.countCourseUser(pageReqVO);
    }

    /**
     * 检查状态有效期
     */
    private boolean checkStatusValid(EliteCourseStudyPageReqVO pageReqVO) {
        EliteCourseDO course = eliteCourseService.getEliteCourse(pageReqVO.getCourseId());
        Integer validityPeriod = course.getLearningValidityPeriod();
        Integer learningStatus = pageReqVO.getLearningStatus();

        if (PERMANENT.getCode().equals(validityPeriod)) {
            return EliteCourseLearningStatusEnum.EXPIRED.getCode().equals(learningStatus);
        }

        if (BY_DEADLINE.getCode().equals(validityPeriod)) {
            Date deadline = DateUtil.date(course.getDeadline());
            boolean isAfterDeadline = DateUtil.date().after(deadline);

            // 当前日期在截止日之后 -> 无“未过期”用户；当前日期在截止日之前 -> 无“已过期”用户
            if ((isAfterDeadline && EliteCourseLearningStatusEnum.NORMAL.getCode().equals(learningStatus))
                    || (!isAfterDeadline && EliteCourseLearningStatusEnum.EXPIRED.getCode().equals(learningStatus))) {
                return true;
            }

            pageReqVO.setDeadline(course.getDeadline());
        } else if (BY_DAYS.getCode().equals(validityPeriod)) {
            pageReqVO.setEffectiveDays(course.getEffectiveDays());
        }

        pageReqVO.setLearningValidityPeriod(validityPeriod);
        return false;
    }
}