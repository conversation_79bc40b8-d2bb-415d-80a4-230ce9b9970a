package com.xt.hsk.module.edu.manager.elitecourse.dto;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.xt.hsk.module.edu.enums.elitecourse.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class CourseAndRegisterDto implements Serializable {
    /**
     * 精品课程登记ID
     */
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 课程id
     */
    private Long courseId;
    /**
     * 课程类型：1.普通课程 2.公开课
     */
    private Integer courseType;
    /**
     * 预约或购买时间
     */
    private LocalDateTime enrollmentTime;
    /**
     * 报名途径：1.线上购买 2.线下报名(后台)
     */
    private Integer registerType;
    /**
     * 购买课程订单号
     */
    private String orderNumber;
    /**
     * 订单详情ID
     */
    private Long orderDetailId;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 课程状态 1 正常 2 未开通 3 手动开启
     */
    private Integer courseRegisterStatus;
    /**
     * 开始时间
     */
    private LocalDateTime beginTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 学习有效期 1：长期有效 2：按截止日期 3：按天数
     *
     * @see LearningValidityPeriodEnum
     */
    private Integer learningValidityPeriod;

    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;
    /**
     * 一级分类id
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long primaryCategoryId;
    /**
     * 二级分类id
     */
    private Long secondaryCategoryId;
    /**
     * 课程名称-中文
     */
    private String courseNameCn;
    /**
     * 课程名称-英文
     */
    private String courseNameEn;
    /**
     * 课程名称-其他
     */
    private String courseNameOt;
    /**
     * 课程封面大图URL
     */
    private String coverUrlLarge;
    /**
     * 课程封面小图URL
     */
    private String coverUrlSmall;
    /**
     * 课时数状态 1：课程大纲课时数 2：自定义课时
     *
     * @see ClassHourNumberStatusEnum
     */
    private Integer classHourNumberStatus;
    /**
     * 自定义课时数
     */
    private Integer customClassHourNumber;
    /**
     * 划线价格(人民币)
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal originalPriceCn;
    /**
     * 售卖价格(人民币)
     */
    private BigDecimal sellingPriceCn;
    /**
     * 划线价格(美元)
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal originalPriceEn;
    /**
     * 售卖价格(美元)
     */
    private BigDecimal sellingPriceEn;
    /**
     * 划线价格(越南盾)
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal originalPriceOt;
    /**
     * 售卖价格(越南盾)
     */
    private BigDecimal sellingPriceOt;
    /**
     * 课程详情内容
     */
    private String courseDetail;
    /**
     * 上架方式 1：立即上架 2：定时上架 3：暂不上架
     *
     * @see EliteCourseListingMethodEnum
     */
    private Integer listingMethod;
    /**
     * 上架状态 1：上架 2：下架 3：待上架
     *
     * @see EliteCourseListingStatusEnum
     */
    private Integer listingStatus;
    /**
     * 最后一次上架时间
     */
    private LocalDateTime listingTime;
    /**
     * 销售基数
     */
    private Integer salesBase;

    /**
     * 截至日期
     */
    private LocalDateTime deadline;
    /**
     * 有效天数
     */
    private Integer effectiveDays;
    /**
     * 生效模式
     */
    private Integer effectModel;
    /**
     * 排序序号
     */
    private Integer sort;
    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;
    /**
     * 报名人数
     */
    private Integer enrollmentCount;
    /**
     * 类型 1.普通课程 2.公开课
     *
     * @see EliteCourseTypeEnum
     */
    private Integer type;

}
