package com.xt.hsk.module.edu.dal.dataobject.interactivecourse;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.AppBaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 互动课程学习统计 DO
 *
 * <AUTHOR>
 */
@TableName("edu_interactive_course_study_stats")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InteractiveCourseStudyStatsDO extends AppBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 单元ID，为NULL时表示课程整体统计
     */
    private Long unitId;

    /**
     * 学习人数
     */
    private Integer studyCount;
} 