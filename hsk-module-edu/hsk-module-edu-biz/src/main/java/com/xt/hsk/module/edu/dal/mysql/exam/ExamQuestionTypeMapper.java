package com.xt.hsk.module.edu.dal.mysql.exam;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamQuestionTypePageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamQuestionTypeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Objects;

/**
 * 模考题型 Mapper
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Mapper
public interface ExamQuestionTypeMapper extends BaseMapperX<ExamQuestionTypeDO> {

    default PageResult<ExamQuestionTypeDO> selectPage(ExamQuestionTypePageReqVO reqVO) {
        LambdaQueryWrapper<ExamQuestionTypeDO> queryWrapperX = new LambdaQueryWrapperX<ExamQuestionTypeDO>()
                .eqIfPresent(ExamQuestionTypeDO::getHskLevel, reqVO.getHskLevel())
                .eqIfPresent(ExamQuestionTypeDO::getSubject, reqVO.getSubject())
                .eqIfPresent(ExamQuestionTypeDO::getUnit, reqVO.getUnit())
                .eqIfPresent(ExamQuestionTypeDO::getQuestionTypeIds, reqVO.getQuestionTypeIds())
                .betweenIfPresent(ExamQuestionTypeDO::getCreateTime, reqVO.getCreateTime())
                .apply(Objects.nonNull(reqVO.getQuestionTypeId()), "JSON_CONTAINS(question_type_ids, '" + reqVO.getQuestionTypeId() + "', '$')")
                .orderByAsc(ExamQuestionTypeDO::getHskLevel, ExamQuestionTypeDO::getSubject);

        return selectPage(reqVO, queryWrapperX);
    }

}