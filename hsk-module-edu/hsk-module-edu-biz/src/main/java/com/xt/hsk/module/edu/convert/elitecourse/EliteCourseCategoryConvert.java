package com.xt.hsk.module.edu.convert.elitecourse;

import com.xt.hsk.framework.common.util.i18n.LanguageUtils;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseCategoryRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseCategorySaveReqVO;
import com.xt.hsk.module.edu.controller.app.elitecourse.vo.EliteCourseCategoryAppRespVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseCategoryDO;
import java.util.List;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * 精品课-分类转换类
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface EliteCourseCategoryConvert {

    EliteCourseCategoryConvert INSTANCE = Mappers.getMapper(EliteCourseCategoryConvert.class);

    /**
     * 创建VO类转DO类
     */
    EliteCourseCategoryDO saveReqVOToDO(EliteCourseCategorySaveReqVO saveReqVO);

    /**
     * do 列表 创建 req vo列表
     */
    List<EliteCourseCategoryRespVO> doListToCreateReqVOList(List<EliteCourseCategoryDO> list);

    /**
     * DO列表转app响应VO列表
     */
    List<EliteCourseCategoryAppRespVO> doListToAppRespVOList(List<EliteCourseCategoryDO> list);

    /**
     * DO转app响应VO
     */
    EliteCourseCategoryAppRespVO doToAppRespVO(EliteCourseCategoryDO list);

    @AfterMapping
    default void fillLocalizedFields(EliteCourseCategoryDO bean,
        @MappingTarget EliteCourseCategoryAppRespVO respVO) {
        // 这段代码会在基本映射完成后执行
        // 可以访问源对象bean的所有属性
        // 可以修改目标对象respVO的属性
        respVO.setName(LanguageUtils.getLocalizedValue(
            bean.getNameCn(),
            bean.getNameEn(),
            bean.getNameOt()));
    }
}