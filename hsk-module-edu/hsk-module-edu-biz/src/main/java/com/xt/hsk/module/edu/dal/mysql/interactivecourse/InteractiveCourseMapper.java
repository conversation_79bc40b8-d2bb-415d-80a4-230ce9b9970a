package com.xt.hsk.module.edu.dal.mysql.interactivecourse;

import cn.hutool.core.collection.CollUtil;
import com.xt.hsk.framework.common.enums.IsEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseBaseInfoRespVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCoursePageReqVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitQuoteVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRefCountDto;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseDO;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 互动课 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InteractiveCourseMapper extends BaseMapperX<InteractiveCourseDO> {

    default PageResult<InteractiveCourseDO> selectPage(InteractiveCoursePageReqVO reqVO) {
        LambdaQueryWrapperX<InteractiveCourseDO> query = new LambdaQueryWrapperX<InteractiveCourseDO>();

        // 如果有选中的ID，优先使用ID列表查询，忽略其他条件
        if (CollUtil.isNotEmpty(reqVO.getIds())) {
            query.in(InteractiveCourseDO::getId, reqVO.getIds());
        } else {
            // 没有选中ID时，使用其他查询条件
            query.eqIfPresent(InteractiveCourseDO::getType, reqVO.getType())
                .likeIfPresent(InteractiveCourseDO::getCourseNameCn, reqVO.getCourseNameCn())
                .eqIfPresent(InteractiveCourseDO::getDisplayStatus, reqVO.getDisplayStatus())
                .eqIfPresent(InteractiveCourseDO::getHskLevel, reqVO.getHskLevel());
        }

        return selectPage(reqVO, query.orderByAsc(InteractiveCourseDO::getHskLevel,InteractiveCourseDO::getSort));
    }

    /**
     * APP端专用分页查询
     */
    default PageResult<InteractiveCourseDO> selectPageForApp(com.xt.hsk.module.edu.controller.app.interactivecourse.vo.AppInteractiveCoursePageReqVO reqVO) {
        LambdaQueryWrapperX<InteractiveCourseDO> query = new LambdaQueryWrapperX<InteractiveCourseDO>()
            .eq(InteractiveCourseDO::getHskLevel, reqVO.getHskLevel())
            .eq(InteractiveCourseDO::getDisplayStatus, IsEnum.YES.getCode());
        return selectPage(reqVO, query.orderByAsc(InteractiveCourseDO::getSort));
    }

    /**
     * 根据资源ID列表获取互动课基本信息列表
     *
     * @param resourceIdList 资源ID列表
     * @return 互动课基本信息列表
     */
    List<InteractiveCourseBaseInfoRespVO> listByResourceIdList(@Param("resourceIdList") List<Long> resourceIdList);

    /**
     * 根据互动课程名称获取资源ID列表
     *
     * @param courseName 资源 ID 列表
     * @return 资源ID列表
     */
    List<Long> listResourceIdByCourseName(@Param("courseName") String courseName);

    /**
     * 引用数
     *
     * @param wordId
     * @return
     */
    int isQuoteWord(Long wordId);

    /**
     * 互动课程引用
     *
     * @param wordId
     * @return
     */
    List<InteractiveCourseUnitQuoteVO> getInteractiveCourseQuoteByWordId(Long wordId);

    long countQuestionQuote(List<Long> questionIds);

    List<QuestionRefCountDto> getInteractiveCourseQuestionQuoteCount(Collection<Long> questionIds);
}