package com.xt.hsk.module.edu.dal.mysql.word;

import com.xt.hsk.framework.common.enums.LanguangeEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordMeaningPageReqVO;
import com.xt.hsk.module.edu.controller.app.word.vo.AppWordSearchVo;
import com.xt.hsk.module.edu.dal.dataobject.word.WordMeaningDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 词语多释义表（冗余word） Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WordMeaningMapper extends BaseMapperX<WordMeaningDO> {

    default PageResult<WordMeaningDO> selectPage(WordMeaningPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WordMeaningDO>()
                .eqIfPresent(WordMeaningDO::getWordId, reqVO.getWordId())
                .eqIfPresent(WordMeaningDO::getWord, reqVO.getWord())
                .eqIfPresent(WordMeaningDO::getTranslationEn, reqVO.getTranslationEn())
                .eqIfPresent(WordMeaningDO::getInterpretation, reqVO.getInterpretation())
                .eqIfPresent(WordMeaningDO::getTranslationOt, reqVO.getTranslationOt())
                .eqIfPresent(WordMeaningDO::getKind, reqVO.getKind())
                .eqIfPresent(WordMeaningDO::getIsSpecial, reqVO.getIsSpecial())
                .betweenIfPresent(WordMeaningDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WordMeaningDO::getId));
    }

    default PageResult<WordMeaningDO> searchPage(AppWordSearchVo appWordSearchVo) {
        MPJLambdaWrapperX<WordMeaningDO> wrapperX = new MPJLambdaWrapperX<>();
        if (appWordSearchVo.getWord() != null) {
            if (LanguangeEnum.CHINESE.code.equals(appWordSearchVo.getOriginal())) {
                wrapperX.like(WordMeaningDO::getWord, appWordSearchVo.getWord());
            } else if (LanguangeEnum.ENGLISH.code.equals(appWordSearchVo.getOriginal())) {
                wrapperX.apply("JSON_CONTAINS(CONCAT('[\"', REPLACE(translation_en, ';', '\",\"'), '\"]'),JSON_QUOTE({0}),'$')", appWordSearchVo.getWord());
            } else {
                wrapperX.apply("JSON_CONTAINS(CONCAT('[\"', REPLACE(translation_ot, ';', '\",\"'), '\"]'),JSON_QUOTE({0}),'$')", appWordSearchVo.getWord());
            }
        } else {
            return PageResult.empty();
        }
        wrapperX.select(WordMeaningDO::getWordId);
        wrapperX.selectMax(WordMeaningDO::getWord);
        wrapperX.selectMax(WordMeaningDO::getPinyin);
        wrapperX.select("GROUP_CONCAT(translation_en ORDER BY sort SEPARATOR ';') as translation_en");
        wrapperX.select("GROUP_CONCAT(translation_ot ORDER BY sort SEPARATOR ';') as translation_ot");
        wrapperX.groupBy(WordMeaningDO::getWordId, WordMeaningDO::getWord, WordMeaningDO::getPinyin);
//        wrapperX
        return selectPage(appWordSearchVo, wrapperX);
    }

    ;
}