package com.xt.hsk.module.edu.api.question;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.xt.hsk.module.edu.api.question.dto.TextbookDTO;
import com.xt.hsk.module.edu.dal.dataobject.textbook.TextbookDO;
import com.xt.hsk.module.edu.service.textbook.TextbookService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 教材 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Service
public class TextbookApiImpl implements TextbookApi {

    @Resource
    private TextbookService textbookService;

    /**
     * 通过ID列表获取
     *
     * @param idList 教材 ID 列表
     * @return 教材列表
     */
    @Override
    public List<TextbookDTO> getByIds(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return Collections.emptyList();
        }

        return textbookService.lambdaQuery()
                .select(TextbookDO::getId, TextbookDO::getNameCn, TextbookDO::getNameEn, TextbookDO::getNameOt)
                .in(TextbookDO::getId, idList)
                .list()
                .stream()
                .map(textbookDO -> BeanUtil.copyProperties(textbookDO, TextbookDTO.class))
                .toList();
    }
}
