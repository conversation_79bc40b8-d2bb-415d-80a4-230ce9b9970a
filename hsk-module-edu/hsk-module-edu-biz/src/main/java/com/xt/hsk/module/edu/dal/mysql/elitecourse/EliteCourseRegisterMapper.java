package com.xt.hsk.module.edu.dal.mysql.elitecourse;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.*;
import com.xt.hsk.module.edu.controller.app.elitecourse.vo.EliteCourseAppPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseRegisterDO;
import com.xt.hsk.module.edu.manager.elitecourse.dto.CourseAndRegisterDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 课程登记 Mapper
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface EliteCourseRegisterMapper extends BaseMapperX<EliteCourseRegisterDO> {

    default PageResult<EliteCourseRegisterDO> selectPage(EliteCourseRegisterPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<EliteCourseRegisterDO>()
                .eqIfPresent(EliteCourseRegisterDO::getUserId, reqVO.getUserId())
                .eqIfPresent(EliteCourseRegisterDO::getCourseId, reqVO.getCourseId())
                .eqIfPresent(EliteCourseRegisterDO::getCourseType, reqVO.getCourseType())
                .betweenIfPresent(EliteCourseRegisterDO::getEnrollmentTime, reqVO.getEnrollmentTime())
                .eqIfPresent(EliteCourseRegisterDO::getRegisterType, reqVO.getRegisterType())
                .eqIfPresent(EliteCourseRegisterDO::getOrderNumber, reqVO.getOrderNumber())
                .eqIfPresent(EliteCourseRegisterDO::getOrderDetailId, reqVO.getOrderDetailId())
                .eqIfPresent(EliteCourseRegisterDO::getRemarks, reqVO.getRemarks())
                .eqIfPresent(EliteCourseRegisterDO::getCourseRegisterStatus, reqVO.getCourseRegisterStatus())
                .betweenIfPresent(EliteCourseRegisterDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(EliteCourseRegisterDO::getId));
    }

    /**
     * 分页获取课程用户
     */
    IPage<EliteCourseUserRespVO> getCourseUserPage(Page<EliteCourseUserRespVO> page, @Param("req") EliteCourseStudyPageReqVO pageReqVO);

    /**
     * 课程用户总数
     */
    Long countCourseUser(@Param("req") EliteCourseStudyPageReqVO pageReqVO);

    /**
     * 分页获取课程注册用户
     */
    IPage<EliteCourseRegisterUserRespVO> getCourseRegisterUserPage(Page<EliteCourseRegisterUserRespVO> page, @Param("req") EliteCourseRegisterUserPageReqVO pageReqVO);

    /**
     * 课程注册用户总数
     */
    Long countCourseRegisterUser(@Param("req") EliteCourseRegisterUserPageReqVO pageReqVO);

    Long getUserCourseRegisterCount(Long userId);

    IPage<CourseAndRegisterDto> getUserCourseRegister(Page<EliteCourseUserRespVO> page, @Param("req") EliteCourseAppPageReqVO pageVO);
}