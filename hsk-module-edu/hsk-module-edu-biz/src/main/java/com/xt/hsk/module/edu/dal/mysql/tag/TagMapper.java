package com.xt.hsk.module.edu.dal.mysql.tag;

import java.util.*;
import java.util.stream.Collectors;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.module.edu.dal.dataobject.tag.TagDO;
import org.apache.ibatis.annotations.Mapper;
import com.xt.hsk.module.edu.controller.admin.tag.vo.*;

/**
 * 标签 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TagMapper extends BaseMapperX<TagDO> {

    default PageResult<TagDO> selectPage(TagPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TagDO>()
                .likeIfPresent(TagDO::getTagName, reqVO.getTagName())
                .eqIfPresent(TagDO::getRemark, reqVO.getRemark())
                .eqIfPresent(TagDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(TagDO::getUpdateTime, reqVO.getCreateTime())
                .orderByDesc(TagDO::getUpdateTime));
    }

    default Collection<String> getAllTagName() {
        return selectList(new LambdaQueryWrapperX<TagDO>()
                .select(TagDO::getTagName))
                .stream()
                .map(TagDO::getTagName)
                .collect(Collectors.toSet());
    }

    ;
}