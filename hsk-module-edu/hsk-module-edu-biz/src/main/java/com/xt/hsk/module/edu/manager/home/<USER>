package com.xt.hsk.module.edu.manager.home;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xt.hsk.framework.common.enums.CommonStatusEnum;
import com.xt.hsk.framework.common.enums.IsShowEnum;
import com.xt.hsk.framework.common.util.i18n.LanguageUtils;
import com.xt.hsk.module.edu.controller.app.home.vo.ExamAvailabilityVO;
import com.xt.hsk.module.edu.controller.app.home.vo.HomeInfoVO;
import com.xt.hsk.module.edu.controller.app.home.vo.LastLearnedCourseRespVO;
import com.xt.hsk.module.edu.controller.app.home.vo.SpecialExerciseAvailabilityVO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseDO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.UserInteractiveCourseRecordDO;
import com.xt.hsk.module.edu.dal.dataobject.question.QuestionDO;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import com.xt.hsk.module.edu.service.exam.ExamService;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseService;
import com.xt.hsk.module.edu.service.interactivecourse.UserInteractiveCourseRecordService;
import com.xt.hsk.module.edu.service.question.QuestionService;
import com.xt.hsk.module.game.api.SpecialExerciseApi;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 首页管理器
 *
 * <AUTHOR>
 * @since 2025/07/15
 */
@Slf4j
@Component
public class HomeManager {

    /**
     * 用户互动课记录服务
     */
    @Resource
    private UserInteractiveCourseRecordService userInteractiveCourseRecordService;

    /**
     * 互动课服务
     */
    @Resource
    private InteractiveCourseService interactiveCourseService;

    /**
     * 专项练习API
     */
    @Resource
    private SpecialExerciseApi specialExerciseApi;

    /**
     * 真题题目服务
     */
    @Resource
    private QuestionService questionService;

    /**
     * 模考服务
     */
    @Resource
    private ExamService examService;

    /**
     * 默认课程位置常量
     */
    private static final int DEFAULT_FIRST_COURSE_POSITION = 1;

    /**
     * 安全执行
     *
     * @param action       函数
     * @param errorMsg     错误消息
     * @param defaultValue 默认值
     * @return {@code T }
     */
    private <T> T executeSafely(Supplier<T> action, String errorMsg, T defaultValue) {
        try {
            return action.get();
        } catch (Exception e) {
            log.error(errorMsg, e);
            return defaultValue;
        }
    }

    /**
     * 安全执行
     *
     * @param action   函数
     * @param errorMsg 错误消息
     * @return {@code T }
     */
    private <T> T executeSafely(Supplier<T> action, String errorMsg) {
        return executeSafely(action, errorMsg, null);
    }


    /**
     * 获取用户在指定等级下最后学习的课程
     * <p>
     *     1.如果用户有学习记录，则返回最近学习课程ID
     *     2.如果用户没有学习记录，则返回当前等级下第一个课程ID和课程信息，但是要标注为没有学习记录
     *     3.如果当前等级下没有课程，则告诉客户端，当前等级下没有课程
     *
     * @param hskLevel HSK等级
     * @return 最后学习的课程信息，包含课程ID、课程名称和课程在指定等级下的排序位置
     */
    private LastLearnedCourseRespVO getUserLastLearnedCourseInLevel(Integer hskLevel) {
        // 验证HSK等级参数
        if (hskLevel == null) {
            return buildNoLastLearnedCourseResponse();
        }

        // 获取指定等级下的课程列表
        List<Long> courseIdList = interactiveCourseService.getDisplayInteractiveCourseListByHskLevel(hskLevel);
        if (CollUtil.isEmpty(courseIdList)) {
            return buildNoCourseResponse();
        }

        // 未登录时直接返回有课程但无学习记录的响应
        if (!StpUtil.isLogin()) {
            return buildHasCourseButNoRecordResponse();
        }

        // 获取当前登录用户ID并查询学习记录
        Long userId = StpUtil.getLoginIdAsLong();
        UserInteractiveCourseRecordDO recordDO = getLatestUserRecord(userId, courseIdList);

        // 如果没有学习记录，返回该等级下第一个课程的信息
        if (recordDO == null) {
            return buildFirstCourseResponse(hskLevel);
        }

        // 构建最近学习课程的响应
        return buildLastLearnedCourseResponse(hskLevel, recordDO);
    }

    /**
     * 构建无最近学习课程的响应
     */
    private LastLearnedCourseRespVO buildNoLastLearnedCourseResponse() {
        return LastLearnedCourseRespVO.builder()
                .hasLastLearnedCourse(false)
                .build();
    }

    /**
     * 构建无课程的响应
     */
    private LastLearnedCourseRespVO buildNoCourseResponse() {
        return LastLearnedCourseRespVO.builder()
                .hasLastLearnedCourse(false)
                .hasCourse(false)
                .build();
    }

    /**
     * 构建有课程但无学习记录的响应
     */
    private LastLearnedCourseRespVO buildHasCourseButNoRecordResponse() {
        return LastLearnedCourseRespVO.builder()
                .hasLastLearnedCourse(false)
                .hasCourse(true)
                .build();
    }

    /**
     * 获取用户最新的学习记录
     */
    private UserInteractiveCourseRecordDO getLatestUserRecord(Long userId, List<Long> courseIdList) {
        return userInteractiveCourseRecordService.lambdaQuery()
                .eq(UserInteractiveCourseRecordDO::getUserId, userId)
                .in(UserInteractiveCourseRecordDO::getCourseId, courseIdList)
                .orderByDesc(UserInteractiveCourseRecordDO::getId)
                .last("LIMIT 1")
                .one();
    }

    /**
     * 构建第一个课程的响应（无学习记录时）
     */
    private LastLearnedCourseRespVO buildFirstCourseResponse(Integer hskLevel) {
        InteractiveCourseDO courseDO = interactiveCourseService.getFirstCourseByHskLevel(hskLevel);
        return LastLearnedCourseRespVO.builder()
                .coverUrl(courseDO.getCoverUrl())
                .courseId(courseDO.getId())
                .courseName(courseDO.getCourseNameCn())
                .position(DEFAULT_FIRST_COURSE_POSITION)
                .hasCourse(true)
                .hasLastLearnedCourse(false)
                .build();
    }

    /**
     * 构建最近学习课程的响应
     */
    private LastLearnedCourseRespVO buildLastLearnedCourseResponse(Integer hskLevel, UserInteractiveCourseRecordDO recordDO) {
        Long courseId = recordDO.getCourseId();
        InteractiveCourseDO courseDO = interactiveCourseService.getById(courseId);
        if (courseDO == null) {
            return buildNoLastLearnedCourseResponse();
        }

        Integer position = interactiveCourseService.getCoursePositionInLevel(hskLevel, courseId);
        if (position == null) {
            return buildNoLastLearnedCourseResponse();
        }

        return LastLearnedCourseRespVO.builder()
                .courseName(LanguageUtils.getLocalizedValue(
                        courseDO.getCourseNameCn(),
                        courseDO.getCourseNameEn(),
                        courseDO.getCourseNameOt()))
                .courseId(courseDO.getId())
                .position(position)
                .coverUrl(courseDO.getCoverUrl())
                .hasCourse(true)
                .hasLastLearnedCourse(true)
                .build();
    }

    /**
     * 获取指定HSK等级下的专项练习可用性信息
     *
     * @param hskLevel HSK等级
     * @return 专项练习可用性信息
     */
    private SpecialExerciseAvailabilityVO getSpecialExerciseAvailability(Integer hskLevel) {
        try {
            if (hskLevel == null) {
                return SpecialExerciseAvailabilityVO.builder()
                        .typeAvailabilityList(new ArrayList<>())
                        .build();
            }

            // 批量查询指定HSK等级下各类型专项练习的可用性
            Map<String, Boolean> availabilityMap = specialExerciseApi.getSpecialExerciseAvailabilityByHskLevel(hskLevel);

            List<SpecialExerciseAvailabilityVO.SpecialExerciseTypeAvailabilityVO> typeAvailabilityList = new ArrayList<>();

            // 遍历所有专项练习类型，构建返回结果
            for (SpecialExerciseTypeEnum typeEnum : SpecialExerciseTypeEnum.values()) {
                boolean hasQuestions = availabilityMap.getOrDefault(String.valueOf(typeEnum.getCode()), false);

                SpecialExerciseAvailabilityVO.SpecialExerciseTypeAvailabilityVO typeAvailability =
                        SpecialExerciseAvailabilityVO.SpecialExerciseTypeAvailabilityVO.builder()
                                .type(typeEnum.getCode())
                                .hasQuestions(hasQuestions)
                                .build();

                typeAvailabilityList.add(typeAvailability);
            }

            return SpecialExerciseAvailabilityVO.builder()
                    .typeAvailabilityList(typeAvailabilityList)
                    .build();
        } catch (Exception e) {
            log.error("获取专项练习可用性信息失败, hskLevel: {}", hskLevel, e);
            return SpecialExerciseAvailabilityVO.builder()
                    .typeAvailabilityList(new ArrayList<>())
                    .build();
        }
    }

    /**
     * 获取主页 信息
     *
     * @param hskLevel HSK等级
     * @return 首页相关信息
     */
    public HomeInfoVO getHomeInfo(Integer hskLevel) {
        LastLearnedCourseRespVO learnedCourseRespVO = executeSafely(
            () -> getUserLastLearnedCourseInLevel(hskLevel),
            "获取用户最近学习课程失败"
        );

        SpecialExerciseAvailabilityVO specialExerciseAvailability = executeSafely(
            () -> getSpecialExerciseAvailability(hskLevel),
            "获取专项练习可用性信息失败"
        );

        boolean haveQuestions = executeSafely(
            () -> getQuestions(hskLevel),
            "获取等级下题目信息失败",
            false
        );

        List<ExamAvailabilityVO> examAvailabilityList = executeSafely(
            () -> getExamAvailability(hskLevel),
            "获取模考可用性信息失败",
            Collections.emptyList()
        );

        return HomeInfoVO.builder()
            .lastLearnedCourse(learnedCourseRespVO)
            .specialExerciseAvailability(specialExerciseAvailability)
            .haveQuestions(haveQuestions)
            .examAvailabilityList(examAvailabilityList)
            .build();
    }

    private List<ExamAvailabilityVO> getExamAvailability(Integer hskLevel) {
        if (hskLevel == null) {
            return Collections.emptyList();
        }

        // 不管有没有 都要固定返回三个值
        List<ExamAvailabilityVO> result = new ArrayList<>();
        Map<String, Boolean> availabilityMap = examService.getExamAvailability(hskLevel);
        for (ExamTypeEnum typeEnum : ExamTypeEnum.values()) {
            ExamAvailabilityVO hasExams = ExamAvailabilityVO.builder()
                .type(typeEnum.getCode())
                .hasExams(availabilityMap.getOrDefault(String.valueOf(typeEnum.getCode()), false))
                .build();
            result.add(hasExams);
        }
        return result;
    }

    private boolean getQuestions(Integer hskLevel) {
        LambdaQueryWrapper<QuestionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuestionDO::getHskLevel, hskLevel);
        queryWrapper.eq(QuestionDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
        queryWrapper.eq(QuestionDO::getIsShow, IsShowEnum.SHOW.getCode());
        return questionService.count(queryWrapper) > 0;
    }
}
