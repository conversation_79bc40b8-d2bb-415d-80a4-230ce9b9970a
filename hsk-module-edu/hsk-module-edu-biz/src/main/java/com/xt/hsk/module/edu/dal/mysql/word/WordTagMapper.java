package com.xt.hsk.module.edu.dal.mysql.word;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.module.edu.controller.admin.tag.vo.TagPageReqVO;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordTagPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordTagDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 词语标签 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WordTagMapper extends BaseMapperX<WordTagDO> {

    default PageResult<WordTagDO> selectPage(WordTagPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WordTagDO>()
                .eqIfPresent(WordTagDO::getWordId, reqVO.getWordId())
                .eqIfPresent(WordTagDO::getWord, reqVO.getWord())
                .eqIfPresent(WordTagDO::getPinyin, reqVO.getPinyin())
                .eqIfPresent(WordTagDO::getTagId, reqVO.getTagId())
                .betweenIfPresent(WordTagDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WordTagDO::getId));
    }

    default PageResult<WordTagDO> getWordTagPageByTagIds(TagPageReqVO tagPageReqVO) {
        return selectPage(tagPageReqVO, new LambdaQueryWrapperX<WordTagDO>()
                .inIfPresent(WordTagDO::getTagId, tagPageReqVO.getIds())
                .orderByDesc(WordTagDO::getId));
    }
}