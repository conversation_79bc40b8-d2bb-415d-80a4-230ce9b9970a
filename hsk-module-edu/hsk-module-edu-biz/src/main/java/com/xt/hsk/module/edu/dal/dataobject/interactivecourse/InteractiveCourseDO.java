package com.xt.hsk.module.edu.dal.dataobject.interactivecourse;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 互动课 DO
 *
 * <AUTHOR>
 */
@TableName("edu_interactive_course")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InteractiveCourseDO extends BaseDO {

    /**
     * 课程ID
     */
    @TableId
    private Long id;
    /**
     * 课程类型 1-普通课程
     */
    private Integer type;
    /**
     * 课程名称 CN
     */
    private String courseNameCn;
    /**
     * 课程名称 EN
     */
    private String courseNameEn;
    /**
     * 课程名称 OT
     */
    private String courseNameOt;
    /**
     * 展示状态 0-隐藏 1-显示
     */
    private Integer displayStatus;
    /**
     * 排序序号
     */
    private Integer sort;
    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;
    /**
     * 封面URL
     */
    private String coverUrl;
    /**
     * 学习基数
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer learningBase;

}