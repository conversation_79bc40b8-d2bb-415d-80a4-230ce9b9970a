package com.xt.hsk.module.edu.enums;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 视频配套资料类型枚举
 *
 * <AUTHOR>
 * @since 2025/06/12
 */
@Getter
@AllArgsConstructor
public enum VideoMaterialTypeEnum implements BasicEnum<Integer> {

    /**
     * 课件
     */
    COURSEWARE(1, "课件"),

    /**
     * 生词
     */
    VOCABULARY(2, "生词");

    /**
     * 类型
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

} 