package com.xt.hsk.module.edu.dal.dataobject.userselfassessmentrecord;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;

/**
 * 用户等级自测记录 DO
 *
 * <AUTHOR>
 */
@TableName("edu_user_self_assessment_record")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserSelfAssessmentRecordDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 已作答数量
     */
    private Integer answerNum;
    /**
     * 已正确数量
     */
    private Integer correctNum;
    /**
     * 题目数量
     */
    private Integer questionNum;
    /**
     * 开始作答时间
     */
    private LocalDateTime startTime;
    /**
     * 结束作答时间
     */
    private LocalDateTime endTime;
    /**
     * 记录状态 1 进行中 2 生成报告
     */
    private Integer recordStatus;
    /**
     * 本次练习的全部题目id(英文逗号拼接)
     */
    private String questionIds;
    /**
     * 预测水平 hskLevel 1 2 4 8 16 32
     */
    private Integer predictionLevel;

}