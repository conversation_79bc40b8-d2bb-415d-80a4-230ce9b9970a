package com.xt.hsk.module.edu.dal.mysql.sourceQuestion;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionPageReqVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRespVO;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionTypeCount;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionUnitCount;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionSearchReqVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionTypeCountRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.TextbookChapterQuestionRespVO;
import com.xt.hsk.module.edu.dal.dataobject.sourceQuestion.SourceQuestionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * 题目 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SourceQuestionMapper extends BaseMapperX<SourceQuestionDO> {

    default PageResult<SourceQuestionDO> selectPage(QuestionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SourceQuestionDO>()
                .eqIfPresent(SourceQuestionDO::getTextbookId, reqVO.getTextbookId())
                .eqIfPresent(SourceQuestionDO::getChapterId, reqVO.getChapterId())
                .eqIfPresent(SourceQuestionDO::getParentId, reqVO.getParentId())
                .eqIfPresent(SourceQuestionDO::getUnitId, reqVO.getUnitId())
                .eqIfPresent(SourceQuestionDO::getTypeId, reqVO.getTypeId())
                .orderByDesc(SourceQuestionDO::getId));
    }


    default long countByTextbookId(Long id) {
        return selectCount(SourceQuestionDO::getTextbookId, id);
    }

    default long countByUnitId(Long id) {
        return selectCount(SourceQuestionDO::getUnitId, id);
    }

    @Select("<script>" +
            "SELECT type_id, COUNT(*) AS count FROM edu_question WHERE deleted = 0 AND subject = #{subject} " +
            "<if test='questionTypeIds != null and questionTypeIds.size() > 0'> AND type_id IN " +
            "<foreach item='item' index='index' collection='questionTypeIds' open='(' separator=',' close=')'>#{item}</foreach>" +
            "</if> GROUP BY type_id" +
            "</script>")
    List<QuestionTypeCount> countBySubjectAndQuestionTypeIds(@Param("subject") Integer subject, @Param("questionTypeIds") Set<Long> questionTypeIds);

    @Select("SELECT max(id) FROM edu_question")
    Long getMaxId();

    @Select("<script>" +
            "SELECT type_id, COUNT(*) AS count FROM edu_question WHERE deleted = 0  " +
            "<if test='unitIds != null and unitIds.size() > 0'> AND unit_id IN " +
            "<foreach item='item' index='index' collection='unitIds' open='(' separator=',' close=')'>#{item}</foreach>" +
            "</if> GROUP BY type_id" +
            "</script>")
    List<QuestionUnitCount> countByUnitIds(@Param("unitIds") Set<Long> unitIds);

    IPage<QuestionRespVO> queryQuestionPage(Page<QuestionRespVO> page, @Param("req") QuestionPageReqVO pageReqVO);

    QuestionRespVO queryQuestionById(Long id);



    List<QuestionTypeCountRespVO> getQuestionTypeCountRespVOByUnitIds(@Param("reqVo") QuestionSearchReqVO reqVO);

    default Long countByChapterId(Long id) {
        return selectCount(SourceQuestionDO::getChapterId, id);
    }

    List<TextbookChapterQuestionRespVO> getTextbookChapterQuestions(@Param("reqVo") QuestionSearchReqVO reqVO);

    List<Long> selectUserPracticeQuestions(QuestionSearchReqVO reqVO);

    List<Long> selectIdList();

}