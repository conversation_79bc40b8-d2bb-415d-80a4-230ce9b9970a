package com.xt.hsk.module.edu.manager.elitecourse;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.BaseCourseStudyRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteClassHourPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteClassHourStudySummaryRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseRegisterUserPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseRegisterUserRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyDetailPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyDetailRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyStatsPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyStatsRespVO;
import com.xt.hsk.module.edu.convert.elitecourse.EliteClassHourConvert;
import com.xt.hsk.module.edu.convert.elitecourse.EliteCourseStudyRecordConvert;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteClassHourDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseRegisterDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseStudyRecordDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseVideoDO;
import com.xt.hsk.module.edu.service.elitecourse.EliteClassHourService;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseRegisterService;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseStudyRecordService;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseVideoService;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 精品课程学习统计 管理后台 Manager
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Slf4j
@Component
public class EliteCourseStudyStatsManager {

    @Resource
    private EliteCourseStudyRecordService eliteCourseStudyRecordService;

    @Resource
    private EliteCourseRegisterService eliteCourseRegisterService;

    @Resource
    private EliteClassHourService eliteClassHourService;

    @Resource
    private EliteCourseVideoService eliteCourseVideoService;

    /**
     * 分页获取课程学习统计
     * <p>
     * 根据请求参数分页查询课程注册用户及其对应学习记录，按注册ID分组填充学习统计信息并返回结果。
     *
     * @param pageReqVO 分页请求参数
     * @return 分页学习统计数据
     */
    public PageResult<EliteCourseStudyStatsRespVO> getCourseStudyStatsPage(EliteCourseStudyStatsPageReqVO pageReqVO) {
        // 1. 转换请求参数
        EliteCourseRegisterUserPageReqVO registerPageReqVO = EliteCourseStudyRecordConvert
                .INSTANCE.studyReqVoToRegisterUserReqVo(pageReqVO);

        // 2. 获取课程注册用户数据
        PageResult<EliteCourseRegisterUserRespVO> registerUserPage = eliteCourseRegisterService.getCourseRegisterUserPage(registerPageReqVO);

        if (CollUtil.isEmpty(registerUserPage.getList())) {
            return new PageResult<>(0L);
        }

        // 3. 提取courseRegisterId列表
        List<Long> courseRegisterIds = registerUserPage.getList().stream()
                .map(EliteCourseRegisterUserRespVO::getCourseRegisterId)
                .toList();

        // 4. 根据courseRegisterId查询学习记录
        List<EliteCourseStudyRecordDO> studyRecords = eliteCourseStudyRecordService.lambdaQuery()
                .in(EliteCourseStudyRecordDO::getCourseRegisterId, courseRegisterIds)
                .list();

        // 5. 按courseRegisterId分组处理学习记录
        Map<Long, List<EliteCourseStudyRecordDO>> recordsMap = studyRecords.stream()
                .collect(Collectors.groupingBy(EliteCourseStudyRecordDO::getCourseRegisterId));

        // 获取录播课的时长
        Integer totalClassHourLength = getTotalClassHourLength(registerPageReqVO.getCourseId());

        // 组装返回数据
        List<EliteCourseStudyStatsRespVO> resultList = new ArrayList<>();
        for (EliteCourseRegisterUserRespVO registerUser : registerUserPage.getList()) {

            EliteCourseStudyStatsRespVO statsVO = EliteCourseStudyRecordConvert
                    .INSTANCE.registerUserRespVoToStudyStatsRespVo(registerUser);

            // 获取该用户的学习记录
            List<EliteCourseStudyRecordDO> userRecords = recordsMap.getOrDefault(registerUser.getCourseRegisterId(), Collections.emptyList());

            // 如果无记录，填充默认学习信息
            if (CollUtil.isEmpty(userRecords)) {
                fillDefaultStudyDetail(statsVO);
            } else {
                // 否则，根据学习记录填充详细信息
                fillStudyDetailWithRecords(statsVO, userRecords);

                // 计算学习进度
                BigDecimal studyProgress = calculateStudyProgress(totalClassHourLength,
                    calculateTotalMaxPlayLength(userRecords));
                statsVO.setStudyProgress(studyProgress.doubleValue());
                statsVO.setStudyProgressStr(
                    (NumberUtil.equals(studyProgress, BigDecimal.ZERO) ? 0 : studyProgress) + "%");
            }

            resultList.add(statsVO);
        }

        return new PageResult<>(resultList, registerUserPage.getTotal());
    }

    /**
     * 计算学习进度
     *
     * @param totalClassHourLength 总课时时长
     * @param totalStudyTime       总学习时间
     * @return 学习进度
     */
    private BigDecimal calculateStudyProgress(Integer totalClassHourLength,
        Integer totalStudyTime) {
        if (totalClassHourLength == null || totalClassHourLength == 0
            || totalStudyTime == null || totalStudyTime == 0) {

            return BigDecimal.ZERO;
        } else {

            // 进度比例
            BigDecimal progressRatio = NumberUtil.div(totalStudyTime, totalClassHourLength);
            // 转换百分比
            BigDecimal progressPercent = NumberUtil.mul(progressRatio, BigDecimal.valueOf(100));
            // 精确到两位小数，直接截断
            return progressPercent.setScale(2, RoundingMode.DOWN);
        }
    }

    /**
     * 计算最大课时的总学习时长
     *
     * @param userRecords 用户记录
     * @return 总学习时长
     */
    private Integer calculateTotalMaxPlayLength(List<EliteCourseStudyRecordDO> userRecords) {
        if (CollUtil.isEmpty(userRecords)) {
            return 0;
        }

        return userRecords.stream()
            .collect(Collectors.groupingBy(EliteCourseStudyRecordDO::getClassHourId))
            .values()
            .stream()
            .mapToInt(classHourRecords ->
                classHourRecords.stream()
                    .filter(e -> Objects.nonNull(e.getPlayLength()))
                    .mapToInt(EliteCourseStudyRecordDO::getPlayLength)
                    .max()
                    .orElse(0)
            )
            .sum();
    }

    /**
     * 计算总学习时长
     */
    private Integer calculateTotalStudyTime(List<EliteCourseStudyRecordDO> records) {
        if (CollUtil.isEmpty(records)) {
            return 0;
        }

        return records.stream()
                .filter(e -> Objects.nonNull(e.getPlayLength()))
                .mapToInt(EliteCourseStudyRecordDO::getPlayLength)
                .sum();
    }

    /**
     * 分页获取用户学习明细列表
     * <p>
     * 根据课程登记ID获取课时列表及用户学习记录，按课时分组并构建包含学习详情的分页明细列表。
     *
     * @param pageReqVO 分页请求参数
     * @return 用户学习明细分页结果
     */
    public PageResult<EliteCourseStudyDetailRespVO> getUserStudyDetailPage(EliteCourseStudyDetailPageReqVO pageReqVO) {
        // 根据登记ID获取课程登记信息
        EliteCourseRegisterDO courseRegister = eliteCourseRegisterService.getById(pageReqVO.getCourseRegisterId());

        // 如果登记信息不存在，返回空列表
        if (courseRegister == null) {
            return new PageResult<>(0L);
        }

        // 构建查询条件
        LambdaQueryWrapper<EliteClassHourDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EliteClassHourDO::getCourseId, courseRegister.getCourseId())
                .orderByAsc(EliteClassHourDO::getSort);

        // 分页查询课时信息
        EliteClassHourPageReqVO classHourPageReqVO = new EliteClassHourPageReqVO();
        classHourPageReqVO.setPageNo(pageReqVO.getPageNo());
        classHourPageReqVO.setPageSize(pageReqVO.getPageSize());
        classHourPageReqVO.setCourseId(courseRegister.getCourseId());
        PageResult<EliteClassHourDO> classHourPage = eliteClassHourService.getClassHourPage(classHourPageReqVO);
        List<EliteClassHourDO> classHourList = classHourPage.getList();

        if (CollUtil.isEmpty(classHourList)) {
            return new PageResult<>(0L);
        }

        List<Long> classHourIdList = classHourList.stream().map(EliteClassHourDO::getId).toList();

        // 查询用户在该登记课程下的所有学习记录
        List<EliteCourseStudyRecordDO> studyRecords = eliteCourseStudyRecordService.lambdaQuery()
                .in(EliteCourseStudyRecordDO::getClassHourId, classHourIdList)
                .eq(EliteCourseStudyRecordDO::getCourseRegisterId, courseRegister.getId())
                .list();

        // 按课时ID将学习记录分组
        Map<Long, List<EliteCourseStudyRecordDO>> recordsMap = studyRecords.stream()
                .collect(Collectors.groupingBy(EliteCourseStudyRecordDO::getClassHourId));

        // 构建VO列表，转换课时为学习明细
        List<EliteCourseStudyDetailRespVO> voList = EliteClassHourConvert.INSTANCE
                .doListToStudyDetailRespVoList(classHourList);

        // 获取课时时长
        Map<Long, Integer> classHourLengthMap = getClassHourLength(classHourList);

        for (EliteCourseStudyDetailRespVO vo : voList) {
            List<EliteCourseStudyRecordDO> userRecords = recordsMap.get(vo.getClassHourId());

            // 如果无记录，填充默认学习信息
            if (CollUtil.isEmpty(userRecords)) {
                fillDefaultStudyDetail(vo);
            } else {
                // 否则，根据学习记录填充详细信息
                fillStudyDetailWithRecords(vo, userRecords);
            }

            // 设置课时时长
            vo.setClassHourLength(classHourLengthMap.getOrDefault(vo.getVideoId(), 0));
            vo.setClassHourLengthStr(DateUtil.formatBetween(vo.getClassHourLength() * 1000L, BetweenFormatter.Level.SECOND));

            // 计算学习进度
            BigDecimal studyProgress = calculateStudyProgress(vo.getClassHourLength(),
                calculateTotalMaxPlayLength(userRecords));
            vo.setStudyProgress(studyProgress.doubleValue());
            vo.setStudyProgressStr(
                (NumberUtil.equals(studyProgress, BigDecimal.ZERO) ? 0 : studyProgress) + "%");
        }

        return new PageResult<>(voList, classHourPage.getTotal());
    }

    /**
     * 获取课时时长
     */
    private Map<Long, Integer> getClassHourLength(List<EliteClassHourDO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return Collections.emptyMap();
        }
        List<Long> videoIdList = voList.stream()
                .map(EliteClassHourDO::getVideoId)
                .filter(Objects::nonNull)
                .toList();

        if (CollUtil.isEmpty(videoIdList)) {
            return Collections.emptyMap();
        }

        return eliteCourseVideoService.lambdaQuery()
                .in(EliteCourseVideoDO::getVideoId, videoIdList)
                .list()
                .stream()
                .filter(e -> Objects.nonNull(e.getLength()))
                .collect(Collectors.toMap(
                        EliteCourseVideoDO::getVideoId,
                        EliteCourseVideoDO::getLength,
                        (k1, k2) -> k2
                ));

    }

    /**
     * 获取课时时长
     */
    private Integer getTotalClassHourLength(Long courseId) {
        List<Long> videoIdList = eliteClassHourService.lambdaQuery()
            .eq(EliteClassHourDO::getCourseId, courseId)
            .list()
            .stream()
            .map(EliteClassHourDO::getVideoId)
            .filter(Objects::nonNull)
            .toList();

        if (CollUtil.isEmpty(videoIdList)) {
            return 0;
        }

        return eliteCourseVideoService.lambdaQuery()
            .in(EliteCourseVideoDO::getVideoId, videoIdList)
            .list()
            .stream()
            .filter(e -> Objects.nonNull(e.getLength()))
            .mapToInt(EliteCourseVideoDO::getLength)
            .sum();

    }

    /**
     * 获取课时时长
     */
    private Integer getClassHourLength(Long videoId) {
        if (videoId == null) {
            return 0;
        }

        EliteCourseVideoDO eliteCourseVideoDO = eliteCourseVideoService.lambdaQuery()
            .eq(EliteCourseVideoDO::getVideoId, videoId)
            .orderByDesc(EliteCourseVideoDO::getId)
            .last("limit 1")
            .one();

        return Objects.isNull(eliteCourseVideoDO) ? 0 : eliteCourseVideoDO.getLength();
    }

    /**
     * 设置默认学习记录
     *
     * @param vo 学习明细VO
     */
    private void fillDefaultStudyDetail(BaseCourseStudyRespVO vo) {
        vo.setStudyLength(0);
        vo.setStudyLengthStr("0");
        vo.setStudyCount(0);
    }

    /**
     * 设置学习记录信息
     * <p>
     * 对学习记录按ID升序排序后，统计总学习时长、学习次数，并设置首次和最后学习时间及格式化后的时长展示。
     *
     * @param vo 学习明细VO
     * @param records 该课时的所有学习记录
     */
    private void fillStudyDetailWithRecords(BaseCourseStudyRespVO vo, List<EliteCourseStudyRecordDO> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }

        // 按ID排序，确保顺序正确
        records.sort(Comparator.comparing(EliteCourseStudyRecordDO::getId));

        // 计算总学习时长（单位：秒）
        int totalStudyTime = calculateTotalStudyTime(records);
        vo.setStudyLength(totalStudyTime);

        // 格式化学习时长
        vo.setStudyLengthStr(totalStudyTime == 0 ? "0" :
                DateUtil.formatBetween(totalStudyTime * 1000L, BetweenFormatter.Level.SECOND));

        // 设置首次和最后学习时间
        vo.setFirstStudyTime(records.get(0).getPlayBeginTime());
        vo.setLastStudyTime(records.get(records.size() - 1).getPlayEndTime());

        // 设置学习次数
        vo.setStudyCount(records.size());

    }


    /**
     * 获取精品课课时的学习汇总数据
     * <p>
     * 该方法用于获取某个精品课课时的学习统计信息，包括学习人数、平均学习时长及其字符串表示形式。
     *
     * @param classHourId 课时ID
     * @return 课时学习统计响应对象
     */
    public EliteClassHourStudySummaryRespVO getClassHourStudySummary(Long classHourId) {
        // 1.获取课时信息
        EliteClassHourDO classHour = eliteClassHourService.getEliteClassHour(classHourId);

        // 2.将课时实体转换为学习统计响应对象，并初始化相关字段
        EliteClassHourStudySummaryRespVO studySummaryRespVo = EliteClassHourConvert.INSTANCE.doToStudySummaryRespVo(classHour);
        studySummaryRespVo.setStudentCount(0L);
        studySummaryRespVo.setAvgStudyLength(0L);
        studySummaryRespVo.setAvgStudyLengthStr("0秒");

        // 3.查询课时的学习记录，按记录ID倒序排列
        List<EliteCourseStudyRecordDO> courseStudyRecordList = eliteCourseStudyRecordService.lambdaQuery()
                .eq(EliteCourseStudyRecordDO::getClassHourId, classHourId)
                .orderByDesc(EliteCourseStudyRecordDO::getId)
                .list();

        // 4.获取学习用户数
        long studentCount = eliteCourseRegisterService.lambdaQuery()
                .eq(EliteCourseRegisterDO::getCourseId, classHour.getCourseId())
                .count();
        studySummaryRespVo.setStudentCount(studentCount);

        // 5.如果有学习用户，计算平均学习时长
        if (studentCount > 0) {
            // 累加所有记录的播放时长
            long totalStudyTime = courseStudyRecordList.stream()
                    .filter(Objects::nonNull)
                    .mapToLong(EliteCourseStudyRecordDO::getPlayLength)
                    .sum();

            // 计算平均学习时长（向下取整）
            long avgStudyLength = (long) NumberUtil.div(totalStudyTime, studentCount, 0, RoundingMode.DOWN);
            studySummaryRespVo.setAvgStudyLength(avgStudyLength);

            // 将平均时长转换为格式化的字符串，例如"1小时3分5秒"
            studySummaryRespVo.setAvgStudyLengthStr(
                    DateUtil.formatBetween(avgStudyLength * 1000L, BetweenFormatter.Level.SECOND)
            );
        }

        // 6.返回学习汇总数据
        return studySummaryRespVo;
    }

    /**
     * 获取精品课课时的学习统计分页数据
     * <p>
     * 分页获取课程注册用户数据，并填充每位用户在该课时下的学习统计数据。
     *
     * @param pageReqVO 分页查询参数对象，包含课时ID等
     * @return 分页结果，包含每个用户的学习详情
     */
    public PageResult<EliteCourseStudyStatsRespVO> getClassHourStudyStatsPage(EliteCourseRegisterUserPageReqVO pageReqVO) {

        // 根据课时ID获取课时详情，确保课时存在
        EliteClassHourDO classHour = eliteClassHourService.getEliteClassHour(pageReqVO.getClassHourId());
        pageReqVO.setCourseId(classHour.getCourseId());

        // 获取课程注册用户数据
        PageResult<EliteCourseRegisterUserRespVO> registerUserPage = eliteCourseRegisterService.getCourseRegisterUserPage(pageReqVO);

        List<EliteCourseRegisterUserRespVO> registerUserList = registerUserPage.getList();

        // 如果列表为空，直接返回分页结果
        if (CollUtil.isEmpty(registerUserList)) {
            return new PageResult<>(0L);
        }

        // 提取所有用户ID
        List<Long> userIdList = registerUserList.stream()
                .map(EliteCourseRegisterUserRespVO::getUserId)
                .distinct()
                .toList();

        // 查询所有这些用户在当前课时下的学习记录
        List<EliteCourseStudyRecordDO> courseStudyRecordList = eliteCourseStudyRecordService.lambdaQuery()
                .eq(EliteCourseStudyRecordDO::getClassHourId, classHour.getId())
                .in(EliteCourseStudyRecordDO::getUserId, userIdList)
                .list();

        // 将查询到的学习记录按用户ID分组
        Map<Long, List<EliteCourseStudyRecordDO>> recordMap = courseStudyRecordList.stream()
                .collect(Collectors.groupingBy(EliteCourseStudyRecordDO::getCourseRegisterId));

        // 获取课时时长
        Integer classHourLength = getClassHourLength(classHour.getVideoId());

        List<EliteCourseStudyStatsRespVO> voList = new ArrayList<>();

        // 遍历分页数据中的每个用户，填充学习详情
        for (EliteCourseRegisterUserRespVO registerUserRespVO : registerUserList) {

            EliteCourseStudyStatsRespVO vo = EliteCourseStudyRecordConvert
                    .INSTANCE.registerUserRespVoToStudyStatsRespVo(registerUserRespVO);

            List<EliteCourseStudyRecordDO> recordList = recordMap.get(vo.getCourseRegisterId());

            // 如果无记录，填充默认学习信息
            if (CollUtil.isEmpty(recordList)) {
                fillDefaultStudyDetail(vo);
            } else {
                // 否则，根据学习记录填充详细信息
                fillStudyDetailWithRecords(vo, recordList);

                // 设置课时时长
                vo.setClassHourLength(classHourLength);
                vo.setClassHourLengthStr(
                    DateUtil.formatBetween(classHourLength * 1000L, BetweenFormatter.Level.SECOND));

                // 计算学习进度
                BigDecimal studyProgress = calculateStudyProgress(classHourLength,
                    calculateTotalMaxPlayLength(recordList));
                vo.setStudyProgress(studyProgress.doubleValue());
                vo.setStudyProgressStr(
                    (NumberUtil.equals(studyProgress, BigDecimal.ZERO) ? 0 : studyProgress) + "%");
            }

            voList.add(vo);
        }

        // 返回最终处理后的分页对象
        return new PageResult<>(voList, registerUserPage.getTotal());
    }

    /**
     * 课时学习人数总数
     */
    public Long countClassHourStudyStats(EliteCourseRegisterUserPageReqVO reqVO) {
        EliteClassHourDO classHour = eliteClassHourService.getEliteClassHour(reqVO.getClassHourId());
        reqVO.setCourseId(classHour.getCourseId());

        return eliteCourseRegisterService.countCourseRegisterUser(reqVO);
    }
}