package com.xt.hsk.module.edu.api.interactivecourse;

import cn.hutool.core.collection.CollUtil;
import com.xt.hsk.module.edu.api.interactivecourse.dto.InteractiveCourseApi;
import com.xt.hsk.module.edu.api.interactivecourse.dto.InteractiveCourseBaseInfoRespDTO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseBaseInfoRespVO;
import com.xt.hsk.module.edu.convert.interactivecourse.InteractiveCourseConvert;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseService;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 交互式课程 API 实现
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Service("interactiveCourseApiImpl")
public class InteractiveCourseApiImpl implements InteractiveCourseApi {

    @Resource
    private InteractiveCourseService interactiveCourseService;

    /**
     * 根据资源ID列表获取互动课基本信息列表
     *
     * @param resourceIdList 资源ID列表
     * @return 互动课基本信息列表
     */
    @Override
    public List<InteractiveCourseBaseInfoRespDTO> listByResourceIdList(List<Long> resourceIdList) {
        if (CollUtil.isEmpty(resourceIdList)) {
            return Collections.emptyList();
        }
        List<InteractiveCourseBaseInfoRespVO> voList = interactiveCourseService.listByResourceIdList(resourceIdList);

        return InteractiveCourseConvert.INSTANCE.voListToInfoDtoList(voList);

    }

    /**
     * 根据互动课程名称获取资源ID列表
     *
     * @param courseName 资源 ID 列表
     * @return 资源ID列表
     */
    @Override
    public List<Long> listResourceIdByCourseName(String courseName) {
        return interactiveCourseService.listResourceIdByCourseName(courseName);
    }
}
