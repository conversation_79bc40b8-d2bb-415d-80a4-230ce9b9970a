package com.xt.hsk.module.edu.dal.dataobject.elitecourse;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 精品课-视频 DO
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@TableName("edu_elite_course_video")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EliteCourseVideoDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 视频/回放ID
     */
    private Long videoId;
    /**
     * 房间ID
     */
    private Long roomId;
    /**
     * 视频名称-中文
     */
    private String nameCn;
    /**
     * 视频名称-英文
     */
    private String nameEn;
    /**
     * 视频名称-其他
     */
    private String nameOt;
    /**
     * 视频的url
     */
    private String videoUrl;
    /**
     * 封面图片的url
     */
    private String prefaceUrl;
    /**
     * 添加/回放生成时间
     */
    private LocalDateTime videoCreateTime;
    /**
     * 视频大小，单位：字节
     */
    private Long totalSize;
    /**
     * 视频时长单位：秒
     */
    private Integer length;
    /**
     * 1.录播
     */
    private Integer type;

}