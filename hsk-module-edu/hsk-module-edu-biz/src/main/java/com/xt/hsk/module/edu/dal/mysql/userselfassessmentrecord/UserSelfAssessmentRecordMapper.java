package com.xt.hsk.module.edu.dal.mysql.userselfassessmentrecord;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.module.edu.controller.app.userselfassessmentrecord.vo.UserSelfAssessmentRecordPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.userselfassessmentrecord.UserSelfAssessmentRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户等级自测记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserSelfAssessmentRecordMapper extends BaseMapperX<UserSelfAssessmentRecordDO> {

    default PageResult<UserSelfAssessmentRecordDO> selectPage(UserSelfAssessmentRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<UserSelfAssessmentRecordDO>()
                .eqIfPresent(UserSelfAssessmentRecordDO::getUserId, reqVO.getUserId())
                .eqIfPresent(UserSelfAssessmentRecordDO::getAnswerNum, reqVO.getAnswerNum())
                .eqIfPresent(UserSelfAssessmentRecordDO::getCorrectNum, reqVO.getCorrectNum())
                .eqIfPresent(UserSelfAssessmentRecordDO::getQuestionNum, reqVO.getQuestionNum())
                .betweenIfPresent(UserSelfAssessmentRecordDO::getStartTime, reqVO.getStartTime())
                .betweenIfPresent(UserSelfAssessmentRecordDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(UserSelfAssessmentRecordDO::getRecordStatus, reqVO.getRecordStatus())
                .eqIfPresent(UserSelfAssessmentRecordDO::getQuestionIds, reqVO.getQuestionIds())
                .eqIfPresent(UserSelfAssessmentRecordDO::getPredictionLevel, reqVO.getPredictionLevel())
                .betweenIfPresent(UserSelfAssessmentRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(UserSelfAssessmentRecordDO::getId));
    }

}