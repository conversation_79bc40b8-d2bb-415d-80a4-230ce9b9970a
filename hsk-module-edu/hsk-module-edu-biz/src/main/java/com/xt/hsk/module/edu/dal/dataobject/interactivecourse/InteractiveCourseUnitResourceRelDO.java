package com.xt.hsk.module.edu.dal.dataobject.interactivecourse;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 互动课单元资源关联 DO
 *
 * <AUTHOR>
 */
@TableName("edu_interactive_course_unit_resource_rel")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InteractiveCourseUnitResourceRelDO extends BaseDO {

    /**
     * 关联ID
     */
    @TableId
    private Long id;

    /**
     * 单元ID
     */
    private Long unitId;

    /**
     * 资源类型 1-课件 2-生词 3-专项练习 4-真题练习
     *
     * @see com.xt.hsk.module.edu.enums.interactivecourse.UnitResourceTypeEnum
     */
    private Integer resourceType;

    /**
     * 资源ID 当resourceType=1时，关联课件表 当resourceType=2时，关联生词表 当resourceType=3时，关联专项练习表
     * 当resourceType=4时，关联真题练习表
     */
    private Long resourceId;

    /**
     * 排序序号
     */
    private Integer sort;
} 