package com.xt.hsk.module.edu.dal.dataobject.elitecourse;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 课程登记 DO
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@TableName("edu_elite_course_register")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EliteCourseRegisterDO extends BaseDO {

    /**
     * 精品课程登记ID
     */
    @TableId
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 课程id
     */
    private Long courseId;
    /**
     * 课程类型：1.普通课程 2.公开课
     */
    private Integer courseType;
    /**
     * 预约或购买时间
     */
    private LocalDateTime enrollmentTime;
    /**
     * 报名途径：1.线上购买 2.线下报名(后台)
     */
    private Integer registerType;
    /**
     * 购买课程订单号
     */
    private String orderNumber;
    /**
     * 订单详情ID
     */
    private Long orderDetailId;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 课程状态 1 正常 2 未开通 3 手动开启
     */
    private Integer courseRegisterStatus;
    /**
     * 开始时间
     */
    private LocalDateTime beginTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 学习有效期 1：长期有效 2：按截止日期 3：按天数
     */
    private Integer learningValidityPeriod;

}