package com.xt.hsk.module.edu.dal.dataobject.sourceword;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 汉语词典基础数据 DO
 *
 * <AUTHOR>
 */
@TableName("words")
@KeySequence("word_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SourceWordDO {

    /**
     * 例句唯一ID（自增）
     */
    @TableId
    private Long id;
    /**
     * 数据类型（如cnen表示汉英）
     */
    private String type;
    /**
     * 汉字/词语（如爱）
     */
    private String word;
    /**
     * 拼音（如ài）
     */
    private String pinyin;
    /**
     * 音标（如ai）
     */
    private String phonetic;

   /* private String antonyms;

    private String synonyms;
*/
    /**
     * 注音符号（如ㄞˋ）
     */
    private String zhuyin;

    private String audioUrl;

    private String viWordId;

    private Integer status;

}