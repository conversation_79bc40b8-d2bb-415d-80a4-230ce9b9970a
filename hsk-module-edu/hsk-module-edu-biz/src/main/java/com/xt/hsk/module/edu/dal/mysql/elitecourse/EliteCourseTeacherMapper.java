package com.xt.hsk.module.edu.dal.mysql.elitecourse;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseTeacherPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseTeacherDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 精品课程讲师关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface EliteCourseTeacherMapper extends BaseMapperX<EliteCourseTeacherDO> {

    default PageResult<EliteCourseTeacherDO> selectPage(EliteCourseTeacherPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<EliteCourseTeacherDO>()
                .eqIfPresent(EliteCourseTeacherDO::getTeacherId, reqVO.getTeacherId())
                .eqIfPresent(EliteCourseTeacherDO::getEliteCourseId, reqVO.getEliteCourseId())
                .betweenIfPresent(EliteCourseTeacherDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(EliteCourseTeacherDO::getId));
    }

}