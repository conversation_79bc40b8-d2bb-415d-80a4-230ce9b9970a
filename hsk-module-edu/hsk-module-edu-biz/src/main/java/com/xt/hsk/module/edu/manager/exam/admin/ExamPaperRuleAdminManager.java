package com.xt.hsk.module.edu.manager.exam.admin;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_PAPER_RULE_NOT_EXISTS;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_RULE_ALREADY_EXISTS_CANNOT_ENABLE;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_RULE_EXISTS_CANNOT_CREATE;
import static com.xt.hsk.module.edu.enums.LogRecordConstants.EXAM_PAPER_RULE_UPDATE_STATUS_SUB_TYPE;
import static com.xt.hsk.module.edu.enums.LogRecordConstants.EXAM_PAPER_RULE_UPDATE_STATUS_SUB_TYPE_SUCCESS;

import cn.hutool.core.collection.CollUtil;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.web.core.util.WebFrameworkUtils;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleBaseInfoRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleDuplicateCheckReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleDuplicateCheckRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRulePageReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRulePageRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleQuestionTypeSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleSaveReqVO;
import com.xt.hsk.module.edu.convert.exam.ExamPaperRuleConvert;
import com.xt.hsk.module.edu.convert.exam.ExamPaperRuleDetailConvert;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamPaperRuleDO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamPaperRuleDetailDO;
import com.xt.hsk.module.edu.enums.exam.ExamPaperRuleStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamQuestionTypeUnitEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import com.xt.hsk.module.edu.service.exam.ExamPaperRuleDetailService;
import com.xt.hsk.module.edu.service.exam.ExamPaperRuleService;
import com.xt.hsk.module.edu.service.question.questiontype.QuestionTypeService;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;


/**
 * 模考组卷规则 后台 Manager
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Slf4j
@Component
public class ExamPaperRuleAdminManager {

    @Resource
    private ExamPaperRuleService examPaperRuleService;

    @Resource
    private ExamPaperRuleDetailService examPaperRuleDetailService;

    @Resource
    private QuestionTypeService questionTypeService;

    /**
     * 创建模考组卷规则
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createExamPaperRule(ExamPaperRuleSaveReqVO createReqVO) {
        ExamPaperRuleDO examPaperRule = ExamPaperRuleConvert.INSTANCE.saveReqVoToDo(createReqVO);

        // 判断是否存在
        boolean exists = checkExamPaperRuleExists(
                null,
                examPaperRule.getHskLevel(),
                examPaperRule.getExamType(),
                examPaperRule.getStatus());
        if (exists) {
            throw exception(EXAM_RULE_EXISTS_CANNOT_CREATE);
        }

        // 设置名称
        examPaperRule.setName(getRuleName(examPaperRule.getHskLevel(), examPaperRule.getExamType()));

        List<ExamPaperRuleQuestionTypeSaveReqVO> questionTypeReqVOList = Stream.of(
                        createReqVO.getListeningSubjectList(),
                        createReqVO.getReadingSubjectList(),
                        createReqVO.getWritingSubjectList()
                )
                .filter(CollUtil::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        // 设置题目数量
        examPaperRule.setQuestionCount(getQuestionCount(questionTypeReqVOList));

        examPaperRuleService.save(examPaperRule);

        Long examPaperRuleId = examPaperRule.getId();

        // 保存规则详情
        List<ExamPaperRuleDetailDO> examPaperRuleDetailList = paperRuleDetailsConvert(
                questionTypeReqVOList,
                examPaperRuleId,
                createReqVO.getHskLevel(),
                createReqVO.getExamType());

        examPaperRuleDetailService.saveBatch(examPaperRuleDetailList);

        // 设置日志上下文变量
        LogRecordContext.putVariable("ruleId", examPaperRuleId);
        LogRecordContext.putVariable("rule", examPaperRule);

        return examPaperRuleId;
    }

    /**
     * 更新模考组卷规则
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateExamPaperRule(ExamPaperRuleSaveReqVO updateReqVO) {
        validateExamPaperRuleExists(updateReqVO.getId());

        ExamPaperRuleDO examPaperRule = ExamPaperRuleConvert.INSTANCE.saveReqVoToDo(updateReqVO);

        // 判断是否存在
        boolean exists = checkExamPaperRuleExists(
                examPaperRule.getId(),
                examPaperRule.getHskLevel(),
                examPaperRule.getExamType(),
                examPaperRule.getStatus());
        if (exists) {
            throw exception(EXAM_RULE_EXISTS_CANNOT_CREATE);
        }

        // 设置名称
        examPaperRule.setName(getRuleName(examPaperRule.getHskLevel(), examPaperRule.getExamType()));

        List<ExamPaperRuleQuestionTypeSaveReqVO> questionTypeReqVOList = Stream.of(
                        updateReqVO.getListeningSubjectList(),
                        updateReqVO.getReadingSubjectList(),
                        updateReqVO.getWritingSubjectList()
                )
                .filter(CollUtil::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        // 设置题目数量
        examPaperRule.setQuestionCount(getQuestionCount(questionTypeReqVOList));

        examPaperRuleService.updateById(examPaperRule);

        Long examPaperRuleId = examPaperRule.getId();

        // 更新规则详情
        List<ExamPaperRuleDetailDO> examPaperRuleDetailList = paperRuleDetailsConvert(
                questionTypeReqVOList,
                examPaperRuleId,
                updateReqVO.getHskLevel(),
                updateReqVO.getExamType());

        examPaperRuleDetailService.updateBatchById(examPaperRuleDetailList);

        // 记录操作日志上下文
        LogRecordContext.putVariable("rule", examPaperRule);
    }

    /**
     * 获取规则名称
     *
     * @param hskLevel HSK等级
     * @param examType 模考类型
     * @return 规则名称
     */
    private String getRuleName(Integer hskLevel, Integer examType) {
        String hskLevelDesc = HskEnum.getDescByCode(hskLevel);
        String examTypeDesc = ExamTypeEnum.getDescByCode(examType);

        return String.join("-", hskLevelDesc, examTypeDesc);
    }

    /**
     * 获取题目数量
     *
     * @param reqVOList 模考组卷规则题型列表
     * @return 题目数量
     */
    private int getQuestionCount(List<ExamPaperRuleQuestionTypeSaveReqVO> reqVOList) {
        return Optional.ofNullable(reqVOList)
                .orElse(Collections.emptyList())
                .stream()
                .mapToInt(ExamPaperRuleQuestionTypeSaveReqVO::getQuestionCount)
                .sum();
    }


    /**
     * 模考组卷规则详情转换
     */
    private List<ExamPaperRuleDetailDO> paperRuleDetailsConvert(List<ExamPaperRuleQuestionTypeSaveReqVO> detailList,
                                                                Long paperRuleId,
                                                                Integer hskLevel,
                                                                Integer examType) {
        if (CollUtil.isEmpty(detailList)) {
            return Collections.emptyList();
        }

        return detailList.stream()
                .map(e -> {
                    ExamPaperRuleDetailDO detailDO = ExamPaperRuleDetailConvert.INSTANCE.saveReqVoToDo(e);
                    detailDO.setPaperRuleId(paperRuleId);
                    detailDO.setHskLevel(hskLevel);
                    detailDO.setExamType(examType);
                    return detailDO;
                })
                .toList();
    }


    public void deleteExamPaperRule(Long id) {
        // 校验存在
        ExamPaperRuleDO ruleDO = validateExamPaperRuleExists(id);
        // 删除
        examPaperRuleService.removeById(id);

        // 记录操作日志上下文
        LogRecordContext.putVariable("rule", ruleDO);
    }

    /**
     * 验证试卷规则是否存在
     *
     * @param id 模考组卷规则ID
     * @return 模考组卷规则DO
     */
    private ExamPaperRuleDO validateExamPaperRuleExists(Long id) {
        ExamPaperRuleDO examPaperRule = examPaperRuleService.getById(id);
        if (examPaperRule == null) {
            throw exception(EXAM_PAPER_RULE_NOT_EXISTS);
        }
        return examPaperRule;
    }

    /**
     * 根据id获取模考组卷规则
     */
    public ExamPaperRuleRespVO getExamPaperRule(Long id) {
        ExamPaperRuleDO examPaperRule = validateExamPaperRuleExists(id);

        return ExamPaperRuleConvert.INSTANCE.doToRespVO(examPaperRule);
    }

    /**
     * 分页获取模考组卷规则
     */
    public PageResult<ExamPaperRulePageRespVO> getExamPaperRulePage(ExamPaperRulePageReqVO pageReqVO) {
        // 查询分页数据
        PageResult<ExamPaperRuleDO> pageResult = examPaperRuleService.selectPage(pageReqVO);
        List<ExamPaperRuleDO> examPaperRuleDOList = pageResult.getList();

        if (CollUtil.isEmpty(examPaperRuleDOList)) {
            return PageResult.empty();
        }

        // 转换
        List<ExamPaperRulePageRespVO> respVOList = ExamPaperRuleConvert.INSTANCE.doListToRespVoList(examPaperRuleDOList);

        // 获取规则 ID 并查询详情数据
        List<Long> ruleIds = respVOList.stream()
                .map(ExamPaperRulePageRespVO::getId)
                .toList();

        Map<Long, List<ExamPaperRuleDetailDO>> detailsGroupedByRuleId = getGroupedRuleDetails(ruleIds);

        // 按规则逐一处理填充 subject+unit 数据
        for (ExamPaperRulePageRespVO vo : respVOList) {
            // 阅读考试时长
            String readingDurationStr = formatSeconds(vo.getReadingDuration());
            vo.setReadingDurationStr(readingDurationStr);
            // 书写考试时长
            String writingDurationStr = formatSeconds(vo.getWritingDuration());
            vo.setWritingDurationStr(writingDurationStr);

            // 单元部分
            List<ExamPaperRuleDetailDO> ruleDetails = detailsGroupedByRuleId.getOrDefault(vo.getId(), Collections.emptyList());
            fillSubjectUnitList(vo, ruleDetails);
        }

        return new PageResult<>(respVOList, pageResult.getTotal());
    }

    /**
     * 获取分组规则详细信息
     *
     * @param ruleIds 规则 ID 列表
     */
    private Map<Long, List<ExamPaperRuleDetailDO>> getGroupedRuleDetails(List<Long> ruleIds) {
        if (CollUtil.isEmpty(ruleIds)) {
            return Collections.emptyMap();
        }

        List<ExamPaperRuleDetailDO> detailList = examPaperRuleDetailService.lambdaQuery()
                .in(ExamPaperRuleDetailDO::getPaperRuleId, ruleIds)
                .orderByAsc(ExamPaperRuleDetailDO::getPaperRuleId)
                .orderByAsc(ExamPaperRuleDetailDO::getSubject)
                .orderByAsc(ExamPaperRuleDetailDO::getUnit)
                .list();

        return detailList.stream()
                .collect(Collectors.groupingBy(ExamPaperRuleDetailDO::getPaperRuleId));
    }

    /**
     * 设置单元列表
     *
     * @param vo      模考组卷规则VO
     * @param details 模考组卷规则明细列表
     */
    private void fillSubjectUnitList(ExamPaperRulePageRespVO vo, List<ExamPaperRuleDetailDO> details) {
        // 按 subject 分组
        Map<Integer, List<ExamPaperRuleDetailDO>> detailsGroupedBySubject = details.stream()
                .collect(Collectors.groupingBy(ExamPaperRuleDetailDO::getSubject));

        // 遍历
        for (SubjectEnum subjectEnum : SubjectEnum.values()) {
            List<ExamPaperRuleDetailDO> subjectDetails = detailsGroupedBySubject.getOrDefault(subjectEnum.getCode(), Collections.emptyList());

            // 按 unit 分组
            Map<Integer, List<ExamPaperRuleDetailDO>> detailsGroupedByUnit = subjectDetails.stream()
                    .collect(Collectors.groupingBy(ExamPaperRuleDetailDO::getUnit));

            // 构建 unitList
            List<String> unitList = new ArrayList<>();
            for (ExamQuestionTypeUnitEnum unitEnum : ExamQuestionTypeUnitEnum.values()) {
                List<ExamPaperRuleDetailDO> unitDetails = detailsGroupedByUnit.getOrDefault(unitEnum.getCode(), Collections.emptyList());
                if (CollUtil.isNotEmpty(unitDetails)) {
                    int sum = unitDetails.stream().mapToInt(ExamPaperRuleDetailDO::getQuestionCount).sum();
                    unitList.add(String.join("：", unitEnum.getDesc(), String.valueOf(sum)));
                }
            }

            // 根据 subject 类型设置到 VO 中
            if (CollUtil.isNotEmpty(unitList)) {
                switch (subjectEnum) {
                    case LISTENING -> vo.setListeningUnitList(unitList);
                    case READING -> vo.setReadingUnitList(unitList);
                    case WRITING -> vo.setWritingUnitList(unitList);
                    default -> {
                    }
                }
            }
        }
    }

    /**
     * 格式化秒
     *
     * @param totalSeconds 总秒数
     * @return 字符串
     */
    public static String formatSeconds(Integer totalSeconds) {

        if (totalSeconds == null || totalSeconds <= 0) {
            return "-";
        }

        int minutes = totalSeconds / 60;
        int seconds = totalSeconds % 60;
        return String.format("%02d:%02d", minutes, seconds);
    }

    /**
     * 更新状态
     *
     * @param id id
     */
    @LogRecord(type = LogRecordType.EXAM_PAPER_RULE,
            subType = EXAM_PAPER_RULE_UPDATE_STATUS_SUB_TYPE,
            bizNo = "{{#id}}",
            success = EXAM_PAPER_RULE_UPDATE_STATUS_SUB_TYPE_SUCCESS)
    public void updateStatus(Long id) {
        // 校验存在
        ExamPaperRuleDO examPaperRule = validateExamPaperRuleExists(id);

        examPaperRule.setStatus(examPaperRule.getStatus() == 0 ? 1 : 0);

        // 检查模考组卷规则是否存在
        boolean exists = checkExamPaperRuleExists(
                examPaperRule.getId(),
                examPaperRule.getHskLevel(),
                examPaperRule.getExamType(),
                examPaperRule.getStatus());
        if (exists) {
            throw exception(EXAM_RULE_ALREADY_EXISTS_CANNOT_ENABLE);
        }

        // 更新
        examPaperRuleService.lambdaUpdate()
                .eq(ExamPaperRuleDO::getId, id)
                .set(ExamPaperRuleDO::getStatus, examPaperRule.getStatus())
                .set(ExamPaperRuleDO::getUpdateTime, LocalDateTime.now())
                .set(ExamPaperRuleDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                .update();

        // 记录操作日志上下文
        LogRecordContext.putVariable("examPaperRule", examPaperRule);
        LogRecordContext.putVariable("status", examPaperRule.getStatus());
    }

    /**
     * 检查模考组卷规则是否存在
     *
     * @param paperRuleId 模考组卷规则ID
     * @param hskLevel    HSK等级
     * @param examType    模考类型
     * @param status      状态
     * @return boolean 存在返回true
     */
    private boolean checkExamPaperRuleExists(Long paperRuleId, Integer hskLevel, Integer examType, Integer status) {

        if (ExamPaperRuleStatusEnum.DISABLED.getCode().equals(status)) {
            return false;
        }

        return examPaperRuleService.lambdaQuery()
                .ne(Objects.nonNull(paperRuleId), ExamPaperRuleDO::getId, paperRuleId)
                .eq(ExamPaperRuleDO::getHskLevel, hskLevel)
                .eq(ExamPaperRuleDO::getExamType, examType)
                .eq(ExamPaperRuleDO::getStatus, ExamPaperRuleStatusEnum.ENABLED.getCode())
                .exists();
    }

    /**
     * 检查模考组卷规则是否存在重复数据
     *
     * @param checkReqVO 重复检查请求对象
     * @return 重复检查结果
     */
    public ExamPaperRuleDuplicateCheckRespVO checkExamPaperRuleDuplicate(ExamPaperRuleDuplicateCheckReqVO checkReqVO) {
        // 查询重复数据数量
        Long duplicateCount = examPaperRuleService.lambdaQuery()
                .ne(Objects.nonNull(checkReqVO.getExcludeId()), ExamPaperRuleDO::getId, checkReqVO.getExcludeId())
                .eq(ExamPaperRuleDO::getHskLevel, checkReqVO.getHskLevel())
                .eq(ExamPaperRuleDO::getExamType, checkReqVO.getExamType())
                .eq(ExamPaperRuleDO::getStatus, ExamPaperRuleStatusEnum.ENABLED.getCode())
                .count();

        return new ExamPaperRuleDuplicateCheckRespVO(duplicateCount > 0, duplicateCount);
    }

    /**
     * 根据HSK等级查询模考组卷规则列表
     *
     * @param hskLevel HSK等级
     * @return 模考组卷规则列表
     */
    public List<ExamPaperRuleBaseInfoRespVO> getBaseInfoListByHskLevel(Integer hskLevel) {
        List<ExamPaperRuleDO> examPaperRuleList = examPaperRuleService.lambdaQuery()
                .eq(ExamPaperRuleDO::getHskLevel, hskLevel)
                .eq(ExamPaperRuleDO::getStatus, ExamPaperRuleStatusEnum.ENABLED.getCode())
                .orderByAsc(ExamPaperRuleDO::getHskLevel)
                .orderByAsc(ExamPaperRuleDO::getExamType)
                .list();
        return ExamPaperRuleConvert.INSTANCE.toBaseInfoRespVOList(examPaperRuleList);
    }

}