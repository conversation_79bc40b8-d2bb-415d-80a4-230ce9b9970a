package com.xt.hsk.module.edu.manager.exam.admin;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.exam.vo.UserExamRecordPageReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.UserExamRecordPageRespVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.UserExamRecordDO;
import com.xt.hsk.module.edu.service.exam.UserExamRecordService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.DATA_NOT_EXIST;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;


/**
 * 用户模考记录 后台 Manager
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Slf4j
@Component
public class UserExamRecordAdminManager {

    @Resource
    private UserExamRecordService userExamRecordService;

    /**
     * 验证用户模考记录是否存在
     *
     * @param id 用户模考记录id
     */
    private void validateUserExamRecordExists(Long id) {
        if (userExamRecordService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }

    /**
     * 根据id获取用户模考记录
     */
    public UserExamRecordDO getUserExamRecord(Long id) {
        return userExamRecordService.getById(id);
    }

    /**
     * 分页获取用户模考记录
     */
    public PageResult<UserExamRecordPageRespVO> getUserExamRecordPage(@Valid UserExamRecordPageReqVO pageReqVO) {
        PageResult<UserExamRecordPageRespVO> page = userExamRecordService.getUserExamRecordPage(pageReqVO);

        page.getList().forEach(vo -> {
            Integer answerTime = vo.getAnswerTime();
            if (answerTime != null && answerTime > 0) {
                int minutes = answerTime / 60;
                int seconds = answerTime % 60;
                String answerTimeStr = String.format("%d分%02d秒", minutes, seconds);
                vo.setAnswerTimeStr(answerTimeStr);
            }
        });

        return page;
    }

}