package com.xt.hsk.module.edu.dal.mysql.elitecourse;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteChapterPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteChapterDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 精品课章节 Mapper
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface EliteChapterMapper extends BaseMapperX<EliteChapterDO> {

    default PageResult<EliteChapterDO> selectPage(EliteChapterPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<EliteChapterDO>()
                .eqIfPresent(EliteChapterDO::getChapterNameCn, reqVO.getChapterNameCn())
                .eqIfPresent(EliteChapterDO::getChapterNameEn, reqVO.getChapterNameEn())
                .eqIfPresent(EliteChapterDO::getChapterNameOt, reqVO.getChapterNameOt())
                .eqIfPresent(EliteChapterDO::getCourseId, reqVO.getCourseId())
                .eqIfPresent(EliteChapterDO::getSort, reqVO.getSort())
                .betweenIfPresent(EliteChapterDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(EliteChapterDO::getId));
    }

}