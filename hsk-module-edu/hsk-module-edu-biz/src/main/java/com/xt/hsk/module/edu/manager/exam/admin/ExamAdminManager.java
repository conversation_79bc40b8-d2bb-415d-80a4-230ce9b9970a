package com.xt.hsk.module.edu.manager.exam.admin;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_INVALID_PUBLISH_STATUS;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_PUBLISHED_CANNOT_DELETE;

import cn.hutool.core.collection.CollUtil;
import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.constants.RedisKeyPrefix;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.web.core.util.WebFrameworkUtils;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPageReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPageRespVO;
import com.xt.hsk.module.edu.convert.exam.ExamConvert;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamDO;
import com.xt.hsk.module.edu.enums.exam.ExamPublishStatusEnum;
import com.xt.hsk.module.edu.service.exam.ExamService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Component;


/**
 * 模考 后台 Manager
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Slf4j
@Component
public class ExamAdminManager {

    @Resource
    private ExamService examService;

    /**
     * 删除模考
     */
    @CacheEvict(value = RedisKeyPrefix.EXAM_AVAILABILITY, allEntries = true)
    public void deleteExam(Long id) {
        // 校验存在
        ExamDO exam = examService.getExam(id);
        
        // 校验发布状态：已发布的模考不允许删除
        if (ExamPublishStatusEnum.PUBLISHED.getCode().equals(exam.getPublishStatus())) {
            throw exception(EXAM_PUBLISHED_CANNOT_DELETE);
        }

        // 设置日志上下文变量
        LogRecordContext.putVariable("exam", exam);

        // 删除
        examService.removeById(id);
    }

    /**
     * 根据id获取模考
     */
    public ExamDO getExam(Long id) {
        return examService.getById(id);
    }

    /**
     * 分页获取模考
     */
    public PageResult<ExamPageRespVO> getExamPage(@Valid ExamPageReqVO pageReqVO) {
        PageResult<ExamDO> page = examService.selectPage(pageReqVO);

        List<ExamDO> pageList = page.getList();
        if (CollUtil.isEmpty(pageList)) {
            return PageResult.empty();
        }

        List<ExamPageRespVO> voList = ExamConvert.INSTANCE.toPageRespVoList(pageList);

        return new PageResult<>(voList, page.getTotal());
    }

    /**
     * 更新模考发布状态
     *
     * @param id           模考ID
     * @param targetStatus 目标状态
     */
    @CacheEvict(value = RedisKeyPrefix.EXAM_AVAILABILITY, allEntries = true)
    public void updateExamPublishStatus(Long id, Integer targetStatus) {
        // 获取模考信息
        ExamDO exam = examService.getExam(id);
        Integer currentStatus = exam.getPublishStatus();

        // 校验目标状态的有效性
        ExamPublishStatusEnum targetEnum = ExamPublishStatusEnum.getByCode(targetStatus);
        if (targetEnum == null) {
            throw exception(EXAM_INVALID_PUBLISH_STATUS);
        }

        // 如果当前状态和目标状态相同，或修改为未发布，无需操作
        if (currentStatus.equals(targetStatus) || ExamPublishStatusEnum.UNPUBLISHED.getCode().equals(targetStatus)) {
            return;
        }

        // 更新状态
        ExamDO updateExam = new ExamDO();
        updateExam.setId(id);
        updateExam.setPublishStatus(targetStatus);
        updateExam.setUpdateTime(LocalDateTime.now());
        updateExam.setUpdater(String.valueOf(WebFrameworkUtils.getLoginUserId()));
        examService.updateById(updateExam);

        // 设置日志上下文变量
        String statusText = ExamPublishStatusEnum.PUBLISHED.getCode().equals(targetStatus) ? "发布" : "取消发布";
        LogRecordContext.putVariable("statusText", statusText);
        LogRecordContext.putVariable("exam", exam);
    }

}