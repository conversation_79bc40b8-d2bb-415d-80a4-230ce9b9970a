package com.xt.hsk.module.edu.dal.dataobject.word;

import lombok.*;

import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;

/**
 * 汉语词典基础数据 DO
 *
 * <AUTHOR>
 */
@TableName("edu_word")
@KeySequence("edu_word_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WordDO extends BaseDO {

    /**
     * 例句唯一ID（自增）
     */
    @TableId
    private Long id;
    /**
     * 数据类型（如cnen表示汉英）
     */
    private String type;
    /**
     * 汉字/词语（如爱）
     */
    private String word;
    /**
     * 拼音（如ài）
     */
    private String pinyin;
    /**
     * 音标（如ai）
     */
    private String phonetic;
    /**
     * 复合词（多个词语用分号分隔，如爱不忍释; 爱不释手）
     */
    private String compound;
    /**
     * 反义词数组（如[恨, 恶, 憎]）
     */
    private String antonyms;
    /**
     * 同义词数组（如[热爱, 喜爱]）
     */
    private String synonyms;
    /**
     * 注音符号（如ㄞˋ）
     */
    private String zhuyin;
    /**
     * 音频文件ID
     */
    private Integer audioId;
    /**
     * 是否汉越词 0 否 1 是
     */
    private Integer isSpecial;
    /**
     * 音频文件URL
     */
    private String audioUrl;
    /**
     * hsk 等级
     */
    private Integer hskLevel;

}