package com.xt.hsk.module.edu.dal.dataobject.sourceQuestion;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 题目 DO
 *
 * <AUTHOR>
 */
@TableName("question")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SourceQuestionDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 教材ID
     */
    private Long textbookId;
    /**
     * 章节ID
     */
    private Long chapterId;

    private Long parentId;
    /**
     * 单元ID
     */
    private Long unitId;
    /**
     * 题型ID
     */
    private Long typeId;


    private String answer;

    private String explainCn;






}