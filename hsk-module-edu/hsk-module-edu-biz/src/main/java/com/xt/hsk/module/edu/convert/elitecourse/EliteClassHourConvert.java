package com.xt.hsk.module.edu.convert.elitecourse;

import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteClassHourRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteClassHourSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteClassHourStudySummaryRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyDetailRespVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteClassHourDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 精品课课时转换类
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Mapper
public interface EliteClassHourConvert {

    EliteClassHourConvert INSTANCE = Mappers.getMapper(EliteClassHourConvert.class);

    /**
     * 保存请求VO转DO
     */
    EliteClassHourDO saveReqVOToDO(EliteClassHourSaveReqVO saveReqVO);

    /**
     * DO类转响应VO类
     */
    @Mapping(source = "id", target = "classHourId")
    EliteClassHourRespVO doToRespVO(EliteClassHourDO classHours);

    /**
     * DO列表转响应VO列表
     */
    List<EliteClassHourRespVO> doListToRespVOList(List<EliteClassHourDO> classHours);

    /**
     * DO类转学习详情响应VO类
     */
    @Mapping(source = "id", target = "classHourId")
    EliteCourseStudyDetailRespVO doToStudyDetailRespVo(EliteClassHourDO classHour);

    /**
     * DO列表转学习详情响应VO列表
     */
    List<EliteCourseStudyDetailRespVO> doListToStudyDetailRespVoList(List<EliteClassHourDO> classHourList);

    /**
     * DO类转学习汇总响应VO类
     */
    @Mapping(source = "id", target = "classHourId")
    EliteClassHourStudySummaryRespVO doToStudySummaryRespVo(EliteClassHourDO classHour);
}