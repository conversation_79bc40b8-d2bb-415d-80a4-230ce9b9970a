package com.xt.hsk.module.edu.dal.mysql.userquestionanswerdata;

import java.util.*;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.module.edu.dal.dataobject.userquestionanswerdata.UserQuestionAnswerDataDO;
import org.apache.ibatis.annotations.Mapper;
import com.xt.hsk.module.edu.controller.admin.userquestionanswerdata.vo.*;

/**
 * 用户题目作答数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserQuestionAnswerDataMapper extends BaseMapperX<UserQuestionAnswerDataDO> {

    default PageResult<UserQuestionAnswerDataDO> selectPage(UserQuestionAnswerDataPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<UserQuestionAnswerDataDO>()
                .eqIfPresent(UserQuestionAnswerDataDO::getRecordId, reqVO.getRecordId())
                .eqIfPresent(UserQuestionAnswerDataDO::getUserAnswer, reqVO.getUserAnswer())
                .eqIfPresent(UserQuestionAnswerDataDO::getAnswer, reqVO.getAnswer())
                .eqIfPresent(UserQuestionAnswerDataDO::getIsCorrect, reqVO.getIsCorrect())
                .betweenIfPresent(UserQuestionAnswerDataDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(UserQuestionAnswerDataDO::getId));
    }

}