package com.xt.hsk.module.edu.enums.exam;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 模考类型枚举
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Getter
@AllArgsConstructor
public enum ExamTypeEnum implements BasicEnum<Integer>, VO, ArrayValuable<Integer> {
    /**
     * 模考类型枚举
     */
    FULL_REAL(1, "全真模考"),
    THIRTY_MIN(2, "30分钟模考"),
    FIFTEEN_MIN(3, "15分钟模考"),
    ;

    private static final Integer[] ARRAYS = Arrays.stream(values()).map(ExamTypeEnum::getCode).toArray(Integer[]::new);
    private final Integer code;
    private final String desc;

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

    /**
     * 根据模考类型码获取对应的描述信息
     *
     * @param code 模考类型码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (ExamTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据模考类型码获取对应的枚举实例
     *
     * @param code 模考类型码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static ExamTypeEnum getByCode(Integer code) {
        for (ExamTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
} 