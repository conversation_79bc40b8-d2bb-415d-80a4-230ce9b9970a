package com.xt.hsk.module.edu.enums;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 学习状态枚举
 *
 * <AUTHOR>
 * @since 2025/06/12
 */
@Getter
@AllArgsConstructor
public enum LearningStatusEnum implements BasicEnum<Integer> {

    /**
     * 未开始
     */
    NOT_STARTED(1, "未开始"),

    /**
     * 正在进行
     */
    IN_PROGRESS(2, "正在进行"),

    /**
     * 已完成
     */
    COMPLETED(3, "已完成");

    /**
     * 状态
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;
} 