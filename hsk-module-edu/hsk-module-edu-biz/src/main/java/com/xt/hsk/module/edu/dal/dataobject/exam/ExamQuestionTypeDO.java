package com.xt.hsk.module.edu.dal.dataobject.exam;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import com.xt.hsk.module.edu.enums.exam.ExamQuestionTypeUnitEnum;
import lombok.*;

/**
 * 模考题型 DO
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@TableName("edu_exam_question_type")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExamQuestionTypeDO extends BaseDO {

    /**
     * 模考题型ID
     */
    @TableId
    private Long id;
    /**
     * HSK等级
     */
    private Integer hskLevel;
    /**
     * 科目
     *
     * @see SubjectEnum
     */
    private Integer subject;
    /**
     * 单元部分
     *
     * @see ExamQuestionTypeUnitEnum
     */
    private Integer unit;
    /**
     * 题型题型id列表
     */
    private String questionTypeIds;

}