package com.xt.hsk.module.edu.dal.mysql.elitecourse;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseCategoryPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseCategoryDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 精品课-分类 Mapper
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface EliteCourseCategoryMapper extends BaseMapperX<EliteCourseCategoryDO> {

    default PageResult<EliteCourseCategoryDO> selectPage(EliteCourseCategoryPageReqVO reqVO) {

        LambdaQueryWrapperX<EliteCourseCategoryDO> query = new LambdaQueryWrapperX<EliteCourseCategoryDO>();

        query.likeIfPresent(EliteCourseCategoryDO::getNameCn, reqVO.getNameCn())
                .eqIfPresent(EliteCourseCategoryDO::getHskLevel, reqVO.getHskLevel())
                .orderByAsc(EliteCourseCategoryDO::getHskLevel, EliteCourseCategoryDO::getSort);

        return selectPage(reqVO,query);
    }

}