package com.xt.hsk.module.edu.dal.dataobject.question.questiontype;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 题型 DO
 *
 * <AUTHOR>
 */
@TableName("edu_question_type")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionTypeDO extends BaseDO {

    /**
     * 题型id
     */
    @TableId
    private Long id;
    /**
     * 题型名称
     */
    private String nameCn;
    /**
     * 题型名称 英文
     */
    private String nameEn;
    /**
     * 题型名称 其他语言
     */
    private String nameOt;

    private Integer subject;
    /**
     * hsk等级 com.xt.hsk.framework.common.enums.HskEnum
     */
    private Integer hskLevel;
}