package com.xt.hsk.module.edu.dal.dataobject.word;

import com.xt.hsk.module.edu.controller.admin.word.vo.WordExampleSaveReqVO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;

import java.util.List;

/**
 * 词语多释义表（冗余word） DO
 *
 * <AUTHOR>
 */
@TableName("edu_word_meaning")
@KeySequence("edu_word_meaning_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WordMeaningDO extends BaseDO {

    /**
     * 例句唯一ID（自增）
     */
    @TableId
    private Long id;
    /**
     * 冗余主表words的id（如38544）
     */
    private Long wordId;
    /**
     * 中文单词
     */
    private String word;
    /**
     * 英文单词
     */
    private String translationEn;
    /**
     * 中文解释（这儿放的是中文解释，非单词如对人或事物有很深的感情）
     */
    private String interpretation;
    /**
     * 越南语单词
     */
    private String translationOt;
    /**
     * 词性分类（如v,n，v-动词，n-名词）
     */
    private Integer kind;
    /**
     * 是否汉越词 0 否 1 是
     */
    private Integer isSpecial;

    /**
     * 例句
     */
    @TableField(exist = false)
    private List<WordExampleSaveReqVO> examples;
    /**
     * 是否删除
     */
    private Boolean deleted;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 拼音（如ài）
     */
    private String pinyin;

}