package com.xt.hsk.module.edu.dal.dataobject.course;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 课程推荐表 DO
 *
 * <AUTHOR>
 */
@TableName("edu_recommended_course")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecommendedCourseDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 排序序号
     */
    private Integer sort;
} 