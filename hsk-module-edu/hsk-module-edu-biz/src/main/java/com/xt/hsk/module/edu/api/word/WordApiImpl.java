package com.xt.hsk.module.edu.api.word;

import cn.hutool.core.text.CharSequenceUtil;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.api.word.dto.WordRespDTO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordDO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordMeaningDO;
import com.xt.hsk.module.edu.service.word.WordService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 汉语词典基础数据 API 实现
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
public class WordApiImpl implements WordApi {

    @Resource
    private WordService wordService;

    /**
     * 根据单词列表查询对应的单词信息
     *
     * @param wordList 单词列表
     * @return 单词响应DTO列表
     */
    @Override
    public List<WordRespDTO> listByWordList(List<String> wordList) {
        // 过滤空白和重复项，防止无效查询
        List<String> filteredWords = Optional.ofNullable(wordList)
                .orElse(Collections.emptyList())
                .stream()
                .filter(CharSequenceUtil::isNotBlank)
                .distinct()
                .toList();

        // 若过滤后为空，直接返回空列表
        if (filteredWords.isEmpty()) {
            return Collections.emptyList();
        }

        // 根据单词查询
        List<WordDO> wordDOList = wordService.lambdaQuery()
                .in(WordDO::getWord, filteredWords)
                .list();

        if (wordDOList.isEmpty()) {
            return Collections.emptyList();
        }

        // 提取单词ID列表，用于查询单词含义
        List<Long> wordIds = wordDOList.stream()
                .map(WordDO::getId)
                .toList();

        if (wordIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 查询所有单词含义，并按单词ID分组
        List<WordMeaningDO> meaningDOList = wordService.getWordsMeaningsByWordIds(wordIds);
        Map<String, List<WordMeaningDO>> meaningMap = meaningDOList.stream()
                .collect(Collectors.groupingBy(WordMeaningDO::getWord));

        List<WordRespDTO> result = new ArrayList<>();
        for (WordDO word : wordDOList) {

            WordRespDTO respDTO = new WordRespDTO();
            respDTO.setId(word.getId());
            respDTO.setWord(word.getWord());

            List<WordMeaningDO> meaningList = meaningMap.getOrDefault(respDTO.getWord(), Collections.emptyList());

            List<String> translationOts = meaningList.stream()
                    .map(WordMeaningDO::getTranslationOt)
                    .flatMap(s -> Arrays.stream(s.split(";")))
                    .map(String::trim)
                    .filter(CharSequenceUtil::isNotBlank)
                    .distinct()
                    .toList();

            respDTO.setTranslationOts(translationOts);
            result.add(respDTO);
        }
        return result;

    }

    /**
     * 根据字词名称和拼音查询字词信息
     *
     * @param word   字词名称
     * @param pinyin 拼音
     * @return 字词信息
     */
    @Override
    public WordRespDTO getWordByName(String word, String pinyin) {
        WordDO wordDO = wordService.lambdaQuery()
                .eq(WordDO::getWord, word)
                .eq(WordDO::getPinyin, pinyin)
                .last("LIMIT 1")
                .one();
        return BeanUtils.toBean(wordDO, WordRespDTO.class);
    }

    /**
     * 根据单词ID列表查询对应的单词信息
     *
     * @param wordIdList 单词列表
     * @return 单词响应DTO列表
     */
    @Override
    public List<WordRespDTO> listByWordIdList(List<Long> wordIdList) {

        Map<Long, List<WordMeaningDO>> meaningMap = wordService.getWordsMeaningsByWordIds(wordIdList)
                .stream()
                .collect(Collectors.groupingBy(WordMeaningDO::getWordId));

        List<WordRespDTO> result = new ArrayList<>();

        for (Map.Entry<Long, List<WordMeaningDO>> entry : meaningMap.entrySet()) {
            WordRespDTO respDTO = new WordRespDTO();
            respDTO.setId(entry.getKey());
            List<WordMeaningDO> meaningList = entry.getValue();

            List<String> translationOtList = meaningList.stream()
                    .map(WordMeaningDO::getTranslationOt)
                    .flatMap(s -> Arrays.stream(s.split(";")))
                    .map(String::trim)
                    .filter(CharSequenceUtil::isNotBlank)
                    .distinct()
                    .toList();
            respDTO.setTranslationOts(translationOtList);
            result.add(respDTO);
        }
        return result;
    }

}
