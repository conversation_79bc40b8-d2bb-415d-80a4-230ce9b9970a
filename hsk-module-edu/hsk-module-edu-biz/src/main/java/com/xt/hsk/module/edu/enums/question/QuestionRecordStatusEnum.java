package com.xt.hsk.module.edu.enums.question;

import com.xt.hsk.framework.common.enums.BasicEnum;

/**
 * 记录状态 1 进行中 2 已提交 3 ai批改未完成  4 ai批改完成 5 ai批改失败
 */
public enum QuestionRecordStatusEnum implements BasicEnum<Integer> {

    /**
     * 进行中
     */
    IN_PROGRESS(1, "进行中"),
    /**
     * 已提交
     */
    SUBMITTED(2, "已提交"),
    /**
     * ai批改未完成
     */
    AI_CORRECTION_NOT_COMPLETED(3, "ai批改未完成"),
    /**
     * ai批改完成
     */
    AI_CORRECTION_COMPLETED(4, "ai批改完成"),
    /**
     * ai批改失败
     */
    AI_CORRECTION_FAILED(5, "ai批改失败");

    private final Integer code;
    private final String desc;

    QuestionRecordStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
