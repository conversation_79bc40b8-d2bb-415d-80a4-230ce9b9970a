package com.xt.hsk.module.edu.dal.dataobject.elitecourse;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 精品课程学习记录 DO
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@TableName("edu_elite_course_study_record")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EliteCourseStudyRecordDO extends BaseDO {

    /**
     * 精品课程学习明细id
     */
    @TableId
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 课程id（edu_elite_course表id）
     */
    private Long courseId;
    /**
     * 课程登记id（edu_elite_course_register表id）
     */
    private Long courseRegisterId;
    /**
     * 章节id（edu_elite_chapter表id）
     */
    private Long chapterId;
    /**
     * 课时id（edu_elite_class_hour表id）
     */
    private Long classHourId;
    /**
     * 视频id
     */
    private Long videoId;
    /**
     * 日期，格式：yyyy-MM-dd
     */
    private LocalDate date;
    /**
     * 起始播放时间，格式如：yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime playBeginTime;
    /**
     * 结束播放时间，格式如：yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime playEndTime;
    /**
     * 观看时长，单位：秒
     */
    private Integer playLength;
    /**
     * 客户端类型：1：Android 2：iOS 3:H5
     */
    private Integer clientType;
    /**
     * 课程类型 1.普通课程 2.公开课
     */
    private Integer courseType;
    /**
     * 记录类型 1.直播 2.录播 3.回放
     */
    private Integer recordType;
    /**
     * 用户IP
     */
    private String userIp;
    /**
     * 设备信息
     */
    private String deviceInfo;
    /**
     * 备注
     */
    private String remark;

}