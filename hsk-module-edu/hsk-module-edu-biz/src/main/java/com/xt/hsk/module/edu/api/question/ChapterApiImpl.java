package com.xt.hsk.module.edu.api.question;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.xt.hsk.module.edu.api.question.dto.ChapterDTO;
import com.xt.hsk.module.edu.dal.dataobject.chapter.ChapterDO;
import com.xt.hsk.module.edu.service.chapter.ChapterService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 课程大纲 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class ChapterApiImpl implements ChapterApi {

    @Resource
    private ChapterService chapterService;

    /**
     * 通过ID列表获取
     *
     * @param idList 章节 ID 列表
     * @return 章节列表
     */
    @Override
    public List<ChapterDTO> getByIds(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return Collections.emptyList();
        }

        return chapterService.lambdaQuery()
                .select(ChapterDO::getId, ChapterDO::getChapterNameCn, ChapterDO::getChapterNameEn, ChapterDO::getChapterNameOt)
                .in(ChapterDO::getId, idList)
                .list()
                .stream()
                .map(chapterDO -> BeanUtil.copyProperties(chapterDO, ChapterDTO.class))
                .toList();
    }
}
