package com.xt.hsk.module.edu.dal.mysql.exam;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamDetailPageReqVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRefCountDto;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamDetailDO;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 模考详情 Mapper
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Mapper
public interface ExamDetailMapper extends BaseMapperX<ExamDetailDO> {

    default PageResult<ExamDetailDO> selectPage(ExamDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ExamDetailDO>()
                .eqIfPresent(ExamDetailDO::getExamId, reqVO.getExamId())
                .eqIfPresent(ExamDetailDO::getSubject, reqVO.getSubject())
                .eqIfPresent(ExamDetailDO::getUnit, reqVO.getUnit())
                .eqIfPresent(ExamDetailDO::getExamQuestionTypeId, reqVO.getExamQuestionTypeId())
                .eqIfPresent(ExamDetailDO::getQuestionTypeIds, reqVO.getQuestionTypeIds())
                .eqIfPresent(ExamDetailDO::getQuestionNames, reqVO.getQuestionNames())
                .eqIfPresent(ExamDetailDO::getQuestionCount, reqVO.getQuestionCount())
                .eqIfPresent(ExamDetailDO::getQuestions, reqVO.getQuestions())
                .betweenIfPresent(ExamDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ExamDetailDO::getId));
    }

    @InterceptorIgnore(tenantLine = "true")
    long countQuestionQuote(List<Long> questionIds);

    @InterceptorIgnore(tenantLine = "true")
    List<QuestionRefCountDto> getExamQuestionQuoteCount(Collection<Long> questionIds);
}