package com.xt.hsk.module.edu.dal.mysql.question;

import java.util.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionTypeCount;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionUnitCount;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionSearchReqVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionTypeCountRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.TextbookChapterQuestionRespVO;
import com.xt.hsk.module.edu.dal.dataobject.question.QuestionDO;
import org.apache.ibatis.annotations.Mapper;
import com.xt.hsk.module.edu.controller.admin.question.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 题目 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionMapper extends BaseMapperX<QuestionDO> {

    default PageResult<QuestionDO> selectPage(QuestionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<QuestionDO>()
                .eqIfPresent(QuestionDO::getHskLevel, reqVO.getHskLevel())
                .eqIfPresent(QuestionDO::getTextbookId, reqVO.getTextbookId())
                .eqIfPresent(QuestionDO::getChapterId, reqVO.getChapterId())
                .eqIfPresent(QuestionDO::getUnitId, reqVO.getUnitId())
                .eqIfPresent(QuestionDO::getTypeId, reqVO.getTypeId())
                .eqIfPresent(QuestionDO::getSubject, reqVO.getSubject())
                .eqIfPresent(QuestionDO::getMaterialAudio, reqVO.getMaterialAudio())
                .eqIfPresent(QuestionDO::getMaterialImage, reqVO.getMaterialImage())
                .eqIfPresent(QuestionDO::getMaterialContent, reqVO.getMaterialContent())
                .eqIfPresent(QuestionDO::getOptions, reqVO.getOptions())
                .eqIfPresent(QuestionDO::getQuestionNum, reqVO.getQuestionNum())
                .eqIfPresent(QuestionDO::getStatus, reqVO.getStatus())
                .eqIfPresent(QuestionDO::getIsShow, reqVO.getIsShow())
                .eqIfPresent(QuestionDO::getVersion, reqVO.getVersion())
                .betweenIfPresent(QuestionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(QuestionDO::getId));
    }

    default long countByTextbookId(Long id) {
        return selectCount(QuestionDO::getTextbookId, id);
    }

    default long countByUnitId(Long id) {
        return selectCount(QuestionDO::getUnitId, id);
    }

    @Select("<script>" +
            "SELECT type_id, COUNT(*) AS count FROM edu_question WHERE deleted = 0 AND subject = #{subject} " +
            "<if test='questionTypeIds != null and questionTypeIds.size() > 0'> AND type_id IN " +
            "<foreach item='item' index='index' collection='questionTypeIds' open='(' separator=',' close=')'>#{item}</foreach>" +
            "</if> GROUP BY type_id" +
            "</script>")
    List<QuestionTypeCount> countBySubjectAndQuestionTypeIds(@Param("subject") Integer subject, @Param("questionTypeIds") Set<Long> questionTypeIds);

    @Select("SELECT max(id) FROM edu_question")
    Long getMaxId();

    List<QuestionUnitCount> countByUnitIds(@Param("unitIds") Set<Long> unitIds);

    IPage<QuestionRespVO> queryQuestionPage(Page<QuestionRespVO> page, @Param("req") QuestionPageReqVO pageReqVO);

    QuestionRespVO queryQuestionById(Long id);

    Long queryQuestionCount( @Param("req") QuestionPageReqVO pageReqVO);



    List<QuestionTypeCountRespVO> getQuestionTypeCountRespVOByUnitIds(@Param("reqVo") QuestionSearchReqVO reqVO);

    default Long countByChapterId(Long id) {
        return selectCount(QuestionDO::getChapterId, id);
    }

    List<TextbookChapterQuestionRespVO> getTextbookChapterQuestions(@Param("reqVo") QuestionSearchReqVO reqVO);

    List<Long> selectUserPracticeQuestions(QuestionSearchReqVO reqVO);

    List<Long> selectIdList();

    List<QuestionTextbookCount> countByTextbookIds(Set<Long> textbookIds);

    /**
     * 根据题目id列表查询题目
     *
     * @param idList ID列表
     * @return 题目列表
     */
    List<QuestionRespVO> queryQuestionByIdList(@Param("idList") List<Long> idList);

    List<Long> getRandomQuestionIds(int hskLevel, int subject, int limit);
}