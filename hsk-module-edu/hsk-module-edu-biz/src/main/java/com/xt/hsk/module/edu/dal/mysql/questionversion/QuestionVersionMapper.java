package com.xt.hsk.module.edu.dal.mysql.questionversion;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.question.questionversion.vo.QuestionVersionPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questionversion.QuestionVersionDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 题目表版本库 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionVersionMapper extends BaseMapperX<QuestionVersionDO> {

    default PageResult<QuestionVersionDO> selectPage(QuestionVersionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<QuestionVersionDO>()
                .eqIfPresent(QuestionVersionDO::getQuestionId, reqVO.getQuestionId())
                .eqIfPresent(QuestionVersionDO::getHskLevel, reqVO.getHskLevel())
                .eqIfPresent(QuestionVersionDO::getTextbookId, reqVO.getTextbookId())
                .eqIfPresent(QuestionVersionDO::getChapterId, reqVO.getChapterId())
                .eqIfPresent(QuestionVersionDO::getUnitId, reqVO.getUnitId())
                .eqIfPresent(QuestionVersionDO::getTypeId, reqVO.getTypeId())
                .eqIfPresent(QuestionVersionDO::getSubject, reqVO.getSubject())
                .eqIfPresent(QuestionVersionDO::getMaterialAudio, reqVO.getMaterialAudio())
                .eqIfPresent(QuestionVersionDO::getMaterialImage, reqVO.getMaterialImage())
                .eqIfPresent(QuestionVersionDO::getMaterialContent, reqVO.getMaterialContent())
                .eqIfPresent(QuestionVersionDO::getOptions, reqVO.getOptions())
                .eqIfPresent(QuestionVersionDO::getQuestionNum, reqVO.getQuestionNum())
                .eqIfPresent(QuestionVersionDO::getStatus, reqVO.getStatus())
                .eqIfPresent(QuestionVersionDO::getIsShow, reqVO.getIsShow())
                .eqIfPresent(QuestionVersionDO::getVersion, reqVO.getVersion())
                .betweenIfPresent(QuestionVersionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(QuestionVersionDO::getId));
    }

    QuestionVersionDO getQuestionByIdAndVersion(Long questionId, Integer version);
}