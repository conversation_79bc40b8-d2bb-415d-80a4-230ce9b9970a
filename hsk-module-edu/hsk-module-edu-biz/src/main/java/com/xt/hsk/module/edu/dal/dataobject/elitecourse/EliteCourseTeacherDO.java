package com.xt.hsk.module.edu.dal.dataobject.elitecourse;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 精品课程讲师关联 DO
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@TableName("edu_elite_course_teacher")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EliteCourseTeacherDO extends BaseDO {

    /**
     * 精品课程讲师关联id
     */
    @TableId
    private Long id;
    /**
     * 讲师id（edu_teacher表id）
     */
    private Long teacherId;
    /**
     * 精品课程id（edu_elite_course表id）
     */
    private Long eliteCourseId;

}