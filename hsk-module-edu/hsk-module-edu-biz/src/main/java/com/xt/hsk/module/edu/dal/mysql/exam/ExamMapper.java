package com.xt.hsk.module.edu.dal.mysql.exam;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPageReqVO;
import com.xt.hsk.module.edu.controller.app.exam.vo.ExamAppPageReqVO;
import com.xt.hsk.module.edu.controller.app.exam.vo.ExamAppPageRespVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 模考 Mapper
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Mapper
public interface ExamMapper extends BaseMapperX<ExamDO> {

    default PageResult<ExamDO> selectPage(ExamPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ExamDO>()
                .eqIfPresent(ExamDO::getHskLevel, reqVO.getHskLevel())
                .eqIfPresent(ExamDO::getPaperRuleId, reqVO.getPaperRuleId())
                .likeIfPresent(ExamDO::getName, reqVO.getName())
                .eqIfPresent(ExamDO::getType, reqVO.getType())
                .eqIfPresent(ExamDO::getCoverUrl, reqVO.getCoverUrl())
                .eqIfPresent(ExamDO::getDescription, reqVO.getDescription())
                .eqIfPresent(ExamDO::getListeningDuration, reqVO.getListeningDuration())
                .eqIfPresent(ExamDO::getReadingDuration, reqVO.getReadingDuration())
                .eqIfPresent(ExamDO::getWritingDuration, reqVO.getWritingDuration())
                .eqIfPresent(ExamDO::getSort, reqVO.getSort())
                .eqIfPresent(ExamDO::getExamCount, reqVO.getExamCount())
                .eqIfPresent(ExamDO::getTotalScore, reqVO.getTotalScore())
                .eqIfPresent(ExamDO::getPublishStatus, reqVO.getPublishStatus())
                .betweenIfPresent(ExamDO::getCreateTime, reqVO.getCreateTime())
                .orderByAsc(ExamDO::getHskLevel, ExamDO::getSort));
    }

    /**
     * 分页获取App模考列表
     *
     * @param reqVO 请求参数
     * @return 模考列表
     */

    IPage<ExamAppPageRespVO> getAppExamPage(Page<ExamAppPageRespVO> page, @Param("req") ExamAppPageReqVO reqVO);
}