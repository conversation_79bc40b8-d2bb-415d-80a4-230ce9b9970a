package com.xt.hsk.module.edu.util;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.QUESTION_SET_ANSWER;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.xt.hsk.framework.common.exception.ServerException;
import com.xt.hsk.module.edu.controller.admin.question.vo.OptionContentVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionDetailRespVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRespVO;
import com.xt.hsk.module.edu.dal.dataobject.question.QuestionDO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiondetail.QuestionDetailDO;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * 题目内容工具类
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
public class QuestionContentUtil {

    // 定义日期时间格式（与你的输入字符串一致："yyyy-MM-dd HH:mm:ss"）
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 配置全局唯一的 ObjectMapper（关键）
    private static final ObjectMapper objectMapper = new ObjectMapper();
    static {
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 1. 禁用 Jackson 默认的时间戳模式（避免 LocalDateTime 被序列化为时间戳）
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        // 2. 创建 JSR-310 模块（处理 java.time 类型）
        JavaTimeModule javaTimeModule = new JavaTimeModule();

        // 3. 注册 LocalDateTime 序列化器（对象转 JSON 时，转为指定格式的字符串）
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(FORMATTER));

        // 4. 注册 LocalDateTime 反序列化器（JSON 转对象时，按指定格式解析字符串）
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(FORMATTER));
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(FORMATTER) {
            @Override
            public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider provider) throws IOException {
                System.out.println("[DEBUG] 序列化 LocalDateTime: " + value); // 关键日志
                super.serialize(value, gen, provider);
            }
        });
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(FORMATTER) {
            @Override
            public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
                String dateStr = p.getValueAsString();
                System.out.println("[DEBUG] 反序列化 LocalDateTime: " + dateStr); // 关键日志
                return super.deserialize(p, ctxt);
            }
        });
        // 5. 注册模块到 ObjectMapper
        objectMapper.registerModule(javaTimeModule);
    }



    public static List<OptionContentVO> optionConvert(String options)  {
        // 参数校验：避免空输入
        try {
            if (options == null || options.trim().isEmpty()) {
                throw new IllegalArgumentException("输入的 JSON 字符串不能为空");
            }
            // 使用 Jackson 进行反序列化，指定目标类型为 List<OptionContentVO>
            return objectMapper.readValue(options, new TypeReference<List<OptionContentVO>>() {});
        }catch (Exception e){
            log.error("JSON 转换为选项内容对象列表失败", e);
        }
        return null;
    }

    public static List<QuestionDetailRespVO> questionDetailConvert(List<QuestionDetailDO> questionDetail) {
        try {
            if (questionDetail == null || questionDetail.isEmpty()) {
                throw new IllegalArgumentException("输入的列表不能为空");
            }
            // 序列化（验证输出）
            String json = objectMapper.writeValueAsString(questionDetail);
            // 反序列化（使用相同配置的 mapper）
            return objectMapper.readValue(json, new TypeReference<List<QuestionDetailRespVO>>() {});

        } catch (Exception e) {
            log.error("转换失败", e);
            throw new ServerException(QUESTION_SET_ANSWER);
        }
    }

    public static QuestionDO materialConvert(QuestionRespVO question) {
        QuestionDO.QuestionDOBuilder questionDOBuilder = QuestionDO.builder()
                .id(question.getId())
                .textbookId(question.getTextbookId())
                .chapterId(question.getChapterId())
                .unitId(question.getUnitId())
                .typeId(question.getTypeId())
                .subject(question.getSubject())
                .materialAudio(question.getMaterialAudio())
                .materialImage(question.getMaterialImage())
                .materialContent(question.getMaterialContent())
                .options(question.getOptions())
                .questionNum(question.getQuestionNum())
                .status(question.getStatus())
                .isShow(question.getIsShow())
                .version(question.getVersion())
                .questionCode(question.getQuestionCode())
                .correctAnswerCount(question.getCorrectAnswerCount())
                .totalAnswerCount(question.getTotalAnswerCount());
        return questionDOBuilder.build();
    }
}