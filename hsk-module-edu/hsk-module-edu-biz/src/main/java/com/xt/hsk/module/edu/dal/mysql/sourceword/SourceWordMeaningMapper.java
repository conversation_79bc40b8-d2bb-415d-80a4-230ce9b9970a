package com.xt.hsk.module.edu.dal.mysql.sourceword;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordMeaningPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.sourceword.SourceWordMeaningDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 词语多释义表（冗余word） Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SourceWordMeaningMapper extends BaseMapperX<SourceWordMeaningDO> {

    default PageResult<SourceWordMeaningDO> selectPage(WordMeaningPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SourceWordMeaningDO>()
                .eqIfPresent(SourceWordMeaningDO::getWordId, reqVO.getWordId())
                .eqIfPresent(SourceWordMeaningDO::getWord, reqVO.getWord())
                );
    }


    ;
}