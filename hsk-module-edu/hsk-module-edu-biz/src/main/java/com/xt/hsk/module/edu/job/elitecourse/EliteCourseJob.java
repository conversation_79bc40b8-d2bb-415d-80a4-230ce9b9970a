package com.xt.hsk.module.edu.job.elitecourse;

import com.baomidou.lock.annotation.Lock4j;
import com.xt.hsk.framework.quartz.core.handler.JobHandler;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 精品课程 定时任务
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Component
public class EliteCourseJob implements JobHandler {

    @Resource
    private EliteCourseService eliteCourseService;

    @Override
    @Lock4j(name = "eliteCourseRelease")
    public String execute(String param) throws Exception {
        log.info("执行精品课程定时上架定时任务");
        // 执行任务
        Integer updateCount = eliteCourseService.releaseEliteCourse();
        // 返回结果 记录更新成功的数量
        return String.format("更新%s条课程数据", updateCount);
    }
}
