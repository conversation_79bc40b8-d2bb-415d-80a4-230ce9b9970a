package com.xt.hsk.module.edu.dal.dataobject.interactivecourse;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.AppBaseDO;
import com.xt.hsk.module.edu.enums.interactivecourse.AICorrectionStatusEnum;
import com.xt.hsk.module.edu.enums.interactivecourse.InteractiveCourseStatusEnum;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 互动课-用户学习记录 DO
 *
 * <AUTHOR>
 * @since 2025/06/11
 */
@TableName("edu_user_interactive_course_record")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserInteractiveCourseRecordDO extends AppBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 用户ID user表主键
     */
    private Long userId;
    /**
     * 互动课ID edu_interactive_course表主键
     */
    private Long courseId;
    /**
     * 互动课单元ID edu_interactive_course_unit表主键
     */
    private Long unitId;
    /**
     * 业务类型 1-视频观看记录 2-专项练习 3-真题练习
     */
    private Integer bizType;
    /**
     * 业务ID 对应其他记录表主键 如果是视频记录这里就是null
     */
    private Long bizId;
    /**
     * 是否是用户在课程下最新记录 1-是 0-否
     */
    private Integer isLatest;
    /**
     * 视频总时间长(单位秒)
     */
    private Integer videoDuration;
    /**
     * 用户观看总时长(单位秒)
     */
    private Integer viewingDuration;
    /**
     * 视频观看进度百分比 viewing_duration / video_duration * 100
     */
    private BigDecimal videoProgress;
    /**
     * 状态 1-进行中 2-已完成
     * @see InteractiveCourseStatusEnum
     */
    private Integer status;
    /**
     * AI批改状态 0-没有批改记录 1-有批改记录
     * @see AICorrectionStatusEnum
     */
    private Integer aiCorrectionStatus;
    /**
     * 正确率
     */
    private BigDecimal accuracy;

    /**
     * 资源版本号
     */
    private Integer resourceVersion;

    /**
     * 专项练习ID
     */
    private Long gameId;

    /**
     * 题目ID列表，逗号分隔 真题练习时使用。
     */
    private String questionIds;

    /**
     * 记录保存选项 1-不保存 2-保存
     * 如果选择不保存，下次进入时强制继续练习，不提示重新开始
     * @see com.xt.hsk.module.edu.enums.interactivecourse.RecordSaveOptionEnum
     */
    private Integer saveOption;
}
