package com.xt.hsk.module.edu.dal.mysql.elitecourse;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyRecordPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseStudyRecordDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 精品课程学习记录 Mapper
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Mapper
public interface EliteCourseStudyRecordMapper extends BaseMapperX<EliteCourseStudyRecordDO> {

    default PageResult<EliteCourseStudyRecordDO> selectPage(EliteCourseStudyRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<EliteCourseStudyRecordDO>()
                .eqIfPresent(EliteCourseStudyRecordDO::getUserId, reqVO.getUserId())
                .eqIfPresent(EliteCourseStudyRecordDO::getCourseId, reqVO.getCourseId())
                .eqIfPresent(EliteCourseStudyRecordDO::getCourseRegisterId, reqVO.getCourseRegisterId())
                .eqIfPresent(EliteCourseStudyRecordDO::getChapterId, reqVO.getChapterId())
                .eqIfPresent(EliteCourseStudyRecordDO::getClassHourId, reqVO.getClassHourId())
                .eqIfPresent(EliteCourseStudyRecordDO::getVideoId, reqVO.getVideoId())
                .betweenIfPresent(EliteCourseStudyRecordDO::getDate, reqVO.getDate())
                .betweenIfPresent(EliteCourseStudyRecordDO::getPlayBeginTime, reqVO.getPlayBeginTime())
                .betweenIfPresent(EliteCourseStudyRecordDO::getPlayEndTime, reqVO.getPlayEndTime())
                .eqIfPresent(EliteCourseStudyRecordDO::getPlayLength, reqVO.getPlayLength())
                .eqIfPresent(EliteCourseStudyRecordDO::getClientType, reqVO.getClientType())
                .eqIfPresent(EliteCourseStudyRecordDO::getCourseType, reqVO.getCourseType())
                .eqIfPresent(EliteCourseStudyRecordDO::getRecordType, reqVO.getRecordType())
                .eqIfPresent(EliteCourseStudyRecordDO::getUserIp, reqVO.getUserIp())
                .eqIfPresent(EliteCourseStudyRecordDO::getDeviceInfo, reqVO.getDeviceInfo())
                .eqIfPresent(EliteCourseStudyRecordDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(EliteCourseStudyRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(EliteCourseStudyRecordDO::getId));
    }

    List<EliteCourseStudyRecordDO> getUserMaxStudyRecord(List<Long> hourIds, long userId);
}