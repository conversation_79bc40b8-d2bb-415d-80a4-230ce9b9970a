package com.xt.hsk.module.edu.dal.dataobject.interactivecourse;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 互动课单元 DO
 *
 * <AUTHOR>
 */
@TableName("edu_interactive_course_unit")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InteractiveCourseUnitDO extends BaseDO {

    /**
     * 课程单元ID
     */
    @TableId
    private Long id;
    
    /**
     * 课程ID
     */
    private Long courseId;
    
    /**
     * 单元名称-中文
     */
    private String unitNameCn;

    /**
     * 单元名称-英文
     */
    private String unitNameEn;
    
    /**
     * 单元名称-其他
     */
    private String unitNameOt;
    
    /**
     * 展示状态 0-隐藏 1-显示
     */
    private Boolean displayStatus;
    
    /**
     * 排序序号
     */
    private Integer sort;
    
    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;
    
    /**
     * 题目来源 1-视频 2-专项练习 3-真题练习
     */
    private Integer questionSource;
    /**
     * 视频信息 ID
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long videoInfoId;

    /**
     * 封面URL
     */
    private String coverUrl;
    
    /**
     * 推荐学习时长(秒)
     */
    private Integer recommendedDuration;

    /**
     * 资源版本
     */
    private Integer resourceVersion;
}