package com.xt.hsk.module.edu.manager.exam.admin;

import cn.hutool.core.collection.CollUtil;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleEditReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleEditSubjectRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleQuestionTypeRespVO;
import com.xt.hsk.module.edu.convert.exam.ExamQuestionTypeConvert;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamPaperRuleDO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamQuestionTypeDO;
import com.xt.hsk.module.edu.service.exam.ExamPaperRuleDetailService;
import com.xt.hsk.module.edu.service.exam.ExamPaperRuleService;
import com.xt.hsk.module.edu.service.exam.ExamQuestionTypeService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 模考组卷规则明细 后台 Manager
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Slf4j
@Component
public class ExamPaperRuleDetailAdminManager {

    @Resource
    private ExamPaperRuleDetailService examPaperRuleDetailService;

    @Resource
    private ExamQuestionTypeService examQuestionTypeService;

    @Resource
    private ExamPaperRuleService examPaperRuleService;

    /**
     * 获取组卷规则编辑数据
     *
     * @param reqVO 请求参数
     * @return 组卷规则编辑响应数据
     */
    public ExamPaperRuleEditSubjectRespVO getExamPaperRuleForEdit(ExamPaperRuleEditReqVO reqVO) {
        // 获取题型列表
        List<ExamPaperRuleQuestionTypeRespVO> paperRuleQuestionTypeList = getPaperRuleQuestionTypeList(reqVO);
        
        // 如果没有题型数据，直接返回空结果
        if (CollUtil.isEmpty(paperRuleQuestionTypeList)) {
            return new ExamPaperRuleEditSubjectRespVO();
        }
        
        // 按科目分组并构建响应对象
        return buildSubjectResponse(paperRuleQuestionTypeList);
    }

    /**
     * 获取题型列表
     *
     * @param reqVO 请求参数
     * @return 题型列表
     */
    private List<ExamPaperRuleQuestionTypeRespVO> getPaperRuleQuestionTypeList(ExamPaperRuleEditReqVO reqVO) {
        Long paperRuleId = reqVO.getPaperRuleId();
        Integer hskLevel = reqVO.getHskLevel();

        if(paperRuleId == null){
            return createDefaultQuestionTypeList(hskLevel);
        }
        
        ExamPaperRuleDO paperRule = examPaperRuleService.getPaperRule(paperRuleId);
        if (paperRule == null) {
            return Collections.emptyList();
        }
        
        // 如果HSK级别相同，直接使用现有的规则
        if (Objects.equals(paperRule.getHskLevel(), hskLevel)) {
            return examPaperRuleDetailService.getPaperRuleQuestionType(paperRuleId);
        }
        
        // 如果HSK级别不同，根据新的级别创建默认题型列表
        return createDefaultQuestionTypeList(hskLevel);
    }

    /**
     * 创建默认题型列表
     *
     * @param hskLevel HSK级别
     * @return 默认题型列表
     */
    private List<ExamPaperRuleQuestionTypeRespVO> createDefaultQuestionTypeList(Integer hskLevel) {
        List<ExamQuestionTypeDO> examQuestionTypeList = examQuestionTypeService.lambdaQuery()
                .eq(ExamQuestionTypeDO::getHskLevel, hskLevel)
                .list();

        List<ExamPaperRuleQuestionTypeRespVO> respVoList = examQuestionTypeList.stream()
                .map(ExamQuestionTypeConvert.INSTANCE::doToQuestionTypeRespVO)
                .toList();

        // 设置题型名称
        examPaperRuleDetailService.setQuestionTypeNames(respVoList);

        return respVoList;
    }

    /**
     * 按科目分组并构建响应对象
     *
     * @param paperRuleQuestionTypeList 题型列表
     * @return 响应对象
     */
    private ExamPaperRuleEditSubjectRespVO buildSubjectResponse(List<ExamPaperRuleQuestionTypeRespVO> paperRuleQuestionTypeList) {
        ExamPaperRuleEditSubjectRespVO vo = new ExamPaperRuleEditSubjectRespVO();
        
        // 按科目分组
        Map<Integer, List<ExamPaperRuleQuestionTypeRespVO>> subjectMap = paperRuleQuestionTypeList.stream()
                .collect(Collectors.groupingBy(ExamPaperRuleQuestionTypeRespVO::getSubject));
        
        // 设置各科目的题型列表
        for (SubjectEnum subject : SubjectEnum.values()) {
            List<ExamPaperRuleQuestionTypeRespVO> subjectList = subjectMap.getOrDefault(subject.getCode(), Collections.emptyList());
            setSubjectList(vo, subject, subjectList);
        }
        
        return vo;
    }

    /**
     * 设置科目列表
     *
     * @param vo 响应对象
     * @param subject 科目枚举
     * @param subjectList 科目题型列表
     */
    private void setSubjectList(ExamPaperRuleEditSubjectRespVO vo, SubjectEnum subject, List<ExamPaperRuleQuestionTypeRespVO> subjectList) {
        switch (subject) {
            case LISTENING -> vo.setListeningSubjectList(subjectList);
            case READING -> vo.setReadingSubjectList(subjectList);
            case WRITING -> vo.setWritingSubjectList(subjectList);
            default -> {
                // 其他科目暂不处理
            }
        }
    }
}