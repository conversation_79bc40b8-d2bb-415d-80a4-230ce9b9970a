package com.xt.hsk.module.edu.controller.app.exam.vo;

import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.module.edu.enums.exam.ExamCorrectionStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import lombok.Data;

/**
 * 模考 app 分页 resp vo
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class ExamAppPageRespVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 模考ID
     */
    private Long examId;

    /**
     * HSK等级
     *
     * @see HskEnum
     */
    private Integer hskLevel;

    /**
     * 模考名称
     */
    private String name;

    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    private Integer type;

    /**
     * 模考封面图片URL
     */
    private String coverUrl;

    /**
     * 模考记录id
     */
    private Long examRecordId;

    /**
     * 批改状态 1进行中 2待批改 3已批改
     *
     * @see ExamCorrectionStatusEnum
     */
    private Integer correctionStatus;

    /**
     * 总得分
     */
    private Integer actualScore;

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 进度
     */
    private Integer progress;

    /**
     * 状态 1进行中 2已完成
     */
    private Integer status;
}