package com.xt.hsk.module.edu.enums;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 专项练习类型枚举
 *
 * <AUTHOR>
 * @since 2025/06/12
 */
@Getter
@AllArgsConstructor
public enum SpecialPracticeTypeEnum implements BasicEnum<Integer> {

    /**
     * 单词连连看
     */
    WORD_MATCHING(1, "单词连连看"),

    /**
     * 笔画书写
     */
    STROKE_WRITING(2, "笔画书写"),

    /**
     * 连词成句
     */
    SENTENCE_FORMING(3, "连词成句"),

    /**
     * 卡拉ok
     */
    KARAOKE(4, "卡拉ok");

    /**
     * 类型
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;
} 