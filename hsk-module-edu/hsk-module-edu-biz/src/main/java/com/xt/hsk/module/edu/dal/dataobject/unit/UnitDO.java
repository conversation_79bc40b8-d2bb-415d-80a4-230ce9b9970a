package com.xt.hsk.module.edu.dal.dataobject.unit;

import lombok.*;

import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;

/**
 * 单元 DO
 *
 * <AUTHOR>
 */
@TableName("edu_unit")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnitDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 所属章节
     */
    private Long chapterId;
    /**
     * 教材id
     */
    private Long textbookId;
    /**
     * 科目
     */
    private Integer subject;
    /**
     * 单元名称
     */
    private String unitNameCn;
    /**
     * 单元名称
     */
    private String unitNameEn;
    /**
     * 单元名称
     */
    private String unitNameOt;
    /**
     * 排序序号
     */
    private Integer sort;
    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;
//    /**
//     * 难度等级 1 简单 2中等 3 困难
//     */
//    private Integer difficultyLevel;

}