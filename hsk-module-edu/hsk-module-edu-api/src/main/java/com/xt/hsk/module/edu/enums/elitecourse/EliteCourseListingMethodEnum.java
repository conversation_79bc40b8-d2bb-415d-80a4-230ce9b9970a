package com.xt.hsk.module.edu.enums.elitecourse;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.Getter;

/**
 * 精品课上架方式枚举
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Getter
public enum EliteCourseListingMethodEnum implements BasicEnum<Integer> {
    /**
     * 上架方式枚举
     */
    IMMEDIATE(1, "立即上架"),
    SCHEDULED(2, "定时上架"),
    NOT_LISTED(3, "暂不上架"),
    ;

    private final Integer code;
    private final String desc;

    EliteCourseListingMethodEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据上架方式码获取对应的描述信息
     *
     * @param code 上架方式码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (EliteCourseListingMethodEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据上架方式码获取对应的枚举实例
     *
     * @param code 上架方式码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static EliteCourseListingMethodEnum getByCode(Integer code) {
        for (EliteCourseListingMethodEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
} 