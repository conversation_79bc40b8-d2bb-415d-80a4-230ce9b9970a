package com.xt.hsk.module.edu.enums;

import com.xt.hsk.framework.common.exception.ErrorCode;
import com.xt.hsk.framework.common.exception.enums.ServiceErrorCodeRange;

/**
 * 错误代码常量
 * 模块 edu 教育模块错误码区间 [1-024-001-000 ~ 1-030-000-000)]
 * 教育系统，互动课业务 使用 024 业务模块编码
 * 互动课模块，错误码从 001 开始
 * 字词库模块，错误码从 002 开始
 * 讲师模块，错误码从 003 开始
 * 精品课模块，错误码从 004 开始
 * 模考模块，错误码从 005 开始
 * @see ServiceErrorCodeRange yudao设计说明
 * <AUTHOR>
 * @since 2025/05/23
 */
public interface ErrorCodeConstants {

    /** 互动课模块错误代码 **/
    ErrorCode INTERACTIVE_COURSE_NOT_EXISTS = new ErrorCode(1_024_001_001, "互动课不存在");
    ErrorCode INTERACTIVE_COURSE_NOT_ON_SHELF = new ErrorCode(1_024_001_002, "互动课已下架");
    
    /** 互动课单元模块错误代码 **/
    ErrorCode INTERACTIVE_COURSE_UNIT_NOT_EXISTS = new ErrorCode(1_024_001_002, "互动课单元不存在");
    ErrorCode VIDEO_INFO_NOT_EXISTS = new ErrorCode(1_024_001_004, "视频信息不存在");
    ErrorCode COURSEWARE_NOT_EXISTS = new ErrorCode(1_024_001_005, "课件不存在");
    ErrorCode UNIT_RESOURCE_REL_NOT_EXISTS = new ErrorCode(1_024_001_006, "单元资源关联不存在");
    ErrorCode INVALID_REQUEST_PARAM_ERROR = new ErrorCode(1_024_001_007, "单元资源参数不合法");
    ErrorCode INTERNAL_SERVER_ERROR = new ErrorCode(1_024_001_008, "互动课单元视频参数不合法");
    ErrorCode INVALID_PRACTICE_PARAM_ERROR = new ErrorCode(1_024_001_009, "练习参数不合法");

    /**
     * 讲师模块错误代码
     **/
    ErrorCode TEACHER_NOT_EXISTS = new ErrorCode(1_024_003_001, "讲师不存在");
    ErrorCode TEACHER_COUNTRY_CODE_MOBILE_EXISTS = new ErrorCode(1_024_003_002, "手机号已存在");
    ErrorCode TEACHER_NICKNAME_EXISTS = new ErrorCode(1_024_003_003, "讲师名称已存在");

    /**
     * 精品课错误代码
     **/
    ErrorCode ELITE_COURSE_NOT_EXISTS = new ErrorCode(1_024_004_001, "精品课不存在",
        "Business.course_not_found");
    ErrorCode ELITE_COURSE_CATEGORY_NOT_EXISTS = new ErrorCode(1_024_004_002, "精品课分类不存在");
    ErrorCode ELITE_COURSE_CUSTOM_CLASS_HOUR_REQUIRED = new ErrorCode(1_024_004_003, "选择自定义课时时，自定义课时数不能为空");
    ErrorCode ELITE_COURSE_LISTING_TIME_REQUIRED = new ErrorCode(1_024_004_004, "选择定时上架时，上架时间不能为空");
    ErrorCode ELITE_COURSE_DEADLINE_REQUIRED = new ErrorCode(1_024_004_005, "选择按截止日期时，截止日期不能为空");
    ErrorCode ELITE_COURSE_EFFECTIVE_DAYS_REQUIRED = new ErrorCode(1_024_004_006, "选择按天数时，有效天数不能为空");
    ErrorCode ELITE_CHAPTER_HAS_CLASS_HOURS = new ErrorCode(1_024_004_007, "该章节下存在课时信息，无法删除");
    ErrorCode ELITE_CLASS_HOUR_VIDEO_URL_NOT_EMPTY = new ErrorCode(1_024_004_008, "请填写视频地址");
    ErrorCode ELITE_CHAPTER_NOT_EXISTS = new ErrorCode(1_024_004_009, "精品课章节不存在");
    ErrorCode ELITE_CLASS_HOUR_NOT_EXISTS = new ErrorCode(1_024_004_010, "精品课课时不存在");
    ErrorCode ELITE_COURSE_HAS_CHAPTERS = new ErrorCode(1_024_004_011, "课程下有章节信息，不能删除！");
    ErrorCode ELITE_COURSE_TEACHER_REQUIRED = new ErrorCode(1_024_004_012, "请选择课程讲师！");
    ErrorCode ELITE_COURSE_TEACHER_EXCEED_LIMIT = new ErrorCode(1_024_004_013, "讲师数超过上限");
    ErrorCode ELITE_COURSE_CATEGORY_HAS_COURSES = new ErrorCode(1_024_004_014, "分类下存在课程，不能删除！");
    ErrorCode ELITE_COURSE_SELLING_PRICE_CN_REQUIRED = new ErrorCode(1_024_004_014, "售卖价格(人民币)不能为空");
    ErrorCode ELITE_COURSE_ORIGINAL_PRICE_EN_REQUIRED = new ErrorCode(1_024_004_015, "售卖价格(美元)不能为空");
    ErrorCode ELITE_COURSE_ORIGINAL_PRICE_OT_REQUIRED = new ErrorCode(1_024_004_016, "售卖价格(越南盾)不能为空");
    ErrorCode ELITE_COURSE_NOT_ON_SHELF = new ErrorCode(1_024_004_017, "课程已下架",
        "Business.course_removed");

    /**
     * 模考模块错误代码
     **/
    ErrorCode EXAM_QUESTION_TYPE_NOT_EXISTS = new ErrorCode(1_024_005_001, "模考题型不存在");
    ErrorCode EXAM_PAPER_RULE_NOT_EXISTS = new ErrorCode(1_024_005_002, "模考组卷规则不存在");
    ErrorCode EXAM_RULE_ALREADY_EXISTS_CANNOT_ENABLE = new ErrorCode(1_024_005_003, "规则已存在，无法启用");
    ErrorCode EXAM_RULE_EXISTS_CANNOT_CREATE = new ErrorCode(1_024_005_004, "规则已存在，请勿重复创建");
    ErrorCode EXAM_QUESTION_TYPE_IN_USE_CANNOT_DELETE = new ErrorCode(1_024_005_005, "该题型已被应用于组卷规则中，无法删除");
    ErrorCode EXAM_PAPER_RULE_NO_VALID_QUESTION_TYPE = new ErrorCode(1_024_005_006, "组卷规则中没有找到有效的题型配置");
    ErrorCode EXAM_DETAIL_LIST_CANNOT_BE_EMPTY = new ErrorCode(1_024_005_007, "考试详情不能为空");
    ErrorCode EXAM_SUBJECT_NOT_EXISTS = new ErrorCode(1_024_005_008, "科目不存在:{}");
    ErrorCode EXAM_SUBJECT_NOT_FOUND_IN_RULE = new ErrorCode(1_024_005_009, "科目{}在组卷规则中未找到对应配置");
    ErrorCode EXAM_SUBJECT_UNIT_LIST_CANNOT_BE_EMPTY = new ErrorCode(1_024_005_010, "科目{}的单元列表不能为空");
    ErrorCode EXAM_UNIT_NOT_EXISTS = new ErrorCode(1_024_005_011, "单元不存在");
    ErrorCode EXAM_UNIT_NOT_FOUND_IN_RULE = new ErrorCode(1_024_005_012, "科目{}的单元{}在组卷规则中未找到对应配置");
    ErrorCode EXAM_UNIT_QUESTION_LIST_CANNOT_BE_EMPTY = new ErrorCode(1_024_005_013, "科目{}的单元{}的题目列表不能为空");
    ErrorCode EXAM_UNIT_QUESTION_COUNT_MISMATCH = new ErrorCode(1_024_005_014, "科目{}的单元{}的题目数量不匹配，要求数量：{}，实际数量：{}");
    ErrorCode EXAM_MISSING_REQUIRED_UNITS = new ErrorCode(1_024_005_015, "科目{}缺少必需的单元：{}");
    ErrorCode EXAM_DUPLICATE_SUBJECT = new ErrorCode(1_024_005_016, "存在重复的科目：{}");
    ErrorCode EXAM_DUPLICATE_UNIT = new ErrorCode(1_024_005_017, "科目{}存在重复的单元：{}");
    ErrorCode EXAM_NOT_EXISTS = new ErrorCode(1_024_005_018, "模考不存在");
    ErrorCode EXAM_PUBLISHED_CANNOT_UPDATE = new ErrorCode(1_024_005_019, "已发布的模考不允许修改");
    ErrorCode EXAM_OFFLINE_CANNOT_UPDATE = new ErrorCode(1_024_005_020, "已下架的模考不允许修改");
    ErrorCode EXAM_PUBLISHED_CANNOT_DELETE = new ErrorCode(1_024_005_021, "已发布的模考不允许删除");
    ErrorCode EXAM_QUESTION_TYPE_EXISTS_CANNOT_CREATE = new ErrorCode(1_024_005_022, "该条数据已存在，请勿重复创建");
    ErrorCode EXAM_QUESTION_DETAIL_NOT_FOUND = new ErrorCode(1_024_005_023, "题目详情未找到");
    ErrorCode EXAM_INVALID_PUBLISH_STATUS = new ErrorCode(1_024_005_024, "无效的发布状态");
    ErrorCode EXAM_NOT_EXISTS_CANNOT_PARTICIPATE = new ErrorCode(1_024_005_025, "试卷不存在，无法考试");
    ErrorCode EXAM_RECORD_NOT_EXISTS = new ErrorCode(1_024_005_026, "模考记录不存在");
    ErrorCode EXAM_RECORD_COMPLETED = new ErrorCode(1_024_005_027, "该模考记录已完成，请重新作答");
}
