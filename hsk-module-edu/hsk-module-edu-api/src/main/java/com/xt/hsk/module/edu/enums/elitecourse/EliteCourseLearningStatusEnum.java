package com.xt.hsk.module.edu.enums.elitecourse;

import lombok.Getter;

/**
 * 精品课程学习状态枚举
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Getter
public enum EliteCourseLearningStatusEnum {
    /**
     * 课程学习状态枚举
     */
    NORMAL(1, "正常"),
    EXPIRED(2, "已过期"),
    ;

    private final Integer code;
    private final String desc;

    EliteCourseLearningStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据精品课程学习状态码获取对应的描述信息
     *
     * @param code 精品课程学习状态码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (EliteCourseLearningStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据精品课程学习状态码获取对应的枚举实例
     *
     * @param code 精品课程学习状态码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static EliteCourseLearningStatusEnum getByCode(Integer code) {
        for (EliteCourseLearningStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

}
