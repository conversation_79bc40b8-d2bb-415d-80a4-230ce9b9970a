package com.xt.hsk.module.edu.api.interactivecourse.dto;

import java.util.List;

/**
 * 交互式课程 API
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
public interface InteractiveCourseApi {

    /**
     * 根据资源ID列表获取互动课基本信息列表
     *
     * @param resourceIdList 资源ID列表
     * @return 互动课基本信息列表
     */
    List<InteractiveCourseBaseInfoRespDTO> listByResourceIdList(List<Long> resourceIdList);

    /**
     * 根据互动课程名称获取资源ID列表
     *
     * @param courseName 资源 ID 列表
     * @return 资源ID列表
     */
    List<Long> listResourceIdByCourseName(String courseName);
}
