package com.xt.hsk.module.edu.api.dto;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 精品课程注册创建 DTO
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Data
public class EliteCourseRegisterCreateReqDTO {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 课程类型：1.普通课程 2.公开课
     */
    private Integer courseType;

    /**
     * 预约或购买时间
     */
    private LocalDateTime enrollmentTime;

    /**
     * 报名途径：1.线上购买 2.线下报名(后台)
     */
    private Integer registerType;

    /**
     * 购买课程订单号
     */
    private String orderNumber;

    /**
     * 订单详情ID
     */
    private Long orderDetailId;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 课程状态 1 正常 2 未开通 3 手动开启
     */
    private Integer courseRegisterStatus;
} 