package com.xt.hsk.module.edu.enums.interactivecourse;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 互动课状态枚举
 *
 * <AUTHOR>
 * @since 2025/07/10
 */
@Getter
@AllArgsConstructor
public enum InteractiveCourseStatusEnum implements BasicEnum<Integer>{
    /**
     * 进行中
     */
    IN_PROGRESS(1, "进行中"),
    /**
     * 已完成
     */
    COMPLETED(2, "已完成");

    private final Integer code;
    private final String desc;

}
