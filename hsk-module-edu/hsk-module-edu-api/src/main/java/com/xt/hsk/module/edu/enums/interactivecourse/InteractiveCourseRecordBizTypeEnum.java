package com.xt.hsk.module.edu.enums.interactivecourse;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 互动课程记录类型枚举
 *
 * <AUTHOR>
 * @since 2025/07/07
 */
@Getter
@AllArgsConstructor
public enum InteractiveCourseRecordBizTypeEnum implements BasicEnum<Integer> {
    /**
     * 视频观看记录
     */
    VIDEO_VIEWING_RECORD(1, "视频观看记录"),
    /**
     * 专项练习记录
     */
    SPECIAL_EXERCISE_RECORD(2, "专项练习记录"),
    /**
     * 真题练习记录
     */
    QUESTION_PRACTICE_RECORD(3, "真题练习记录"),
    ;


    private final Integer code;
    private final String desc;

}
