package com.xt.hsk.module.edu.enums.interactivecourse;

import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 互动课视频类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum VideoTypeEnum implements BasicEnum<Integer>, ArrayValuable<Integer> {

    /**
     * 视频带课件
     */
    COURSEWARE(1, "视频带课件"),
    /**
     * 视频带生词
     */
    VOCABULARY(2, "视频带生词");

    /**
     * 类型
     */
    private final Integer code;
    /**
     * 描述
     */
    private final String desc;

    private static final Integer[] ARRAYS = Arrays.stream(values()).map(VideoTypeEnum::getCode)
        .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}