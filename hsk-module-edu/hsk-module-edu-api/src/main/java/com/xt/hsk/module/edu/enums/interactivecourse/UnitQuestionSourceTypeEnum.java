package com.xt.hsk.module.edu.enums.interactivecourse;

import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 互动课单元类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum UnitQuestionSourceTypeEnum implements BasicEnum<Integer>, ArrayValuable<Integer> {

    /**
     * 视频
     */
    VIDEO(1, "本地视频"),
    /**
     * 专项练习
     */
    SPECIAL_PRACTICE(2, "专项练习"),
    /**
     * 真题练习
     */
    QUESTION(3, "真题练习");

    /**
     * 类型
     */
    private final Integer code;
    /**
     * 描述
     */
    private final String desc;

    private static final Integer[] ARRAYS = Arrays.stream(values())
        .map(UnitQuestionSourceTypeEnum::getCode)
        .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (UnitQuestionSourceTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }
}