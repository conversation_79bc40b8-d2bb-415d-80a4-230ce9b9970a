package com.xt.hsk.module.edu.event.interactivecourse;

import com.xt.hsk.module.edu.enums.interactivecourse.InteractiveCourseRecordBizTypeEnum;
import java.math.BigDecimal;
import lombok.Getter;

/**
 * 练习完成事件
 *
 * <AUTHOR>
 * @since 2025/07/10
 */
@Getter
public class PracticeCompletedEvent extends AbstractPracticeEvent {

    /**
     * 正确率
     */
    private final BigDecimal accuracy;
    /**
     * 是否有AI批改
     */
    private final Boolean hasAICorrection;

    /**
     * 创建练习完成事件
     *
     * @param source         事件源
     * @param userId         用户ID
     * @param unitId         单元ID
     * @param bizType        业务类型 {@link InteractiveCourseRecordBizTypeEnum}
     * @param accuracy       正确率
     * @param hasAICorrection 是否有AI批改
     */
    public PracticeCompletedEvent(Object source, Long userId, Long unitId,
        InteractiveCourseRecordBizTypeEnum bizType, BigDecimal accuracy, Boolean hasAICorrection, Long bizId) {
        super(source, userId, unitId, bizType, bizId);
        this.accuracy = accuracy;
        this.hasAICorrection = hasAICorrection;
    }

}
