package com.xt.hsk.module.edu.enums.interactivecourse;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 互动课单元图标枚举 保存配置表中的KEY值
 * 关联 ConfigDO 表
 * 提前准备好 洗数据的脚本：update edu_interactive_course_unit set cover_url = '?' WHERE cover_url = '1'
 * 在新增时通过判断直接设置url,因为在列表查询时判断 需要查多张表 效率较差。
 * <AUTHOR>
 * @since 2025/07/07
 */
@Getter
@AllArgsConstructor
public enum InteractiveCourseUnitIconUrlEnum implements BasicEnum<String> {
    /**
     * 笔画书写、单词连连看、卡拉OK、连词成句、视频、视频带课件、视频带生词、真题练习
     */
    STROKE_WRITING("url.icon_stroke_writing", "笔画书写"),
    WORD_MATCHING("url.icon_word_matching", "单词连连看"),
    KARAOKE("url.icon_karaoke", "卡拉OK"),
    SENTENCE_FORMATION("url.icon_sentence_formation", "连词成句"),
    VIDEO("url.icon_video", "视频"),
    VIDEO_WITH_COURSEWARE("url.icon_video_with_courseware", "视频带课件"),
    VIDEO_WITH_VOCABULARY("url.icon_video_with_vocabulary", "视频带生词"),
    REAL_EXERCISE("url.icon_real_exercise", "真题练习");

    private final String code;
    private final String desc;
}
