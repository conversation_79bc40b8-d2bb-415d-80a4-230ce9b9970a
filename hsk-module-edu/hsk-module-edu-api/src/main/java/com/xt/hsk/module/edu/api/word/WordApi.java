package com.xt.hsk.module.edu.api.word;

import com.xt.hsk.module.edu.api.word.dto.WordRespDTO;

import java.util.List;

/**
 * 汉语词典基础数据 API
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface WordApi {

    /**
     * 根据单词列表查询对应的单词信息
     *
     * @param wordList 单词列表
     * @return 单词响应DTO列表
     */
    List<WordRespDTO> listByWordList(List<String> wordList);

    /**
     * 根据字词名称和拼音查询字词信息
     *
     * @param word   字词名称
     * @param pinyin 拼音
     * @return 字词信息
     */
    WordRespDTO getWordByName(String word, String pinyin);

    /**
     * 根据单词ID列表查询对应的单词信息
     *
     * @param wordIdList 单词列表
     * @return 单词响应DTO列表
     */
    List<WordRespDTO> listByWordIdList(List<Long> wordIdList);

}