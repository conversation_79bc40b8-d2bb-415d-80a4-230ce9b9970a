package com.xt.hsk.module.edu.enums;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 语言枚举
 *
 * <AUTHOR>
 * @since 2025/07/15
 */
@Getter
@AllArgsConstructor
public enum LanguageEnum implements BasicEnum<String> {

    /**
     * 中文
     */
    LANGUAGE_CN("zh-<PERSON>", "中文"),
    /**
     * 英文
     */
    LANGUAGE_EN("en", "英文"),
    /**
     * 越南语
     */
    LANGUAGE_VI("vi", "越南语");

    private final String code;
    private final String desc;
}
