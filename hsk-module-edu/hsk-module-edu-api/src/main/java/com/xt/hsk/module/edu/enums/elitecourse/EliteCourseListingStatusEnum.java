package com.xt.hsk.module.edu.enums.elitecourse;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.Getter;

/**
 * 精品课上架状态枚举
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Getter
public enum EliteCourseListingStatusEnum implements BasicEnum<Integer> {
    /**
     * 上架状态枚举
     */
    LISTED(1, "上架"),
    UNLISTED(2, "下架"),
    PENDING(3, "待上架"),
    ;

    private final Integer code;
    private final String desc;

    EliteCourseListingStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据上架状态码获取对应的描述信息
     *
     * @param code 上架状态码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (EliteCourseListingStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据上架状态码获取对应的枚举实例
     *
     * @param code 上架状态码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static EliteCourseListingStatusEnum getByCode(Integer code) {
        for (EliteCourseListingStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
} 