package com.xt.hsk.module.edu.enums.elitecourse;

import lombok.Getter;

/**
 * 精品课课时复用状态枚举
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Getter
public enum EliteClassHourReuseStatusEnum {

    /**
     * 精品课课时复用状态枚举
     */
    NO_REUSE(0, "没有复用"),
    REUSED(1, "有复用"),
    BE_REUSED(2, "有被复用");

    private final Integer code;
    private final String desc;

    EliteClassHourReuseStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据课精品课课时复用状态码获取对应的描述信息
     *
     * @param code 精品课课时复用状态码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (EliteClassHourReuseStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据精品课课时复用状态码获取对应的枚举实例
     *
     * @param code 精品课课时复用状态码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static EliteClassHourReuseStatusEnum getByCode(Integer code) {
        for (EliteClassHourReuseStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}