package com.xt.hsk.module.edu.enums.interactivecourse;

import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 互动课视频比例枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum VideoAspectRatioEnum implements BasicEnum<Integer>, ArrayValuable<Integer> {

    /**
     * 比率 9 16
     */
    RATIO_9_16(1, "9:16"),
    /**
     * 比率 16 9
     */
    RATIO_16_9(2, "16:9");

    /**
     * 类型
     */
    private final Integer code;
    /**
     * 描述
     */
    private final String desc;

    private static final Integer[] ARRAYS = Arrays.stream(values())
        .map(VideoAspectRatioEnum::getCode)
        .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }
} 