package com.xt.hsk.module.edu.event.interactivecourse;

import com.xt.hsk.module.edu.enums.interactivecourse.InteractiveCourseRecordBizTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 互动课关联练习事件基础类
 *
 * <AUTHOR>
 * @since 2025/07/10
 */
@Getter
public abstract class AbstractPracticeEvent extends ApplicationEvent {

    /**
     * 用户 ID
     */
    @NotNull(message = "用户ID不能为空")
    private final Long userId;
    /**
     * 课程单元 ID
     */
    @NotNull(message = "课程单元ID不能为空")
    private final Long unitId;
    /**
     * 业务类型
     */
    @NotNull(message = "业务类型不能为空")
    private final InteractiveCourseRecordBizTypeEnum bizType;

    /**
     * 业务ID
     */
    @NotNull(message = "业务ID不能为空")
    private final Long bizId;

    /**
     * 构造器
     *
     * @param source   源
     * @param userId   用户 ID
     * @param unitId   商品 ID
     * @param bizType  业务类型
     * @param bizId    业务 ID
     */
    protected AbstractPracticeEvent(Object source, Long userId, Long unitId, InteractiveCourseRecordBizTypeEnum bizType, Long bizId) {
        super(source);
        this.userId = userId;
        this.unitId = unitId;
        this.bizType = bizType;
        this.bizId = bizId;
    }
}
