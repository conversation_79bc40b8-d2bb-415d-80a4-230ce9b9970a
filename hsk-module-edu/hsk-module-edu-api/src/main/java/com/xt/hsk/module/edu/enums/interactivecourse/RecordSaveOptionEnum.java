package com.xt.hsk.module.edu.enums.interactivecourse;

import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 互动课记录保存选项枚举
 *
 * <AUTHOR>
 * @since 2025/07/17
 */
@Getter
@AllArgsConstructor
public enum RecordSaveOptionEnum implements BasicEnum<Integer>, ArrayValuable<Integer> {

    /**
     * 不保存 - 下次进入时强制继续练习，不提示重新开始
     */
    NOT_SAVE(1, "不保存"),
    
    /**
     * 保存 - 下次进入时可以选择重新开始或继续练习
     */
    SAVE(2, "保存");

    /**
     * 选项值
     */
    private final Integer code;
    
    /**
     * 选项描述
     */
    private final String desc;

    private static final Integer[] ARRAYS = Arrays.stream(values())
        .map(RecordSaveOptionEnum::getCode)
        .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

}
