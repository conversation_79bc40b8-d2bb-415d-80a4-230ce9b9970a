package com.xt.hsk.module.edu.api.dto;

import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseTypeEnum;
import com.xt.hsk.module.edu.enums.elitecourse.LearningValidityPeriodEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 精品课程有效期 DTO
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Data
public class EliteCourseValidityPeriodReqDTO {

    /**
     * 学习有效期 1：长期有效 2：按截止日期 3：按天数
     *
     * @see LearningValidityPeriodEnum
     */
    private Integer learningValidityPeriod;
    /**
     * 截至日期
     */
    private LocalDateTime deadline;
    /**
     * 有效天数
     */
    private Integer effectiveDays;
    /**
     * 类型 1.普通课程 2.公开课
     *
     * @see EliteCourseTypeEnum
     */
    private Integer type;
}
