package com.xt.hsk.module.edu.enums.elitecourse;

import lombok.Getter;

/**
 * 课程购买类型 enum
 *
 * <AUTHOR>
 * @since 2024-10-20
 */
@Getter
public enum EliteCourseRegisterTypeEnum {
    /**
     * 线上购买
     */
    ONLINE(1, "线上"),
    /**
     * 线下报名
     */
    OFFLINE(2, "线下"),
    ;

    private final Integer code;
    private final String desc;

    EliteCourseRegisterTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据课程购买类型码获取对应的描述信息
     *
     * @param code 课程购买类型码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (EliteCourseRegisterTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据课程购买类型码获取对应的枚举实例
     *
     * @param code 课程购买类型码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static EliteCourseRegisterTypeEnum getByCode(Integer code) {
        for (EliteCourseRegisterTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
