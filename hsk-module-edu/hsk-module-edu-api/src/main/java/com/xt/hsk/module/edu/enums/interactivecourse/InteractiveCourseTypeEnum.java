package com.xt.hsk.module.edu.enums.interactivecourse;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 互动课类型枚举
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
@Getter
@AllArgsConstructor
public enum InteractiveCourseTypeEnum implements BasicEnum<Integer>, VO, ArrayValuable<Integer> {
    /**
     * 一期只有普通课程
     */
    INTERACTIVE_COURSE(1, "普通课程"),
    ;
    private final Integer code;
    private final String desc;

    public static final Integer[] ARRAYS = Arrays.stream(values())
        .map(InteractiveCourseTypeEnum::getCode)
        .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}
