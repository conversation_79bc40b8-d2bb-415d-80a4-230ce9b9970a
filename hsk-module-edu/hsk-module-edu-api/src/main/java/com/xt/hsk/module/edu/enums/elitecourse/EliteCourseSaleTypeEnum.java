package com.xt.hsk.module.edu.enums.elitecourse;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 精品课售卖类型枚举
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Getter
@AllArgsConstructor
public enum EliteCourseSaleTypeEnum implements BasicEnum<Integer> {
    /**
     * 精品课售卖类型枚举
     */
    PAID(1, "付费"),
    FREE(2, "免费"),
    ;

    private final Integer code;
    private final String desc;

    /**
     * 根据售卖类型码获取对应的描述信息
     *
     * @param code 售卖类型码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (EliteCourseSaleTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据售卖类型码获取对应的枚举实例
     *
     * @param code 售卖类型码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static EliteCourseSaleTypeEnum getByCode(Integer code) {
        for (EliteCourseSaleTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
} 