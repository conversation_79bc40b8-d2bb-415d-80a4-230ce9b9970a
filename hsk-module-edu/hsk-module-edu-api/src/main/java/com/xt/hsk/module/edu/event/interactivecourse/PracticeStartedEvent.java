package com.xt.hsk.module.edu.event.interactivecourse;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;

import com.xt.hsk.module.edu.enums.ErrorCodeConstants;
import com.xt.hsk.module.edu.enums.interactivecourse.InteractiveCourseRecordBizTypeEnum;
import java.util.List;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 练习开始事件
 *
 * <AUTHOR>
 * @since 2025/07/10
 */
@Slf4j
@Getter
public class PracticeStartedEvent extends AbstractPracticeEvent {


    /**
     * 专项练习 ID 只有来源是专项练习时才有效
     */
    private final Long gameId;

    /**
     * 题目 ID 列表 只有来源是真题练习时才有效
     */
    private final List<Long> questionIds;

    /**
     * 创建练习开始事件
     *
     * @param source   事件源
     * @param userId   用户ID
     * @param unitId   单元ID
     * @param bizType  业务类型 {@link InteractiveCourseRecordBizTypeEnum}
     */
    public PracticeStartedEvent(Object source,
        Long userId,
        Long unitId,
        InteractiveCourseRecordBizTypeEnum bizType,
        Long bizId,
        Long gameId,
        List<Long> questionIds) {

        super(source, userId, unitId, bizType, bizId);

        if (InteractiveCourseRecordBizTypeEnum.SPECIAL_EXERCISE_RECORD == bizType) {
            if (gameId == null) {
                log.error(
                    "互动课关联专项练习时,必填参数为空,userId = {},unitId = {},bizType = {},bizId = {},gameId = {},questionIds = {}",
                    userId,
                    unitId, bizType, bizId, gameId, questionIds);
                throw exception(ErrorCodeConstants.INVALID_PRACTICE_PARAM_ERROR, "专项练习 ID 为空");
            }
            this.gameId = gameId;
        } else {
            this.gameId = null;
        }

        if (InteractiveCourseRecordBizTypeEnum.QUESTION_PRACTICE_RECORD == bizType) {
            if (questionIds == null) {
                log.error(
                    "互动课关联真题练习时,必填参数为空,userId = {},unitId = {},bizType = {},bizId = {},gameId = {},questionIds = {}",
                    userId,
                    unitId, bizType, bizId, gameId, questionIds);
                throw exception(ErrorCodeConstants.INVALID_PRACTICE_PARAM_ERROR, "题目 ID 列表 为空");
            }
            this.questionIds = questionIds;
        } else {
            this.questionIds = null;
        }
    }
}
