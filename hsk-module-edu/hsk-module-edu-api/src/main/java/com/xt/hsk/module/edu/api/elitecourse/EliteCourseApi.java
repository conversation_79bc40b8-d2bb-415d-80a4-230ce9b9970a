package com.xt.hsk.module.edu.api.elitecourse;

import com.xt.hsk.module.edu.api.dto.EliteCourseRespDTO;
import com.xt.hsk.module.edu.api.dto.EliteCourseValidityPeriodReqDTO;

import java.util.List;
import java.util.Map;

/**
 * 精品课程 API
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
public interface EliteCourseApi {

    /**
     * 根据精品课ID查询精品课
     *
     * @param id 精品课ID
     * @return 精品课信息
     */
    EliteCourseRespDTO getById(Long id);

    /**
     * 根据精品课IDList查询精品课
     */
    List<EliteCourseRespDTO> getListByIds(List<Long> ids);

    /**
     * 根据学校有效期获取课程id列表
     */
    List<Long> listCourseIdByValidityPeriod(EliteCourseValidityPeriodReqDTO reqDTO);

    /**
     * 通过ID列表获取有效期
     *
     * @param ids ID列表
     * @return 有效期map
     */
    Map<Long, String> getPeriodValidityByIds(List<Long> ids);
}
