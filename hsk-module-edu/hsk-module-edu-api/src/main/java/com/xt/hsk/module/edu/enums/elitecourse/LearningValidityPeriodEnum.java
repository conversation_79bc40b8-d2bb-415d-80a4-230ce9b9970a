package com.xt.hsk.module.edu.enums.elitecourse;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.Getter;

/**
 * 学习有效期枚举
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Getter
public enum LearningValidityPeriodEnum implements BasicEnum<Integer> {
    /**
     * 学习有效期枚举
     */
    PERMANENT(1, "长期有效"),
    BY_DEADLINE(2, "按截止日期"),
    BY_DAYS(3, "按天数"),
    ;

    private final Integer code;
    private final String desc;

    LearningValidityPeriodEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据学习有效期码获取对应的描述信息
     *
     * @param code 学习有效期码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (LearningValidityPeriodEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据学习有效期码获取对应的枚举实例
     *
     * @param code 学习有效期码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static LearningValidityPeriodEnum getByCode(Integer code) {
        for (LearningValidityPeriodEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
} 