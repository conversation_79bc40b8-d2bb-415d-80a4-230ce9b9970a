package com.xt.hsk.module.edu.api.elitecourse;

import com.xt.hsk.module.edu.api.dto.EliteCourseRegisterCreateReqDTO;
import java.util.List;

/**
 * 精品课程注册 API
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
public interface EliteCourseRegisterApi {

    /**
     * 批量创建课程注册记录
     *
     * @param reqList 课程注册请求列表
     * @return 是否成功
     */
    boolean batchRegister(List<EliteCourseRegisterCreateReqDTO> reqList);

    /**
     * 获取用户课程注册总数
     */
    Long getUserCourseRegisterCount(Long userId);
} 