package com.xt.hsk.module.edu.enums.interactivecourse;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 互动课 ai批改 状态枚举
 *
 * <AUTHOR>
 * @since 2025/07/10
 */
@Getter
@AllArgsConstructor
public enum AICorrectionStatusEnum implements BasicEnum<Integer> {
    /**
     * AI批改状态枚举
     */
    NO_CORRECTION(0, "没有批改记录"),
    HAS_CORRECTION(1, "有批改记录");

    private final Integer code;
    private final String desc;
}