package com.xt.hsk.module.edu.enums.interactivecourse;

import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 互动课单元资源类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum UnitResourceTypeEnum implements BasicEnum<Integer>, ArrayValuable<Integer> {

    /**
     * 课件
     */
    COURSEWARE(1, "课件"),
    /**
     * 专项练习
     */
    SPECIAL_PRACTICE(2, "专项练习"),
    /**
     * 真题练习
     */
    QUESTION(3, "真题练习"),
    /**
     * 生词
     */
    VOCABULARY(4, "生词");

    /**
     * 类型
     */
    private final Integer code;
    /**
     * 描述
     */
    private final String desc;

    private static final Integer[] ARRAYS = Arrays.stream(values())
        .map(UnitResourceTypeEnum::getCode)
        .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

} 