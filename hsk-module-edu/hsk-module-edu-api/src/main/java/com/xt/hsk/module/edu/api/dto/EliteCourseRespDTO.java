package com.xt.hsk.module.edu.api.dto;

import com.xt.hsk.module.edu.enums.elitecourse.ClassHourNumberStatusEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseListingMethodEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseListingStatusEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseTypeEnum;
import com.xt.hsk.module.edu.enums.elitecourse.LearningValidityPeriodEnum;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 精品课程 DTO
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Data
public class EliteCourseRespDTO {

    /**
     * 精品课ID
     */
    private Long courseId;
    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;
    /**
     * 一级分类id
     */
    private Long primaryCategoryId;
    /**
     * 二级分类id
     */
    private Long secondaryCategoryId;
    /**
     * 课程名称-中文
     */
    private String courseNameCn;
    /**
     * 课程名称-英文
     */
    private String courseNameEn;
    /**
     * 课程名称-其他
     */
    private String courseNameOt;
    /**
     * 课程封面大图URL
     */
    private String coverUrlLarge;
    /**
     * 课程封面小图URL
     */
    private String coverUrlSmall;
    /**
     * 课时数状态 1：课程大纲课时数 2：自定义课时
     *
     * @see ClassHourNumberStatusEnum
     */
    private Integer classHourNumberStatus;
    /**
     * 自定义课时数
     */
    private Integer customClassHourNumber;
    /**
     * 划线价格(人民币)
     */
    private BigDecimal originalPriceCn;
    /**
     * 售卖价格(人民币)
     */
    private BigDecimal sellingPriceCn;
    /**
     * 划线价格(美元)
     */
    private BigDecimal originalPriceEn;
    /**
     * 售卖价格(美元)
     */
    private BigDecimal sellingPriceEn;
    /**
     * 划线价格(越南盾)
     */
    private BigDecimal originalPriceOt;
    /**
     * 售卖价格(越南盾)
     */
    private BigDecimal sellingPriceOt;
    /**
     * 课程详情内容
     */
    private String courseDetail;
    /**
     * 上架方式 1：立即上架 2：定时上架 3：暂不上架
     *
     * @see EliteCourseListingMethodEnum
     */
    private Integer listingMethod;
    /**
     * 上架状态 1：上架 2：下架 3：待上架
     * @see EliteCourseListingStatusEnum
     */
    private Integer listingStatus;
    /**
     * 最后一次上架时间
     */
    private LocalDateTime listingTime;
    /**
     * 销售基数
     */
    private Integer salesBase;
    /**
     * 学习有效期 1：长期有效 2：按截止日期 3：按天数
     * @see LearningValidityPeriodEnum
     */
    private Integer learningValidityPeriod;
    /**
     * 截至日期
     */
    private LocalDateTime deadline;
    /**
     * 有效天数
     */
    private Integer effectiveDays;
    /**
     * 生效模式
     */
    private Integer effectModel;
    /**
     * 排序序号
     */
    private Integer sort;
    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;
    /**
     * 报名人数
     */
    private Integer enrollmentCount;
    /**
     * 类型 1.普通课程 2.公开课
     * @see EliteCourseTypeEnum
     */
    private Integer type;
}
