package com.xt.hsk.module.edu.enums.elitecourse;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.Getter;

/**
 * 精品课课时类型枚举
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Getter
public enum EliteClassHourTypeEnum implements BasicEnum<Integer> {
    /**
     * 精品课课时类型枚举
     */
    LIVE(1, "直播课"),
    RECORDED(2, "录播课");
    ;

    private final Integer code;
    private final String desc;

    EliteClassHourTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据课精品课课时类型码获取对应的描述信息
     *
     * @param code 精品课课时类型码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (EliteClassHourTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据精品课课时类型码获取对应的枚举实例
     *
     * @param code 精品课课时类型码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static EliteClassHourTypeEnum getByCode(Integer code) {
        for (EliteClassHourTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
} 