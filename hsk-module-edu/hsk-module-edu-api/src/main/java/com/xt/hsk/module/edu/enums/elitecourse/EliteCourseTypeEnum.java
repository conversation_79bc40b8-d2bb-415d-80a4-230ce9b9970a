package com.xt.hsk.module.edu.enums.elitecourse;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.Getter;

/**
 * 精品课类型枚举
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Getter
public enum EliteCourseTypeEnum implements BasicEnum<Integer> {
    /**
     * 精品课类型枚举
     */
    REGULAR_COURSE(1, "精品课"),
    PUBLIC_COURSE(2, "公开课"),
    ;

    private final Integer code;
    private final String desc;

    EliteCourseTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据课程类型码获取对应的描述信息
     *
     * @param code 课程类型码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (EliteCourseTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据课程类型码获取对应的枚举实例
     *
     * @param code 课程类型码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static EliteCourseTypeEnum getByCode(Integer code) {
        for (EliteCourseTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
} 