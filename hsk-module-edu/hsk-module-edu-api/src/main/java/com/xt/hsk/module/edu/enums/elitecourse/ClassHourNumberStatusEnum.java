package com.xt.hsk.module.edu.enums.elitecourse;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.Getter;

/**
 * 精品课课时数状态枚举
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Getter
public enum ClassHourNumberStatusEnum implements BasicEnum<Integer> {
    /**
     * 课时数状态枚举
     */
    OUTLINE_HOURS(1, "课程大纲课时数"),
    CUSTOM_HOURS(2, "自定义课时"),
    ;

    private final Integer code;
    private final String desc;

    ClassHourNumberStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据状态码获取对应的描述信息
     *
     * @param code 状态码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (ClassHourNumberStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取对应的枚举实例
     *
     * @param code 状态码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static ClassHourNumberStatusEnum getByCode(Integer code) {
        for (ClassHourNumberStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
} 