package com.xt.hsk.module.user.enums;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 学习汉语目的枚举
 *
 * <AUTHOR>
 * @since 2025/06/11
 */
@Getter
@AllArgsConstructor
public enum LearningPurposeEnum implements BasicEnum<Integer> {
    /**
     * app用户用
     */
    OTHER(0, "其他"),
    STUDY_ABROAD(1, "留学"),
    TRAVEL(2, "旅游"),
    CAREER_DEVELOPMENT(3, "职业发展"),
    PERSONAL_INTEREST(4, "个人兴趣");

    private final Integer code;
    private final String desc;
}
