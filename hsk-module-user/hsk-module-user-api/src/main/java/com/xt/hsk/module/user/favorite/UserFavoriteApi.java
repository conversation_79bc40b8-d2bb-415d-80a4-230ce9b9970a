package com.xt.hsk.module.user.favorite;

import com.xt.hsk.module.user.favorite.dto.UserFavoriteTargetDto;

import java.util.List;
import java.util.Map;

/**
 * 用户收藏 API 接口
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
public interface UserFavoriteApi {

    /**
     * 检查用户是否收藏了指定的目标
     *
     * @param userId       用户ID
     * @param targetId     目标ID
     * @param favoriteType 收藏类型
     * @return 是否已收藏
     */
    Boolean checkUserFavoriteStatus(Long userId, Long targetId, Integer favoriteType);

    /**
     * 批量收藏真题题目
     *
     * @param userId 用户ID
     * @param favoriteSource 收藏类型
     * @param targetList 目标ID列表
     * @param isWrongQuestion 是否是错题
     * @return
     */
    Boolean addRealQuestionBatch(Long userId, Integer favoriteSource, List<UserFavoriteTargetDto> targetList, Boolean isWrongQuestion);

    /**
     * 根据目标ID列表查询收藏状态
     *
     * @param userId       用户ID
     * @param targetIdList 目标ID列表
     * @param favoriteType 收藏类型
     * @return map<目标ID, 收藏状态>
     */
    Map<Long, Boolean> selectStatus(Long userId, List<Long> targetIdList, Integer favoriteType);
}