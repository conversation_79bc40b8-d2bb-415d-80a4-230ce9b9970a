package com.xt.hsk.module.user.api.dto;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * App用户信息 Response DTO
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppUserRespDTO implements java.io.Serializable {

    /**
     * 用户编号
     */
    private Long id;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户状态
     */
    private Integer status;

    /**
     * 手机国家区号
     */
    private String countryCode;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 用户注册来源
     */
    private Integer userSource;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 当前HSK等级（1-6）
     */
    private Integer currentHskLevel;

}