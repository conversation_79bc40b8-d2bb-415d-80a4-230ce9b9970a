package com.xt.hsk.module.user.enums;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 收藏类型枚举
 *
 * <AUTHOR>
 * @since 2025-01-XX
 */
@Getter
@AllArgsConstructor
public enum FavoriteTypeEnum implements BasicEnum<Integer>, VO, ArrayValuable<Integer> {

    /**
     * 字词
     */
    VOCABULARY(1, "字词"),

    /**
     * 真题题目
     */
    REAL_QUESTION(2, "真题题目"),

    /**
     * 小游戏题目
     */
    MINI_GAME_QUESTION(3, "小游戏题目"),
    ;

    private static final Integer[] ARRAYS = Arrays.stream(values())
        .map(FavoriteTypeEnum::getCode)
        .toArray(Integer[]::new);

    private final Integer code;
    private final String desc;

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

}
