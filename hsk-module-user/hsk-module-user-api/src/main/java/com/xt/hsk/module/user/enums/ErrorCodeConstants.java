package com.xt.hsk.module.user.enums;


import com.xt.hsk.framework.common.exception.ErrorCode;
import com.xt.hsk.framework.common.exception.enums.ServiceErrorCodeRange;

/**
 * 错误代码常量 模块 user 用户模块错误码区间 [1-025-000-000 ~ 1-026-000-000) 用户系统，用户管理业务 使用 001 业务模块编码 用户模块，错误码从 001
 * 开始
 *
 * @see ServiceErrorCodeRange yudao设计说明
 * <AUTHOR>
 * @since 2025/01/27
 */
public interface ErrorCodeConstants {

    /**
     * 用户模块错误代码
     **/
    ErrorCode USER_NOT_EXISTS = new ErrorCode(1_001_001_001, "用户不存在", "Business.user_not_exist");
    ErrorCode USER_PHONE_EXISTS = new ErrorCode(1_025_001_002, "该手机号已注册", "Business.user_phone_exists");
    ErrorCode USER_PASSWORD_ERROR = new ErrorCode(1_025_001_004, "密码错误", "Business.user_password_error");
    ErrorCode USER_DISABLED = new ErrorCode(1_025_001_005, "您的账号已被禁用","Business.user_disabled");
    ErrorCode USER_PHONE_FORMAT_ERROR = new ErrorCode(1_025_001_006, "手机号格式错误");
    ErrorCode USER_COUNTRY_CODE_ERROR = new ErrorCode(1_025_001_007, "国家区号错误", "Business.country_code_error");
    ErrorCode USER_PASSWORD_ERROR_EXCEED_MAX_COUNT = new ErrorCode(1_025_001_008,
        "密码错误次数过多","Business.password_error_too_many_times");
    ErrorCode USER_PASSWORD_CONFIRM_NOT_MATCH = new ErrorCode(1_025_001_009, "新密码与确认密码不一致");
    ErrorCode USER_PASSWORD_SAME_AS_OLD = new ErrorCode(1_025_001_010, "新密码不能与原密码相同");

    /**
     * 用户反馈模块错误代码
     */
    ErrorCode USER_FEEDBACK_DAILY_LIMIT_REACHED = new ErrorCode(1_025_002_001,
        "当日反馈次数已达上限，请明天再试");
    ErrorCode USER_FEEDBACK_MOBILE_CODE_REQUIRED = new ErrorCode(1_025_002_002,
        "联系方式为手机号时，区号不能为空");

    /**
     * 用户收藏模块错误代码
     */
    ErrorCode USER_FAVORITE_ALREADY_EXISTS = new ErrorCode(1_025_003_001, "该项目已收藏");
    ErrorCode USER_FAVORITE_NOT_EXISTS = new ErrorCode(1_025_003_002, "该项目未收藏");
    ErrorCode USER_FAVORITE_INVALID_TYPE = new ErrorCode(1_025_003_003, "收藏类型无效");
    ErrorCode USER_FAVORITE_INVALID_PARAMS = new ErrorCode(1_025_003_004, "收藏参数无效");

    /**
     * 修改手机号模块错误代码
     */
    ErrorCode CHANGE_MOBILE_SESSION_EXPIRED = new ErrorCode(1_025_004_001,
        "修改手机号会话已过期，请重新开始", "Business.change_mobile_session_expired");
    ErrorCode CHANGE_MOBILE_INVALID_STATE = new ErrorCode(1_025_004_002,
        "操作状态异常，请按正确流程操作", "Business.change_mobile_invalid_state");
    ErrorCode CHANGE_MOBILE_UNAUTHORIZED_ACCESS = new ErrorCode(1_025_004_003,
        "未授权的访问，请按正确流程操作", "Business.unauthorized_access");
    ErrorCode CHANGE_MOBILE_SECURITY_VIOLATION = new ErrorCode(1_025_004_004,
        "安全验证失败", "Business.security_violation");
    ErrorCode CHANGE_MOBILE_INCOMPLETE_VERIFICATION = new ErrorCode(1_025_004_005,
        "验证流程不完整", "Business.incomplete_verification");
    ErrorCode CHANGE_MOBILE_SECURITY_TOKEN_INVALID = new ErrorCode(1_025_004_006,
        "安全令牌无效", "Business.security_token_invalid");
    ErrorCode SMS_DAILY_LIMIT_EXCEEDED = new ErrorCode(1_025_004_007,
        "今日短信发送次数已达上限，请明天再试", "Business.sms_daily_limit_exceeded");
    ErrorCode SMS_HOURLY_LIMIT_EXCEEDED = new ErrorCode(1_025_004_008,
        "短信发送过于频繁，请1小时后再试", "Business.sms_hourly_limit_exceeded");
    ErrorCode CHANGE_MOBILE_SESSION_TIMEOUT = new ErrorCode(1_025_004_009,
        "操作超时，请重新开始", "Business.change_mobile_timeout");
    ErrorCode CHANGE_MOBILE_OLD_CODE_EXPIRED = new ErrorCode(1_025_004_010,
        "旧手机号验证码已过期，请重新验证", "Business.old_code_expired");
    ErrorCode CHANGE_MOBILE_NEW_CODE_EXPIRED = new ErrorCode(1_025_004_011,
        "新手机号验证码已过期，请重新获取", "Business.new_code_expired");
    ErrorCode CHANGE_MOBILE_VERIFICATION_EXPIRED = new ErrorCode(1_025_004_012,
        "验证已过期，请重新开始流程", "Business.verification_expired");
    ErrorCode CHANGE_MOBILE_TOO_MANY_ERRORS = new ErrorCode(1_025_004_013,
        "验证码错误次数过多，请重新开始", "Business.too_many_errors");
    ErrorCode CHANGE_MOBILE_PARAMS_MISMATCH = new ErrorCode(1_025_004_014,
        "手机号参数不一致，请重新操作", "Business.params_mismatch");

}