package com.xt.hsk.module.user.enums;

import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户反馈联系类型枚举
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Getter
@AllArgsConstructor
public enum ContactInfoTypeEnum implements BasicEnum<Integer>, ArrayValuable<Integer> {

    /**
     * whchat
     */
    WECHAT(1, "微信"),
    /**
     * e- mail
     */
    EMAIL(2, "邮箱"),
    /**
     * zalo
     */
    ZALO(3, "zalo"),
    /**
     * phone
     */
    PHONE(4, "手机"),
    /**
     * whatapp
     */
    WHATSAPP(5, "WhatsApp"),
    /**
     * facebook
     */
    FACEBOOK(6, "Facebook"),

    ;
    private final Integer code;
    private final String desc;

    private static final Integer[] ARRAYS = Arrays.stream(values())
        .map(ContactInfoTypeEnum::getCode)
        .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}
