package com.xt.hsk.module.user.annotation;


import com.xt.hsk.module.user.enums.UserOperateTypeEnum;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用户操作日志注解
 * <p>
 * 标记需要记录用户操作日志的方法。使用AOP拦截带有此注解的方法，自动记录用户操作日志。
 * 可用于用户注册、密码修改、状态变更等关键操作的日志记录。
 * <p>
 * 使用示例:
 * <pre>
 * {@code
 * @UserOperateLog(operateType = UserOperateTypeEnum.CHANGE_PASSWORD, 
 *                 userIdSpEL = "#userId",
 *                 remark = "用户修改密码",
 *                 beforeStatusSpEL = "#oldPasswordChanged ? 1 : 0",
 *                 afterStatusSpEL = "1")
 * public void updateUserPassword(Long userId, String newPassword, boolean oldPasswordChanged) {
 *     // 修改密码逻辑
 * }
 * }
 * </pre>
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface UserOperateLog {

    /**
     * 操作类型
     * <p>
     * 定义当前操作的类型，如创建用户、修改密码、状态变更等
     */
    UserOperateTypeEnum operateType();

    /**
     * 用户ID的SpEL表达式
     * <p>
     * 用于从方法参数中获取用户ID，支持SpEL表达式
     * 例如: "#userId", "#user.id", "#reqVO.userId"
     */
    String userIdSpEL();

    /**
     * 操作备注
     * <p>
     * 描述本次操作的具体内容，支持SpEL表达式
     * 例如: "用户注册", "'用户' + #userId + '修改密码'"
     */
    String remark() default "";

    /**
     * 操作前状态的SpEL表达式
     * <p>
     * 用于获取操作前的状态值，支持SpEL表达式
     * 例如: "#user.status", "#oldStatus"
     */
    String beforeStatusSpEL() default "";

    /**
     * 操作后状态的SpEL表达式
     * <p>
     * 用于获取操作后的状态值，支持SpEL表达式
     * 例如: "#user.status", "#newStatus", "1"
     */
    String afterStatusSpEL() default "";

    /**
     * 操作者ID的SpEL表达式
     * <p>
     * 用于获取操作者ID，支持SpEL表达式
     * 如果不指定，将自动从当前登录用户获取
     * 例如: "#operatorId", "#adminId"
     */
    String operatorIdSpEL() default "";

    /**
     * 扩展信息的SpEL表达式
     * <p>
     * 用于记录额外的操作信息，支持SpEL表达式，结果会转换为JSON格式
     * 例如: "#extraInfo", "{'oldEmail': #oldEmail, 'newEmail': #newEmail}"
     */
    String extraSpEL() default "";

    /**
     * 是否记录操作人ID
     * <p>
     * true: 记录操作人ID（后台管理操作）
     * false: 不记录操作人ID（用户端个人操作）
     */
    boolean recordOperator() default false;

    /**
     * 是否启用日志记录
     * <p>
     * 支持SpEL表达式，可以根据条件动态决定是否记录日志
     * 例如: "true", "#user.status != 0"
     */
    String enable() default "true";
}
