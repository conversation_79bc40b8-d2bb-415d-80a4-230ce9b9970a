package com.xt.hsk.module.user.enums;

import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户来源 0-手动注册/管理员注册 1-APP注册 2-PC注册）
 *
 * <AUTHOR>
 * @since 2025/05/30
 */
@Getter
@AllArgsConstructor
public enum UserSourceEnum implements BasicEnum<Integer>, ArrayValuable<Integer> {
    /**
     * app端用户注册来源
     */
    MANUAL_REGISTER(0, "手动注册"),
    APP_REGISTER(1, "APP注册"),
    PC_REGISTER(2, "PC注册"),
    H5_REGISTER(3, "H5注册"),
    ;

    private final Integer code;
    private final String desc;

    private static final Integer[] ARRAYS = Arrays.stream(values()).map(UserSourceEnum::getCode)
        .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}
