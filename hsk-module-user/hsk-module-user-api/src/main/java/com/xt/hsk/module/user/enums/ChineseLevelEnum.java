package com.xt.hsk.module.user.enums;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 汉语水平枚举
 *
 * <AUTHOR>
 * @since 2025/06/11
 */
@Getter
@AllArgsConstructor
public enum ChineseLevelEnum implements BasicEnum<Integer> {
    /**
     * 不确定性
     */
    UNCERTAIN(0, "不确定"),
    FIRST_LEARNING(1, "第一次学中文"),
    BASIC_CONVERSATION(2, "能简单对话"),
    DAILY_COMMUNICATION(3, "能用中文日常交流");

    private final Integer code;
    private final String desc;
}
