package com.xt.hsk.module.user.enums;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户职业枚举
 *
 * <AUTHOR>
 * @since 2025/06/11
 */
@Getter
@AllArgsConstructor
public enum ProfessionEnum implements BasicEnum<Integer> {
    /**
     * 其他
     */
    OTHER(0, "其他"),
    WORKER(1, "上班族"),
    COLLEGE_STUDENT(2, "大学生"),
    HIGH_SCHOOL_STUDENT(3, "中学生");

    private final Integer code;
    private final String desc;

}
