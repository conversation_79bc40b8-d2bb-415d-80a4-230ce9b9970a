package com.xt.hsk.module.user.enums;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户操作类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum UserOperateTypeEnum implements BasicEnum<Integer> {

    /**
     * 创建用户
     */
    CREATE(1, "创建用户"),

    /**
     * 修改密码
     */
    CHANGE_PASSWORD(2, "修改密码"),

    /**
     * 重置密码
     */
    RESET_PASSWORD(3, "重置密码"),

    /**
     * 禁用用户
     */
    DISABLE(4, "禁用用户"),

    /**
     * 启用用户
     */
    ENABLE(5, "启用用户"),

    /**
     * 用户注销 也是删除操作,是app用户主动触发的行为
     */
    LOGOUT(6, "用户注销"),

    /**
     * 修改用户资料
     */
    UPDATE_PROFILE(7, "修改用户资料"),

    /**
     * 绑定手机号
     */
    BIND_MOBILE(8, "绑定手机号"),

    /**
     * 修改手机号
     */
    UPDATE_MOBILE(9, "修改手机号"),

    /**
     * 删除用户（后台删除）
     */
    DELETE(11, "删除用户");

    /**
     * 类型
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

} 