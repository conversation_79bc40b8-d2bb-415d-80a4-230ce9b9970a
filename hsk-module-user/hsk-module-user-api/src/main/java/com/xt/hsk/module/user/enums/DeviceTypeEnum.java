package com.xt.hsk.module.user.enums;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备类型 enum
 *
 * <AUTHOR>
 * @since 2025/05/29
 */
@Getter
@AllArgsConstructor
public enum DeviceTypeEnum implements BasicEnum<String> {
    /**
     * 暂时不区分app端具体设备类型
     */
    PC("PC", "PC"),
    APP("app", "app"),
    ;
    private final String code;
    private final String desc;

}
