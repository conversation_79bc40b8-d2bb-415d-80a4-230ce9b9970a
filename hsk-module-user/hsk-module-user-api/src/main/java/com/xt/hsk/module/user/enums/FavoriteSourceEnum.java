package com.xt.hsk.module.user.enums;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 收藏来源枚举
 *
 * <AUTHOR>
 * @since 2025-01-XX
 */
@Getter
@AllArgsConstructor
public enum FavoriteSourceEnum implements BasicEnum<Integer>, VO, ArrayValuable<Integer> {

    /**
     * 互动课作业
     */
    INTERACTIVE_COURSE(1, "互动课作业"),

    /**
     * 真题练习
     */
    REAL_EXAM_PRACTICE(2, "真题练习"),

    /**
     * 专项练习
     */
    SPECIAL_PRACTICE(3, "专项练习"),
    ;

    private static final Integer[] ARRAYS = Arrays.stream(values())
        .map(FavoriteSourceEnum::getCode)
        .toArray(Integer[]::new);

    private final Integer code;
    private final String desc;

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

}