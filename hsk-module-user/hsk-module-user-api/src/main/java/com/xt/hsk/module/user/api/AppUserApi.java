package com.xt.hsk.module.user.api;

import com.xt.hsk.framework.common.util.collection.CollectionUtils;
import com.xt.hsk.module.user.api.dto.AppUserRespDTO;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * App 用户 API 接口
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
public interface AppUserApi {

    /**
     * 通过用户 ID 查询用户
     *
     * @param id 用户ID
     * @return 用户对象信息
     */
    AppUserRespDTO getUser(Long id);

    /**
     * 获取用户的hsk等级
     */
    Integer getUserHskLevel(Long id);

    /**
     * 通过用户 ID 查询用户们
     *
     * @param ids 用户 ID 们
     * @return 用户对象信息
     */
    List<AppUserRespDTO> getUserList(Collection<Long> ids);

    /**
     * 获得用户 Map
     *
     * @param ids 用户编号数组
     * @return 用户 Map
     */
    default Map<Long, AppUserRespDTO> getUserMap(Collection<Long> ids) {
        List<AppUserRespDTO> users = getUserList(ids);
        return CollectionUtils.convertMap(users, AppUserRespDTO::getId);
    }

    /**
     * 校验用户是否有效。如下情况，视为无效： 1. 用户编号不存在 2. 用户被禁用
     *
     * @param id 用户编号
     * @return 是否有效
     */
    boolean validateUser(Long id);
} 