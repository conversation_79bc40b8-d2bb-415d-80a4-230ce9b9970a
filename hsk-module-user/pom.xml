<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xt.hsk</groupId>
    <artifactId>hsk</artifactId>
    <version>${revision}</version>
  </parent>

  <artifactId>hsk-module-user</artifactId>
  <modules>
    <module>hsk-module-user-biz</module>
    <module>hsk-module-user-api</module>
  </modules>

  <properties>
    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <packaging>pom</packaging>

  <name>${project.artifactId}</name>
  <description>
    app 端用户模块 和后台管理用户区分开 单独存放在user表中
  </description>

</project>