package com.xt.hsk.module.user.manager.user;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.user.enums.ErrorCodeConstants.USER_DISABLED;
import static com.xt.hsk.module.user.enums.ErrorCodeConstants.USER_NOT_EXISTS;
import static com.xt.hsk.module.user.enums.ErrorCodeConstants.USER_PASSWORD_ERROR;
import static com.xt.hsk.module.user.enums.ErrorCodeConstants.USER_PASSWORD_ERROR_EXCEED_MAX_COUNT;

import cn.dev33.satoken.stp.StpUtil;
import com.xt.hsk.framework.common.constants.RedisKeyPrefix;
import com.xt.hsk.framework.common.enums.CommonStatusEnum;
import com.xt.hsk.framework.common.util.monitor.TracerUtils;
import com.xt.hsk.framework.common.util.servlet.ServletUtils;
import com.xt.hsk.framework.redis.utils.RedisUtil;
import com.xt.hsk.module.system.api.sms.SmsCodeApi;
import com.xt.hsk.module.system.api.sms.dto.code.SmsCodeUseReqDTO;
import com.xt.hsk.module.system.enums.logger.LoginLogTypeEnum;
import com.xt.hsk.module.system.enums.logger.LoginResultEnum;
import com.xt.hsk.module.system.enums.sms.SmsSceneEnum;
import com.xt.hsk.module.user.controller.app.user.vo.UserForgetPasswordReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.UserLoginReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.UserLoginRespVO;
import com.xt.hsk.module.user.controller.app.user.vo.UserSmsLoginReqVO;
import com.xt.hsk.module.user.convert.appuser.AppUserConvert;
import com.xt.hsk.module.user.dal.dataobject.user.UserDO;
import com.xt.hsk.module.user.dto.AppUserLoginLogCreateReqDTO;
import com.xt.hsk.module.user.enums.DeviceTypeEnum;
import com.xt.hsk.module.user.enums.ErrorCodeConstants;
import com.xt.hsk.module.user.enums.UserSourceEnum;
import com.xt.hsk.module.user.service.loginlog.UserLoginLogService;
import com.xt.hsk.module.user.service.user.UserService;
import com.xt.hsk.module.user.util.CountryCodeUtils;
import jakarta.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户认证管理器
 *
 * <AUTHOR>
 * @since 2025/01/27
 */
@Slf4j
@Component
public class UserAuthManager {

    @Resource
    private UserService userService;

    @Resource
    private UserLoginLogService updateUserLogin;

    @Resource
    private SmsCodeApi smsCodeApi;

    @Resource
    private RedisUtil redisUtil;

    /**
     * 密码尝试次数上限
     */
    private static final int MAX_PASSWORD_ATTEMPTS = 10;

    /**
     * 密码尝试锁定时间（分钟）
     */
    private static final int PASSWORD_LOCK_TIME_MINUTES = 30;

    /**
     * 密码登录
     *
     * @param loginReqVO 登录信息
     * @return 登录结果
     */
    @Transactional(rollbackFor = Exception.class)
    public UserLoginRespVO login(UserLoginReqVO loginReqVO) {
        UserDO userDO = authenticate(loginReqVO.getCountryCode(), loginReqVO.getMobile(),
            loginReqVO.getPassword());
        // 构建响应
        StpUtil.login(userDO.getId(), DeviceTypeEnum.APP.getCode());
        // 把token取成明文
        UserLoginRespVO loginRespVO = AppUserConvert.INSTANCE.convertToLoginResp(userDO);
        loginRespVO.setToken(StpUtil.getTokenValue());
        return loginRespVO;
    }

    /**
     * 检验用户是否存在
     *
     * @param countryCode 区号
     * @param phone       手机号码
     * @param password    密码
     * @return {@code UserDO }
     */
    public UserDO authenticate(String countryCode, String phone, String password) {
        final LoginLogTypeEnum logTypeEnum = LoginLogTypeEnum.LOGIN_USERNAME;
        // 校验账号是否存在
        UserDO user = userService.getUserByPhone(countryCode, phone);
        if (user == null) {
            createLoginLog(null, countryCode, phone, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(USER_NOT_EXISTS);
        }

        // 检查密码尝试次数
        String passwordAttemptKey = buildPasswordAttemptKey(countryCode, phone);
        Integer attempts = redisUtil.getInteger(passwordAttemptKey);

        // 如果尝试次数已达到上限，提示用户
        if (attempts != null && attempts >= MAX_PASSWORD_ATTEMPTS) {
            createLoginLog(user.getId(), countryCode, phone, logTypeEnum,
                LoginResultEnum.BAD_CREDENTIALS);
            throw exception(USER_PASSWORD_ERROR_EXCEED_MAX_COUNT,
                "密码错误次数过多，请稍后重试或尝试验证码登录");
        }

        // 验证密码
        if (!userService.isPasswordMatch(password, user.getPassword())) {
            // 增加密码错误次数
            increasePasswordAttempt(passwordAttemptKey);

            // 判断是否达到上限
            attempts = redisUtil.getInteger(passwordAttemptKey);
            if (attempts >= MAX_PASSWORD_ATTEMPTS) {
                createLoginLog(user.getId(), countryCode, phone, logTypeEnum,
                    LoginResultEnum.BAD_CREDENTIALS);
                throw exception(USER_PASSWORD_ERROR_EXCEED_MAX_COUNT,
                    "密码错误次数过多，请稍后重试或尝试验证码登录");
            } else {
                createLoginLog(user.getId(), countryCode, phone, logTypeEnum,
                    LoginResultEnum.BAD_CREDENTIALS);
                throw exception(USER_PASSWORD_ERROR, "密码错误，请重试");
            }
        }

        // 密码正确，重置错误计数
        redisUtil.delete(passwordAttemptKey);
        
        // 校验是否禁用
        if (CommonStatusEnum.isDisable(user.getStatus())) {
            createLoginLog(user.getId(), countryCode, phone, logTypeEnum,
                LoginResultEnum.USER_DISABLED);
            throw exception(USER_DISABLED);
        }
        return user;
    }

    /**
     * 增加密码尝试错误次数
     *
     * @param key Redis key
     */
    private void increasePasswordAttempt(String key) {
        // 增加错误次数
        Long count = redisUtil.increment(key);
        // 第一次出错时，设置过期时间
        if (count != null && count == 1) {
            redisUtil.expire(key, PASSWORD_LOCK_TIME_MINUTES, TimeUnit.MINUTES);
        }
    }

    /**
     * 构建密码尝试次数的 Redis key
     *
     * @param countryCode 国家代码
     * @param phone       手机号
     * @return Redis key
     */
    private String buildPasswordAttemptKey(String countryCode, String phone) {
        return String.format(RedisKeyPrefix.USER_PASSWORD_ATTEMPT, countryCode, phone);
    }

    /**
     * 创建登录日志
     *
     * @param userId      用户 ID
     * @param countryCode 区号
     * @param phone       手机号
     * @param logTypeEnum log 类型 enum
     * @param loginResult 登录结果
     */
    private void createLoginLog(Long userId, String countryCode, String phone,
        LoginLogTypeEnum logTypeEnum, LoginResultEnum loginResult) {

        AppUserLoginLogCreateReqDTO reqDTO = new AppUserLoginLogCreateReqDTO();
        reqDTO.setLogType(logTypeEnum.getType());
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setPhone(phone);
        reqDTO.setCountryCode(countryCode);
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(ServletUtils.getClientIP());
        reqDTO.setResult(loginResult.getResult());
        updateUserLogin.createLoginLog(reqDTO);
        // 更新最后登录时间
        if (userId != null && Objects.equals(LoginResultEnum.SUCCESS.getResult(),
            loginResult.getResult())) {
            userService.updateUserLogin(userId, ServletUtils.getClientIP());
        }
    }

    /**
     * 短信登录/注册 新用户会被自动注册
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    @Transactional(rollbackFor = Exception.class)
    public UserLoginRespVO smsLogin(UserSmsLoginReqVO reqVO) {
        // 校验国家区号
        boolean checked = CountryCodeUtils.checkCountryCode(reqVO.getCountryCode());
        if (!checked) {
            throw exception(ErrorCodeConstants.USER_COUNTRY_CODE_ERROR);
        }
        // 使用验证码 验证码有问题会抛出异常
        // useSmsCode(reqVO.getSmsCode(), reqVO.getCountryCode(), reqVO.getMobile(),
        //     SmsSceneEnum.MEMBER_LOGIN_CN);

        // 查询用户是否存在
        UserDO user = userService.getUserByPhone(reqVO.getCountryCode(), reqVO.getMobile());
        // 新用户 自动帮注册
        if (user == null) {
            UserDO userDO = createUser(reqVO.getCountryCode(), reqVO.getMobile());
            // 记录登录日志
            createLoginLog(userDO.getId(), reqVO.getCountryCode(), reqVO.getMobile(),
                LoginLogTypeEnum.LOGIN_SMS, LoginResultEnum.SUCCESS);
            StpUtil.login(userDO.getId(), DeviceTypeEnum.APP.getCode());
            UserLoginRespVO loginRespVO = AppUserConvert.INSTANCE.convertToLoginResp(userDO);
            loginRespVO.setToken(StpUtil.getTokenValue());
            return loginRespVO;
        }

        // 不是新用户 检查用户的状态
        if (CommonStatusEnum.isDisable(user.getStatus())) {
            createLoginLog(user.getId(), reqVO.getCountryCode(), reqVO.getMobile(),
                LoginLogTypeEnum.LOGIN_SMS, LoginResultEnum.USER_DISABLED);
            throw exception(com.xt.hsk.module.user.enums.ErrorCodeConstants.USER_DISABLED);
        }

        // 清除密码错误记录
        String passwordAttemptKey = buildPasswordAttemptKey(reqVO.getCountryCode(),
            reqVO.getMobile());
        redisUtil.delete(passwordAttemptKey);

        // 记录登录日志
        createLoginLog(user.getId(), reqVO.getCountryCode(), reqVO.getMobile(),
            LoginLogTypeEnum.LOGIN_SMS, LoginResultEnum.SUCCESS);
        StpUtil.login(user.getId(), DeviceTypeEnum.APP.getCode());
        UserLoginRespVO loginRespVO = AppUserConvert.INSTANCE.convertToLoginResp(user);
        loginRespVO.setToken(StpUtil.getTokenValue());
        return loginRespVO;
    }

    /**
     * 创建用户
     *
     * @param countryCode 区号
     * @param mobile      手机号码
     * @return {@code UserDO }
     */
    private UserDO createUser(String countryCode, String mobile) {
        // 校验国家区号
        boolean checked = CountryCodeUtils.checkCountryCode(countryCode);
        if (!checked) {
            throw exception(ErrorCodeConstants.USER_COUNTRY_CODE_ERROR);
        }
        return userService.createUser(countryCode, mobile, UserSourceEnum.APP_REGISTER);
    }

    /**
     * 使用短信验证码
     */
    private void useSmsCode(String smsCode, String countryCode, String mobile,
        SmsSceneEnum smsSceneEnum) {

        // 校验国家区号
        boolean checked = CountryCodeUtils.checkCountryCode(countryCode);
        if (!checked) {
            throw exception(ErrorCodeConstants.USER_COUNTRY_CODE_ERROR);
        }

        SmsCodeUseReqDTO smsCodeUseReqDTO = new SmsCodeUseReqDTO();
        smsCodeUseReqDTO.setMobile(mobile);
        smsCodeUseReqDTO.setScene(smsSceneEnum.getScene());
        smsCodeUseReqDTO.setCode(smsCode);
        smsCodeUseReqDTO.setUsedIp(ServletUtils.getClientIP());
        smsCodeUseReqDTO.setCountryCode(countryCode);
        smsCodeApi.useSmsCode(smsCodeUseReqDTO);
    }

    /**
     * 忘记密码
     *
     * @param reqVO req vo
     * @return {@code Boolean }
     */
    public Boolean forgetPassword(UserForgetPasswordReqVO reqVO) {
        // 使用验证码 验证码有问题会抛出异常
        useSmsCode(reqVO.getSmsCode(), reqVO.getCountryCode(), reqVO.getMobile(),
            SmsSceneEnum.MEMBER_RESET_PASSWORD_CN);
        // 校验账号是否存在
        String countryCode = reqVO.getCountryCode();
        String phone = reqVO.getMobile();
        UserDO user = userService.getUserByPhone(countryCode, phone);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
        // 校验是否禁用
        if (CommonStatusEnum.isDisable(user.getStatus())) {
            throw exception(com.xt.hsk.module.user.enums.ErrorCodeConstants.USER_DISABLED);
        }
        userService.updateUserPassword(user.getId(), reqVO.getNewPassword());

        // 重置密码错误次数
        String passwordAttemptKey = buildPasswordAttemptKey(countryCode, phone);
        redisUtil.delete(passwordAttemptKey);
        
        return true;
    }
}