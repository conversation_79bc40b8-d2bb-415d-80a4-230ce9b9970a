package com.xt.hsk.module.user.convert.feedback;

import com.xt.hsk.module.user.controller.admin.feedback.dto.UserFeedbackWithUserDTO;
import com.xt.hsk.module.user.controller.admin.feedback.vo.UserFeedbackRespVO;
import com.xt.hsk.module.user.controller.app.feedback.vo.UserFeedbackCreateReqVO;
import com.xt.hsk.module.user.dal.dataobject.feedback.UserFeedbackDO;
import com.xt.hsk.module.user.dal.dataobject.user.UserDO;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户反馈 Convert
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
public class UserFeedbackConvert {

    public static final UserFeedbackConvert INSTANCE = new UserFeedbackConvert();

    private UserFeedbackConvert() {
        // 私有构造函数，防止外部实例化
    }

    /**
     * 创建请求 VO 转 DO
     *
     * @param createReqVO 用户反馈创建请求
     * @return 用户反馈 DO
     */
    public UserFeedbackDO convert(UserFeedbackCreateReqVO createReqVO) {
        if (createReqVO == null) {
            return null;
        }

        UserFeedbackDO userFeedbackDO = new UserFeedbackDO();
        userFeedbackDO.setContent(createReqVO.getContent());
        userFeedbackDO.setCountryCode(createReqVO.getCountryCode());
        userFeedbackDO.setContactInfoType(createReqVO.getContactInfoType());
        userFeedbackDO.setContactInfo(createReqVO.getContactInfo());

        return userFeedbackDO;
    }

    /**
     * DO 转 Response VO
     *
     * @param bean 用户反馈 DO
     * @return 用户反馈 Response VO
     */
    public UserFeedbackRespVO convert(UserFeedbackDO bean) {
        if (bean == null) {
            return null;
        }

        UserFeedbackRespVO respVO = new UserFeedbackRespVO();
        respVO.setId(bean.getId());
        respVO.setUserId(bean.getUserId());
        respVO.setContent(bean.getContent());
        respVO.setCountryCode(bean.getCountryCode());
        respVO.setContactInfoType(bean.getContactInfoType());
        respVO.setContactInfo(bean.getContactInfo());
        respVO.setCreateTime(bean.getCreateTime());

        return respVO;
    }

    /**
     * DTO 转 Response VO
     *
     * @param dto 包含用户信息的反馈DTO
     * @return 用户反馈 Response VO
     */
    public UserFeedbackRespVO convert(UserFeedbackWithUserDTO dto) {
        if (dto == null) {
            return null;
        }

        UserFeedbackRespVO respVO = new UserFeedbackRespVO();
        respVO.setId(dto.getId());
        respVO.setUserId(dto.getUserId());
        respVO.setContent(dto.getContent());
        respVO.setCountryCode(dto.getCountryCode());
        respVO.setContactInfoType(dto.getContactInfoType());
        respVO.setContactInfo(dto.getContactInfo());
        respVO.setCreateTime(dto.getCreateTime());
        respVO.setNickname(dto.getNickname());
        respVO.setMobile(dto.getMobile());

        return respVO;
    }

    /**
     * DTO列表 转 Response VO列表
     *
     * @param dtoList 包含用户信息的反馈DTO列表
     * @return 用户反馈 Response VO列表
     */
    public List<UserFeedbackRespVO> convertList(List<UserFeedbackWithUserDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }

        List<UserFeedbackRespVO> list = new ArrayList<>(dtoList.size());
        for (UserFeedbackWithUserDTO dto : dtoList) {
            list.add(convert(dto));
        }

        return list;
    }

    /**
     * DO + UserDO 转 Response VO
     *
     * @param bean 用户反馈 DO
     * @param user 用户信息
     * @return 用户反馈 Response VO
     */
    public UserFeedbackRespVO convert(UserFeedbackDO bean, UserDO user) {
        if (bean == null) {
            return null;
        }

        // 从反馈DO转换基本字段
        UserFeedbackRespVO respVO = convert(bean);

        // 设置用户信息
        if (user != null) {
            respVO.setNickname(user.getNickname());
            respVO.setMobile(user.getMobile());
        }

        return respVO;
    }
} 