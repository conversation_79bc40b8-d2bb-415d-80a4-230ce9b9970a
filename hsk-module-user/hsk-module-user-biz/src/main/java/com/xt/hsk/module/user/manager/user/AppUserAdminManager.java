package com.xt.hsk.module.user.manager.user;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.system.enums.ErrorCodeConstants.USER_NOT_EXISTS;
import static com.xt.hsk.module.user.enums.ErrorCodeConstants.USER_PHONE_EXISTS;
import static java.lang.Math.max;

import cn.idev.excel.EasyExcel;
import cn.idev.excel.exception.ExcelAnalysisException;
import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.pojo.ImportResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.BasicEnumUtil;
import com.xt.hsk.module.infra.listener.CompositeRowLimitListener;
import com.xt.hsk.module.user.controller.admin.user.vo.UserAdminRespVO;
import com.xt.hsk.module.user.controller.admin.user.vo.UserCreateReqVO;
import com.xt.hsk.module.user.controller.admin.user.vo.UserImportExcelVO;
import com.xt.hsk.module.user.controller.admin.user.vo.UserPageReqVO;
import com.xt.hsk.module.user.controller.admin.user.vo.UserUpdateReqVO;
import com.xt.hsk.module.user.convert.appuser.AppUserConvert;
import com.xt.hsk.module.user.dal.dataobject.user.UserDO;
import com.xt.hsk.module.user.enums.ChineseLevelEnum;
import com.xt.hsk.module.user.enums.LearningPurposeEnum;
import com.xt.hsk.module.user.enums.ProfessionEnum;
import com.xt.hsk.module.user.listener.UserImportListener;
import com.xt.hsk.module.user.service.user.UserService;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户管理器
 *
 * <AUTHOR>
 * @since 2025/01/27
 */
@Slf4j
@Component
public class AppUserAdminManager {

    @Resource
    private UserService userService;


    /**
     * 创建用户
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    public Long createUser(UserCreateReqVO reqVO) {
        // 校验手机号唯一
        validatePhoneUnique(reqVO.getCountryCode(), reqVO.getMobile(), null);

        UserDO user = userService.createUserByAdmin(reqVO.getCountryCode(),
            reqVO.getMobile(), reqVO.getNickname());

        // 设置日志上下文变量
        LogRecordContext.putVariable("userId", user.getId());
        LogRecordContext.putVariable("user", user);

        // 返回
        return user.getId();
    }

    /**
     * 更新用户
     *
     * @param updateReqVO 更新信息
     */
    public void updateUser(UserUpdateReqVO updateReqVO) {
        // 校验存在
        validateUserExists(updateReqVO.getId());
        // 校验手机号唯一
        validatePhoneUnique(updateReqVO.getCountryCode(), updateReqVO.getMobile(),
            updateReqVO.getId());
        // 更新
        UserDO updateObj = AppUserConvert.INSTANCE.doToRespVO(updateReqVO);
        userService.updateById(updateObj);

        // 设置日志上下文变量
        LogRecordContext.putVariable("user", updateObj);
    }

    /**
     * 删除用户
     *
     * @param id 编号
     */
    public void deleteUser(Long id) {
        // 校验存在
        validateUserExists(id);
        // 获取用户信息
        UserDO user = userService.getById(id);

        // 设置日志上下文变量
        LogRecordContext.putVariable("user", user);

        // 删除
        userService.adminDeleteUser(id);
    }

    /**
     * 更新用户状态（禁用/启用）
     *
     * @param id 用户ID
     */
    public void updateUserStatus(Long id) {
        // 获取用户信息
        UserDO user = userService.getById(id);
        String statusText = user.getStatus() == 0 ? "启用" : "禁用";

        // 设置日志上下文变量
        LogRecordContext.putVariable("statusText", statusText);
        LogRecordContext.putVariable("user", user);

        userService.updateUserStatus(id);
    }

    /**
     * 重置密码
     *
     * @param id 用户ID
     */
    public void resetPassword(Long id) {
        // 校验存在
        UserDO userDO = validateUserExists(id);

        // 设置日志上下文变量
        LogRecordContext.putVariable("user", userDO);

        userService.resetPassword(userDO.getId());
    }

    /**
     * 获取用户分页列表
     *
     * @param pageReqVO 分页查询条件
     * @return 用户分页列表
     */
    public PageResult<UserAdminRespVO> getUserPage(UserPageReqVO pageReqVO) {
        PageResult<UserDO> pageResult = userService.getUserPage(pageReqVO);
        List<UserAdminRespVO> respList = AppUserConvert.INSTANCE.convertList2(pageResult.getList());
        return new PageResult<>(respList, pageResult.getTotal());
    }

    /**
     * 获取用户
     *
     * @param id 用户ID
     * @return 用户信息
     */
    public UserAdminRespVO getUser(Long id) {
        UserDO userDO = userService.getById(id);
        if (userDO == null) {
            return null;
        }

        UserAdminRespVO userAdminRespVO = AppUserConvert.INSTANCE.doToRespVO(userDO);

        //  用户水平描述
        userAdminRespVO.setChineseLevelStr(BasicEnumUtil.getDescByCode(ChineseLevelEnum.class,
            userAdminRespVO.getChineseLevel()));

        // 用户职业描述
        userAdminRespVO.setProfessionStr(
            BasicEnumUtil.getDescByCode(ProfessionEnum.class, userAdminRespVO.getProfession()));

        // 用户学习目的描述
        userAdminRespVO.setLearningPurposeStr(BasicEnumUtil.getDescByCode(LearningPurposeEnum.class,
            userAdminRespVO.getLearningPurpose()));

        return AppUserConvert.INSTANCE.doToRespVO(userDO);
    }

    /**
     * 校验用户存在
     *
     * @param id 用户ID
     * @return 用户
     */
    private UserDO validateUserExists(Long id) {
        UserDO user = userService.getById(id);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
        return user;
    }

    /**
     * 校验手机号唯一
     *
     * @param countryCode 国家区号
     * @param mobile      手机号
     * @param id          用户id
     */
    private void validatePhoneUnique(String countryCode, String mobile, Long id) {
        // 校验手机号唯一
        UserDO user = userService.getUserByPhone(countryCode, mobile);
        if (user == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的用户
        if (id == null) {
            throw exception(USER_PHONE_EXISTS);
        }
        if (!user.getId().equals(id)) {
            throw exception(USER_PHONE_EXISTS);
        }
    }

    /**
     * 导入用户
     *
     * @param file Excel文件
     * @return 导入结果
     */
    public ImportResult importUser(MultipartFile file) {
        ImportResult result = new ImportResult();
        try {
            // 创建数据处理监听器
            UserImportListener dataListener = new UserImportListener(userService);

            // 创建组合监听器，限制最多导入300个用户
            CompositeRowLimitListener<UserImportExcelVO> compositeListener =
                new CompositeRowLimitListener<>(300, dataListener,
                    "一次最多导入300个用户，请分批导入");

            // 执行导入，设置headRowNumber=1表示从第二行开始读取数据（第一行是表头）
            EasyExcel.read(file.getInputStream(), UserImportExcelVO.class, compositeListener)
                .headRowNumber(2)
                .sheet()
                .doRead();

            result.setSuccess(dataListener.SUCCESS);
            result.setMsg(dataListener.msg);
            result.setInvalidCount(max(dataListener.INVALID_COUNT, 0));
            result.setValidCount(dataListener.VALID_COUNT);

        } catch (ExcelAnalysisException e) {
            log.warn("导入用户受限: {}", e.getMessage());
            result.setSuccess(false);
            result.setMsg(Collections.singletonList(e.getMessage()));
            result.setInvalidCount(0);
            result.setValidCount(0);
        } catch (Exception e) {
            log.error("导入用户失败: {}", e.getMessage(), e);
            result.setSuccess(false);
            result.setMsg(Collections.singletonList("导入失败：" + e.getMessage()));
            result.setInvalidCount(0);
            result.setValidCount(0);
        }

        // 设置日志上下文变量
        LogRecordContext.putVariable("fileName", file.getOriginalFilename());
        LogRecordContext.putVariable("importResult", result);

        return result;
    }
}