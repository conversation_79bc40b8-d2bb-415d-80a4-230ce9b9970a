package com.xt.hsk.module.user.dal.dataobject.feedback;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.AppBaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 用户反馈 DO
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@TableName("user_feedback")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserFeedbackDO extends AppBaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 内容
     */
    private String content;

    /**
     * 区号，如果联系方式类型是手机号的话，必填
     */
    private String countryCode;

    /**
     * 操作IP地址
     */
    private String operateIp;

    /**
     * 联系方式类型 1 whchat 2 e-mail 3 zalo 4 手机号 5whatapp 6Facebook
     */
    private Integer contactInfoType;

    /**
     * 联系方式
     */
    private String contactInfo;

} 