package com.xt.hsk.module.user.controller.app.user.vo;

import com.xt.hsk.framework.common.constants.RegexpConstant;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 用户密码登录 Request VO
 *
 * <AUTHOR>
 * @since 2025/06/07
 */
@Data
public class UserLoginReqVO {
    /*
     *  手机号
     */
    @Pattern(regexp = RegexpConstant.MOBILE, message = "{validation.mobile.invalid}")
    private String mobile;
    /**
     * 区号
     */
    @Pattern(regexp = RegexpConstant.COUNTRY_CODE, message = "{validation.invalid.country.code}")
    private String countryCode;
    /**
     * 密码
     */
    @Pattern(regexp = "^(?=.*[a-zA-Z])(?=.*\\d).{8,16}$", message = "{validation.password.invalid}")
    private String password;
}