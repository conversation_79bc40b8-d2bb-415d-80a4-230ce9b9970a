package com.xt.hsk.module.user.dal.mysql.user;

import cn.hutool.core.collection.CollUtil;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.user.controller.admin.user.vo.UserPageReqVO;
import com.xt.hsk.module.user.dal.dataobject.user.UserDO;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserMapper extends BaseMapperX<UserDO> {

    default PageResult<UserDO> selectPage(UserPageReqVO reqVO) {

        if (CollUtil.isNotEmpty(reqVO.getIds())) {
            return selectPage(reqVO, new LambdaQueryWrapperX<UserDO>()
                .in(UserDO::getId, reqVO.getIds())
            );
        }

        return selectPage(reqVO, new LambdaQueryWrapperX<UserDO>()
            .likeIfPresent(UserDO::getNickname, reqVO.getNickname())
            .eqIfPresent(UserDO::getId, reqVO.getId())
            .likeIfPresent(UserDO::getMobile, reqVO.getMobile())
            .eqIfPresent(UserDO::getCountryCode, reqVO.getCountryCode())
            .eqIfPresent(UserDO::getUserSource, reqVO.getUserSource())
            .eqIfPresent(UserDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(UserDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(UserDO::getId));
    }

    default UserDO selectByPhone(String countryCode, String phone) {
        return selectOne(new LambdaQueryWrapperX<UserDO>()
            .eq(UserDO::getCountryCode, countryCode)
            .eq(UserDO::getMobile, phone));
    }

    default UserDO selectByNickname(String nickname) {
        return selectOne(new LambdaQueryWrapperX<UserDO>()
            .eq(UserDO::getNickname, nickname)
            .eq(UserDO::getDeleted, false));
    }

    /**
     * 批量检查手机号是否已存在
     *
     * @param countryCodes 国家区号列表
     * @param mobiles      手机号列表
     * @return 已存在的手机号集合（格式：区号-手机号）
     */
    default Set<String> batchCheckPhones(List<String> countryCodes, List<String> mobiles) {
        if (countryCodes == null || mobiles == null || countryCodes.size() != mobiles.size()
            || countryCodes.isEmpty()) {
            return java.util.Collections.emptySet();
        }

        List<String> existingPhones = new java.util.ArrayList<>();
        // 由于可能数据量较大，这里分批处理，每批最多处理1000条
        int batchSize = 1000;
        int totalSize = countryCodes.size();

        for (int i = 0; i < totalSize; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalSize);
            List<String> batchCountryCodes = countryCodes.subList(i, endIndex);
            List<String> batchMobiles = mobiles.subList(i, endIndex);

            // 构建批量查询条件
            LambdaQueryWrapperX<UserDO> wrapper = new LambdaQueryWrapperX<>();
            wrapper.eq(UserDO::getDeleted, false);

            for (int j = 0; j < batchCountryCodes.size(); j++) {
                String countryCode = batchCountryCodes.get(j);
                String mobile = batchMobiles.get(j);

                // 使用OR连接多个条件
                wrapper.or(w -> w.eq(UserDO::getCountryCode, countryCode)
                    .eq(UserDO::getMobile, mobile));
            }

            // 执行查询并收集结果
            selectList(wrapper).forEach(user ->
                existingPhones.add(user.getCountryCode() + "-" + user.getMobile()));
        }

        return new java.util.HashSet<>(existingPhones);
    }
} 