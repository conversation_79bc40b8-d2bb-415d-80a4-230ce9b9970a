package com.xt.hsk.module.user.controller.app.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xt.hsk.framework.desensitize.core.slider.annotation.SliderDesensitize;
import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户登录 resp vo
 *
 * <AUTHOR>
 * @since 2025/05/30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserLoginRespVO implements java.io.Serializable {

    /**
     * 头像
     */
    private String avatar;

    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 手机号码
     */
    @SliderDesensitize(suffixKeep = 4)
    private String mobile;
    /**
     * 手机区号
     */
    private Integer countryCode;
    /**
     * 是否修改过密码
     */
    private Boolean passwordChanged;

    /**
     * 是否已更新过初始信息
     */
    private Boolean infoUpdated;

    /**
     * 当前HSK等级（1-6）
     */
    private Integer currentHskLevel;

    /**
     * 性别（0-未知 1-男 2-女）
     */
    private Integer gender;

    /**
     * 职业（0-其他 1-上班族 2-大学生 3-中学生）
     */
    private Integer profession;

    /**
     * 学习汉语目的（0-其他 1-留学 2-旅游 3-职业发展 4-个人兴趣）
     */
    private Integer learningPurpose;

    /**
     * 汉语水平（0-不确定 1-第一次学中文 2-能简单对话 3-能用中文日常交流）
     */
    private Integer chineseLevel;

    /**
     * 目标HSK等级（1-6）
     */
    private Integer targetHskLevel;

    /**
     * 计划考试时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime plannedExamDate;

    /**
     * token
     */
    private String token;
} 