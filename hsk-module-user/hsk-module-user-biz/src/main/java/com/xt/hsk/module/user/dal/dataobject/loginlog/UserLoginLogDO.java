package com.xt.hsk.module.user.dal.dataobject.loginlog;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.AppBaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 用户登录日志 DO
 *
 * <AUTHOR>
 * @since 2025/05/29
 */
@TableName("user_login_log")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserLoginLogDO extends AppBaseDO {

    /**
     * 访问ID
     */
    @TableId
    private Long id;
    /**
     * 日志类型
     */
    private Long logType;
    /**
     * 链路追踪编号
     */
    private String traceId;
    /**
     * 用户编号
     */
    private Long userId;
    /**
     * 登录来源：0-未知 1-移动App端
     */
    private Integer loginSource;
    /**
     * 区号
     */
    private String countryCode;
    /**
     * 手机号码
     */
    private String phone;
    /**
     * 登陆结果
     */
    private Integer result;
    /**
     * 用户 IP
     */
    private String userIp;
    /**
     * 浏览器 UA
     */
    private String userAgent;

}