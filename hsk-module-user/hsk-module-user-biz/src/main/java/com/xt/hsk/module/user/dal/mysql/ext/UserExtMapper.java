package com.xt.hsk.module.user.dal.mysql.ext;

import java.util.*;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.module.user.controller.app.user.vo.UserExtPageReqVO;
import com.xt.hsk.module.user.dal.dataobject.ext.UserExtDO;
import org.apache.ibatis.annotations.Mapper;


/**
 * 用户扩展 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserExtMapper extends BaseMapperX<UserExtDO> {

    default PageResult<UserExtDO> selectPage(UserExtPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<UserExtDO>()
                .betweenIfPresent(UserExtDO::getStudyTime, reqVO.getStudyTime())
                .orderByDesc(UserExtDO::getId));
    }

}