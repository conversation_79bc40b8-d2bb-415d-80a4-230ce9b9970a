package com.xt.hsk.module.user.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 修改手机号状态枚举
 *
 * <AUTHOR>
 * @since 2025/07/25
 */
@Getter
@AllArgsConstructor
public enum ChangeMobileState {

    INITIAL("初始状态"),
    OLD_CODE_SENT("旧手机号验证码已发送"),
    OLD_CODE_VERIFIED("旧手机号已验证"),
    NEW_CODE_SENT("新手机号验证码已发送"),
    COMPLETED("修改完成"),
    FAILED("修改失败");

    private final String description;
}
