package com.xt.hsk.module.user.dal.dataobject.user;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.AppBaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 用户操作日志 DO
 *
 * <AUTHOR>
 */
@TableName("user_operate_log")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserOperateLogDO extends AppBaseDO {

    /**
     * 日志ID
     */
    @TableId
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 操作类型
     * <p>
     * 枚举 {@link com.xt.hsk.module.user.enums.UserOperateTypeEnum}
     */
    private Integer operateType;

    /**
     * 操作前状态
     */
    private Integer beforeStatus;

    /**
     * 操作后状态
     */
    private Integer afterStatus;

    /**
     * 操作者ID（管理员ID或系统ID）
     */
    private Long operatorId;

    /**
     * 操作原因/备注
     */
    private String remark;

    /**
     * 操作IP地址
     */
    private String operateIp;

    /**
     * 扩展字段，JSON格式，用于存储额外信息
     */
    private String extra;
} 