package com.xt.hsk.module.user.dal.dataobject.ext;

import lombok.*;

import java.util.*;

import com.baomidou.mybatisplus.annotation.*;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;

/**
 * 用户扩展 DO
 *
 * <AUTHOR>
 */
@TableName("user_ext")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserExtDO {

    /**
     * 用户ID
     */
    @TableId
    private Long id;
    /**
     * 学习时长 秒
     */
    private Long studyTime;

}