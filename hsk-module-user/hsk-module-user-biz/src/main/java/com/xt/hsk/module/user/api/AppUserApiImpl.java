package com.xt.hsk.module.user.api;

import cn.hutool.core.collection.CollUtil;
import com.xt.hsk.framework.common.enums.CommonStatusEnum;
import com.xt.hsk.module.user.api.dto.AppUserRespDTO;
import com.xt.hsk.module.user.convert.appuser.AppUserConvert;
import com.xt.hsk.module.user.dal.dataobject.user.UserDO;
import com.xt.hsk.module.user.service.user.UserService;
import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * App 用户 API 实现类
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@Service
@Validated
@Slf4j
public class AppUserApiImpl implements AppUserApi {

    @Resource
    private UserService userService;

    @Override
    public AppUserRespDTO getUser(Long id) {
        UserDO user = userService.getById(id);
        return AppUserConvert.INSTANCE.convert(user);
    }

    @Override
    public Integer getUserHskLevel(Long id) {
        return getUser(id).getCurrentHskLevel();
    }

    @Override
    public List<AppUserRespDTO> getUserList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<UserDO> users = userService.listByIds(ids);
        return AppUserConvert.INSTANCE.convertList(users);
    }

    @Override
    public boolean validateUser(Long id) {
        if (id == null) {
            return false;
        }
        // 校验用户是否存在
        UserDO user = userService.getById(id);
        if (user == null) {
            return false;
        }
        // 校验用户是否禁用
        return !CommonStatusEnum.isDisable(user.getStatus());
    }
} 