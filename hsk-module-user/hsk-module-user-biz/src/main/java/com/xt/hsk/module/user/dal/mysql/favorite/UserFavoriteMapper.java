package com.xt.hsk.module.user.dal.mysql.favorite;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.user.controller.app.favorite.vo.*;
import com.xt.hsk.module.user.dal.dataobject.favorite.UserFavoriteDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户收藏 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserFavoriteMapper extends BaseMapperX<UserFavoriteDO> {

    default PageResult<UserFavoriteDO> selectPage(UserFavoritePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<UserFavoriteDO>()
            .eqIfPresent(UserFavoriteDO::getUserId, reqVO.getUserId())
            .eqIfPresent(UserFavoriteDO::getFavoriteType, reqVO.getFavoriteType())
            .eqIfPresent(UserFavoriteDO::getFavoriteSource, reqVO.getFavoriteSource())
            .eqIfPresent(UserFavoriteDO::getTargetId, reqVO.getTargetId())
            .eqIfPresent(UserFavoriteDO::getIsWrongQuestion, reqVO.getIsWrongQuestion())
            .orderByDesc(UserFavoriteDO::getId));
    }

    /**
     * 分页查询收藏的小游戏题目列表
     *
     * @param page  分页参数
     * @param reqVO 查询条件
     * @return 分页结果
     */
    Page<FavoriteGameQuestionsAppPageRespVO> selectFavoriteGameQuestionsPage(
        Page<FavoriteGameQuestionsAppPageRespVO> page,
        @Param("req") GameFavoriteAppPageReqVO reqVO);

    /**
     * 分页查询收藏的小游戏题目列表
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    List<FavoriteQuestionPageRespVO> favoriteQuestionList(@Param("req") FavoriteQuestionAppPageReqVO reqVO);

    /**
     * 分页查询收藏的单词列表
     *
     * @param page  分页参数
     * @param reqVO 查询条件
     * @return 分页结果
     */
    Page<FavoriteWordAppPageRespVO> wordBookList(Page<FavoriteWordAppPageRespVO> page, @Param("req") FavoriteWordAppPageReqVO reqVO);
}
