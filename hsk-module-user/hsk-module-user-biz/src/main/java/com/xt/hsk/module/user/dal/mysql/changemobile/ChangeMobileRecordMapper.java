package com.xt.hsk.module.user.dal.mysql.changemobile;

import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.user.dal.dataobject.changemobile.ChangeMobileRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户更换手机号记录 Mapper
 *
 * <AUTHOR>
 * @since 2025/07/28
 */
@Mapper
public interface ChangeMobileRecordMapper extends BaseMapperX<ChangeMobileRecordDO> {

    /**
     * 根据会话ID查询记录
     */
    default ChangeMobileRecordDO selectBySessionId(String sessionId) {
        return selectOne(ChangeMobileRecordDO::getSessionId, sessionId);
    }

    /**
     * 根据用户ID查询最新的记录
     */
    default ChangeMobileRecordDO selectLatestByUserId(Long userId) {
        return selectOne(new LambdaQueryWrapperX<ChangeMobileRecordDO>()
            .eq(ChangeMobileRecordDO::getUserId, userId)
            .orderByDesc(ChangeMobileRecordDO::getCreateTime)
            .last("LIMIT 1"));
    }

}
