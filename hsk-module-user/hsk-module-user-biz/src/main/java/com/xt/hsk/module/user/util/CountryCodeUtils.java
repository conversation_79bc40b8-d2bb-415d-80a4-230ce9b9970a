package com.xt.hsk.module.user.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StringUtils;

/**
 * 国家电话区号工具类 数据来源：https://github.com/BG7ZAG/country-code/blob/master/country-code.json
 *
 * <AUTHOR>
 * @since 2025/06/10
 */
@Slf4j
public class CountryCodeUtils {

    private static final Map<String, String> COUNTRY_CODE_MAP = new HashMap<>();
    private static final String COUNTRY_CODE_FILE = "country-code.json";

    static {
        // 从JSON文件加载完整的国家区号数据
        try {
            loadFromJson();
        } catch (Exception e) {
            log.error("加载国家区号JSON文件失败", e);
            // 如果加载失败，至少添加中国区号作为基本支持
            COUNTRY_CODE_MAP.put("86", "中国");
        }
    }

    /**
     * 从JSON文件加载国家区号数据
     */
    private static void loadFromJson() throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        ClassPathResource resource = new ClassPathResource(COUNTRY_CODE_FILE);

        try (InputStream inputStream = resource.getInputStream()) {
            List<Map<String, Object>> countryList = mapper.readValue(inputStream,
                new TypeReference<List<Map<String, Object>>>() {
                });

            for (Map<String, Object> country : countryList) {
                String code = (String) country.get("code");
                // 使用中文名称
                String name = (String) country.get("cn");

                if (StringUtils.hasText(code) && StringUtils.hasText(name)) {
                    // 去除code中的加号前缀，如"+86"变成"86"
                    if (code.startsWith("+")) {
                        code = code.substring(1);
                    }
                    COUNTRY_CODE_MAP.put(code, name);
                }
            }

            log.info("从JSON文件成功加载{}个国家区号数据", countryList.size());
        }
    }

    /**
     * 根据国际电话区号获取国家名称
     *
     * @param countryCode 国际电话区号
     * @return 国家名称，如果找不到则返回null
     */
    public static String getCountryByCode(String countryCode) {
        if (countryCode == null) {
            return null;
        }

        // 去除可能的加号前缀
        if (countryCode.startsWith("+")) {
            countryCode = countryCode.substring(1);
        }

        return COUNTRY_CODE_MAP.get(countryCode);
    }

    /**
     * 根据国际电话区号获取国家名称
     *
     * @param countryCode    国际电话区号
     * @param defaultCountry 默认国家，当找不到匹配时返回
     * @return 国家名称，如果找不到则返回默认国家
     */
    public static String getCountryByCode(String countryCode, String defaultCountry) {
        String country = getCountryByCode(countryCode);
        return country != null ? country : defaultCountry;
    }

    /**
     * 检查区号存不存在
     */
    public static boolean checkCountryCode(String countryCode) {
        return COUNTRY_CODE_MAP.containsKey(countryCode);
    }
} 