package com.xt.hsk.module.user.manager.favorite;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.i18n.LanguageUtils;
import com.xt.hsk.module.edu.api.question.ChapterApi;
import com.xt.hsk.module.edu.api.question.QuestionTypeApi;
import com.xt.hsk.module.edu.api.question.TextbookApi;
import com.xt.hsk.module.edu.api.question.dto.ChapterDTO;
import com.xt.hsk.module.edu.api.question.dto.QuestionTypeDTO;
import com.xt.hsk.module.edu.api.question.dto.TextbookDTO;
import com.xt.hsk.module.edu.api.word.WordApi;
import com.xt.hsk.module.edu.api.word.dto.WordRespDTO;
import com.xt.hsk.module.user.controller.app.favorite.vo.*;
import com.xt.hsk.module.user.enums.FavoriteTypeEnum;
import com.xt.hsk.module.user.service.favorite.UserFavoriteService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * APP - 收藏夹
 *
 * <AUTHOR>
 * @since 2025/07/23
 */
@Slf4j
@Component
public class UserFavoriteAppManager {

    @Resource
    private UserFavoriteService userFavoriteService;

    @Resource
    private TextbookApi textbookApi;

    @Resource
    private ChapterApi chapterApi;

    @Resource
    private QuestionTypeApi questionTypeApi;

    @Resource
    private WordApi wordApi;

    /**
     * 获取用户收藏的小游戏题目列表
     *
     * @param reqVO 分页参数
     * @return 结果列表
     */
    public PageResult<FavoriteGameQuestionsAppPageRespVO> favoriteGameList(@Valid GameFavoriteAppPageReqVO reqVO) {
        reqVO.setUserId(StpUtil.getLoginIdAsLong());
        return userFavoriteService.favoriteGameList(reqVO);
    }

    /**
     * 收藏/取消收藏
     *
     * @param reqVO 请求参数
     * @return 操作结果
     */
    public Boolean toggleFavorite(UserFavoriteToggleReqVO reqVO) {

        Long userId = StpUtil.getLoginIdAsLong();

        List<UserFavoriteTargetVo> targetList = reqVO.getTargetList();
        if (CollUtil.isEmpty(targetList)) {
            log.warn("收藏/取消收藏失败:目标ID列表为空,用户ID:{},操作类型:{}", userId, reqVO.getIsFavorite());
            return false;
        }

        if (reqVO.getFavoriteType() == null) {
            log.warn("收藏/取消收藏失败:收藏类型为空,用户ID:{},操作类型:{}", userId, reqVO.getIsFavorite());
            return false;
        }

        if (reqVO.getFavoriteSource() == null) {
            log.warn("收藏/取消收藏失败:收藏来源为空,用户ID:{},操作类型:{}", userId, reqVO.getIsFavorite());
            return false;
        }
        List<Long> targetIdList = targetList.stream().map(UserFavoriteTargetVo::getTargetId).toList();
        if (Boolean.TRUE.equals(reqVO.getIsFavorite())) {
            // 收藏操作
            if (FavoriteTypeEnum.MINI_GAME_QUESTION.getCode().equals(reqVO.getFavoriteType())
                    && reqVO.getGameId() != null) {
                // 小游戏题目收藏
                return userFavoriteService.addGameQuestionBatch(userId, reqVO.getFavoriteSource(),
                        targetIdList, reqVO.getGameId(), reqVO.getIsWrongQuestion());
            } else if (FavoriteTypeEnum.REAL_QUESTION.getCode().equals(reqVO.getFavoriteType())) {
                // 真题练习的收藏
                return userFavoriteService.addRealQuestionBatch(userId, reqVO.getFavoriteSource(),
                        targetList, reqVO.getIsWrongQuestion());

            } else {
                // 普通收藏
                return userFavoriteService.addBatch(userId, reqVO.getFavoriteType(),
                        reqVO.getFavoriteSource(), targetIdList, reqVO.getIsWrongQuestion());
            }
        } else if (Boolean.FALSE.equals(reqVO.getIsFavorite())) {
            // 取消收藏
            int result = userFavoriteService.cancelBatch(userId, reqVO.getFavoriteType(), targetIdList);
            return result > 0;
        }

        return false;
    }

    /**
     * 获取用户收藏的真题题目列表
     *
     * @param reqVO 查询条件
     * @return 结果
     */
    public List<FavoriteQuestionAppPageRespVO> favoriteQuestionList(FavoriteQuestionAppPageReqVO reqVO) {
        // 参数校验
        if (reqVO.getHskLevel() == null || reqVO.getSubject() == null) {
            return Collections.emptyList();
        }

        reqVO.setUserId(StpUtil.getLoginIdAsLong());
        List<FavoriteQuestionPageRespVO> respVOList = userFavoriteService.favoriteQuestionList(reqVO);

        if (CollUtil.isEmpty(respVOList)) {
            return Collections.emptyList();
        }

        // 批量获取关联数据
        Map<Long, TextbookDTO> textbookMap = batchGetTextbooks(respVOList);
        Map<Long, ChapterDTO> chapterMap = batchGetChapters(respVOList);
        Map<Long, QuestionTypeDTO> questionTypeMap = batchGetQuestionTypes(respVOList);

        // 填充关联数据
        enrichFavoriteQuestions(respVOList, textbookMap, chapterMap);

        // 按题型和排序分组并构建结果
        return buildGroupedResponse(respVOList, questionTypeMap);
    }

    /**
     * 批量获取教材信息
     */
    private Map<Long, TextbookDTO> batchGetTextbooks(List<FavoriteQuestionPageRespVO> respVOList) {
        Set<Long> textbookIds = respVOList.stream()
                .map(FavoriteQuestionPageRespVO::getTextbookId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (textbookIds.isEmpty()) {
            return Collections.emptyMap();
        }

        return textbookApi.getByIds(new ArrayList<>(textbookIds)).stream()
                .collect(Collectors.toMap(TextbookDTO::getId, Function.identity()));
    }

    /**
     * 批量获取章节信息
     */
    private Map<Long, ChapterDTO> batchGetChapters(List<FavoriteQuestionPageRespVO> respVOList) {
        Set<Long> chapterIds = respVOList.stream()
                .map(FavoriteQuestionPageRespVO::getChapterId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (chapterIds.isEmpty()) {
            return Collections.emptyMap();
        }

        return chapterApi.getByIds(new ArrayList<>(chapterIds)).stream()
                .collect(Collectors.toMap(ChapterDTO::getId, Function.identity()));
    }

    /**
     * 批量获取题型信息
     */
    private Map<Long, QuestionTypeDTO> batchGetQuestionTypes(List<FavoriteQuestionPageRespVO> respVOList) {
        Set<Long> questionTypeIds = respVOList.stream()
                .map(FavoriteQuestionPageRespVO::getQuestionTypeId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (questionTypeIds.isEmpty()) {
            return Collections.emptyMap();
        }

        return questionTypeApi.getByIds(new ArrayList<>(questionTypeIds)).stream()
                .collect(Collectors.toMap(QuestionTypeDTO::getId, Function.identity()));
    }

    /**
     * 填充收藏题目的关联数据
     */
    private void enrichFavoriteQuestions(List<FavoriteQuestionPageRespVO> respVOList,
                                         Map<Long, TextbookDTO> textbookMap,
                                         Map<Long, ChapterDTO> chapterMap) {
        respVOList.forEach(item -> {
            // 填充教材名称
            Optional.ofNullable(textbookMap.get(item.getTextbookId()))
                    .ifPresent(textbook -> item.setTextbookName(
                            LanguageUtils.getLocalizedValue(
                                    textbook.getNameCn(),
                                    textbook.getNameEn(),
                                    textbook.getNameOt()
                            )
                    ));

            // 填充章节名称
            Optional.ofNullable(chapterMap.get(item.getChapterId()))
                    .ifPresent(chapter -> item.setChapterName(
                            LanguageUtils.getLocalizedValue(
                                    chapter.getChapterNameCn(),
                                    chapter.getChapterNameEn(),
                                    chapter.getChapterNameOt()
                            )
                    ));
        });
    }

    /**
     * 构建分组响应结果
     */
    private List<FavoriteQuestionAppPageRespVO> buildGroupedResponse(List<FavoriteQuestionPageRespVO> respVOList,
                                                                     Map<Long, QuestionTypeDTO> questionTypeMap) {

        return respVOList.stream()
                .collect(Collectors.groupingBy(item -> item.getQuestionTypeId() + "-" + item.getSort()))
                .values()
                .stream()
                .map(voList -> buildFavoriteQuestionGroup(voList, questionTypeMap))
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(FavoriteQuestionAppPageRespVO::getSort))
                .toList();
    }

    /**
     * 构建单个题型分组
     */
    private FavoriteQuestionAppPageRespVO buildFavoriteQuestionGroup(List<FavoriteQuestionPageRespVO> questionList,
                                                                     Map<Long, QuestionTypeDTO> questionTypeMap) {
        if (CollUtil.isEmpty(questionList)) {
            return null;
        }

        FavoriteQuestionPageRespVO firstQuestion = questionList.get(0);
        FavoriteQuestionAppPageRespVO groupVO = new FavoriteQuestionAppPageRespVO();

        groupVO.setSort(firstQuestion.getSort());
        groupVO.setQuestionTypeId(firstQuestion.getQuestionTypeId());
        groupVO.setFavoriteQuestionList(questionList);

        // 设置题型名称
        Optional.ofNullable(questionTypeMap.get(firstQuestion.getQuestionTypeId()))
                .ifPresent(questionType -> groupVO.setQuestionTypeName(
                        LanguageUtils.getLocalizedValue(
                                questionType.getNameCn(),
                                questionType.getNameEn(),
                                questionType.getNameOt()
                        )
                ));

        return groupVO;
    }

    /**
     * 获取用户收藏的单词列表
     *
     * @param reqVO 查询条件
     * @return 结果
     */

    public PageResult<FavoriteWordAppPageRespVO> wordBookList(FavoriteWordAppPageReqVO reqVO) {
        reqVO.setUserId(StpUtil.getLoginIdAsLong());
        PageResult<FavoriteWordAppPageRespVO> page = userFavoriteService.wordBookList(reqVO);

        List<FavoriteWordAppPageRespVO> pageList = page.getList();

        if (CollUtil.isEmpty(pageList)) {
            return PageResult.empty();
        }

        // 设置翻译
        setTranslationOt(pageList);

        return new PageResult<>(pageList, page.getTotal());
    }

    /**
     * 设置翻译
     *
     * @param pageList 页面列表
     */
    private void setTranslationOt(List<FavoriteWordAppPageRespVO> pageList) {
        if (CollUtil.isEmpty(pageList)) {
            return;
        }

        List<Long> wordIdList = pageList.stream()
                .map(FavoriteWordAppPageRespVO::getWordId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        Map<Long, List<String>> map = wordApi.listByWordIdList(wordIdList).stream()
                .collect(Collectors.toMap(WordRespDTO::getId, WordRespDTO::getTranslationOts));

        pageList.forEach(item -> item.setTranslationOts(map.getOrDefault(item.getWordId(), Collections.emptyList())));

    }
}
