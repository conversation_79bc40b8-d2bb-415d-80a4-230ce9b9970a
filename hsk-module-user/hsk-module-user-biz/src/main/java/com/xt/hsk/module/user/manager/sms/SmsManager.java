package com.xt.hsk.module.user.manager.sms;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.system.enums.ErrorCodeConstants.AUTH_REGISTER_CAPTCHA_CODE_ERROR;
import static com.xt.hsk.module.user.enums.ErrorCodeConstants.USER_NOT_EXISTS;

import cn.dev33.satoken.stp.StpUtil;
import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.xt.hsk.framework.common.exception.ServiceException;
import com.xt.hsk.framework.common.util.servlet.ServletUtils;
import com.xt.hsk.framework.common.util.validation.ValidationUtils;
import com.xt.hsk.module.system.api.captcha.CaptchaApi;
import com.xt.hsk.module.system.api.sms.SmsCodeApi;
import com.xt.hsk.module.system.api.sms.dto.code.SmsCodeSendReqDTO;
import com.xt.hsk.module.system.enums.sms.SmsSceneEnum;
import com.xt.hsk.module.user.controller.app.sms.vo.AppAuthLoginReqVO;
import com.xt.hsk.module.user.controller.app.sms.vo.CaptchaVerificationReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.SendChangeMobileCodeReqVO;
import com.xt.hsk.module.user.dal.dataobject.user.UserDO;
import com.xt.hsk.module.user.service.user.UserService;
import jakarta.annotation.Resource;
import jakarta.validation.Validator;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 短信管理器
 *
 * <AUTHOR>
 * @since 2025/05/30
 */
@Slf4j
@Component
public class SmsManager {

    @Resource
    private SmsCodeApi smsCodeApi;

    @Resource
    private Validator validator;

    @Resource
    private CaptchaApi captchaApi;

    @Resource
    private UserService userService;

    /**
     * 验证码的开关，默认为 true
     */
    @Value("${hsk.captcha.enable:true}")
    @Setter
    private Boolean captchaEnable;

    /**
     * 发送登录短信验证码
     *
     * @return 发送结果
     */
    public Boolean sendLoginSmsCode(AppAuthLoginReqVO reqVO) {
        log.info("发送登录短信验证码请求: mobile={}, countryCode={}", reqVO.getMobile(),
            reqVO.getCountryCode());

        try {
            String countryCode = reqVO.getCountryCode();
            String mobile = reqVO.getMobile();

            // 校验图形验证码
            ResponseModel responseModel = doValidateCaptcha(reqVO);
            // 验证不通过
            if (!responseModel.isSuccess()) {
                throw exception(AUTH_REGISTER_CAPTCHA_CODE_ERROR, responseModel.getRepMsg());
            }

            // 2. 发送短信验证码
            smsCodeApi.sendSmsCode(new SmsCodeSendReqDTO()
                .setMobile(mobile)
                .setCountryCode(countryCode)
                // !!!!!注意!!!!! 这里虽然选择了场景值 但是同场景值会有多个验证码模板 会在下钻方法中重新判断
                .setScene(SmsSceneEnum.MEMBER_LOGIN_CN.getScene())
                .setCreateIp(ServletUtils.getClientIP()));


            log.info("登录短信验证码发送成功: mobile={}:{}", countryCode, mobile);
            return true;

        } catch (ServiceException e) {
            log.error("发送登录短信验证码失败: mobile={}:{}, error={}", reqVO.getCountryCode(),
                reqVO.getMobile(),
                e.getMessage(), e);
            // 如果是业务异常，直接抛出让全局异常处理器处理
            throw e;
        } catch (Exception e) {
            log.error("发送登录短信验证码失败: mobile={}:{}, error={}", reqVO.getCountryCode(),
                reqVO.getMobile(),
                e.getMessage(), e);
            return false;
        }
    }

    private ResponseModel doValidateCaptcha(CaptchaVerificationReqVO reqVO) {
        // 如果验证码关闭，则不进行校验
        if (Boolean.FALSE.equals(captchaEnable)) {
            return ResponseModel.success();
        }
        ValidationUtils.validate(validator, reqVO, CaptchaVerificationReqVO.CodeEnableGroup.class);
        CaptchaVO captchaVO = new CaptchaVO();
        captchaVO.setCaptchaVerification(reqVO.getCaptchaVerification());
        return captchaApi.verification(captchaVO);
    }

    public Boolean sendResetPasswordSmsCode(AppAuthLoginReqVO reqVO) {
        log.info("发送忘记密码验证码请求: mobile={}, countryCode={}", reqVO.getMobile(),
            reqVO.getCountryCode());

        try {
            String countryCode = reqVO.getCountryCode();
            String mobile = reqVO.getMobile();


            // 校验图形验证码
            ResponseModel responseModel = doValidateCaptcha(reqVO);
            // 验证不通过
            if (!responseModel.isSuccess()) {
                throw exception(AUTH_REGISTER_CAPTCHA_CODE_ERROR, responseModel.getRepMsg());
            }

            // 2. 发送短信验证码
            smsCodeApi.sendSmsCode(new SmsCodeSendReqDTO()
                .setMobile(mobile)
                .setCountryCode(countryCode)
                // !!!!!注意!!!!! 这里虽然选择了场景值 但是同场景值会有多个验证码模板 会在下钻方法中重新判断
                .setScene(SmsSceneEnum.MEMBER_RESET_PASSWORD_CN.getScene())
                .setCreateIp(ServletUtils.getClientIP()));

            log.info("发送忘记密码短信验证码发送成功: mobile={}:{}", countryCode, mobile);
            return true;

        } catch (ServiceException e) {
            log.error("发送登录短信验证码失败: mobile={}:{}, error={}", reqVO.getCountryCode(),
                reqVO.getMobile(),
                e.getMessage(), e);
            // 如果是业务异常，直接抛出让全局异常处理器处理
            throw e;
        } catch (Exception e) {
            log.error("发送登录短信验证码失败: mobile={}:{}, error={}", reqVO.getCountryCode(),
                reqVO.getMobile(),
                e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 发送更换手机号验证码
     * 根据token获取当前用户手机号并发送验证码
     *
     * @param reqVO 请求参数
     * @return 发送结果
     */
    public Boolean sendChangeMobileCode(SendChangeMobileCodeReqVO reqVO) {
        log.info("发送更换手机号验证码请求");

        try {

            Long userId = StpUtil.getLoginIdAsLong();
            UserDO user = userService.getById(userId);
            if (user == null) {
                throw exception(USER_NOT_EXISTS);
            }

            String countryCode = user.getCountryCode();
            String mobile = user.getMobile();

            // 2. 校验图形验证码
            ResponseModel responseModel = doValidateCaptcha(reqVO);
            // 验证不通过
            if (!responseModel.isSuccess()) {
                throw exception(AUTH_REGISTER_CAPTCHA_CODE_ERROR, responseModel.getRepMsg());
            }

            // 3. 发送短信验证码
            smsCodeApi.sendSmsCode(new SmsCodeSendReqDTO()
                .setMobile(mobile)
                .setCountryCode(countryCode)
                // 使用更换手机号场景
                .setScene(SmsSceneEnum.MEMBER_UPDATE_MOBILE_CN.getScene())
                .setCreateIp(ServletUtils.getClientIP()));

            log.info("更换手机号验证码发送成功: mobile={}:{}", countryCode, mobile);
            return true;

        } catch (ServiceException e) {
            log.error("发送更换手机号验证码失败: error={}", e.getMessage(), e);
            // 如果是业务异常，直接抛出让全局异常处理器处理
            throw e;
        } catch (Exception e) {
            log.error("发送更换手机号验证码失败: error={}", e.getMessage(), e);
            return false;
        }
    }
}
