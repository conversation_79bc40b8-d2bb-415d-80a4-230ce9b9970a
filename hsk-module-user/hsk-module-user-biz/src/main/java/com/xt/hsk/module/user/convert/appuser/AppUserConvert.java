package com.xt.hsk.module.user.convert.appuser;

import com.xt.hsk.module.user.api.dto.AppUserRespDTO;
import com.xt.hsk.module.user.controller.admin.user.vo.UserAdminRespVO;
import com.xt.hsk.module.user.controller.admin.user.vo.UserUpdateReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.UserLoginRespVO;
import com.xt.hsk.module.user.controller.app.user.vo.UserSetInfoReqVO;
import com.xt.hsk.module.user.dal.dataobject.user.UserDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 用户 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AppUserConvert {

    AppUserConvert INSTANCE = Mappers.getMapper(AppUserConvert.class);

    UserDO doToRespVO(UserUpdateReqVO bean);

    UserLoginRespVO convertToLoginResp(UserDO bean);

    UserDO doToRespVO(UserSetInfoReqVO bean);

    List<UserAdminRespVO> convertList2(List<UserDO> list);

    UserAdminRespVO doToRespVO(UserDO userDO);

    /**
     * 用户DO转换为API DTO
     */
    AppUserRespDTO convert(UserDO bean);

    /**
     * 用户DO列表转换为API DTO列表
     */
    List<AppUserRespDTO> convertList(List<UserDO> list);
} 