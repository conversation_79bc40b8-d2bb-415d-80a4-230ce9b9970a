package com.xt.hsk.module.user.api;

import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.user.controller.app.favorite.vo.UserFavoriteTargetVo;
import com.xt.hsk.module.user.dal.dataobject.favorite.UserFavoriteDO;
import com.xt.hsk.module.user.favorite.UserFavoriteApi;
import com.xt.hsk.module.user.favorite.dto.UserFavoriteTargetDto;
import com.xt.hsk.module.user.service.favorite.UserFavoriteService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 用户收藏 API 实现
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Service
public class UserFavoriteApiImpl implements UserFavoriteApi {

    @Resource
    private UserFavoriteService userFavoriteService;

    /**
     * 检查用户是否收藏了指定的目标
     *
     * @param userId       用户ID
     * @param targetId     目标ID
     * @param favoriteType 收藏类型
     * @return 是否已收藏
     */
    @Override
    public Boolean checkUserFavoriteStatus(Long userId, Long targetId, Integer favoriteType) {
        return userFavoriteService.lambdaQuery()
                .eq(UserFavoriteDO::getUserId, userId)
                .eq(UserFavoriteDO::getTargetId, targetId)
                .eq(UserFavoriteDO::getFavoriteType, favoriteType)
                .exists();
    }

    /**
     * 批量收藏真题题目
     *
     * @param userId          用户ID
     * @param favoriteSource  收藏类型
     * @param targetList      目标ID列表
     * @param isWrongQuestion 是否是错题
     * @return
     */
    @Override
    public Boolean addRealQuestionBatch(Long userId, Integer favoriteSource, List<UserFavoriteTargetDto> targetList, Boolean isWrongQuestion) {

        List<UserFavoriteTargetVo> favoriteTargetVos = BeanUtils.toBean(targetList, UserFavoriteTargetVo.class);

        return userFavoriteService.addRealQuestionBatch(userId, favoriteSource, favoriteTargetVos, isWrongQuestion);
    }

    /**
     * 根据目标ID列表查询收藏状态
     *
     * @param userId       用户ID
     * @param targetIdList 目标ID列表
     * @param favoriteType 收藏类型
     * @return map<目标ID, 收藏状态>
     */
    @Override
    public Map<Long, Boolean> selectStatus(Long userId, List<Long> targetIdList, Integer favoriteType) {
        return userFavoriteService.selectStatus(userId, targetIdList, favoriteType);
    }

}