package com.xt.hsk.module.user.api;

import com.xt.hsk.module.user.dal.dataobject.favorite.UserFavoriteDO;
import com.xt.hsk.module.user.favorite.UserFavoriteApi;
import com.xt.hsk.module.user.service.favorite.UserFavoriteService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 用户收藏 API 实现
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Service
public class UserFavoriteApiImpl implements UserFavoriteApi {

    @Resource
    private UserFavoriteService userFavoriteService;

    /**
     * 检查用户是否收藏了指定的目标
     *
     * @param userId       用户ID
     * @param targetId     目标ID
     * @param favoriteType 收藏类型
     * @return 是否已收藏
     */
    @Override
    public Boolean checkUserFavoriteStatus(Long userId, Long targetId, Integer favoriteType) {
        return userFavoriteService.lambdaQuery()
                .eq(UserFavoriteDO::getUserId, userId)
                .eq(UserFavoriteDO::getTargetId, targetId)
                .eq(UserFavoriteDO::getFavoriteType, favoriteType)
                .exists();
    }

}