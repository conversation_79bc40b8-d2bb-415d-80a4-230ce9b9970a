package com.xt.hsk.module.user.dto;

import com.xt.hsk.module.user.enums.ChangeMobileState;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 修改手机号会话信息
 *
 * <AUTHOR>
 * @since 2025/07/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChangeMobileSession {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 当前状态
     */
    private ChangeMobileState state;

    /**
     * 原手机号
     */
    private String oldMobile;

    /**
     * 原手机号区号
     */
    private String oldCountryCode;

    /**
     * 旧手机号是否已验证
     */
    private Boolean oldMobileVerified;

    /**
     * 旧手机号验证时间
     */
    private LocalDateTime oldMobileVerifiedTime;

    /**
     * 新手机号
     */
    private String newMobile;

    /**
     * 新手机号区号
     */
    private String newCountryCode;

    /**
     * 新手机号验证码是否已发送
     */
    private Boolean newMobileCodeSent;

    /**
     * 新手机号验证码发送时间
     */
    private LocalDateTime newMobileCodeSentTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 重试次数
     */
    private Integer retryCount;

}
