package com.xt.hsk.module.user.service.favorite;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.user.controller.app.favorite.vo.*;
import com.xt.hsk.module.user.dal.dataobject.favorite.UserFavoriteDO;
import com.xt.hsk.module.user.dal.mysql.favorite.UserFavoriteMapper;
import com.xt.hsk.module.user.enums.FavoriteTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户收藏服务 impl
 *
 * <AUTHOR>
 * @since 2025/07/22
 */
@Slf4j
@Service
@Validated
public class UserFavoriteServiceImpl extends
    ServiceImpl<UserFavoriteMapper, UserFavoriteDO> implements UserFavoriteService {

    @Resource
    private UserFavoriteMapper userFavoriteMapper;

    @Override
    public PageResult<FavoriteGameQuestionsAppPageRespVO> favoriteGameList(
        GameFavoriteAppPageReqVO reqVO) {
        // 创建分页对象
        Page<FavoriteGameQuestionsAppPageRespVO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());

        // 执行分页查询
        Page<FavoriteGameQuestionsAppPageRespVO> resultPage = userFavoriteMapper.selectFavoriteGameQuestionsPage(page, reqVO);

        // 转换为PageResult
        return new PageResult<>(resultPage.getRecords(), resultPage.getTotal());
    }


    @Override
    public PageResult<UserFavoriteDO> selectPage(UserFavoritePageReqVO pageReqVO) {

        return userFavoriteMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(name = "userFavoriteAdd", keys = {"#userId", "#favoriteType"})
    public int add(Long userId, Integer favoriteType, Integer favoriteSource, Long targetId,Boolean isWrongQuestion) {

        // 先查询是否已收藏
        LambdaQueryWrapperX<UserFavoriteDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(UserFavoriteDO::getUserId, userId)
            .eq(UserFavoriteDO::getFavoriteType, favoriteType)
            .eq(UserFavoriteDO::getFavoriteSource, favoriteSource)
            .eq(UserFavoriteDO::getTargetId, targetId);
        if (userFavoriteMapper.selectCount(queryWrapper) > 0) {
            log.warn(
                "用户已将此项目加入收藏夹,userId = {},favoriteType = {},favoriteSource = {},targetId = {}",
                userId, favoriteType, favoriteSource, targetId);
            return 0;
        }

        UserFavoriteDO userFavoriteDO = new UserFavoriteDO();
        userFavoriteDO.setUserId(userId);
        userFavoriteDO.setFavoriteType(favoriteType);
        userFavoriteDO.setFavoriteSource(favoriteSource);
        userFavoriteDO.setTargetId(targetId);
        userFavoriteDO.setIsWrongQuestion(isWrongQuestion);
        userFavoriteDO.setFavoriteTime(LocalDateTime.now());
        return userFavoriteMapper.insert(userFavoriteDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancel(Long userId, Integer favoriteType, Long targetId) {
        LambdaQueryWrapperX<UserFavoriteDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(UserFavoriteDO::getUserId, userId)
            .eq(UserFavoriteDO::getFavoriteType, favoriteType)
            .eq(UserFavoriteDO::getTargetId, targetId);
        if (userFavoriteMapper.selectCount() == 0) {
            log.warn(
                "用户未收藏此项目,userId = {},favoriteType = {},targetId = {}",
                userId, favoriteType, targetId);
            return 0;
        }

        LambdaUpdateWrapper<UserFavoriteDO> updateWrapper = new LambdaUpdateWrapper<UserFavoriteDO>()
            .set(UserFavoriteDO::getCancelTime, LocalDateTime.now())
            .eq(UserFavoriteDO::getUserId, userId)
            .eq(UserFavoriteDO::getFavoriteType, favoriteType)
            .eq(UserFavoriteDO::getTargetId, targetId);
        userFavoriteMapper.update(updateWrapper);

        return userFavoriteMapper.delete(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(name = "userFavoriteAddBatch", keys = {"#userId", "#favoriteType"})
    public Boolean addBatch(Long userId, Integer favoriteType, Integer favoriteSource,
        List<Long> targetIdList, Boolean isWrongQuestion) {
        return batchFavorite(userId, favoriteSource, favoriteType, targetIdList, null,
            isWrongQuestion);
    }

    private Boolean batchFavorite(Long userId, Integer favoriteSource,Integer favoriteType,
        List<Long> targetIdList, Long gameId, Boolean isWrongQuestion) {

        // 防止重复添加 先查询
        LambdaQueryWrapperX<UserFavoriteDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(UserFavoriteDO::getUserId, userId)
            .select(UserFavoriteDO::getTargetId)
            .eq(UserFavoriteDO::getFavoriteType, favoriteType)
            .in(UserFavoriteDO::getTargetId, targetIdList);
        // 剔除掉已存在的 防止重复收藏
        targetIdList.removeAll(
            userFavoriteMapper.selectList(queryWrapper).stream().map(UserFavoriteDO::getTargetId)
                .toList());
        if (targetIdList.isEmpty()) {
            return false;
        }

        // 构建批量插入数据
        List<UserFavoriteDO> userFavoriteDOList = targetIdList.stream()
            .map(targetId -> {
                UserFavoriteDO userFavoriteDO = new UserFavoriteDO();
                userFavoriteDO.setUserId(userId);
                userFavoriteDO.setFavoriteType(favoriteType);
                userFavoriteDO.setFavoriteSource(favoriteSource);
                userFavoriteDO.setTargetId(targetId);
                userFavoriteDO.setGameId(gameId);
                userFavoriteDO.setIsWrongQuestion(isWrongQuestion);
                userFavoriteDO.setFavoriteTime(LocalDateTime.now());
                return userFavoriteDO;
            })
            .toList();
        return userFavoriteMapper.insertBatch(userFavoriteDOList);
    }

    @Override
    public Boolean addGameQuestionBatch(Long userId, Integer favoriteSource,
        List<Long> targetIdList, Long gameId, Boolean isWrongQuestion) {
        return batchFavorite(userId,
            favoriteSource,
            FavoriteTypeEnum.MINI_GAME_QUESTION.getCode(),
            targetIdList,
            gameId,
            isWrongQuestion);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancelBatch(Long userId, Integer favoriteType,
        List<Long> targetIdList) {
        LambdaQueryWrapperX<UserFavoriteDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(UserFavoriteDO::getUserId, userId)
            .eq(UserFavoriteDO::getFavoriteType, favoriteType)
            .in(UserFavoriteDO::getTargetId, targetIdList);
        if (userFavoriteMapper.selectCount(queryWrapper) == 0) {
            log.warn("用户未收藏此项目,userId = {},favoriteType = {},targetIdList = {}",
                userId, favoriteType, targetIdList);
        }
        LambdaUpdateWrapper<UserFavoriteDO> updateWrapper = new LambdaUpdateWrapper<UserFavoriteDO>()
            .set(UserFavoriteDO::getCancelTime, LocalDateTime.now())
            .eq(UserFavoriteDO::getUserId, userId)
            .eq(UserFavoriteDO::getFavoriteType, favoriteType)
            .in(UserFavoriteDO::getTargetId, targetIdList);
        userFavoriteMapper.update(updateWrapper);
        return userFavoriteMapper.delete(queryWrapper);
    }

    @Override
    public Map<Long, Boolean> selectStatus(Long userId, List<Long> targetIdList,
        Integer favoriteType) {
        if (CollUtil.isEmpty(targetIdList) || favoriteType == null || userId == null) {
            return Map.of();
        }

        // 构建查询条件
        LambdaQueryWrapperX<UserFavoriteDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(UserFavoriteDO::getUserId, userId)
            .eq(UserFavoriteDO::getFavoriteType, favoriteType)
            .in(UserFavoriteDO::getTargetId, targetIdList);

        // 获取已收藏的targetId集合
        Set<Long> favoriteTargetIds = userFavoriteMapper.selectList(queryWrapper)
            .stream()
            .map(UserFavoriteDO::getTargetId)
            .collect(Collectors.toSet());

        // 为所有targetId设置收藏状态
        return targetIdList.stream()
            .collect(Collectors.toMap(
                targetId -> targetId,
                favoriteTargetIds::contains
            ));
    }

    /**
     * 收藏题目列表
     *
     * @param reqVO 分页参数
     * @return 结果列表
     */
    @Override
    public List<FavoriteQuestionPageRespVO> favoriteQuestionList(FavoriteQuestionAppPageReqVO reqVO) {
        return userFavoriteMapper.favoriteQuestionList(reqVO);
    }

    /**
     * 分页查询收藏的单词列表
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    @Override
    public PageResult<FavoriteWordAppPageRespVO> wordBookList(FavoriteWordAppPageReqVO reqVO) {

        // 创建分页对象
        Page<FavoriteWordAppPageRespVO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());

        // 执行分页查询
        Page<FavoriteWordAppPageRespVO> resultPage = userFavoriteMapper.wordBookList(page, reqVO);

        // 转换为PageResult
        return new PageResult<>(resultPage.getRecords(), resultPage.getTotal());
    }

    @Override
    public Boolean addRealQuestionBatch(Long userId, Integer favoriteSource, List<UserFavoriteTargetVo> targetList, Boolean isWrongQuestion) {
        if (CollUtil.isEmpty(targetList)) {
            return false;
        }
        // 防止重复添加 先查询
//        userFavoriteMapper.

        return true;
    }

}
