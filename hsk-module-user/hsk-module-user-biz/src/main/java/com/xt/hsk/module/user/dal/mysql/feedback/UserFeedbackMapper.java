package com.xt.hsk.module.user.dal.mysql.feedback;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.module.user.controller.admin.feedback.dto.UserFeedbackWithUserDTO;
import com.xt.hsk.module.user.controller.admin.feedback.vo.UserFeedbackPageReqVO;
import com.xt.hsk.module.user.dal.dataobject.feedback.UserFeedbackDO;
import com.xt.hsk.module.user.dal.dataobject.user.UserDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户反馈 Mapper
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Mapper
public interface UserFeedbackMapper extends BaseMapperX<UserFeedbackDO> {

    /**
     * 使用MyBatis Plus Join查询用户反馈（带分页）
     *
     * @param reqVO 查询条件
     * @return 包含用户信息的反馈列表（带分页信息）
     */
    default Page<UserFeedbackWithUserDTO> selectJoinPage(UserFeedbackPageReqVO reqVO) {
        // 创建分页对象
        Page<UserFeedbackWithUserDTO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        
        // 创建查询构造器
        MPJLambdaWrapper<UserFeedbackDO> wrapper = buildJoinWrapper(reqVO);
        
        // 执行分页查询
        return selectJoinPage(page, UserFeedbackWithUserDTO.class, wrapper);
    }

    /**
     * 使用MyBatis Plus Join查询用户反馈
     *
     * @param reqVO 查询条件
     * @return 包含用户信息的反馈列表
     */
    default List<UserFeedbackWithUserDTO> selectJoinList(UserFeedbackPageReqVO reqVO) {
        // 创建查询构造器
        MPJLambdaWrapper<UserFeedbackDO> wrapper = buildJoinWrapper(reqVO);
        
        // 添加分页
        wrapper.last("LIMIT " + reqVO.getPageSize() * (reqVO.getPageNo() - 1) + ", " + reqVO.getPageSize());

        return selectJoinList(UserFeedbackWithUserDTO.class, wrapper);
    }

    /**
     * 构建联表查询条件
     */
    default MPJLambdaWrapper<UserFeedbackDO> buildJoinWrapper(UserFeedbackPageReqVO reqVO) {
        MPJLambdaWrapper<UserFeedbackDO> wrapper = new MPJLambdaWrapper<>();

        // 设置基本的反馈表字段
        wrapper.selectAs(UserFeedbackDO::getId, UserFeedbackWithUserDTO::getId)
            .selectAs(UserFeedbackDO::getUserId, UserFeedbackWithUserDTO::getUserId)
            .selectAs(UserFeedbackDO::getContent, UserFeedbackWithUserDTO::getContent)
            .selectAs(UserFeedbackDO::getCountryCode, UserFeedbackWithUserDTO::getCountryCode)
            .selectAs(UserFeedbackDO::getContactInfoType, UserFeedbackWithUserDTO::getContactInfoType)
            .selectAs(UserFeedbackDO::getContactInfo, UserFeedbackWithUserDTO::getContactInfo)
            .selectAs(UserFeedbackDO::getCreateTime, UserFeedbackWithUserDTO::getCreateTime)
            .selectAs(UserFeedbackDO::getOperateIp, UserFeedbackWithUserDTO::getOperateIp)
            .selectAs(UserDO::getNickname, UserFeedbackWithUserDTO::getNickname)
            .selectAs(UserDO::getMobile, UserFeedbackWithUserDTO::getMobile)
            .selectAs(UserDO::getCountryCode, UserFeedbackWithUserDTO::getUserCountryCode)
            .leftJoin(UserDO.class, UserDO::getId, UserFeedbackDO::getUserId);

        // 添加所有查询条件
        addQueryConditions(wrapper, reqVO);
        
        return wrapper;
    }

    /**
     * 添加查询条件
     */
    default void addQueryConditions(MPJLambdaWrapper<UserFeedbackDO> wrapper, UserFeedbackPageReqVO reqVO) {
        // 添加用户表过滤条件
        if (reqVO.getCountryCode() != null && !reqVO.getCountryCode().isEmpty()) {
            wrapper.eq(UserDO::getCountryCode, reqVO.getCountryCode());
        }
        if (reqVO.getMobile() != null && !reqVO.getMobile().isEmpty()) {
            wrapper.like(UserDO::getMobile, reqVO.getMobile());
        }
        if (reqVO.getNickname() != null && !reqVO.getNickname().isEmpty()) {
            wrapper.like(UserDO::getNickname, reqVO.getNickname());
        }

        // 添加反馈表基本查询条件
        wrapper.eq(UserFeedbackDO::getDeleted, false);
        if (reqVO.getUserId() != null) {
            wrapper.eq(UserFeedbackDO::getUserId, reqVO.getUserId());
        }
        if (reqVO.getContent() != null && !reqVO.getContent().isEmpty()) {
            wrapper.like(UserFeedbackDO::getContent, reqVO.getContent());
        }
        if (reqVO.getContactInfoType() != null) {
            wrapper.eq(UserFeedbackDO::getContactInfoType, reqVO.getContactInfoType());
        }
        if (reqVO.getContactInfo() != null && !reqVO.getContactInfo().isEmpty()) {
            wrapper.like(UserFeedbackDO::getContactInfo, reqVO.getContactInfo());
        }

        // 时间范围条件
        if (reqVO.getCreateTime() != null && reqVO.getCreateTime().length == 2) {
            wrapper.between(UserFeedbackDO::getCreateTime, reqVO.getCreateTime()[0],
                reqVO.getCreateTime()[1]);
        }

        // 排序
        wrapper.orderByDesc(UserFeedbackDO::getCreateTime);
    }

    /**
     * 判断是否需要添加用户表过滤条件
     *
     * @param reqVO 查询条件
     * @return 是否需要添加用户表过滤条件
     */
    default boolean isNeedUserTableFilter(UserFeedbackPageReqVO reqVO) {
        // 如果需要按照用户昵称、手机号或区号查询，则需要添加用户表过滤条件
        return (reqVO.getNickname() != null && !reqVO.getNickname().isEmpty()) ||
            (reqVO.getMobile() != null && !reqVO.getMobile().isEmpty()) ||
            (reqVO.getCountryCode() != null && !reqVO.getCountryCode().isEmpty());
    }

    /**
     * 使用MyBatis Plus Join查询单个反馈详情
     *
     * @param id 反馈ID
     * @return 包含用户信息的反馈详情
     */
    default UserFeedbackWithUserDTO selectJoinById(Long id) {
        MPJLambdaWrapper<UserFeedbackDO> wrapper = new MPJLambdaWrapper<UserFeedbackDO>()
            .selectAs(UserFeedbackDO::getId, UserFeedbackWithUserDTO::getId)
            .selectAs(UserFeedbackDO::getUserId, UserFeedbackWithUserDTO::getUserId)
            .selectAs(UserFeedbackDO::getContent, UserFeedbackWithUserDTO::getContent)
            .selectAs(UserFeedbackDO::getCountryCode, UserFeedbackWithUserDTO::getCountryCode)
            .selectAs(UserFeedbackDO::getContactInfoType,
                UserFeedbackWithUserDTO::getContactInfoType)
            .selectAs(UserFeedbackDO::getContactInfo, UserFeedbackWithUserDTO::getContactInfo)
            .selectAs(UserFeedbackDO::getCreateTime, UserFeedbackWithUserDTO::getCreateTime)
            .selectAs(UserFeedbackDO::getOperateIp, UserFeedbackWithUserDTO::getOperateIp)
            .selectAs(UserDO::getNickname, UserFeedbackWithUserDTO::getNickname)
            .selectAs(UserDO::getMobile, UserFeedbackWithUserDTO::getMobile)
            .selectAs(UserDO::getCountryCode, UserFeedbackWithUserDTO::getUserCountryCode)
            .leftJoin(UserDO.class, UserDO::getId, UserFeedbackDO::getUserId)
            .eq(UserFeedbackDO::getId, id)
            .eq(UserFeedbackDO::getDeleted, false);

        return selectJoinOne(UserFeedbackWithUserDTO.class, wrapper);
    }

    /**
     * 统计用户当天的反馈数量
     *
     * @param userId     用户ID
     * @param startOfDay 当天开始时间
     * @param endOfDay   当天结束时间
     * @return 反馈数量
     */
    default Long countUserTodayFeedbacks(Long userId, String startOfDay, String endOfDay) {
        LambdaQueryWrapper<UserFeedbackDO> wrapper = new LambdaQueryWrapper<UserFeedbackDO>()
            .eq(UserFeedbackDO::getUserId, userId)
            .between(UserFeedbackDO::getCreateTime, startOfDay, endOfDay);
        return selectCount(wrapper);
    }
} 