package com.xt.hsk.framework.common.pojo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class ImportResult implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 是否成功 只要有一条数据解析失败则为false
     */
    public boolean success = true;
    /**
     * 有效行数
     */
    public int validCount;

    /**
     * 无效行数
     */
    public int invalidCount;

    /**
     * 错误信息
     */
    public List<String> msg = new ArrayList<>();
}
