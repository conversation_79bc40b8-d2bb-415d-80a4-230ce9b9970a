package com.xt.hsk.framework.common.util.json.databind;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;

/**
 * 基于时间戳的 LocalDate 反序列化器
 *
 * <AUTHOR>
 */
public class TimestampLocalDateDeserializer extends JsonDeserializer<LocalDate> {

    public static final TimestampLocalDateDeserializer INSTANCE = new TimestampLocalDateDeserializer();

    @Override
    public LocalDate deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        long timestamp = p.getValueAsLong();
        if (timestamp == 0) {
            return null;
        }
        // 将 Long 时间戳，转换为 LocalDate 对象
        return LocalDate.ofInstant(Instant.ofEpochMilli(p.getValueAsLong()), ZoneId.systemDefault());
    }

}
