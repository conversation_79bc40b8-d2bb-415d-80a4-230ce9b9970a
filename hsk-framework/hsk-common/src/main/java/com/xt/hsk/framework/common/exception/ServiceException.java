package com.xt.hsk.framework.common.exception;

import cn.hutool.core.text.CharSequenceUtil;
import com.xt.hsk.framework.common.exception.enums.ServiceErrorCodeRange;
import com.xt.hsk.framework.common.util.i18n.MessageUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 业务逻辑异常 Exception
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
public final class ServiceException extends RuntimeException {

    /**
     * 业务错误码
     *
     * @see ServiceErrorCodeRange
     */
    private Integer code;

    /**
     * 错误提示
     */
    private String message;

    /**
     * 国际化后的错误提示
     */
    private String i18nMessage;

    /**
     * 国际化消息的key
     */
    private String i18nKey;

    /**
     * 空构造方法，避免反序列化问题
     */
    public ServiceException() {
    }

    /**
     * 创建业务异常
     *
     * @param errorCode 错误码
     */
    public ServiceException(ErrorCode errorCode) {
        this.code = errorCode.getCode();
        this.message = errorCode.getMsg();

        if (errorCode.getI18nKey() != null) {
            this.i18nKey = errorCode.getI18nKey();
            this.loadI18nMessage();
        }
    }

    /**
     * 创建业务异常
     *
     * @param errorCode 错误码
     * @param args      格式化参数
     */
    public ServiceException(ErrorCode errorCode, Object... args) {
        this.code = errorCode.getCode();
        this.message = CharSequenceUtil.format(errorCode.getMsg(), args);

        if (errorCode.getI18nKey() != null) {
            this.i18nKey = errorCode.getI18nKey();
            this.loadI18nMessage(args);
        }
    }

    /**
     * 创建业务异常（不支持国际化）
     *
     * @param code    错误码
     * @param message 错误信息
     */
    public ServiceException(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 创建业务异常（支持国际化）
     *
     * @param code    错误码
     * @param message 错误信息
     * @param i18nKey 国际化key
     */
    public ServiceException(Integer code, String message, String i18nKey) {
        this.code = code;
        this.message = message;
        this.i18nKey = i18nKey;
        this.loadI18nMessage();
    }

    /**
     * 创建业务异常（支持国际化和参数）
     *
     * @param code    错误码
     * @param message 错误信息
     * @param i18nKey 国际化key
     * @param args    国际化参数
     */
    public ServiceException(Integer code, String message, String i18nKey, Object... args) {
        this.code = code;
        this.message = message;
        this.i18nKey = i18nKey;
        this.loadI18nMessage(args);
    }

    public ServiceException setCode(Integer code) {
        this.code = code;
        return this;
    }

    @Override
    public String getMessage() {
        return i18nMessage != null ? i18nMessage : message;
    }

    @Override
    public String getLocalizedMessage() {
        return i18nMessage != null ? i18nMessage : message;
    }

    public ServiceException setMessage(String message) {
        this.message = message;
        return this;
    }

    /**
     * 设置国际化key，并加载国际化消息
     *
     * @param i18nKey 国际化key
     * @return this
     */
    public ServiceException setI18nKey(String i18nKey) {
        this.i18nKey = i18nKey;
        this.loadI18nMessage();
        return this;
    }

    /**
     * 设置国际化key，并加载带参数的国际化消息
     *
     * @param i18nKey 国际化key
     * @param args    国际化参数
     * @return this
     */
    public ServiceException setI18nKey(String i18nKey, Object... args) {
        this.i18nKey = i18nKey;
        this.loadI18nMessage(args);
        return this;
    }

    /**
     * 加载国际化消息
     */
    private void loadI18nMessage() {
        if (this.i18nKey != null) {
            this.i18nMessage = MessageUtils.message(this.i18nKey);
        }
    }

    /**
     * 加载带参数的国际化消息
     *
     * @param args 国际化参数
     */
    private void loadI18nMessage(Object... args) {
        if (this.i18nKey != null) {
            this.i18nMessage = MessageUtils.message(this.i18nKey, args);
        }
    }
}
