package com.xt.hsk.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 是或否枚举
 *
 * <AUTHOR>
 * @since 2025/05/30
 */
@Getter
@AllArgsConstructor
public enum IsEnum implements BasicEnum<Integer> {
    /**
     * 全局是或否枚举
     */
    YES(1, "是", true),
    NO(0, "否", false);

    private final Integer code;
    private final String desc;
    private final boolean is;
}
