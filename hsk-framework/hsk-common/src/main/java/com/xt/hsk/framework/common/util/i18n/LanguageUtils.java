package com.xt.hsk.framework.common.util.i18n;

import cn.hutool.core.util.StrUtil;
import com.xt.hsk.framework.common.util.servlet.ServletUtils;
import jakarta.servlet.http.HttpServletRequest;
import java.util.function.Supplier;

/**
 * 多语言工具类
 *
 * <AUTHOR>
 * @since 2025/06/09
 */
public class LanguageUtils {

    /**
     * 语言头部字段名
     */
    public static final String HEADER_LANGUAGE = "language";

    /**
     * 中文语言类型
     */
    public static final String LANGUAGE_CN = "zh-Hans";

    /**
     * 英文语言类型
     */
    public static final String LANGUAGE_EN = "en";

    /**
     * 越南语言类型
     */
    public static final String LANGUAGE_VI = "vi";

    /**
     * 默认语言类型
     */
    public static final String DEFAULT_LANGUAGE = LANGUAGE_CN;

    /**
     * 获取当前请求的语言类型
     *
     * @return 语言类型
     */
    public static String getLanguage() {
        HttpServletRequest request = ServletUtils.getRequest();
        if (request == null) {
            return DEFAULT_LANGUAGE;
        }
        String language = request.getHeader(HEADER_LANGUAGE);
        if (StrUtil.isBlank(language)) {
            return DEFAULT_LANGUAGE;
        }
        language = language.toLowerCase();
        if (LANGUAGE_CN.equals(language) || LANGUAGE_EN.equals(language) || LANGUAGE_VI.equals(language)) {
            return language;
        }
        return DEFAULT_LANGUAGE;
    }

    /**
     * 根据语言类型和字段前缀获取对应的语言字段值
     * 当目标语言值为空时，返回中文值作为备选
     *
     * @param cnFieldValue 中文字段值
     * @param enFieldValue 英文字段值
     * @param otFieldValue 其他语言字段值
     * @return 对应语言的字段值
     */
    public static String getLocalizedValue(String cnFieldValue, String enFieldValue, String otFieldValue) {
        String language = getLanguage();

        if (LANGUAGE_EN.equals(language)) {
            return StrUtil.isNotBlank(enFieldValue) ? enFieldValue : cnFieldValue;
        } else if (LANGUAGE_VI.equals(language)) {
            return StrUtil.isNotBlank(otFieldValue) ? otFieldValue : cnFieldValue;
        } else {
            return cnFieldValue;
        }
    }

    /**
     * 根据语言类型获取对应的语言字段值（使用方法引用确保类型安全）
     * 当目标语言值为空时，返回中文值作为备选
     *
     * @param cnFieldValue 中文字段值
     * @param enFieldValue 英文字段值
     * @param otFieldValue 其他语言字段值
     * @return 对应语言的字段值
     */
    public static <T> T getLocalizedValue(T cnFieldValue, T enFieldValue, T otFieldValue) {
        String language = getLanguage();

        if (LANGUAGE_EN.equals(language)) {
            return isEmptyValue(enFieldValue) ? cnFieldValue : enFieldValue;
        } else if (LANGUAGE_VI.equals(language)) {
            return isEmptyValue(otFieldValue) ? cnFieldValue : otFieldValue;
        } else {
            return cnFieldValue;
        }
    }

    /**
     * 根据语言类型获取对应的语言字段值（使用Supplier，支持懒加载）
     * 当目标语言值为空时，返回中文值作为备选
     * 适用于计算成本较高的场景
     */
    public static <T> T getLocalizedValueLazy(
            Supplier<T> cnFieldSupplier,
            Supplier<T> enFieldSupplier,
            Supplier<T> otFieldSupplier) {
        String language = getLanguage();

        if (LANGUAGE_EN.equals(language)) {
            T enValue = enFieldSupplier.get();
            return isEmptyValue(enValue) ? cnFieldSupplier.get() : enValue;
        } else if (LANGUAGE_VI.equals(language)) {
            T otValue = otFieldSupplier.get();
            return isEmptyValue(otValue) ? cnFieldSupplier.get() : otValue;
        } else {
            return cnFieldSupplier.get();
        }
    }

    /**
     * 判断值是否为空
     * 支持String、集合、数组等常见类型
     *
     * @param value 要检查的值
     * @return 是否为空
     */
    @SuppressWarnings("unchecked")
    private static <T> boolean isEmptyValue(T value) {
        if (value == null) {
            return true;
        }

        // 处理字符串
        if (value instanceof String) {
            return StrUtil.isBlank((String) value);
        }

        // 处理集合
        if (value instanceof java.util.Collection) {
            return ((java.util.Collection<Object>) value).isEmpty();
        }

        // 处理Map
        if (value instanceof java.util.Map) {
            return ((java.util.Map<Object, Object>) value).isEmpty();
        }

        // 处理数组
        if (value.getClass().isArray()) {
            return java.lang.reflect.Array.getLength(value) == 0;
        }

        // 其他类型视为非空
        return false;
    }
}