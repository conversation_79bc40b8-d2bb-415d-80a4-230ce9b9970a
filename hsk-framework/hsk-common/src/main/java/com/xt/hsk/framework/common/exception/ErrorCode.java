package com.xt.hsk.framework.common.exception;

import com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.xt.hsk.framework.common.exception.enums.ServiceErrorCodeRange;
import com.xt.hsk.framework.common.util.i18n.MessageUtils;
import lombok.Data;

/**
 * 错误码对象 全局错误码，占用 [0, 999], 参见 {@link GlobalErrorCodeConstants} 业务异常错误码，占用 [1 000 000 000, +∞)，参见
 * {@link ServiceErrorCodeRange}
 *
 * <AUTHOR>
 * @since 2025/06/16
 */
@Data
public class ErrorCode {

    /**
     * 错误码
     */
    private final Integer code;
    /**
     * 错误提示
     */
    private final String msg;

    public String getMsg() {
        return i18nKey == null ? msg : MessageUtils.message(this.i18nKey);
    }

    /**
     * il8n的key 用于国际化
     */
    private final String i18nKey;

    public ErrorCode(Integer code, String message) {
        this.code = code;
        this.msg = message;
        this.i18nKey = null;
    }

    public ErrorCode(Integer code, String message, String i18nKey) {
        this.code = code;
        this.msg = message;
        this.i18nKey = i18nKey;
    }

}
