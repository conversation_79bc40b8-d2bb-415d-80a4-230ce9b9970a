package com.xt.hsk.framework.common.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Schema(description = "导出分页参数")
@Data
public class ExportPageParam extends PageParam implements Serializable {

    /**
     * 导出任务名称
     */
    private String taskName;
    /**
     * IDS
     */
    private List<Long> ids;
}
