package com.xt.hsk.framework.common.enums;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 货币枚举 - 列举我们系统当前支持的所有货币
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Getter
@AllArgsConstructor
public enum CurrencyEnum implements BasicEnum<String>, ArrayValuable<String>, VO {

    /**
     * 人民币
     */
    CNY("CNY", "人民币", "￥"),
    /**
     * 美元
     */
    USD("USD", "美元", "$"),
    /**
     * 越南盾
     */
    VND("VND", "越南盾", "₫"),
    ;


    private final String code;
    private final String desc;
    /**
     * 货币单位符号
     */
    public final String symbol;

    private static String[] ARRAYS = Arrays.stream(values()).map(CurrencyEnum::getCode)
        .toArray(String[]::new);

    @Override
    public String[] array() {
        return ARRAYS;
    }

    /**
     * 按货币获取货币符号
     *
     * @param currency 货币
     * @return {@code String }
     */
    public static String getCurrencySymbolByCurrency(String currency) {
        for (CurrencyEnum value : values()) {
            if (value.getCode().equals(currency)) {
                return value.getSymbol();
            }
        }
        return null;
    }

}
