package com.xt.hsk.framework.common.exception.enums;

import com.xt.hsk.framework.common.exception.ErrorCode;

/**
 * 全局错误码枚举
 * 0-999 系统异常编码保留
 *
 * 一般情况下，使用 HTTP 响应状态码 https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Status
 * 虽然说，HTTP 响应状态码作为业务使用表达能力偏弱，但是使用在系统层面还是非常不错的
 * 比较特殊的是，因为之前一直使用 0 作为成功，就不使用 200 啦。
 *
 * <AUTHOR>
 */
public interface GlobalErrorCodeConstants {

    ErrorCode SUCCESS = new ErrorCode(0, "成功");
    ErrorCode ERROR_CODE = new ErrorCode(1, "失败");

    // ========== 客户端错误段 ==========

    ErrorCode BAD_REQUEST = new ErrorCode(400, "请求参数不正确");
    ErrorCode UNAUTHORIZED = new ErrorCode(401, "请登录", "Business.global_user_not_login");

    ErrorCode FORBIDDEN = new ErrorCode(403, "没有该操作权限");
    ErrorCode NOT_FOUND = new ErrorCode(404, "请求未找到");
    ErrorCode METHOD_NOT_ALLOWED = new ErrorCode(405, "请求方法不正确");
    ErrorCode MULTI_DEVICE_CONFLICT = new ErrorCode(409, "您的账号已在其他设备登录");
    ErrorCode FORCED_LOGOUT = new ErrorCode(499, "被强制下线","Business.forced_logout");

    // 并发请求，不允许
    ErrorCode LOCKED = new ErrorCode(423, "请求失败，请稍后重试", "Business.key423");
    ErrorCode TOO_MANY_REQUESTS = new ErrorCode(429, "请求过于频繁，请稍后重试","Business.too_many_requests");


    // ========== 服务端错误段 ==========

    ErrorCode INTERNAL_SERVER_ERROR = new ErrorCode(500, "系统异常","error");
    ErrorCode NOT_IMPLEMENTED = new ErrorCode(501, "功能未实现/未开启");
    ErrorCode ERROR_CONFIGURATION = new ErrorCode(502, "错误的配置项");
    ErrorCode OSS_FILE_UPLOAD_ERROR = new ErrorCode(503, "OSS文件上传失败");
    ErrorCode FILE_NAME_TOO_LONG = new ErrorCode(504, "上传文件名称超出长度，最多允许100个字符");
    ErrorCode IMPORT_DOWNLOAD_TEMPLATE_ERROR = new ErrorCode(505, "下载模板失败");
    ErrorCode IMPORT_TEMPLATE_SELECT_ERROR = new ErrorCode(506, "请选择要上传的文件");
    ErrorCode IMPORT_TEMPLATE_TYPE_ERROR = new ErrorCode(507, "仅支持Excel格式(.xlsx或.xls)的文件导入");
    // 未找到对应枚举
    ErrorCode ENUM_NOT_FOUND = new ErrorCode(506, "未找到对应枚举");

    // ========== 自定义错误段 ==========
    ErrorCode REPEATED_REQUESTS = new ErrorCode(900, "重复请求，请稍后重试");
    ErrorCode DEMO_DENY = new ErrorCode(901, "演示模式，禁止写操作");
    ErrorCode EXPORT_NAME_MISS = new ErrorCode(902, "导出任务名字不能为空");

    ErrorCode UNKNOWN = new ErrorCode(999, "未知错误");
    /**
     * 数据主键不存在
     */
    ErrorCode DATA_NOT_EXIST = new ErrorCode(902, "数据不存在");

    ErrorCode WORD_LONG_ERROR = new ErrorCode(1024001001, "输入的中文超过了10个字", "key1024001001");
    ErrorCode WORD_ONLY_HANZI_ERROR = new ErrorCode(1024001002, "只能输入中文");
    ErrorCode WORD_PINYIN_ERROR = new ErrorCode(1024001003, "只能输入中文拼音");
    ErrorCode WORD_HSK_LEVEL_CONFLICT_ERROR = new ErrorCode(1024001004, "0级不可与其他等级同时选中");
    ErrorCode WORD_COMPOUND_TOO_MANY_ERROR = new ErrorCode(1024001005, "词组数量不能超过限制");
    ErrorCode WORD_REQUIRED_ERROR = new ErrorCode(1024001006, "必填项不能为空");
    ErrorCode WORD_EXAMPLE_TOO_MANY_ERROR = new ErrorCode(1024001007, "例句不能超过5条");
    ErrorCode WORD_INTERPRETATION_TOO_MANY_ERROR = new ErrorCode(1024001008, "中文释义不能超过200个字");
    ErrorCode WORD_HAS_QUOTE_ERROR = new ErrorCode(1024001009, "该字/词被引用，无法删除，请先取消引用。");

    ErrorCode TEXTBOOK_HAS_QUESTION = new ErrorCode(1024002001, "该教材下有题目，请先删除题目");
    ErrorCode UNIT_HAS_QUESTION = new ErrorCode(1024002001, "该单元下有题目，请先删除题目");


    ErrorCode TAG_NAME_DUPLICATE = new ErrorCode(1024004001, "标签名称重复");

    // 题目相关
    ErrorCode QUESTION_NO_DETAIL = new ErrorCode(1024003001, "请填写题目详情");
    ErrorCode QUESTION_SET_ERROR = new ErrorCode(1024003002, "题目设置异常");
    ErrorCode QUESTION_SET_ANSWER = new ErrorCode(1024003003, "题目答案设置异常");
    ErrorCode QUESTION_ANSWERED_ERROR = new ErrorCode(1024003005, "本题已完成,不支持重复作答", "Business.question_answered_error");
    ErrorCode QUESTION_ANSWERED_ERROR_TWO = new ErrorCode(1024003005, "本题已作答,不保存本次记录", "Business.question_answered_error_two");
    ErrorCode QUESTION_IMPORT_ERROR = new ErrorCode(1024003004, "题目导入异常");
    ErrorCode QUESTION_DETAIL_NO_ANSWER = new ErrorCode(1024003006, "题目答案不能为空");
    ErrorCode QUESTION_NOT_EXISTS = new ErrorCode(1024003007, "题目不存在");
    ErrorCode QUESTION_NOT_OPTION = new ErrorCode(1024003008, "题目无选项");
    ErrorCode QUESTION_NOT_IMAGE = new ErrorCode(1024003009, "题目无图片");
    ErrorCode QUESTION_NOT_EXPLAIN = new ErrorCode(1024003010, "题目无解析");
    ErrorCode QUESTION_NOT_IMAGE_DESC = new ErrorCode(1024003011, "题目无图片描述");

    // 真题练习
    ErrorCode PRACTICE_RECORD_EXISTS = new ErrorCode(1024005001, "请勿重复提交练习记录");
    ErrorCode PRACTICE_RECORD_HAVE_QUESTION_NOT_FINISH = new ErrorCode(1024005002, "该练习未全部完成不允许提交");
    ErrorCode QUESTION_NOT_ANSWER = new ErrorCode(1024005003, "题目未作答,不允许提交");
    ErrorCode ANSWER_DATA_ERROR = new ErrorCode(1024005004, "作答数据异常，不允许提交");
    ErrorCode QUESTION_TYPE_NOT_SUPPORT_AI_CORRECTION = new ErrorCode(1024005005, "该题型不支持Ai批改");
    ErrorCode QUESTION_NOT_FINISH_NOT_SUPPORT_AI_CORRECTION = new ErrorCode(1024005006, "请完成所有题目后再进行Ai批改");
    // 题目数据异常，暂不支持AI批改
    ErrorCode QUESTION_DATA_ERROR = new ErrorCode(1024005007, "数据不存在，先看看其他题吧", "Business.question_data_error");
}
