package com.xt.hsk.framework.common.constants;

/**
 * 日志记录类型
 *
 * <AUTHOR>
 * @since 2025/05/24
 */
public interface LogRecordType {

    //  =================edu模块====================
    String INTERACTIVE_COURSE_TYPE = "互动课";
    String INTERACTIVE_COURSE_UNIT_TYPE = "互动课单元";
    String WORD_TYPE = "字词库";
    String TEACHER_TYPE = "讲师";
    String TEXTBOOK_TYPE = "教材";
    String CHAPTER_TYPE = "章节";
    String TAG_TYPE = "标签";
    String APP_USER_TYPE = "用户";
    String ORDER_REMARK_TYPE = "订单备注";
    String ELITE_COURSE_CATEGORY_TYPE = "精品课分类";
    String ELITE_COURSE_REGISTER_TYPE = "精品课课程登记";
    String ELITE_COURSE_CLASS_HOUR_TYPE = "精品课课时";
    String ELITE_COURSE_CHAPTER_TYPE = "精品课章节";
    String ELITE_COURSE_TYPE = "精品课";
    String RECOMMENDED_COURSE = "推荐课程";
    String EXAM_QUESTION_TYPE = "模考题型";
    String EXAM_PAPER_RULE = "模考组卷规则";
    String EXAM = "模考";
    String USER_EXAM_RECORD_TYPE = "用户模考记录";
    // 导出任务
    String EXPORT_TASK_TYPE = "导出任务";
    String SPECIAL_EXERCISE_TYPE = "专项练习";
    String GAME_QUESTION_TYPE = "游戏题目";
    // 营销模块
    String BANNER_TYPE = "Banner管理";
    String ROUTE_CONFIG_TYPE = "路由配置管理";

    // 基础设施
    String CONFIG_TYPE = "参数配置";

}
