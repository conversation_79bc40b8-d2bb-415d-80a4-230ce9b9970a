package com.xt.hsk.framework.common.enums;

import lombok.Data;

/**
 * 语言枚举
 */

public enum LanguangeEnum implements BasicEnum<Integer> {
    CHINESE(1, "中文"),
    ENGLISH(2, "英文");

    public Integer code;
    public String desc;

    LanguangeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
