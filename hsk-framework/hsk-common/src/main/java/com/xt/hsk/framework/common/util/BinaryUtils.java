package com.xt.hsk.framework.common.util;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

public class BinaryUtils {
    /**
     * 将数字转换为二进制位数组 7-> 1 2 4
     *
     * @param number
     * @return
     */
    public static List<Integer> getBinaryBitValues(Integer number) {
        List<Integer> result = new ArrayList<>();
        if (number == null) {
            return result;
        }
        // 处理负数的情况
        if (number < 0) {
            return Collections.emptyList();
        }
        if (number == 0) {
            result.add(0);
            return result;
        }

        int mask = 1;
        while (mask <= number && mask > 0) {  // mask > 0 防止整数溢出
            if ((number & mask) != 0) {
                result.add(mask);
            }
            mask <<= 1;  // 左移一位，检查下一个二进制位
        }

//         为了从最高位到最低位排序（可选）
        Collections.reverse(result);

        return result;
    }

    /**
     * 求和数组,这里去重了的
     */
    public static Integer getArrayNum(Collection<Integer> values) {
        if (values == null || values.isEmpty()) {
            return -1;
        }
        // 去重再计算
        values = new ArrayList<>(new HashSet<>(values));
        Integer result = 0;
        for (Integer value : values) {
            result += value;
        }
        return result;
    }
}
