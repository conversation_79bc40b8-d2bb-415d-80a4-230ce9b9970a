package com.xt.hsk.framework.common.util.i18n;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

/**
 * 获取i18n资源文件
 *
 * <AUTHOR>
 */
@Slf4j
public class MessageUtils {

    private MessageUtils() {
    }

    /**
     * 根据消息键和参数 获取消息 委托给spring messageSource
     *
     * @param code 消息键
     * @param args 参数
     * @return 获取国际化翻译值
     */
    public static String message(String code, Object... args) {
        try {
            // 明确指定使用名为"messageSource"的bean，而不是通过类型查找
            MessageSource messageSource = SpringUtil.getBean("messageSource", MessageSource.class);
            return messageSource.getMessage(code, args, LocaleContextHolder.getLocale());
        } catch (Exception e) {
            log.warn("获取国际化消息失败，key: {}, 错误信息: {}", code, e.getMessage());
            // 返回code作为默认消息
            return code;
        }
    }
}
