package com.xt.hsk.framework.common.enums;

import lombok.Getter;

@Getter
public enum PracticeModeEnum implements BasicEnum<Integer> {
    //练习模式：1-题型练习 2-全真模考 3-30分钟模考 4-15分钟模考
    TYPE_PRACTICE(1, "题型练习"),
    EXAM(2, "全真模考"),
    THIRTY_MINUTE_EXAM(3, "30分钟模考"),
    FIFTEEN_MINUTE_EXAM(4, "15分钟模考"),
    INTERACTIVE_COURSE(5, "互动课");
    private final Integer code;
    private final String desc;

    PracticeModeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
