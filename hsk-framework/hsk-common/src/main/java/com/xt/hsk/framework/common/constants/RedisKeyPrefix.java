package com.xt.hsk.framework.common.constants;

/**
 * Redis 键前缀
 *
 * <AUTHOR>
 * @since 2025/05/29
 */
public interface RedisKeyPrefix {

    /**
     * 项目固定前缀
     */
    String HSK = "HSK:";

    // =============================用户模块==============================

    /**
     * 用户手机号登录发送计数（每日限制） KEY ： sms:login:send:count:{countryCode}:{mobile}:{date}
     */
    String SMS_LOGIN_SEND_COUNT = HSK + "sms:login:send:count:%s:%s:%s";

    /**
     * 用户手机号登录发送频率限制（1分钟限制） KEY ： sms:login:frequency:{countryCode}:{mobile}
     */
    String SMS_LOGIN_FREQUENCY = HSK + "sms:login:frequency:%s:%s";

    /**
     * 用户密码错误尝试次数缓存
     * 格式：HSK:user:pwd:attempt:{countryCode}:{mobile}
     */
    String USER_PASSWORD_ATTEMPT = HSK + "user:pwd:attempt:%s:%s";

    /**
     * 用户收藏状态缓存
     * 格式：HSK:user:favorite:status:{userId}:{favoriteType}
     */
    String USER_FAVORITE_STATUS = HSK + "user:favorite:status:%s:%s";
    // =============================用户模块==============================


    // =============================真题练习模块==============================
    /**
     * 当日题目作答次数缓存
     * KEY ： question:answer_count:{yyyy-MM-dd}:{questionId}
     * VALUE ： Integer, 作答次数
     * 在定时任务中写入数据表中后手动删除
     */
    String QUESTION_ANSWER_COUNT = HSK + "question:answer_count:%s:%s";
    /**
     * 当日题目正确作答次数缓存
     * KEY ： question:answer_correct_count:{yyyy-MM-dd}:{questionId}
     * VALUE ： Integer, 正确作答次数
     * 在定时任务中写入数据表中后手动删除
     */
    String QUESTION_ANSWER_CORRECT_COUNT = HSK + "question:answer_correct_count:%s:%s";
    /**
     * 真题写作用户每日ai批改次数
     * KEY ： writing:ai_correction:count:{yyyyMMdd}:{userId}
     * VALUE ： Integer, 使用次数
     */
    String WRITING_AI_CORRECTION_COUNT = HSK + "writing:ai_correction:count:%s:%s";
    // =============================真题练习模块==============================


    // =============================第三方调用模块==============================
    /**
     * coze token缓存
     * KEY ： coze:token
     * VALUE ： String
     */
    String COZE_TOKEN = HSK + "coze:token";
    // =============================第三方调用模块==============================//


    // ===============================互动课===================================//
    /**
     * 展示的课程数量缓存
     */
    String INTERACTIVE_COURSE_DISPLAY_COUNT = HSK + "interactive:course:display:count";
    /**
     * 显示的课程列表ID缓存
     */
    String INTERACTIVE_COURSE_DISPLAY_LIST = HSK + "interactive:course:display:list";
    /**
     * 互动课程在当前等级中的排序位置缓存
     * KEY格式：HSK:interactive:course:position:{hskLevel}:{courseId}
     */
    String INTERACTIVE_COURSE_POSITION = HSK + "interactive:course:position";

    /**
     * 当前等级下 第一门课程
     * KEY格式：HSK:interactive:course:first:{hskLevel}
     */
    String INTERACTIVE_COURSE_FIRST = HSK + "interactive:course:first";

    // ===============================互动课===================================//

    // ===============================专项练习===================================//
    /**
     * 专项练习可用性缓存
     * KEY格式：HSK:special:exercise:availability:{hskLevel}
     * VALUE：Map<Integer, Boolean> 类型到是否有题目的映射
     */
    String SPECIAL_EXERCISE_AVAILABILITY = HSK + "special:exercise:availability";
    /**
     * 专项练习卡拉OK使用次数
     * KEY格式：HSK:special:exercise:karaoke:usage:count:{yyyyMMdd}:{userId}
     * VALUE：Map<Integer, Boolean> 类型到是否有题目的映射
     */
    String SPECIAL_EXERCISE_KARAOKE_USAGE_COUNT = HSK + "special:exercise:karaoke:usage:count:%s:%s";

    // ===============================专项练习===================================//

    // ===============================模考===================================//
    /**
     * 模考可用性缓存
     * KEY格式：HSK:exam:availability:{hskLevel}
     * VALUE：Map<Integer, Boolean> 类型到是否有题目的映射
     */
    String EXAM_AVAILABILITY = HSK + "exam:availability";
    // ===============================模考===================================//
}
