package com.xt.hsk.framework.common.util.json.databind;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * 基于时间戳的 LocalDate 序列化器
 *
 * <AUTHOR>
 */
public class TimestampLocalDateSerializer extends JsonSerializer<LocalDate> {

    public static final TimestampLocalDateSerializer INSTANCE = new TimestampLocalDateSerializer();

    @Override
    public void serialize(LocalDate value, JsonGenerator gen, SerializerProvider serializers) throws IOException {

        // 将 LocalDate 对象，转换为 LocalDateTime 对象
        LocalDateTime localDateTime = value.atStartOfDay();
        // 将 LocalDateTime 对象，转换为 Long 时间戳
        gen.writeNumber(localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
    }

}
