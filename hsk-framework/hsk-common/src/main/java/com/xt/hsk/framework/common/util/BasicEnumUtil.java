package com.xt.hsk.framework.common.util;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.ENUM_NOT_FOUND;

import cn.hutool.core.convert.Convert;
import com.xt.hsk.framework.common.enums.BasicEnum;
import com.xt.hsk.framework.common.exception.ServiceException;
import java.util.Objects;

/**
 * 基础枚举工具类 用于处理实现了 BasicEnum 接口的枚举类型的通用操作
 *
 * <AUTHOR>
 */
public class BasicEnumUtil {

    /**
     * 私有构造函数，防止实例化
     */
    private BasicEnumUtil() {
    }

    /**
     * 未知值的默认显示文本
     */
    public static final String UNKNOWN = "未知";

    /**
     * 安全地根据值获取对应的枚举实例 如果未找到对应的枚举值，返回null而不是抛出异常
     *
     * @param enumClass 枚举类的Class对象
     * @param value     要查找的值
     * @param <E>       枚举类型
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static <E extends Enum<E>> E fromValueSafely(Class<E> enumClass, Object value) {
        E target = null;

        for (E enumConstant : enumClass.getEnumConstants()) {
            BasicEnum<?> basicEnum = (BasicEnum<?>) enumConstant;
            if (Objects.equals(basicEnum.getCode(), value)) {
                target = (E) basicEnum;
            }
        }

        return target;
    }

    /**
     * 根据值获取对应的枚举实例 如果未找到对应的枚举值，将抛出业务异常
     *
     * @param enumClass 枚举类的Class对象
     * @param value     要查找的值
     * @param <E>       枚举类型
     * @return 对应的枚举实例
     * @throws ServiceException 当未找到对应的枚举值时抛出异常
     */
    public static <E extends Enum<E>> E fromValue(Class<E> enumClass, Object value) {
        E target = null;

        for (E enumConstant : enumClass.getEnumConstants()) {
            BasicEnum basicEnum = (BasicEnum) enumConstant;
            if (Objects.equals(basicEnum.getCode(), value)) {
                target = (E) basicEnum;
            }
        }

        if (target == null) {
            throw new ServiceException(ENUM_NOT_FOUND);
        }

        return target;
    }

    /**
     * 根据布尔值获取枚举的描述文本 将布尔值转换为整数(true=1, false=0)后获取对应的描述
     *
     * @param enumClass 枚举类的Class对象
     * @param bool      布尔值
     * @param <E>       枚举类型
     * @return 枚举的描述文本，如果未找到返回"未知"
     */
    public static <E extends Enum<E>> String getDescriptionByBool(Class<E> enumClass,
        Boolean bool) {
        Integer value = Convert.toInt(bool, 0);
        return getDescByCode(enumClass, value);
    }

    /**
     * 根据值获取枚举的描述文本
     *
     * @param enumClass 枚举类的Class对象
     * @param value     要查找的值
     * @param <E>       枚举类型
     * @return 枚举的描述文本，如果未找到返回"未知"
     */
    public static <E extends Enum<E>> String getDescByCode(Class<E> enumClass, Object value) {
        E basicEnum = fromValueSafely(enumClass, value);
        if (basicEnum != null) {
            return ((BasicEnum<?>) basicEnum).getDesc();
        }
        return "";
    }

    /**
     * 根据decs获取枚举的code
     */
    public static <E extends Enum<E>> Object getCodeByDesc(Class<E> enumClass, String desc) {
        for (E enumConstant : enumClass.getEnumConstants()) {
            BasicEnum basicEnum = (BasicEnum) enumConstant;
            if (basicEnum.getDesc().equals(desc)) {
                return basicEnum.getCode();
            }
        }
        return null;
    }

}
