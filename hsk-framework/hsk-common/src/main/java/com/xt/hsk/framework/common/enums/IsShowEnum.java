package com.xt.hsk.framework.common.enums;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 展示枚举
 *
 * <AUTHOR>
 * @since 2025/05/30
 */
@Getter
@AllArgsConstructor
public enum IsShowEnum implements BasicEnum<Integer>, VO, ArrayValuable<Integer> {
    /**
     * 全局是或否枚举
     */
    SHOW(1, "显示"),
    DISPLAY(0, "隐藏");

    private final Integer code;
    private final String desc;

    /**
     * 校验显示状态，非法时返回默认枚举值的code
     *
     * @param isShow      显示状态码
     * @param defaultEnum 默认显示枚举值
     * @return 合法则返回 isShow，否则返回 defaultEnum.getCode()
     */
    public static Integer validCodeOrDefault(Integer isShow, IsShowEnum defaultEnum) {
        // 判断是否是“显示”或“展示”两种合法状态中的任意一种
        if (!IsShowEnum.SHOW.getCode().equals(isShow) && !IsShowEnum.DISPLAY.getCode().equals(isShow)) {
            return defaultEnum.getCode();
        }
        return isShow;
    }

    private static final Integer[] ARRAYS = Arrays.stream(values()).map(IsShowEnum::getCode)
        .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}
