package com.xt.hsk.framework.common.enums;

import com.fhs.core.trans.vo.VO;

public enum TextbookTypeEnum implements BasicEnum<Integer>, VO {
    ORIGINAL_QUESTION(1, "考场真题"),
    /**
     * 模拟题
     */
    MOCK_QUESTION(2, "模拟题");

    private Integer code;
    private String desc;

    TextbookTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
