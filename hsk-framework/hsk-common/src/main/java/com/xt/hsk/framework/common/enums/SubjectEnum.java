package com.xt.hsk.framework.common.enums;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.annotation.JSONType;
import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import org.springframework.data.annotation.Id;

import java.util.Arrays;

/**
 * 科目枚举
 */
@Getter
@ToString
@JSONType(serializeEnumAsJavaBean = true)
@AllArgsConstructor
public enum SubjectEnum implements BasicEnum<Integer>, VO, ArrayValuable<Integer> {
    /**
     * 听力
     * 阅读
     * 书写
     */
    LISTENING(1, "听力"),
    READING(2, "阅读"),
    WRITING(4, "书写");

    private static final Integer[] ARRAYS = Arrays.stream(values()).map(SubjectEnum::getCode).toArray(Integer[]::new);
    @Id
    @JSONField(name = "code")
    public final Integer code;
    @JSONField(name = "desc")
    public final String desc;

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

    public static SubjectEnum getByCode(Integer code) {
        for (SubjectEnum subjectEnum : SubjectEnum.values()) {
            if (subjectEnum.getCode().equals(code)) {
                return subjectEnum;
            }
        }
        return null;
    }

    public Object getPkey() {
        return this.code;
    }

    // 通过desc拿code
    public static SubjectEnum getByDesc(String desc) {
        for (SubjectEnum subjectEnum : SubjectEnum.values()) {
            if (subjectEnum.getDesc().equals(desc)) {
                return subjectEnum;
            }
        }
        return null;
    }

}
