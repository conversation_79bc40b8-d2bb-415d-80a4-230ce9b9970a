package com.xt.hsk.framework.common.exception.util;

import com.google.common.annotations.VisibleForTesting;
import com.xt.hsk.framework.common.exception.ErrorCode;
import com.xt.hsk.framework.common.exception.ServiceException;
import com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants;
import lombok.extern.slf4j.Slf4j;

/**
 * {@link ServiceException} 工具类
 *
 * 目的在于，格式化异常信息提示。
 * 考虑到 String.format 在参数不正确时会报错，因此使用 {} 作为占位符，并使用 {@link #doFormat(int, String, Object...)} 方法来格式化
 *
 */
@Slf4j
public class ServiceExceptionUtil {

    // ========== 通过 ErrorCode 创建异常 ==========

    /**
     * 创建 ServiceException 异常
     *
     * @param errorCode 错误码
     * @return ServiceException
     */
    public static ServiceException exception(ErrorCode errorCode) {
        return new ServiceException(errorCode);
    }

    /**
     * 创建 ServiceException 异常 自定义错误提示，使用 errorCode 的国际化 key 进行国际化
     *
     * @param errorCode 错误码
     * @param message   错误提示
     * @param params    参数
     * @return ServiceException
     */
    public static ServiceException exception(ErrorCode errorCode, String message,
        Object... params) {
        String formattedMessage = doFormat(errorCode.getCode(), message, params);
        if (errorCode.getI18nKey() != null) {
            return new ServiceException(errorCode.getCode(), formattedMessage,
                errorCode.getI18nKey(), params);
        } else {
            return new ServiceException(errorCode.getCode(), formattedMessage);
        }
    }

    /**
     * 创建 ServiceException 异常 错误提示使用 errorCode 的 msg 进行格式化
     *
     * @param errorCode 错误码
     * @param params    参数
     * @return ServiceException
     */
    public static ServiceException exception(ErrorCode errorCode, Object... params) {
        return new ServiceException(errorCode, params);
    }

    // ========== 通过错误码和消息创建异常 ==========

    /**
     * 创建 ServiceException 异常（不支持国际化）
     *
     * @param code    错误码
     * @param message 错误提示
     * @return ServiceException
     */
    public static ServiceException exception(Integer code, String message) {
        return new ServiceException(code, message);
    }

    /**
     * 创建 ServiceException 异常（不支持国际化） 通过格式化错误提示创建异常
     *
     * @param code           错误码
     * @param messagePattern 错误提示模板
     * @param params         参数
     * @return ServiceException
     */
    public static ServiceException exception(Integer code, String messagePattern,
        Object... params) {
        String message = doFormat(code, messagePattern, params);
        return new ServiceException(code, message);
    }

    /**
     * 创建 ServiceException 异常（支持国际化）
     *
     * @param code           错误码
     * @param messagePattern 错误提示模板
     * @param i18nKey        国际化key
     * @param params         参数
     * @return ServiceException
     */
    public static ServiceException exceptionWithI18n(Integer code, String messagePattern,
        String i18nKey, Object... params) {
        String message = doFormat(code, messagePattern, params);
        return new ServiceException(code, message, i18nKey, params);
    }

    // ========== 创建参数校验异常 ==========

    /**
     * 创建参数校验 ServiceException 异常
     *
     * @param messagePattern 错误提示模板
     * @param params         参数
     * @return ServiceException
     */
    public static ServiceException invalidParamException(String messagePattern, Object... params) {
        return exception(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), messagePattern, params);
    }

    /**
     * 创建参数校验 ServiceException 异常（支持国际化）
     *
     * @param messagePattern 错误提示模板
     * @param i18nKey        国际化key
     * @param params         参数
     * @return ServiceException
     */
    public static ServiceException invalidParamExceptionWithI18n(String messagePattern,
        String i18nKey, Object... params) {
        return exceptionWithI18n(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), messagePattern,
            i18nKey, params);
    }

    // ========== 格式化方法 ==========

    /**
     * 将错误编号对应的消息使用 params 进行格式化。
     *
     * @param code           错误编号
     * @param messagePattern 消息模版
     * @param params         参数
     * @return 格式化后的提示
     */
    @VisibleForTesting
    public static String doFormat(int code, String messagePattern, Object... params) {
        StringBuilder sbuf = new StringBuilder(messagePattern.length() + 50);
        int i = 0;
        int j;
        int l;
        for (l = 0; l < params.length; l++) {
            j = messagePattern.indexOf("{}", i);
            if (j == -1) {
                log.error("[doFormat][参数过多：错误码({})|错误内容({})|参数({})", code, messagePattern, params);
                if (i == 0) {
                    return messagePattern;
                } else {
                    sbuf.append(messagePattern.substring(i));
                    return sbuf.toString();
                }
            } else {
                sbuf.append(messagePattern, i, j);
                sbuf.append(params[l]);
                i = j + 2;
            }
        }
        if (messagePattern.indexOf("{}", i) != -1) {
            log.error("[doFormat][参数过少：错误码({})|错误内容({})|参数({})", code, messagePattern, params);
        }
        sbuf.append(messagePattern.substring(i));
        return sbuf.toString();
    }

}
