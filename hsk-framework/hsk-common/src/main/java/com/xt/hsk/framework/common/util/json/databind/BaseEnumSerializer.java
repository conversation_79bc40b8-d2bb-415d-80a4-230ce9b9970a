package com.xt.hsk.framework.common.util.json.databind;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.xt.hsk.framework.common.enums.BasicEnum;

import java.io.IOException;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * 基于BasicEnum 的枚举序列化器
 *
 * <AUTHOR>
 */
public class BaseEnumSerializer extends JsonSerializer<BasicEnum> {

    public static final BaseEnumSerializer INSTANCE = new BaseEnumSerializer();

    @Override
    public void serialize(BasicEnum value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        // 将枚举序列化一下
        if (value == null) {
            gen.writeNull();
            return;
        }

        gen.writeStartObject();

        // 序列化枚举的值 (code)

        gen.writeObjectField("code", value.getCode());
        // 可选的：添加枚举名称
        gen.writeStringField("desc", value.getDesc());

        gen.writeEndObject();
    }

}
