package com.xt.hsk.framework.common.enums;

import lombok.Getter;

/**
 * 单词词性枚举
 */
@Getter
public enum WordKindEnum implements BasicEnum<String> {

    VERB("v", "动词", 1),
    NOUN("n", "名词", 2),
    NUMERAL("numb", "数词", 4),
    ONOMATOPOEIA("onom", "拟声词", 8),
    ADJECTIVE("adj", "形容词", 16),
    ADVERB("adv", "副词", 32),
    TIME("time", "时间词", 64),
    MEASURE("measure", "量词", 128),
    SEPARABLE_VERB("sv", "离合词", 256),
    LOCATION("nlocal", "地点词", 512),
    PRONOUN("pron", "代词", 1024),
    PREPOSITION("prep", "介词", 2048),
    PARTICLE("part", "助词", 4096),
    CONJUNCTION("conj", "连词", 8192),
    PHRASE("phrase", "短语", 16384),
    INTERJECTION("intj", "语气词", 32768),
    AUXILIARY_VERB("particle", "助动词", 65536);

    private final String code;
    private final String desc;
    private final Integer value;

    WordKindEnum(String code, String desc, Integer value) {
        this.code = code;
        this.desc = desc;
        this.value = value;
    }

    public static String getDescByCode(String code) {
        for (WordKindEnum value : WordKindEnum.values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    public static Integer getValueByCode(String code) {
        for (WordKindEnum value : WordKindEnum.values()) {
            if (value.code.equals(code)) {
                return value.value;
            }
        }
        return null;
    }

    public static String getDescByValue(Integer value) {
        for (WordKindEnum kindEnum : WordKindEnum.values()) {
            if (kindEnum.value.equals(value)) {
                return kindEnum.desc;
            }
        }
        return null;
    }

    public static String getCodeByValue(Integer value) {
        for (WordKindEnum kindEnum : WordKindEnum.values()) {
            if (kindEnum.value.equals(value)) {
                return kindEnum.code;
            }
        }
        return null;
    }

    public static WordKindEnum getByValue(Integer value) {
        for (WordKindEnum kindEnum : WordKindEnum.values()) {
            if (kindEnum.value.equals(value)) {
                return kindEnum;
            }
        }
        return null;
    }
}
