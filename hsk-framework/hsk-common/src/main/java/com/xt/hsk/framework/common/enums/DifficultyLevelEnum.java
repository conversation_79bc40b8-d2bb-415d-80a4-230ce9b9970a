package com.xt.hsk.framework.common.enums;

import com.fhs.core.trans.vo.VO;

/**
 * 难度等级 1 简单 2中等 3 困难
 */
public enum DifficultyLevelEnum implements BasicEnum<Integer>, VO {
    EASY(1, "Easy"),
    MEDIUM(2, "Medium"),
    HARD(3, "Hard");
    public Integer code;
    public String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    DifficultyLevelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DifficultyLevelEnum getByCode(Integer code) {
        for (DifficultyLevelEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
