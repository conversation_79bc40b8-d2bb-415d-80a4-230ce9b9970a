package com.xt.hsk.framework.idempotent.config;

import com.xt.hsk.framework.idempotent.core.aop.IdempotentAspect;
import com.xt.hsk.framework.idempotent.core.keyresolver.impl.DefaultIdempotentKeyResolver;
import com.xt.hsk.framework.idempotent.core.keyresolver.impl.ExpressionIdempotentKeyResolver;
import com.xt.hsk.framework.idempotent.core.keyresolver.IdempotentKeyResolver;
import com.xt.hsk.framework.idempotent.core.keyresolver.impl.UserIdempotentKeyResolver;
import com.xt.hsk.framework.idempotent.core.redis.IdempotentRedisDAO;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import com.xt.hsk.framework.redis.config.HskRedisAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.List;

@AutoConfiguration(after = HskRedisAutoConfiguration.class)
public class HskIdempotentConfiguration {

    @Bean
    public IdempotentAspect idempotentAspect(List<IdempotentKeyResolver> keyResolvers, IdempotentRedisDAO idempotentRedisDAO) {
        return new IdempotentAspect(keyResolvers, idempotentRedisDAO);
    }

    @Bean
    public IdempotentRedisDAO idempotentRedisDAO(StringRedisTemplate stringRedisTemplate) {
        return new IdempotentRedisDAO(stringRedisTemplate);
    }

    // ========== 各种 IdempotentKeyResolver Bean ==========

    @Bean
    public DefaultIdempotentKeyResolver defaultIdempotentKeyResolver() {
        return new DefaultIdempotentKeyResolver();
    }

    @Bean
    public UserIdempotentKeyResolver userIdempotentKeyResolver() {
        return new UserIdempotentKeyResolver();
    }

    @Bean
    public ExpressionIdempotentKeyResolver expressionIdempotentKeyResolver() {
        return new ExpressionIdempotentKeyResolver();
    }

}
