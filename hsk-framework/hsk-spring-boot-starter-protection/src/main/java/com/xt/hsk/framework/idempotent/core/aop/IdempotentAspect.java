package com.xt.hsk.framework.idempotent.core.aop;

import com.xt.hsk.framework.common.exception.ServiceException;
import com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.xt.hsk.framework.common.util.collection.CollectionUtils;
import com.xt.hsk.framework.idempotent.core.annotation.Idempotent;
import com.xt.hsk.framework.idempotent.core.keyresolver.IdempotentKeyResolver;
import com.xt.hsk.framework.idempotent.core.redis.IdempotentRedisDAO;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.util.Assert;

/**
 * 拦截声明了 {@link Idempotent} 注解的方法，实现幂等操作
 *
 * <AUTHOR>
 */
@Aspect
@Slf4j
public class IdempotentAspect {

    /**
     * IdempotentKeyResolver 集合
     */
    private final Map<Class<? extends IdempotentKeyResolver>, IdempotentKeyResolver> keyResolvers;

    private final IdempotentRedisDAO idempotentRedisDAO;

    public IdempotentAspect(List<IdempotentKeyResolver> keyResolvers, IdempotentRedisDAO idempotentRedisDAO) {
        this.keyResolvers = CollectionUtils.convertMap(keyResolvers, IdempotentKeyResolver::getClass);
        this.idempotentRedisDAO = idempotentRedisDAO;
    }

    @Around(value = "@annotation(idempotent)")
    public Object aroundPointCut(ProceedingJoinPoint joinPoint, Idempotent idempotent) throws Throwable {
        // 获得 IdempotentKeyResolver
        IdempotentKeyResolver keyResolver = keyResolvers.get(idempotent.keyResolver());
        Assert.notNull(keyResolver, "找不到对应的 IdempotentKeyResolver");
        // 解析 Key
        String key = keyResolver.resolver(joinPoint, idempotent);

        // 1. 锁定 Key
        boolean success = idempotentRedisDAO.setIfAbsent(key, idempotent.timeout(), idempotent.timeUnit());
        // 锁定失败，抛出异常
        if (!success) {
            log.info("[aroundPointCut][方法({}) 参数({}) 存在重复请求]", joinPoint.getSignature().toString(), joinPoint.getArgs());

            String message = idempotent.message();
            // 检查是否为国际化key格式（以 { 开头和 } 结尾）
            if (message.startsWith("{") && message.endsWith("}")) {
                // 提取国际化key（去掉大括号）
                String i18nKey = message.substring(1, message.length() - 1);
                throw new ServiceException(GlobalErrorCodeConstants.REPEATED_REQUESTS.getCode(),
                    GlobalErrorCodeConstants.REPEATED_REQUESTS.getMsg(), i18nKey);
            } else {
                // 保持原有逻辑，直接使用message
                throw new ServiceException(GlobalErrorCodeConstants.REPEATED_REQUESTS.getCode(), message);
            }
        }

        // 2. 执行逻辑
        try {
            return joinPoint.proceed();
        } catch (Throwable throwable) {
            // 3. 异常时，删除 Key
            // 参考美团 GTIS 思路：https://tech.meituan.com/2016/09/29/distributed-system-mutually-exclusive-idempotence-cerberus-gtis.html
            if (idempotent.deleteKeyWhenException()) {
                idempotentRedisDAO.delete(key);
            }
            throw throwable;
        }
    }

}
