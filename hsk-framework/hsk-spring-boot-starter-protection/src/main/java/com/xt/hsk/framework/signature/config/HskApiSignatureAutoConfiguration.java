package com.xt.hsk.framework.signature.config;

import com.xt.hsk.framework.redis.config.HskRedisAutoConfiguration;
import com.xt.hsk.framework.signature.core.aop.ApiSignatureAspect;
import com.xt.hsk.framework.signature.core.redis.ApiSignatureRedisDAO;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * HTTP API 签名的自动配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration(after = HskRedisAutoConfiguration.class)
public class HskApiSignatureAutoConfiguration {

    @Bean
    public ApiSignatureAspect signatureAspect(ApiSignatureRedisDAO signatureRedisDAO) {
        return new ApiSignatureAspect(signatureRedisDAO);
    }

    @Bean
    public ApiSignatureRedisDAO signatureRedisDAO(StringRedisTemplate stringRedisTemplate) {
        return new ApiSignatureRedisDAO(stringRedisTemplate);
    }

}
