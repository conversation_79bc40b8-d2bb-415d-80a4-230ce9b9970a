package com.xt.hsk.framework.mybatis.core.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.xt.hsk.framework.mybatis.core.dataobject.AppBaseDO;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import com.xt.hsk.framework.web.core.util.WebFrameworkUtils;
import java.time.LocalDateTime;
import java.util.Objects;
import org.apache.ibatis.reflection.MetaObject;

/**
 * 通用参数填充实现类
 *
 * 如果没有显式的对通用参数进行赋值，这里会对通用参数进行填充、赋值
 *
 * <AUTHOR>
 */
public class DefaultDBFieldHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        if (Objects.isNull(metaObject)) {
            return;
        }

        Object originalObject = metaObject.getOriginalObject();
        LocalDateTime current = LocalDateTime.now();

        // 处理BaseDO及其子类
        if (originalObject instanceof BaseDO baseDO) {

            // 创建时间为空，则以当前时间为插入时间
            if (Objects.isNull(baseDO.getCreateTime())) {
                baseDO.setCreateTime(current);
            }
            // 更新时间为空，则以当前时间为更新时间
            if (Objects.isNull(baseDO.getUpdateTime())) {
                baseDO.setUpdateTime(current);
            }

            Long userId = WebFrameworkUtils.getLoginUserId();
            // 当前登录用户不为空，创建人为空，则当前登录用户为创建人
            if (Objects.nonNull(userId) && Objects.isNull(baseDO.getCreator())) {
                baseDO.setCreator(userId.toString());
            }
            // 当前登录用户不为空，更新人为空，则当前登录用户为更新人
            if (Objects.nonNull(userId) && Objects.isNull(baseDO.getUpdater())) {
                baseDO.setUpdater(userId.toString());
            }
        }
        // 处理AppBaseDO及其子类（专为app端设计，不需要creator和updater）
        else if (originalObject instanceof AppBaseDO appBaseDO) {

            // 创建时间为空，则以当前时间为插入时间
            if (Objects.isNull(appBaseDO.getCreateTime())) {
                appBaseDO.setCreateTime(current);
            }
            // 更新时间为空，则以当前时间为更新时间
            if (Objects.isNull(appBaseDO.getUpdateTime())) {
                appBaseDO.setUpdateTime(current);
            }
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        if (Objects.isNull(metaObject)) {
            return;
        }

        Object originalObject = metaObject.getOriginalObject();

        // 处理BaseDO及其子类
        if (originalObject instanceof BaseDO) {
            // 更新时间为空，则以当前时间为更新时间
            Object modifyTime = getFieldValByName("updateTime", metaObject);
            if (Objects.isNull(modifyTime)) {
                setFieldValByName("updateTime", LocalDateTime.now(), metaObject);
            }

            // 当前登录用户不为空，更新人为空，则当前登录用户为更新人
            Object modifier = getFieldValByName("updater", metaObject);
            Long userId = WebFrameworkUtils.getLoginUserId();
            if (Objects.nonNull(userId) && Objects.isNull(modifier)) {
                setFieldValByName("updater", userId.toString(), metaObject);
            }
        }
        // 处理AppBaseDO及其子类（只更新时间，不更新updater）
        else if (originalObject instanceof AppBaseDO) {
            // 更新时间为空，则以当前时间为更新时间
            Object modifyTime = getFieldValByName("updateTime", metaObject);
            if (Objects.isNull(modifyTime)) {
                setFieldValByName("updateTime", LocalDateTime.now(), metaObject);
            }
        }
    }
}
