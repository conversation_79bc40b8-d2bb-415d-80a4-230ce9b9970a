package com.xt.hsk.framework.mybatis.core.dataobject;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fhs.core.trans.vo.TransPojo;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * App端基础实体对象
 * <p>
 * 专为app端设计，去掉了创建者和更新者字段，因为在app端用户操作自己的数据时， 创建者和更新者都是用户本身，这些字段是冗余的。
 * <p>
 * 为什么实现 {@link TransPojo} 接口？ 因为使用 Easy-Trans TransType.SIMPLE 模式，集成 MyBatis Plus 查询 由于 Easy-Trans
 * 会添加 transMap 属性，避免 Jackson 在 Spring Cache 反序列化报错
 *
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(value = "transMap")
public abstract class AppBaseDO implements Serializable, TransPojo {

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 最后更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    private Boolean deleted;

} 