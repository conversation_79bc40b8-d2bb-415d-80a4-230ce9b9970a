package com.xt.hsk.framework.redis.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

/**
 * Redis 工具类
 * <p>
 * 提供常用的 Redis 操作方法，统一处理类型转换和异常情况
 *
 * <AUTHOR>
 * @since 2025/05/29
 */
@Component
@Slf4j
public class RedisUtil {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    // ========== String 操作 ==========

    /**
     * 设置键值对
     *
     * @param key   键
     * @param value 值
     */
    public void set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
        } catch (Exception e) {
            log.error("Redis set操作失败: key={}, value={}", key, value, e);
        }
    }

    /**
     * 设置键值对，并指定过期时间
     *
     * @param key     键
     * @param value   值
     * @param timeout 过期时间
     * @param unit    时间单位
     */
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        try {
            redisTemplate.opsForValue().set(key, value, timeout, unit);
        } catch (Exception e) {
            log.error("Redis set操作失败: key={}, value={}, timeout={}, unit={}", key, value,
                    timeout, unit, e);
        }
    }

    /**
     * 设置键值对，并指定过期时间
     *
     * @param key      键
     * @param value    值
     * @param duration 过期时间
     */
    public void set(String key, Object value, Duration duration) {
        try {
            redisTemplate.opsForValue().set(key, value, duration);
        } catch (Exception e) {
            log.error("Redis set操作失败: key={}, value={}, duration={}", key, value, duration, e);
        }
    }

    /**
     * 获取值
     *
     * @param key 键
     * @return 值
     */
    public Object get(String key) {
        try {
            return redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error("Redis get操作失败: key={}", key, e);
            return null;
        }
    }

    /**
     * 获取字符串值
     *
     * @param key 键
     * @return 字符串值
     */
    public String getString(String key) {
        Object value = get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 获取整数值
     *
     * @param key 键
     * @return 整数值，如果不存在或转换失败返回0
     */
    public Integer getInteger(String key) {
        Object value = get(key);
        if (value == null) {
            return 0;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            log.warn("Redis值转换为Integer失败: key={}, value={}", key, value, e);
            return 0;
        }
    }

    /**
     * 获取长整数值
     *
     * @param key 键
     * @return 长整数值，如果不存在或转换失败返回null
     */
    public Long getLong(String key) {
        Object value = get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.valueOf(value.toString());
        } catch (NumberFormatException e) {
            log.warn("Redis值转换为Long失败: key={}, value={}", key, value, e);
            return null;
        }
    }

    /**
     * 获取布尔值
     *
     * @param key 键
     * @return 布尔值，如果不存在返回null
     */
    public Boolean getBoolean(String key) {
        Object value = get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return Boolean.valueOf(value.toString());
    }

    /**
     * 如果键不存在则设置
     *
     * @param key   键
     * @param value 值
     * @return true-设置成功，false-键已存在
     */
    public Boolean setIfAbsent(String key, Object value) {
        try {
            return redisTemplate.opsForValue().setIfAbsent(key, value);
        } catch (Exception e) {
            log.error("Redis setIfAbsent操作失败: key={}, value={}", key, value, e);
            return false;
        }
    }

    /**
     * 如果键不存在则设置，并指定过期时间
     *
     * @param key     键
     * @param value   值
     * @param timeout 过期时间
     * @param unit    时间单位
     * @return true-设置成功，false-键已存在
     */
    public Boolean setIfAbsent(String key, Object value, long timeout, TimeUnit unit) {
        try {
            return redisTemplate.opsForValue().setIfAbsent(key, value, timeout, unit);
        } catch (Exception e) {
            log.error("Redis setIfAbsent操作失败: key={}, value={}, timeout={}, unit={}", key,
                    value, timeout, unit, e);
            return false;
        }
    }

    /**
     * 递增
     *
     * @param key 键
     * @return 递增后的值
     */
    public Long increment(String key) {
        try {
            return redisTemplate.opsForValue().increment(key);
        } catch (Exception e) {
            log.error("Redis increment操作失败: key={}", key, e);
            return null;
        }
    }

    /**
     * 递增指定值
     *
     * @param key   键
     * @param delta 增量
     * @return 递增后的值
     */
    public Long increment(String key, long delta) {
        try {
            return redisTemplate.opsForValue().increment(key, delta);
        } catch (Exception e) {
            log.error("Redis increment操作失败: key={}, delta={}", key, delta, e);
            return null;
        }
    }

    /**
     * 递减
     *
     * @param key 键
     * @return 递减后的值
     */
    public Long decrement(String key) {
        try {
            return redisTemplate.opsForValue().decrement(key);
        } catch (Exception e) {
            log.error("Redis decrement操作失败: key={}", key, e);
            return null;
        }
    }

    /**
     * 递减指定值
     *
     * @param key   键
     * @param delta 减量
     * @return 递减后的值
     */
    public Long decrement(String key, long delta) {
        try {
            return redisTemplate.opsForValue().decrement(key, delta);
        } catch (Exception e) {
            log.error("Redis decrement操作失败: key={}, delta={}", key, delta, e);
            return null;
        }
    }

    // ========== 通用键操作 ==========

    /**
     * 删除键
     *
     * @param key 键
     * @return 是否删除成功
     */
    public Boolean delete(String key) {
        try {
            return redisTemplate.delete(key);
        } catch (Exception e) {
            log.error("Redis delete操作失败: key={}", key, e);
            return false;
        }
    }

    /**
     * 批量删除键
     *
     * @param keys 键集合
     * @return 删除的键的数量
     */
    public Long delete(Collection<String> keys) {
        try {
            return redisTemplate.delete(keys);
        } catch (Exception e) {
            log.error("Redis 批量delete操作失败: keys={}", keys, e);
            return 0L;
        }
    }

    /**
     * 检查键是否存在
     *
     * @param key 键
     * @return 是否存在
     */
    public Boolean exists(String key) {
        try {
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            log.error("Redis exists操作失败: key={}", key, e);
            return false;
        }
    }

    /**
     * 设置键的过期时间
     *
     * @param key     键
     * @param timeout 过期时间
     * @param unit    时间单位
     * @return 是否设置成功
     */
    public Boolean expire(String key, long timeout, TimeUnit unit) {
        try {
            return redisTemplate.expire(key, timeout, unit);
        } catch (Exception e) {
            log.error("Redis expire操作失败: key={}, timeout={}, unit={}", key, timeout, unit, e);
            return false;
        }
    }

    /**
     * 设置键的过期时间
     *
     * @param key      键
     * @param duration 过期时间
     * @return 是否设置成功
     */
    public Boolean expire(String key, Duration duration) {
        try {
            return redisTemplate.expire(key, duration);
        } catch (Exception e) {
            log.error("Redis expire操作失败: key={}, duration={}", key, duration, e);
            return false;
        }
    }

    /**
     * 获取键的剩余过期时间
     *
     * @param key  键
     * @param unit 时间单位
     * @return 剩余过期时间
     */
    public Long getExpire(String key, TimeUnit unit) {
        try {
            return redisTemplate.getExpire(key, unit);
        } catch (Exception e) {
            log.error("Redis getExpire操作失败: key={}, unit={}", key, unit, e);
            return -1L;
        }
    }

    /**
     * 移除键的过期时间
     *
     * @param key 键
     * @return 是否移除成功
     */
    public Boolean persist(String key) {
        try {
            return redisTemplate.persist(key);
        } catch (Exception e) {
            log.error("Redis persist操作失败: key={}", key, e);
            return false;
        }
    }

    // ========== Hash 操作 ==========

    /**
     * 设置Hash字段
     *
     * @param key   键
     * @param field 字段
     * @param value 值
     */
    public void hSet(String key, String field, Object value) {
        try {
            redisTemplate.opsForHash().put(key, field, value);
        } catch (Exception e) {
            log.error("Redis hSet操作失败: key={}, field={}, value={}", key, field, value, e);
        }
    }

    /**
     * 获取Hash字段值
     *
     * @param key   键
     * @param field 字段
     * @return 字段值
     */
    public Object hGet(String key, String field) {
        try {
            return redisTemplate.opsForHash().get(key, field);
        } catch (Exception e) {
            log.error("Redis hGet操作失败: key={}, field={}", key, field, e);
            return null;
        }
    }

    /**
     * 获取Hash的所有字段和值
     *
     * @param key 键
     * @return 字段值映射
     */
    public Map<Object, Object> hGetAll(String key) {
        try {
            return redisTemplate.opsForHash().entries(key);
        } catch (Exception e) {
            log.error("Redis hGetAll操作失败: key={}", key, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 批量设置Hash字段
     *
     * @param key 键
     * @param map 字段值映射
     */
    public void hSetAll(String key, Map<String, Object> map) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
        } catch (Exception e) {
            log.error("Redis hSetAll操作失败: key={}, map={}", key, map, e);
        }
    }

    /**
     * 删除Hash字段
     *
     * @param key    键
     * @param fields 字段数组
     * @return 删除的字段数量
     */
    public Long hDelete(String key, Object... fields) {
        try {
            return redisTemplate.opsForHash().delete(key, fields);
        } catch (Exception e) {
            log.error("Redis hDelete操作失败: key={}, fields={}", key, Arrays.toString(fields), e);
            return 0L;
        }
    }

    /**
     * 检查Hash字段是否存在
     *
     * @param key   键
     * @param field 字段
     * @return 是否存在
     */
    public Boolean hExists(String key, String field) {
        try {
            return redisTemplate.opsForHash().hasKey(key, field);
        } catch (Exception e) {
            log.error("Redis hExists操作失败: key={}, field={}", key, field, e);
            return false;
        }
    }

    /**
     * Hash字段递增
     *
     * @param key   键
     * @param field 字段
     * @param delta 增量
     * @return 递增后的值
     */
    public Long hIncrement(String key, String field, long delta) {
        try {
            return redisTemplate.opsForHash().increment(key, field, delta);
        } catch (Exception e) {
            log.error("Redis hIncrement操作失败: key={}, field={}, delta={}", key, field, delta, e);
            return null;
        }
    }

    // ========== List 操作 ==========

    /**
     * 从左侧推入列表
     *
     * @param key   键
     * @param value 值
     * @return 列表长度
     */
    public Long lLeftPush(String key, Object value) {
        try {
            return redisTemplate.opsForList().leftPush(key, value);
        } catch (Exception e) {
            log.error("Redis lLeftPush操作失败: key={}, value={}", key, value, e);
            return null;
        }
    }

    /**
     * 从右侧推入列表
     *
     * @param key   键
     * @param value 值
     * @return 列表长度
     */
    public Long lRightPush(String key, Object value) {
        try {
            return redisTemplate.opsForList().rightPush(key, value);
        } catch (Exception e) {
            log.error("Redis lRightPush操作失败: key={}, value={}", key, value, e);
            return null;
        }
    }

    /**
     * 从左侧弹出列表元素
     *
     * @param key 键
     * @return 弹出的元素
     */
    public Object lLeftPop(String key) {
        try {
            return redisTemplate.opsForList().leftPop(key);
        } catch (Exception e) {
            log.error("Redis lLeftPop操作失败: key={}", key, e);
            return null;
        }
    }

    /**
     * 从右侧弹出列表元素
     *
     * @param key 键
     * @return 弹出的元素
     */
    public Object lRightPop(String key) {
        try {
            return redisTemplate.opsForList().rightPop(key);
        } catch (Exception e) {
            log.error("Redis lRightPop操作失败: key={}", key, e);
            return null;
        }
    }

    /**
     * 获取列表指定范围的元素
     *
     * @param key   键
     * @param start 开始位置
     * @param end   结束位置
     * @return 元素列表
     */
    public List<Object> lRange(String key, long start, long end) {
        try {
            return redisTemplate.opsForList().range(key, start, end);
        } catch (Exception e) {
            log.error("Redis lRange操作失败: key={}, start={}, end={}", key, start, end, e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取列表长度
     *
     * @param key 键
     * @return 列表长度
     */
    public Long lSize(String key) {
        try {
            return redisTemplate.opsForList().size(key);
        } catch (Exception e) {
            log.error("Redis lSize操作失败: key={}", key, e);
            return 0L;
        }
    }

    // ========== Set 操作 ==========

    /**
     * 向集合添加元素
     *
     * @param key    键
     * @param values 值数组
     * @return 添加的元素数量
     */
    public Long sAdd(String key, Object... values) {
        try {
            return redisTemplate.opsForSet().add(key, values);
        } catch (Exception e) {
            log.error("Redis sAdd操作失败: key={}, values={}", key, Arrays.toString(values), e);
            return 0L;
        }
    }

    /**
     * 移除集合元素
     *
     * @param key    键
     * @param values 值数组
     * @return 移除的元素数量
     */
    public Long sRemove(String key, Object... values) {
        try {
            return redisTemplate.opsForSet().remove(key, values);
        } catch (Exception e) {
            log.error("Redis sRemove操作失败: key={}, values={}", key, Arrays.toString(values), e);
            return 0L;
        }
    }

    /**
     * 检查集合是否包含元素
     *
     * @param key   键
     * @param value 值
     * @return 是否包含
     */
    public Boolean sIsMember(String key, Object value) {
        try {
            return redisTemplate.opsForSet().isMember(key, value);
        } catch (Exception e) {
            log.error("Redis sIsMember操作失败: key={}, value={}", key, value, e);
            return false;
        }
    }

    /**
     * 获取集合所有元素
     *
     * @param key 键
     * @return 集合元素
     */
    public Set<Object> sMembers(String key) {
        try {
            return redisTemplate.opsForSet().members(key);
        } catch (Exception e) {
            log.error("Redis sMembers操作失败: key={}", key, e);
            return Collections.emptySet();
        }
    }

    /**
     * 获取集合大小
     *
     * @param key 键
     * @return 集合大小
     */
    public Long sSize(String key) {
        try {
            return redisTemplate.opsForSet().size(key);
        } catch (Exception e) {
            log.error("Redis sSize操作失败: key={}", key, e);
            return 0L;
        }
    }

    // ========== 分布式锁 ==========

    /**
     * 尝试获取分布式锁
     *
     * @param lockKey    锁键
     * @param requestId  请求ID（用于标识锁的持有者）
     * @param expireTime 过期时间（秒）
     * @return 是否获取成功
     */
    public boolean tryLock(String lockKey, String requestId, long expireTime) {
        try {
            Boolean result = redisTemplate.opsForValue()
                    .setIfAbsent(lockKey, requestId, expireTime, TimeUnit.SECONDS);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("Redis 获取分布式锁失败: lockKey={}, requestId={}, expireTime={}", lockKey,
                    requestId, expireTime, e);
            return false;
        }
    }

    /**
     * 释放分布式锁
     *
     * @param lockKey   锁键
     * @param requestId 请求ID
     * @return 是否释放成功
     */
    public boolean releaseLock(String lockKey, String requestId) {
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        try {
            DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>(script, Long.class);
            Long result = redisTemplate.execute(redisScript, Collections.singletonList(lockKey),
                    requestId);
            return Long.valueOf(1).equals(result);
        } catch (Exception e) {
            log.error("Redis 释放分布式锁失败: lockKey={}, requestId={}", lockKey, requestId, e);
            return false;
        }
    }

    // ========== 批量操作 ==========

    /**
     * 批量获取
     *
     * @param keys 键集合
     * @return 值列表
     */
    public List<Object> multiGet(Collection<String> keys) {
        try {
            if (CollUtil.isEmpty(keys)) {
                return Collections.emptyList();
            }
            return redisTemplate.opsForValue().multiGet(keys);
        } catch (Exception e) {
            log.error("Redis multiGet操作失败: keys={}", keys, e);
            return Collections.emptyList();
        }
    }

    /**
     * 批量设置
     *
     * @param map 键值映射
     */
    public void multiSet(Map<String, Object> map) {
        try {
            if (CollUtil.isEmpty(map)) {
                return;
            }
            redisTemplate.opsForValue().multiSet(map);
        } catch (Exception e) {
            log.error("Redis multiSet操作失败: map={}", map, e);
        }
    }

    // ========== 工具方法 ==========

    /**
     * 执行Lua脚本
     *
     * @param script     脚本内容
     * @param keys       键列表
     * @param args       参数列表
     * @param resultType 返回类型
     * @return 执行结果
     */
    public <T> T executeScript(String script, List<String> keys, List<Object> args,
                               Class<T> resultType) {
        try {
            DefaultRedisScript<T> redisScript = new DefaultRedisScript<>(script, resultType);
            return redisTemplate.execute(redisScript, keys, args.toArray());
        } catch (Exception e) {
            log.error("Redis 执行Lua脚本失败: script={}, keys={}, args={}", script, keys, args, e);
            return null;
        }
    }

    /**
     * 模式匹配获取键
     *
     * @param pattern 匹配模式
     * @return 匹配的键集合
     */
    public Set<String> keys(String pattern) {
        try {
            return redisTemplate.keys(pattern);
        } catch (Exception e) {
            log.error("Redis keys操作失败: pattern={}", pattern, e);
            return Collections.emptySet();
        }
    }

    /**
     * 获取 Redis 信息
     *
     * @return Redis 信息
     */
    public Properties info() {
        try {
            return redisTemplate.getConnectionFactory().getConnection().info();
        } catch (Exception e) {
            log.error("Redis info操作失败", e);
            return new Properties();
        }
    }

    /**
     * 安全地转换对象类型
     *
     * @param value        原始值
     * @param converter    转换函数
     * @param defaultValue 默认值
     * @param <T>          目标类型
     * @return 转换后的值
     */
    public <T> T safeConvert(Object value, Function<Object, T> converter, T defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        try {
            return converter.apply(value);
        } catch (Exception e) {
            log.warn("Redis值转换失败: value={}, defaultValue={}", value, defaultValue, e);
            return defaultValue;
        }
    }

    /**
     * 构建带前缀的键
     *
     * @param prefix 前缀
     * @param key    键
     * @return 带前缀的键
     */
    public static String buildKey(String prefix, String key) {
        if (StrUtil.isBlank(prefix)) {
            return key;
        }
        return prefix + ":" + key;
    }

    /**
     * 构建带前缀和多个部分的键
     *
     * @param prefix 前缀
     * @param parts  键的各个部分
     * @return 带前缀的键
     */
    public static String buildKey(String prefix, String... parts) {
        if (StrUtil.isBlank(prefix)) {
            return String.join(":", parts);
        }
        return prefix + ":" + String.join(":", parts);
    }

    /**
     * 扫描指定前缀的key,返回指定数量的key
     *
     * @param prefix    key前缀
     * @param batchSize 每次扫描的key数量（正整数）
     * @return 匹配的key列表，空列表表示扫描完成或发生错误
     * @throws IllegalArgumentException 当prefix为空或batchSize非正时抛出
     */
    public List<String> scanKeysByPrefix(String prefix, int batchSize) {
        if (prefix == null || prefix.isEmpty()) {
            throw new IllegalArgumentException("Prefix must not be null or empty");
        }
        if (batchSize <= 0) {
            throw new IllegalArgumentException("Batch size must be positive");
        }

        ScanOptions options = ScanOptions.scanOptions()
                .match(prefix + "*")
                .count(batchSize)
                .build();

        List<String> keys = new ArrayList<>(batchSize);

        try (Cursor<String> cursor = redisTemplate.scan(options)) {
            while (cursor.hasNext() && keys.size() < batchSize) {
                keys.add(cursor.next());
            }
        } catch (Exception e) {
            // 记录异常日志（根据项目实际情况调整日志级别）
            log.error("Redis scan interrupted: {}", e.getMessage());
            // 返回空列表表示扫描异常终止
            return Collections.emptyList();
        }

        return keys;
    }

    /**
     * 统计指定前缀的key数量
     *
     * @param prefix key前缀（不能为空）
     * @return 匹配的key数量，-1表示发生错误
     * @throws IllegalArgumentException 当prefix为空时抛出
     */
    public long countKeysByPrefix(String prefix) {
        if (prefix == null || prefix.isEmpty()) {
            throw new IllegalArgumentException("Prefix must not be null or empty");
        }

        ScanOptions options = ScanOptions.scanOptions()
                .match(prefix + "*")
                .count(1000)
                .build();

        long count = 0;
        try (Cursor<String> cursor = redisTemplate.scan(options)) {
            while (cursor.hasNext()) {
                cursor.next();
                count++;
            }
        } catch (Exception e) {
            // 记录异常日志（根据项目实际情况调整日志级别）
            log.error("Error counting keys: {}", e.getMessage());
            return -1L; // 返回-1表示统计异常
        }

        return count;
    }
}