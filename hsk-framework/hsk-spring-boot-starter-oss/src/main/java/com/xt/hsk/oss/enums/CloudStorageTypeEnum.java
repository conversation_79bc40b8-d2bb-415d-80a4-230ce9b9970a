package com.xt.hsk.oss.enums;

import lombok.Getter;

/**
 * 云存储类型枚举
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@Getter
public enum CloudStorageTypeEnum {

    /**
     * 阿里云OSS
     */
    ALIYUN("aliyun", "阿里云OSS"),

    /**
     * 腾讯云COS
     */
    TENCENT("tencent", "腾讯云COS"),

    /**
     * 华为云OBS
     */
    HUAWEI("huawei", "华为云OBS"),

    /**
     * 微软Azure
     */
    AZURE("azure", "微软Azure"),

    /**
     * 亚马逊S3
     */
    AWS_S3("aws-s3", "亚马逊S3"),

    /**
     * 谷歌云存储
     */
    GOOGLE_CLOUD("google-cloud", "谷歌云存储"),

    /**
     * 本地存储
     */
    LOCAL("local", "本地存储");

    private final String code;
    private final String desc;

    CloudStorageTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据代码获取枚举
     */
    public static CloudStorageTypeEnum getByCode(String code) {
        for (CloudStorageTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        throw new IllegalArgumentException("不支持的云存储类型: " + code);
    }
} 