package com.xt.hsk.oss.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 云存储自动配置
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@Configuration
@EnableConfigurationProperties(CloudStorageProperties.class)
@ConditionalOnProperty(prefix = "hsk.cloud-storage", name = "type")
@Slf4j
public class CloudStorageAutoConfiguration {

    // /**
    //  * 云存储服务工厂
    //  */
    // @Bean
    // public CloudStorageServiceFactory cloudStorageServiceFactory(
    //     CloudStorageProperties properties) {
    //     return new CloudStorageServiceFactory(properties);
    // }
    //
    // /**
    //  * 云存储服务（当前配置的实现）
    //  */
    // @Bean
    // public CloudStorageService cloudStorageService(CloudStorageServiceFactory factory) {
    //     CloudStorageService service = factory.getCurrentService();
    //     log.info("云存储服务已初始化，当前类型: {}", service.getStorageType());
    //     return service;
    // }
} 