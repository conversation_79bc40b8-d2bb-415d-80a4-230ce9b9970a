package com.xt.hsk.oss.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 云存储配置属性
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@Data
@ConfigurationProperties(prefix = "hsk.cloud-storage")
public class CloudStorageProperties {

    /**
     * 存储类型：aliyun, tencent, huawei, azure, aws-s3, google-cloud, local
     */
    private String type = "aliyun";

    /**
     * 阿里云OSS配置
     */
    private AliyunConfig aliyun = new AliyunConfig();

    /**
     * 腾讯云COS配置
     */
    private TencentConfig tencent = new TencentConfig();

    /**
     * 华为云OBS配置
     */
    private HuaweiConfig huawei = new HuaweiConfig();

    /**
     * 本地存储配置
     */
    private LocalConfig local = new LocalConfig();

    /**
     * 阿里云OSS配置
     */
    @Data
    public static class AliyunConfig {

        /**
         * 访问端点
         */
        private String endpoint;
        /**
         * AccessKey ID
         */
        private String accessKeyId;
        /**
         * AccessKey Secret
         */
        private String accessKeySecret;
        /**
         * 存储桶名称
         */
        private String bucketName;
        /**
         * 访问域名
         */
        private String domain;
        /**
         * 文件存储目录前缀
         */
        private String dir = "";
    }

    /**
     * 腾讯云COS配置
     */
    @Data
    public static class TencentConfig {

        /**
         * 地域信息
         */
        private String region;
        /**
         * SecretId
         */
        private String secretId;
        /**
         * SecretKey
         */
        private String secretKey;
        /**
         * 存储桶名称
         */
        private String bucketName;
        /**
         * 访问域名
         */
        private String domain;
    }

    /**
     * 华为云OBS配置
     */
    @Data
    public static class HuaweiConfig {

        /**
         * 访问端点
         */
        private String endpoint;
        /**
         * Access Key
         */
        private String accessKey;
        /**
         * Secret Key
         */
        private String secretKey;
        /**
         * 存储桶名称
         */
        private String bucketName;
        /**
         * 访问域名
         */
        private String domain;
    }

    /**
     * 本地存储配置
     */
    @Data
    public static class LocalConfig {

        /**
         * 存储路径
         */
        private String path = "/upload";
        /**
         * 访问域名
         */
        private String domain = "http://localhost:8080";
    }
} 