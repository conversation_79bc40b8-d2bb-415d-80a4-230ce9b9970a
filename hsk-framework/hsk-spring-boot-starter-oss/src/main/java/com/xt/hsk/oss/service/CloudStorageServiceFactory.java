package com.xt.hsk.oss.service;

import com.xt.hsk.oss.config.CloudStorageProperties;
import com.xt.hsk.oss.enums.CloudStorageTypeEnum;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * 云存储服务工厂 实现策略模式，根据配置选择对应的云存储实现
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@Slf4j
public class CloudStorageServiceFactory {

    @Getter
    private final CloudStorageProperties properties;
    private final Map<String, CloudStorageService> serviceCache = new ConcurrentHashMap<>();
    @Getter
    private CloudStorageService currentService;

    public CloudStorageServiceFactory(CloudStorageProperties properties) {
        this.properties = properties;
        this.currentService = null;
    }


    /**
     * 根据类型获取云存储服务
     */
    public CloudStorageService getService(String type) {
        return serviceCache.computeIfAbsent(type, this::createServiceByType);
    }

    /**
     * 切换云存储类型
     */
    public void switchStorageType(String type) {
        CloudStorageService newService = getService(type);
        if (newService != null) {
            this.currentService = newService;
            log.info("云存储类型已切换为: {}", type);
        } else {
            log.warn("切换云存储类型失败，不支持的类型: {}", type);
        }
    }

    /**
     * 创建云存储服务实例
     */
    private CloudStorageService createCloudStorageService() {
        String type = properties.getType();
        if (!StringUtils.hasText(type)) {
            type = CloudStorageTypeEnum.ALIYUN.getCode(); // 默认使用阿里云
        }
        return createServiceByType(type);
    }

    /**
     * 根据类型创建对应的服务实例
     */
    private CloudStorageService createServiceByType(String type) {
        // try {
        //     CloudStorageTypeEnum typeEnum = CloudStorageTypeEnum.getByCode(type);
        //
        //     switch (typeEnum) {
        //         case ALIYUN:
        //             return createAliyunService();
        //         case TENCENT:
        //             return createTencentService();
        //         case HUAWEI:
        //             return createHuaweiService();
        //         case LOCAL:
        //             return createLocalService();
        //         default:
        //             log.warn("暂不支持的云存储类型: {}，使用默认的阿里云OSS", type);
        //             return createAliyunService();
        //     }
        // } catch (Exception e) {
        //     log.error("创建云存储服务失败: type={}, error={}", type, e.getMessage(), e);
        //     return createAliyunService(); // 降级到阿里云
        // }
        throw new UnsupportedOperationException("暂不支持的云存储类型: " + type);
    }

    // /**
    //  * 创建阿里云OSS服务
    //  */
    // private CloudStorageService createAliyunService() {
    //     CloudStorageProperties.AliyunConfig config = properties.getAliyun();
    //     if (config == null || !StringUtils.hasText(config.getEndpoint())) {
    //         throw new IllegalArgumentException("阿里云OSS配置不完整");
    //     }
    //     return new AliyunCloudStorageServiceImpl(config);
    // }

    /**
     * 创建腾讯云COS服务
     */
    private CloudStorageService createTencentService() {
        // TODO: 实现腾讯云COS服务
        throw new UnsupportedOperationException("腾讯云COS暂未实现");
    }

    /**
     * 创建华为云OBS服务
     */
    private CloudStorageService createHuaweiService() {
        // TODO: 实现华为云OBS服务
        throw new UnsupportedOperationException("华为云OBS暂未实现");
    }

    /**
     * 创建本地存储服务
     */
    private CloudStorageService createLocalService() {
        // TODO: 实现本地存储服务
        throw new UnsupportedOperationException("本地存储暂未实现");
    }

    /**
     * 获取所有支持的存储类型
     */
    public String[] getSupportedTypes() {
        return new String[]{
            CloudStorageTypeEnum.ALIYUN.getCode()
            // TODO: 添加其他已实现的类型
        };
    }
} 