// package com.xt.hsk.oss.service.impl;
//
// import com.aliyun.oss.OSS;
// import com.aliyun.oss.OSSClientBuilder;
// import com.aliyun.oss.model.ObjectMetadata;
// import com.xt.hsk.framework.common.exception.ServiceException;
// import com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants;
// import com.xt.hsk.oss.config.CloudStorageProperties;
// import com.xt.hsk.oss.enums.CloudStorageTypeEnum;
// import com.xt.hsk.oss.service.CloudStorageService;
// import lombok.extern.slf4j.Slf4j;
//
// import java.io.ByteArrayInputStream;
// import java.io.InputStream;
//
// /**
//  * 阿里云OSS存储实现
//  *
//  * <AUTHOR>
//  * @since 2025/06/04
//  */
// @Slf4j
// public class AliyunCloudStorageServiceImpl implements CloudStorageService {
//
//     private final OSS ossClient;
//     private final CloudStorageProperties.AliyunConfig config;
//
//     public AliyunCloudStorageServiceImpl(CloudStorageProperties.AliyunConfig config) {
//         this.config = config;
//         this.ossClient = new OSSClientBuilder().build(
//             config.getEndpoint(),
//             config.getAccessKeyId(),
//             config.getAccessKeySecret()
//         );
//         log.info("初始化阿里云OSS客户端成功，endpoint: {}, bucketName: {}",
//             config.getEndpoint(), config.getBucketName());
//     }
//
//     @Override
//     public String upload(InputStream inputStream, String path) {
//         try {
//             // 构建完整的文件路径（包含目录前缀）
//             String fullPath = buildFullPath(path);
//
//             ObjectMetadata metadata = new ObjectMetadata();
//             metadata.setContentType(getContentType(path));
//
//             ossClient.putObject(config.getBucketName(), fullPath, inputStream, metadata);
//             log.debug("阿里云OSS上传文件成功: {}", fullPath);
//             return getFileUrl(fullPath);
//         } catch (Exception e) {
//             log.error("阿里云OSS上传文件失败: path={}, error={}", path, e.getMessage(), e);
//             throw new ServiceException(GlobalErrorCodeConstants.OSS_FILE_UPLOAD_ERROR);
//         }
//     }
//
//     @Override
//     public String upload(byte[] data, String path) {
//         return upload(new ByteArrayInputStream(data), path);
//     }
//
//     @Override
//     public boolean delete(String path) {
//         try {
//             String fullPath = buildFullPath(path);
//             ossClient.deleteObject(config.getBucketName(), fullPath);
//             log.debug("阿里云OSS删除文件成功: {}", fullPath);
//             return true;
//         } catch (Exception e) {
//             log.error("阿里云OSS删除文件失败: path={}, error={}", path, e.getMessage(), e);
//             return false;
//         }
//     }
//
//     @Override
//     public String getFileUrl(String path) {
//         // 如果path已经是完整路径，直接使用；否则构建完整路径
//         String fullPath = path.contains(config.getDir()) ? path : buildFullPath(path);
//
//         if (config.getDomain() != null && !config.getDomain().isEmpty()) {
//             return config.getDomain() + "/" + fullPath;
//         }
//         return "https://" + config.getBucketName() + "." +
//             config.getEndpoint().replace("https://", "") + "/" + fullPath;
//     }
//
//     @Override
//     public boolean exists(String path) {
//         try {
//             String fullPath = buildFullPath(path);
//             return ossClient.doesObjectExist(config.getBucketName(), fullPath);
//         } catch (Exception e) {
//             log.error("阿里云OSS检查文件是否存在失败: path={}, error={}", path, e.getMessage(), e);
//             return false;
//         }
//     }
//
//     @Override
//     public String getStorageType() {
//         return CloudStorageTypeEnum.ALIYUN.getCode();
//     }
//
//     /**
//      * 构建完整的文件路径（包含目录前缀）
//      */
//     private String buildFullPath(String path) {
//         if (config.getDir() == null || config.getDir().isEmpty()) {
//             return path;
//         }
//
//         // 确保目录前缀以/结尾，路径不以/开头
//         String dir = config.getDir().endsWith("/") ? config.getDir() : config.getDir() + "/";
//         String fileName = path.startsWith("/") ? path.substring(1) : path;
//
//         return dir + fileName;
//     }
//
//     /**
//      * 根据文件扩展名获取Content-Type
//      */
//     private String getContentType(String path) {
//         String extension = path.substring(path.lastIndexOf(".") + 1).toLowerCase();
//         switch (extension) {
//             case "jpg":
//             case "jpeg":
//                 return "image/jpeg";
//             case "png":
//                 return "image/png";
//             case "gif":
//                 return "image/gif";
//             case "pdf":
//                 return "application/pdf";
//             case "txt":
//                 return "text/plain";
//             case "xlsx":
//                 return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
//             case "xls":
//                 return "application/vnd.ms-excel";
//             default:
//                 return "application/octet-stream";
//         }
//     }
//
//     /**
//      * 销毁资源
//      */
//     public void destroy() {
//         if (ossClient != null) {
//             ossClient.shutdown();
//             log.info("阿里云OSS客户端已关闭");
//         }
//     }
// }