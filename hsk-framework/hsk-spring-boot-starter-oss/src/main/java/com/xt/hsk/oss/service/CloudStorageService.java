package com.xt.hsk.oss.service;

import java.io.InputStream;

/**
 * 云存储服务接口 支持多云厂商的统一接口定义
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
public interface CloudStorageService {

    /**
     * 上传文件
     *
     * @param inputStream 文件流
     * @param path        文件路径
     * @return 文件访问URL
     */
    String upload(InputStream inputStream, String path);

    /**
     * 上传文件
     *
     * @param data 文件字节数组
     * @param path 文件路径
     * @return 文件访问URL
     */
    String upload(byte[] data, String path);

    /**
     * 删除文件
     *
     * @param path 文件路径
     * @return 是否删除成功
     */
    boolean delete(String path);

    /**
     * 获取文件访问URL
     *
     * @param path 文件路径
     * @return 文件访问URL
     */
    String getFileUrl(String path);

    /**
     * 检查文件是否存在
     *
     * @param path 文件路径
     * @return 是否存在
     */
    boolean exists(String path);

    /**
     * 获取存储类型
     *
     * @return 存储类型
     */
    String getStorageType();
} 