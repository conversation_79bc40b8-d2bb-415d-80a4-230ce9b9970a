package com.xt.hsk.module.trade.api.order.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

/**
 * 赠送订单创建 DTO
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Data
public class GiftOrderCreateReqDTO {

    /**
     * 学生用户ID列表
     */
    @NotEmpty(message = "学生用户ID列表不能为空")
    private List<Long> userIds;

    /**
     * 精品课程ID
     */
    @NotNull(message = "精品课程ID不能为空")
    private Long courseId;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人IP
     */
    private String operatorIp;

    /**
     * 备注信息
     */
    private String remarks;
} 