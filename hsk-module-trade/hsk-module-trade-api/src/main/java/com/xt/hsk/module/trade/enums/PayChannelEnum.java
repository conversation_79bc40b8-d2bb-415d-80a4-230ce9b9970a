package com.xt.hsk.module.trade.enums;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.Getter;

/**
 * 支付方式枚举
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
@Getter
public enum PayChannelEnum implements BasicEnum<Integer>, ArrayValuable<Integer>, VO {

    /**
     * 后台赠送
     */
    ADMIN_GIFT(0, "后台赠送");

    // 未来可以扩展其他支付方式
    // WECHAT_PAY(1, "微信支付"),
    // ALIPAY(2, "支付宝"),
    // APPLE_PAY(3, "Apple Pay");

    private final Integer code;
    private final String desc;

    PayChannelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Integer[] ARRAYS = Arrays.stream(values()).map(PayChannelEnum::getCode)
        .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

} 