package com.xt.hsk.module.trade.api.order;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.trade.api.order.dto.GiftOrderItemDTO;
import com.xt.hsk.module.trade.api.order.dto.OrderAdminRespDTO;
import com.xt.hsk.module.trade.api.order.dto.UserOrderPageReqDTO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * 交易订单 API
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Valid
public interface TradeOrderApi {

    /**
     * 根据用户ID查询交易订单
     *
     * @param userOrderPageReqDTO 用户订单页面 req dto
     * @return {@code PageResult<OrderAdminRespDTO> }
     */
    PageResult<OrderAdminRespDTO> getUserOrderItemList(
        @Valid UserOrderPageReqDTO userOrderPageReqDTO);

    /**
     * 批量赠送精品课程
     *
     * @param userIds    用户ID列表
     * @param courseId   课程ID
     * @param operatorId 操作人ID
     * @param operatorIp 操作人IP
     * @return 用户ID到订单信息的映射，Key为用户ID，Value为订单信息DTO
     */
    Map<Long, GiftOrderItemDTO> batchGiftEliteCourse(@NotEmpty List<Long> userIds,
        @NotEmpty Long courseId, @NotEmpty Long operatorId, @NotBlank String operatorIp);
}
