package com.xt.hsk.module.trade.enums;

import com.xt.hsk.framework.common.exception.ErrorCode;
import com.xt.hsk.framework.common.exception.enums.ServiceErrorCodeRange;

/**
 * 错误代码常量 模块 trade 错误码区间 [1-036-000-000 ~ 1-040-000-000)] 036 主订单模块
 * <p>
 * 教育系统，互动课业务 使用 024 业务模块编码
 *
 * <AUTHOR>
 * @see ServiceErrorCodeRange yudao设计说明
 * @since 2025/06/19
 */
public interface ErrorCodeConstants {

    /**
     * 订单不存在
     */
    ErrorCode ORDER_NOT_EXISTS = new ErrorCode(1_036_001_001, "订单不存在");

    /**
     * 订单创建校验失败
     */
    ErrorCode ORDER_CREATE_VALIDATE_FAILURE = new ErrorCode(1_036_001_002, "订单创建校验失败");

}
