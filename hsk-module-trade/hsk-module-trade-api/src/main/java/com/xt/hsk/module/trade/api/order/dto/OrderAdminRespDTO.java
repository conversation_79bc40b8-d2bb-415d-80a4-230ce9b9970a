package com.xt.hsk.module.trade.api.order.dto;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.enums.CurrencyEnum;
import com.xt.hsk.module.trade.enums.OrderSourceEnum;
import com.xt.hsk.module.trade.enums.OrderStatusEnum;
import com.xt.hsk.module.trade.enums.OrderTypeEnum;
import com.xt.hsk.module.trade.enums.PayChannelEnum;
import com.xt.hsk.module.trade.enums.ProductTypeEnum;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;

/**
 * 订单DTO
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Data
public class OrderAdminRespDTO implements VO {

    /**
     * 交易订单 ID
     */
    private Long tradeOrderId;

    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户昵称
     */
    private String userNickname;
    /**
     * 用户手机区号
     */
    private String userCountryCode;
    /**
     * 用户手机号
     */
    private String userMobile;
    /**
     * 货币单位 (例如: CNY, USD, VND)
     *
     * @see CurrencyEnum
     */
    @Trans(type = TransType.ENUM, key = "code", target = CurrencyEnum.class, ref = "currencyDesc")
    private String currency;
    /**
     * 货币描述
     */
    private String currencyDesc;

    /**
     * 订单原始总金额
     */
    private BigDecimal totalAmount;
    /**
     * 实际支付金额
     */
    private BigDecimal payAmount;
    /**
     * 订单状态（0-待支付 1-已完成 2-支付中 3-已关闭 4-退款申请中 5-部分退款 6-全部退款 7-退款失败）
     *
     * @see OrderStatusEnum
     */
    @Trans(type = TransType.ENUM, key = "code", target = OrderStatusEnum.class, ref = "orderStatusDesc")
    private Integer orderStatus;
    /**
     * 订单状态描述
     */
    private String orderStatusDesc;
    /**
     * 订单来源（0-后台赠送 1-app 2-h5 ）
     *
     * @see OrderSourceEnum
     */
    @Trans(type = TransType.ENUM, key = "code", target = OrderSourceEnum.class, ref = "orderSourceDesc")
    private Integer orderSource;
    /**
     * 订单源描述
     */
    private String orderSourceDesc;
    /**
     * 订单类型（0-赠送订单 1-普通订单）
     *
     * @see OrderTypeEnum
     */
    @Trans(type = TransType.ENUM, key = "code", target = OrderTypeEnum.class, ref = "orderTypeDesc")
    private Integer orderType;
    /**
     * 订单类型 描述
     */
    private String orderTypeDesc;
    /**
     * 支付方式（0-后台赠送）
     *
     * @see PayChannelEnum
     */
    @Trans(type = TransType.ENUM, key = "code", target = PayChannelEnum.class, ref = "payChannelDesc")
    private Integer payChannel;
    /**
     * 支付渠道名称
     */
    private String payChannelDesc;

    /**
     * 订单时间
     */
    private LocalDateTime orderTime;

    /**
     * 商品ID
     */
    private Long productId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品类型
     */
    @Trans(type = TransType.ENUM, key = "code", target = ProductTypeEnum.class, ref = "productDesc")
    private Integer productType;
    /**
     * 商品类型描述
     */
    private String productDesc;
    /**
     * 上架状态 1：上架 2：下架 3：待上架
     */
    private Integer listingStatus;
    /**
     * 学习有效期 1：长期有效 2：按截止日期 3：按天数
     */
    private Integer learningValidityPeriod;
    /**
     * 有效期
     */
    private String periodValidity;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;

    private Map<String, Object> transMap = new HashMap<>();
}
