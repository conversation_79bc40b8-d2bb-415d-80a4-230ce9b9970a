package com.xt.hsk.module.trade.enums;

import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.Getter;

/**
 * 售后状态枚举
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
@Getter
public enum AfterSaleStatusEnum implements BasicEnum<Integer>, ArrayValuable<Integer> {

    /**
     * 未售后
     */
    NO_AFTER_SALE(0, "未售后"),

    /**
     * 售后中
     */
    AFTER_SALE_PROCESSING(1, "售后中"),

    /**
     * 部分退款
     */
    PARTIAL_REFUNDED(2, "部分退款"),

    /**
     * 全部退款
     */
    FULL_REFUNDED(3, "全部退款"),
    /**
     * 售后失败
     */
    AFTER_SALE_FAILED(4, "售后失败"),
    ;

    private final Integer code;
    private final String desc;

    AfterSaleStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Integer[] ARRAYS = Arrays.stream(values()).map(AfterSaleStatusEnum::getCode)
        .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

} 