package com.xt.hsk.module.trade.enums;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.Getter;

/**
 * 操作人类型枚举
 * <p>
 * 定义了订单操作中不同的操作人类型，用于区分操作来源。
 *
 * <AUTHOR>
 * @since 2025/06/22
 */
@Getter
public enum OperatorTypeEnum implements BasicEnum<Integer>, ArrayValuable<Integer>, VO {

    /**
     * 用户操作
     * <p>
     * 由系统用户（如购买者）发起的操作。
     */
    USER(1, "用户"),

    /**
     * 管理员操作
     * <p>
     * 由系统管理员或客服人员发起的操作。
     */
    ADMIN(2, "管理员"),

    /**
     * 系统操作
     * <p>
     * 由系统自动执行的操作，如定时任务、自动处理流程等。
     */
    SYSTEM(3, "系统"),
    ;

    private final Integer code;
    private final String desc;

    OperatorTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Integer[] ARRAYS = Arrays.stream(values()).map(OperatorTypeEnum::getCode)
        .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

} 