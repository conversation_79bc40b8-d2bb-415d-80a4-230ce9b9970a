package com.xt.hsk.module.trade.enums;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.Getter;

/**
 * 订单日志操作类型枚举
 * <p>
 * 定义了订单生命周期中各种操作的类型，用于在日志中记录不同的操作行为。
 *
 * <AUTHOR>
 * @since 2025/06/22
 */
@Getter
public enum OrderLogActionTypeEnum implements BasicEnum<Integer>, ArrayValuable<Integer>, VO {

    /**
     * 创建订单
     * <p>
     * 用户提交订单，系统生成新订单记录的操作。
     */
    CREATE(1, "创建订单"),

    /**
     * 支付操作
     * <p>
     * 用户发起支付、支付成功或支付失败的相关操作。
     */
    PAYMENT(2, "支付"),

    /**
     * 退款操作
     * <p>
     * 申请退款、审核退款、退款成功或退款失败的相关操作。
     */
    REFUND(3, "退款"),

    /**
     * 关闭订单
     * <p>
     * 用户主动取消订单或系统自动关闭超时未支付订单的操作。
     */
    CLOSE(4, "关闭"),

    /**
     * 修改订单
     * <p>
     * 修改订单信息、更新订单状态等操作。
     */
    MODIFY(5, "修改"),

    /**
     * 赠送订单
     * <p>
     * 系统或管理员创建赠品订单的操作。
     */
    GIFT(6, "赠送"),
    ;

    private final Integer code;
    private final String desc;

    OrderLogActionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Integer[] ARRAYS = Arrays.stream(values()).map(OrderLogActionTypeEnum::getCode)
        .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

} 