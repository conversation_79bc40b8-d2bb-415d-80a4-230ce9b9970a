package com.xt.hsk.module.trade.enums;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.Getter;

/**
 * 订单类型枚举
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
@Getter
public enum OrderTypeEnum implements BasicEnum<Integer>, ArrayValuable<Integer>, VO {

    /**
     * 赠送订单
     */
    GIFT_ORDER(0, "赠送订单"),

    /**
     * 普通订单
     */
    NORMAL_ORDER(1, "普通订单");

    private final Integer code;
    private final String desc;

    OrderTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Integer[] ARRAYS = Arrays.stream(values()).map(OrderTypeEnum::getCode)
        .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

} 