package com.xt.hsk.module.trade.api.order.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 赠送订单子项 DTO
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Data
public class GiftOrderItemDTO {

    /**
     * 主订单ID
     */
    private Long orderId;

    /**
     * 主订单号
     */
    private String orderNo;

    /**
     * 子订单ID
     */
    private Long orderItemId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 课程ID
     */
    private Long courseId;
    /**
     * 创建时间
     */
    private LocalDateTime orderTime;
}