package com.xt.hsk.module.trade.enums;

import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.Getter;

/**
 * 订单操作类型枚举
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
@Getter
public enum OrderActionTypeEnum implements BasicEnum<Integer>, ArrayValuable<Integer> {

    /**
     * 创建订单
     */
    CREATE_ORDER(1, "创建订单"),

    /**
     * 支付
     */
    PAYMENT(2, "支付"),

    /**
     * 退款
     */
    REFUND(3, "退款"),

    /**
     * 关闭
     */
    CLOSE(4, "关闭");

    private final Integer code;
    private final String desc;

    OrderActionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Integer[] ARRAYS = Arrays.stream(values()).map(OrderActionTypeEnum::getCode)
        .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }


} 