package com.xt.hsk.module.trade.annotation;

import com.xt.hsk.module.trade.enums.OperatorTypeEnum;
import com.xt.hsk.module.trade.enums.OrderLogActionTypeEnum;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 订单日志注解
 * <p>
 * 标记需要记录订单操作日志的方法。使用AOP拦截带有此注解的方法，自动记录订单操作日志。 可用于订单状态变更、订单支付、退款等关键操作的日志记录。
 * <p>
 * 使用示例:
 * <pre>
 * {@code
 * @OrderLog(actionType = OrderLogActionTypeEnum.PAYMENT, operatorType = OperatorTypeEnum.USER,
 *           spEL = "'用户' + #userId + '支付订单' + #orderNo",
 *           beforeStatus = "#oldStatus", afterStatus = "#newStatus")
 * public void payOrder(Long orderId, Long userId, String orderNo, Integer oldStatus, Integer newStatus) {
 *     // 支付订单逻辑
 * }
 * }
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/06/22
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OrderLog {

    /**
     * 操作类型
     * <p>
     * 定义当前操作的类型，如创建订单、支付、退款等
     */
    OrderLogActionTypeEnum actionType();

    /**
     * 操作人类型
     * <p>
     * 定义操作的来源，如用户、管理员或系统
     */
    OperatorTypeEnum operatorType() default OperatorTypeEnum.SYSTEM;

    /**
     * 订单ID的SpEL表达式
     * <p>
     * 用于从方法参数中获取订单ID。默认尝试获取名为orderId的参数
     */
    String orderIdSpEL() default "#orderId";

    /**
     * 子订单ID的SpEL表达式
     * <p>
     * 用于从方法参数中获取子订单ID，若操作针对主订单，可不填
     */
    String orderItemIdSpEL() default "";

    /**
     * 操作人ID的SpEL表达式
     * <p>
     * 用于从方法参数中获取操作人ID，若未指定则尝试从当前登录用户获取
     */
    String operatorIdSpEL() default "";

    /**
     * 日志内容的SpEL表达式
     * <p>
     * 自定义日志内容，支持SpEL表达式从方法参数中获取值
     */
    String spEL() default "";

    /**
     * 操作前状态的SpEL表达式
     * <p>
     * 用于从方法参数中获取操作前的状态
     */
    String beforeStatusSpEL() default "";

    /**
     * 操作后状态的SpEL表达式
     * <p>
     * 用于从方法参数中获取操作后的状态
     */
    String afterStatusSpEL() default "";

    /**
     * 错误码的SpEL表达式
     * <p>
     * 用于从方法抛出的异常中获取错误码，仅在方法执行失败时使用
     */
    String errorCodeSpEL() default "";

} 