package com.xt.hsk.module.trade.enums;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.Getter;

/**
 * 订单状态枚举
 * <p>
 * 定义了订单在生命周期中可能处于的各种状态。
 * 订单状态反映了订单处理的不同阶段，用于追踪订单从创建到完成或关闭的整个流程。
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
@Getter
public enum OrderStatusEnum implements BasicEnum<Integer>, ArrayValuable<Integer>, VO {

    /**
     * 待支付
     * <p>
     * 订单已创建但尚未支付的初始状态。
     * 在此状态下，用户可以选择支付订单或取消订单。
     * 系统也会监控订单是否超时，若超时则自动关闭。
     */
    WAITING_PAYMENT(0, "待支付"),

    /**
     * 已完成
     * <p>
     * 订单支付成功并且已经完成交付的状态。
     * 对于虚拟商品，支付成功后直接进入此状态。
     * 在此状态下，用户可以申请售后/退款。
     */
    COMPLETED(1, "已完成"),

    /**
     * 支付中
     * <p>
     * 用户已发起支付请求，但支付结果尚未确认的中间状态。
     * 系统等待支付平台的回调通知以确认支付结果。
     */
    PAYING(2, "支付中"),

    /**
     * 已关闭
     * <p>
     * 订单被取消或超时关闭的终态。
     * 可能由用户主动取消或系统因超时自动关闭。
     * 已关闭的订单不可再操作。
     */
    CLOSED(3, "已关闭"),

    /**
     * 退款申请中
     * <p>
     * 用户已提交退款申请，等待商家或系统审核的状态。
     * 可能来源于已完成状态或部分退款后的再次申请。
     */
    REFUND_APPLYING(4, "退款申请中"),

    /**
     * 部分退款
     * <p>
     * 订单中的部分商品已退款成功的状态。
     * 用户可以对剩余未退款的部分再次申请退款。
     */
    PARTIAL_REFUNDED(5, "部分退款"),

    /**
     * 全部退款
     * <p>
     * 订单全额退款成功的终态。
     * 表示订单的所有商品均已退款，订单交易结束。
     */
    FULL_REFUNDED(6, "全部退款"),

    /**
     * 退款失败
     * <p>
     * 退款申请被拒绝的状态。
     * 当审核人员拒绝用户的退款申请时，订单将变为此状态。
     * 根据业务规则，一般只允许申请一次退款。
     */
    REFUND_FAILED(7, "退款失败"),
    ;

    private final Integer code;
    private final String desc;

    OrderStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Integer[] ARRAYS = Arrays.stream(values()).map(OrderStatusEnum::getCode)
        .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

} 