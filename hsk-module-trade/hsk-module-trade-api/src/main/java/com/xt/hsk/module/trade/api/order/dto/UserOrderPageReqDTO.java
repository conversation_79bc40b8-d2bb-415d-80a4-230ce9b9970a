package com.xt.hsk.module.trade.api.order.dto;

import com.xt.hsk.framework.common.pojo.PageParam;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 用户订单页面请求 DTO
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserOrderPageReqDTO extends PageParam {

    /**
     * 用户 ID
     */
    @NotNull(message = "用户 ID 不能为空")
    private Long userId;
}
