package com.xt.hsk.module.trade.enums;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.Getter;

/**
 * 订单来源枚举
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
@Getter
public enum OrderSourceEnum implements BasicEnum<Integer>, ArrayValuable<Integer>, VO {

    /**
     * 后台赠送
     */
    ADMIN_GIFT(0, "后台赠送"),

    /**
     * APP端
     */
    APP(1, "APP"),

    /**
     * H5端
     */
    H5(2, "H5");

    private final Integer code;
    private final String desc;

    OrderSourceEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Integer[] ARRAYS = Arrays.stream(values()).map(OrderSourceEnum::getCode)
        .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

} 