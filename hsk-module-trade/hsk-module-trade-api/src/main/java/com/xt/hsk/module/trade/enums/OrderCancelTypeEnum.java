package com.xt.hsk.module.trade.enums;

import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.Getter;

/**
 * 订单取消类型枚举
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
@Getter
public enum OrderCancelTypeEnum implements BasicEnum<Integer>, ArrayValuable<Integer> {

    /**
     * 超时未支付关单
     */
    TIMEOUT_CLOSING(1, "超时未支付关单"),

    /**
     * 用户手动取消订单
     */
    USER_CANCELLATION(2, "用户手动取消订单");

    private final Integer code;
    private final String desc;

    OrderCancelTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Integer[] ARRAYS = Arrays.stream(values()).map(OrderCancelTypeEnum::getCode)
        .toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }


} 