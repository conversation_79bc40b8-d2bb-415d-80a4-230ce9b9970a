package com.xt.hsk.module.trade.enums;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 产品类型 枚举
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Getter
@AllArgsConstructor
public enum ProductTypeEnum implements BasicEnum<Integer>, ArrayValuable<Integer>, VO {
    /**
     * 精品课
     */
    ELITE_COURSE(1, "精品课"),
    ;
    private final Integer code;
    private final String desc;
    private static Integer[] ARRAYS = Arrays.stream(values()).map(ProductTypeEnum::getCode)
        .toArray(Integer[]::new);

    public Integer[] array() {
        return ARRAYS;
    }
}
