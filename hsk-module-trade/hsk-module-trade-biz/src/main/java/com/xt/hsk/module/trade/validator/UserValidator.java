package com.xt.hsk.module.trade.validator;

import cn.hutool.core.collection.CollUtil;
import com.xt.hsk.module.user.api.AppUserApi;
import com.xt.hsk.module.user.api.dto.AppUserRespDTO;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 用户校验处理器
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Component
@Slf4j
public class UserValidator extends AbstractGiftOrderValidator {


    @Resource
    private AppUserApi appUserApi;

    @Override
    protected ValidateResult doValidate(List<Long> userIds, Long courseId) {
        log.info("[UserValidator] 开始校验用户信息，用户数量：{}", userIds.size());

        if (CollUtil.isEmpty(userIds)) {
            return ValidateResult.failure("用户ID列表不能为空");
        }

        try {
            // 调用用户模块API校验用户是否存在
            List<AppUserRespDTO> userList = appUserApi.getUserList(userIds);
            if (CollUtil.isEmpty(userList)) {
                return ValidateResult.failure("所有用户ID都无效");
            }

            // 获取有效的用户ID集合
            Set<Long> validUserIds = userList.stream()
                .map(AppUserRespDTO::getId)
                .collect(Collectors.toSet());

            // 找出无效的用户ID
            List<Long> invalidUserIds = userIds.stream()
                .filter(id -> !validUserIds.contains(id))
                .toList();

            if (CollUtil.isNotEmpty(invalidUserIds)) {
                String errorMsg = String.format("存在无效的用户ID：%s",
                    CollUtil.join(invalidUserIds, ","));
                log.warn("[UserValidator] 用户校验失败: {}", errorMsg);
                return ValidateResult.failure(errorMsg);
            }

            log.info("[UserValidator] 用户校验通过");
            return ValidateResult.success();
        } catch (Exception e) {
            log.error("[UserValidator] 用户校验异常", e);
            return ValidateResult.failure("用户校验失败：" + e.getMessage());
        }
    }
}