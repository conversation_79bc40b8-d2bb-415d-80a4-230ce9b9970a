package com.xt.hsk.module.trade.controller.admin.order.vo;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import com.xt.hsk.framework.common.enums.CurrencyEnum;
import com.xt.hsk.framework.common.pojo.PageParam;
import com.xt.hsk.framework.common.validation.InEnum;
import com.xt.hsk.module.edu.enums.elitecourse.LearningValidityPeriodEnum;
import com.xt.hsk.module.trade.enums.OrderSourceEnum;
import com.xt.hsk.module.trade.enums.OrderStatusEnum;
import com.xt.hsk.module.trade.enums.OrderTypeEnum;
import com.xt.hsk.module.trade.enums.PayChannelEnum;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 后台管理系统 订单分页查询条件VO类
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AdminOrderPageReqVO extends PageParam {

    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户手机区号
     */
    private String userCountryCode;
    /**
     * 用户手机号
     */
    private String userMobile;
    /**
     * 商品类型
     */
    private Integer productType;
    /**
     * 商品名称快照
     */
    private String productName;

    /**
     * 订单状态（0-待支付 1-已完成 2-支付中 3-已关闭 4-退款申请中 5-部分退款 6-全部退款 7-退款失败）
     *
     * @see OrderStatusEnum
     */
    @InEnum(value = OrderStatusEnum.class, message = "订单状态必须是 {value}")
    private Integer orderStatus;

    /**
     * 支付方式（0-后台赠送）
     *
     * @see PayChannelEnum
     */
    @InEnum(value = PayChannelEnum.class, message = "支付方式必须是 {value}")
    private Integer payChannel;

    /**
     * 订单来源（0-后台赠送 1-app 2-h5 ）
     *
     * @see OrderSourceEnum
     */
    @InEnum(value = OrderSourceEnum.class, message = "订单来源必须是 {value}")
    private Integer orderSource;

    /**
     * 订单类型（0-赠送订单 1-普通订单）
     *
     * @see OrderTypeEnum
     */
    @InEnum(value = OrderTypeEnum.class, message = "订单类型必须是 {value}")
    private Integer orderType;

    /**
     * 选中的ID列表（用于导出）
     */
    private List<Long> orderItemIds;

    /**
     * 订单时间范围。传时间戳,后端框架会在json传参时自动将时间戳转为Time类型 示例值：["1123123131", "12312321"]。
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] orderTime;

    /**
     * 货币单位 (例如: CNY, USD, VND)
     *
     * @see CurrencyEnum
     */
    @InEnum(value = CurrencyEnum.class, message = "货币单位必须是 {value}")
    private String currency;

    /**
     * 学习有效期 1：长期有效 2：按截止日期 3：按天数
     *
     * @see LearningValidityPeriodEnum
     */
    private Integer learningValidityPeriod;
    /**
     * 截至日期
     */
    private LocalDateTime deadline;
    /**
     * 有效天数
     */
    private Integer effectiveDays;
    /**
     * 商品ID列表
     */
    private List<Long> productIdList;
}
