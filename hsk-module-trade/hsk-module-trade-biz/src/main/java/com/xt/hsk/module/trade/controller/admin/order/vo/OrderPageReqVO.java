package com.xt.hsk.module.trade.controller.admin.order.vo;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import com.xt.hsk.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 订单分页查询VO类
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderPageReqVO extends PageParam {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户昵称
     */
    private String userNickname;

    /**
     * 用户手机区号
     */
    private String userCountryCode;

    /**
     * 用户手机号
     */
    private String userMobile;

    /**
     * 货币单位 (例如: CNY, USD, VND)
     */
    private String currency;

    /**
     * 用户下单IP
     */
    private String userIp;

    /**
     * 商品数量
     */
    private Integer productCount;

    /**
     * 付款时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] payTime;

    /**
     * 支付成功时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] paySuccessTime;

    /**
     * 订单取消时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] cancelTime;

    /**
     * 订单取消类型（1-超时未支付 2-用户手动取消）
     */
    private Integer cancelType;

    /**
     * 订单原始总金额
     */
    private BigDecimal totalAmount;

    /**
     * 总优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 实际支付金额
     */
    private BigDecimal payAmount;

    /**
     * 订单状态（0-待支付 1-已完成 2-支付中 3-已关闭 4-退款申请中 5-部分退款 6-全部退款）
     */
    private Integer orderStatus;

    /**
     * 退款状态（0-未售后 1-售后中 2-部分退款 3-全部退款）
     */
    private Integer afterSaleStatus;

    /**
     * 已退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 优惠券减免金额
     */
    private BigDecimal couponReductionAmount;

    /**
     * 订单来源（0-后台赠送 1-app 2-h5 ）
     */
    private Integer orderSource;

    /**
     * 订单类型（0-赠送订单 1-普通订单）
     */
    private Integer orderType;

    /**
     * 支付方式（0-后台赠送）
     */
    private Integer payChannel;

    /**
     * 版本号，用于乐观锁
     */
    private Integer version;

    /**
     * 创建时间(下单时间)
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
