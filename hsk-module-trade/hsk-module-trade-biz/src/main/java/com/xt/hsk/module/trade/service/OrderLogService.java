package com.xt.hsk.module.trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderLogDO;
import com.xt.hsk.module.trade.enums.OperatorTypeEnum;
import com.xt.hsk.module.trade.enums.OrderLogActionTypeEnum;

/**
 * 订单日志 Service 接口
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
public interface OrderLogService extends IService<OrderLogDO> {

    /**
     * 记录订单操作日志
     *
     * @param tradeOrderId     主订单ID
     * @param tradeOrderNo     主订单号
     * @param tradeOrderItemId 子订单ID，若操作针对整个主订单则为null
     * @param actionType       操作类型
     * @param beforeStatus     操作前状态
     * @param afterStatus      操作后状态
     * @param operatorId       操作人ID
     * @param operatorType     操作人类型
     * @param notes            备注信息
     * @param errorCode        错误码，操作失败时使用
     * @param ip               操作IP
     * @return 订单日志ID
     */
    Long createOrderLog(Long tradeOrderId, String tradeOrderNo, Long tradeOrderItemId,
        OrderLogActionTypeEnum actionType, String beforeStatus, String afterStatus,
        Long operatorId, OperatorTypeEnum operatorType, String notes,
        String errorCode, String ip);

}
