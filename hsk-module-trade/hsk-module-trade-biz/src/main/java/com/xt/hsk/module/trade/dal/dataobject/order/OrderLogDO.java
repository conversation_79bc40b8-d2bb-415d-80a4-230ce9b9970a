package com.xt.hsk.module.trade.dal.dataobject.order;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 订单日志 do
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
@TableName("trade_order_log")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderLogDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 主订单ID
     */
    private Long tradeOrderId;
    /**
     * 主订单号
     */
    private String tradeOrderNo;
    /**
     * 关联子订单ID，若操作针对整个主订单则为空
     */
    private Long tradeOrderItemId;
    /**
     * 操作类型(1-创建订单 2-支付 3-退款 4-关闭)
     */
    private Integer actionType;
    /**
     * 操作前状态
     */
    private String beforeStatus;
    /**
     * 操作后状态
     */
    private String afterStatus;
    /**
     * 操作IP
     */
    private String ip;
    /**
     * 操作人ID
     */
    private Long operatorId;
    /**
     * 操作人类型 (1-用户 2-管理员 3-系统)
     */
    private Integer operatorType;
    /**
     * 失败操作错误码
     */
    private String errorCode;
    /**
     * 备注信息
     */
    private String notes;

}
