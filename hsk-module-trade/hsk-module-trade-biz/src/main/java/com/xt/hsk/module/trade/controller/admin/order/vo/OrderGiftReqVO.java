package com.xt.hsk.module.trade.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

/**
 * 订单赠送 Request VO
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Schema(description = "管理后台 - 订单赠送请求 VO")
@Data
public class OrderGiftReqVO {

    @Schema(description = "学生用户ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "学生用户ID列表不能为空")
    private List<Long> userIds;

    @Schema(description = "精品课程ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "精品课程ID不能为空")
    private Long courseId;

    @Schema(description = "备注信息")
    private String remarks;
} 