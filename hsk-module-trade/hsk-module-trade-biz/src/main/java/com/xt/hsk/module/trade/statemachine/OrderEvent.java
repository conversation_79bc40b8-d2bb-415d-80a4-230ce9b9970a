package com.xt.hsk.module.trade.statemachine;

/**
 * 订单状态机事件
 * <p>
 * 定义了触发订单状态变化的各种事件类型。 这些事件由系统或用户操作触发，用于驱动订单状态机进行状态转换。
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
public enum OrderEvent {
    /**
     * 支付操作 用户发起支付请求，订单状态从待支付变为支付中。
     */
    PAY,

    /**
     * 支付成功 支付平台回调通知支付成功，订单状态从支付中变为已完成。
     */
    PAY_SUCCESS,

    /**
     * 支付失败 支付平台回调通知支付失败，或支付超时，订单状态从支付中回退到待支付。
     */
    PAY_FAILED,

    /**
     * 取消订单 用户主动取消订单，订单状态从待支付变为已关闭。
     */
    CANCEL,

    /**
     * 订单超时 订单在指定时间内未完成支付，系统自动关闭订单，状态从待支付变为已关闭。
     */
    TIMEOUT,

    /**
     * 赠送订单 系统或管理员创建赠品订单，直接从待支付状态变为已完成状态，不需要支付。
     */
    GIFT,

    /**
     * 申请退款 用户申请退款，订单状态从已完成变为退款申请中； 或已部分退款的订单再次申请退款。
     */
    APPLY_REFUND,

    /**
     * 部分退款 审核通过部分退款申请，订单状态从退款申请中变为部分退款。
     */
    PARTIAL_REFUND,

    /**
     * 全额退款 审核通过全额退款申请，订单状态从退款申请中变为全额退款。
     */
    FULL_REFUND,

    /**
     * 拒绝退款 审核拒绝退款申请，订单状态从退款申请中变为退款失败。
     */
    REFUND_REJECT
} 