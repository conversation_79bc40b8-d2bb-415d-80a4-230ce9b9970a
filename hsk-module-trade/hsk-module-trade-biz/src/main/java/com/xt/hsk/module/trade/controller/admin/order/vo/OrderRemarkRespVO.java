package com.xt.hsk.module.trade.controller.admin.order.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 订单备份列表展示 vo
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Schema(description = "管理后台 - 订单备注 Response VO")
@Data
public class OrderRemarkRespVO implements VO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long tradeOrderId;

    /**
     * 备注内容
     */
    private String content;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建者ID
     */
    @Trans(type = TransType.SIMPLE, target = AdminUserDO.class, fields = "nickname", ref = "creatorName")
    private String creator;

    /**
     * 创建者名称
     */
    private String creatorName;
} 