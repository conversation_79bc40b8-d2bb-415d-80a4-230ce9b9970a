package com.xt.hsk.module.trade.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.api.dto.EliteCourseValidityPeriodReqDTO;
import com.xt.hsk.module.edu.api.elitecourse.EliteCourseApi;
import com.xt.hsk.module.trade.api.order.dto.OrderAdminRespDTO;
import com.xt.hsk.module.trade.api.order.dto.UserOrderPageReqDTO;
import com.xt.hsk.module.trade.controller.admin.order.vo.AdminOrderPageReqVO;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderAdminRespVO;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderItemDO;
import com.xt.hsk.module.trade.dal.mysql.order.OrderItemMapper;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 子订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OrderItemServiceImpl extends ServiceImpl<OrderItemMapper, OrderItemDO> implements
    OrderItemService {

    @Resource
    private OrderItemMapper orderItemMapper;

    @Resource
    private EliteCourseApi eliteCourseApi;


    @Override
    public PageResult<OrderAdminRespVO> selectPageJoinOrderPage(AdminOrderPageReqVO pageReqVO) {
        // 处理按有效期查询的逻辑
        handleValidityPeriodQuery(pageReqVO);

        PageResult<OrderAdminRespVO> page = orderItemMapper.selectPageJoinOrderPage(pageReqVO);
        
        // 设置有效期信息
        setPeriodValidity(page.getList(), OrderAdminRespVO::getProductId, OrderAdminRespVO::setPeriodValidity);

        return page;
    }

    @Override
    public Long selectPageJoinOrderPageCount(AdminOrderPageReqVO pageReqVO) {
        return orderItemMapper.selectPageJoinOrderPageCount(pageReqVO);
    }

    @Override
    public PageResult<OrderAdminRespDTO> selectPageOrderItemByUserId(UserOrderPageReqDTO userOrderPageReqDTO) {
        PageResult<OrderAdminRespDTO> page = orderItemMapper.selectPageOrderItemByUserId(userOrderPageReqDTO);
        
        // 设置有效期信息
        setPeriodValidity(page.getList(), OrderAdminRespDTO::getProductId, OrderAdminRespDTO::setPeriodValidity);

        return page;
    }

    @Override
    public List<OrderItemDO> selectOrderItemByOrderId(Long orderId) {
        return this.lambdaQuery().eq(OrderItemDO::getTradeOrderId, orderId).list();
    }

    /**
     * 处理按有效期查询的逻辑
     * 
     * @param pageReqVO 查询参数
     */
    private void handleValidityPeriodQuery(AdminOrderPageReqVO pageReqVO) {
        if (Objects.nonNull(pageReqVO.getLearningValidityPeriod())) {
            EliteCourseValidityPeriodReqDTO reqDTO = new EliteCourseValidityPeriodReqDTO();
            reqDTO.setLearningValidityPeriod(pageReqVO.getLearningValidityPeriod());
            reqDTO.setDeadline(pageReqVO.getDeadline());
            reqDTO.setEffectiveDays(pageReqVO.getEffectiveDays());
            List<Long> courseIdList = eliteCourseApi.listCourseIdByValidityPeriod(reqDTO);
            pageReqVO.setProductIdList(courseIdList);
        }
    }

    /**
     * 通用的设置有效期方法
     * 
     * @param <T> 泛型类型
     * @param pageList 数据列表
     * @param productIdGetter 获取产品ID的函数
     * @param periodValiditySetter 设置有效期的函数
     */
    @Override
    public  <T> void setPeriodValidity(List<T> pageList,
                                       Function<T, Long> productIdGetter,
                                       java.util.function.BiConsumer<T, String> periodValiditySetter) {
        if (CollUtil.isEmpty(pageList)) {
            return;
        }

        // 提取所有产品ID
        List<Long> productIdList = pageList.stream()
                .map(productIdGetter)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        // 批量获取有效期信息
        Map<Long, String> validityMap = eliteCourseApi.getPeriodValidityByIds(productIdList);

        // 设置有效期
        pageList.forEach(item -> {
            Long productId = productIdGetter.apply(item);
            String validity = validityMap.getOrDefault(productId, "-");
            periodValiditySetter.accept(item, validity);
        });
    }
}
