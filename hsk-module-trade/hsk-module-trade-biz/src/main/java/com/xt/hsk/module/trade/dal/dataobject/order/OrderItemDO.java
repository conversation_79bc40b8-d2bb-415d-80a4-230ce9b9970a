package com.xt.hsk.module.trade.dal.dataobject.order;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.common.enums.CurrencyEnum;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 子订单 DO
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
@TableName("trade_order_item")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderItemDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 主订单ID
     */
    private Long tradeOrderId;
    /**
     * 主订单号
     */
    private String tradeOrderNo;
    /**
     * 货币单位 (例如: CNY, USD, VND)
     * @see CurrencyEnum
     */
    private String currency;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * 商品类型
     */
    private Integer productType;
    /**
     * 商品名称快照
     */
    private String productNameCn;
    /**
     * 商品名称快照
     */
    private String productNameEn;
    /**
     * 商品名称快照
     */
    private String productNameOt;
    /**
     * 商品单价
     */
    private BigDecimal productPrice;
    /**
     * 商品数量
     */
    private Integer quantity;
    /**
     * 商品总价 (单价*数量)
     */
    private BigDecimal totalAmount;
    /**
     * 优惠分摊金额
     */
    private BigDecimal discountAmount;
    /**
     * 最终实付金额
     */
    private BigDecimal payAmount;
    /**
     * 优惠券分摊减免金额
     */
    private BigDecimal couponReductionAmount;
    /**
     * 售后状态
     */
    private Integer afterSaleStatus;
    /**
     * 已退款金额
     */
    private BigDecimal refundAmount;
    /**
     * 售后单编号
     */
    private String afterSaleOrderNo;

}
