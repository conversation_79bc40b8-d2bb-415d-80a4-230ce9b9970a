package com.xt.hsk.module.trade.dal.mysql.order;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderPageReqVO;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * Order Mapper （订单映射器）
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
@Mapper
public interface OrderMapper extends BaseMapperX<OrderDO> {

    default PageResult<OrderDO> selectPage(OrderPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<OrderDO>()
            .eqIfPresent(OrderDO::getOrderNo, reqVO.getOrderNo())
            .eqIfPresent(OrderDO::getUserId, reqVO.getUserId())
            .likeIfPresent(OrderDO::getUserNickname, reqVO.getUserNickname())
            .eqIfPresent(OrderDO::getUserCountryCode, reqVO.getUserCountryCode())
            .eqIfPresent(OrderDO::getUserMobile, reqVO.getUserMobile())
            .eqIfPresent(OrderDO::getCurrency, reqVO.getCurrency())
            .eqIfPresent(OrderDO::getUserIp, reqVO.getUserIp())
            .eqIfPresent(OrderDO::getProductCount, reqVO.getProductCount())
            .betweenIfPresent(OrderDO::getPayTime, reqVO.getPayTime())
            .betweenIfPresent(OrderDO::getPaySuccessTime, reqVO.getPaySuccessTime())
            .betweenIfPresent(OrderDO::getCancelTime, reqVO.getCancelTime())
            .eqIfPresent(OrderDO::getCancelType, reqVO.getCancelType())
            .eqIfPresent(OrderDO::getTotalAmount, reqVO.getTotalAmount())
            .eqIfPresent(OrderDO::getDiscountAmount, reqVO.getDiscountAmount())
            .eqIfPresent(OrderDO::getPayAmount, reqVO.getPayAmount())
            .eqIfPresent(OrderDO::getOrderStatus, reqVO.getOrderStatus())
            .eqIfPresent(OrderDO::getAfterSaleStatus, reqVO.getAfterSaleStatus())
            .eqIfPresent(OrderDO::getRefundAmount, reqVO.getRefundAmount())
            .eqIfPresent(OrderDO::getCouponId, reqVO.getCouponId())
            .eqIfPresent(OrderDO::getCouponReductionAmount, reqVO.getCouponReductionAmount())
            .eqIfPresent(OrderDO::getOrderSource, reqVO.getOrderSource())
            .eqIfPresent(OrderDO::getOrderType, reqVO.getOrderType())
            .eqIfPresent(OrderDO::getPayChannel, reqVO.getPayChannel())
            .eqIfPresent(OrderDO::getVersion, reqVO.getVersion())
            .betweenIfPresent(OrderDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(OrderDO::getId));
    }

}
