package com.xt.hsk.module.trade.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderLogDO;
import com.xt.hsk.module.trade.dal.mysql.order.OrderLogMapper;
import com.xt.hsk.module.trade.enums.OperatorTypeEnum;
import com.xt.hsk.module.trade.enums.OrderLogActionTypeEnum;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 订单日志服务实现
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
@Service
@Validated
public class OrderLogServiceImpl extends ServiceImpl<OrderLogMapper, OrderLogDO> implements
    OrderLogService {

    @Override
    public Long createOrderLog(Long tradeOrderId, String tradeOrderNo, Long tradeOrderItemId,
        OrderLogActionTypeEnum actionType, String beforeStatus, String afterStatus,
        Long operatorId, OperatorTypeEnum operatorType, String notes,
        String errorCode, String ip) {
        // 创建订单日志对象
        OrderLogDO orderLog = OrderLogDO.builder()
            .tradeOrderId(tradeOrderId)
            .tradeOrderNo(tradeOrderNo)
            .tradeOrderItemId(tradeOrderItemId)
            .actionType(actionType.getCode())
            .beforeStatus(beforeStatus)
            .afterStatus(afterStatus)
            .operatorId(operatorId)
            .operatorType(operatorType.getCode())
            .notes(notes)
            .errorCode(errorCode)
            .ip(ip)
            .build();

        // 保存订单日志
        save(orderLog);
        return orderLog.getId();
    }

}
