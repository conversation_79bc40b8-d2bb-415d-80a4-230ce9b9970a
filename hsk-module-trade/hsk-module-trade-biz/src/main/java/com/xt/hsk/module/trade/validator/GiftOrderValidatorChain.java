package com.xt.hsk.module.trade.validator;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 赠送订单校验责任链配置
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Component
@Slf4j
public class GiftOrderValidatorChain {

    @Resource
    private UserValidator userValidator;

    @Resource
    private CourseValidator courseValidator;

    /**
     * 责任链头节点
     */
    @Getter
    private AbstractGiftOrderValidator chain;

    /**
     * 初始化责任链
     */
    @PostConstruct
    public void init() {
        log.info("[GiftOrderValidatorChain] 初始化赠送订单校验责任链");

        // 设置责任链顺序：用户校验 -> 课程校验
        userValidator.setNextValidator(courseValidator);

        // 责任链头节点
        this.chain = userValidator;

        log.info("[GiftOrderValidatorChain] 责任链初始化完成");
    }
} 