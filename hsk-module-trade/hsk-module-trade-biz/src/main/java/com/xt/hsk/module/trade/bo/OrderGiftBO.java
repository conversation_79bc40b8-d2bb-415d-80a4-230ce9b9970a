package com.xt.hsk.module.trade.bo;

import com.xt.hsk.module.trade.enums.OrderTypeEnum;
import com.xt.hsk.module.trade.statemachine.OrderEvent;
import java.util.List;
import lombok.Data;

/**
 * 订单赠送 bo
 *
 * <AUTHOR>
 * @since 2025/06/23
 */
@Data
public class OrderGiftBO extends OrderCreateBO {

    /**
     * 用户ID列表 PS:赠送订单用 可能用于批量赠送
     */
    private List<Long> userIds;

    @Override
    public OrderEvent getOrederEvent() {
        return OrderEvent.GIFT;
    }

    /**
     * 获取订单类型
     */
    public Integer getOrderType() {
        return OrderTypeEnum.GIFT_ORDER.getCode();
    }
}
