package com.xt.hsk.module.trade.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderPageReqVO;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderDO;
import com.xt.hsk.module.trade.dal.mysql.order.OrderMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;


/**
 * 主订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OrderServiceImpl extends ServiceImpl<OrderMapper, OrderDO> implements OrderService {

    @Resource
    private OrderMapper orderMapper;

    @Override
    public PageResult<OrderDO> selectPage(OrderPageReqVO pageReqVO) {
        return orderMapper.selectPage(pageReqVO);
    }

}
