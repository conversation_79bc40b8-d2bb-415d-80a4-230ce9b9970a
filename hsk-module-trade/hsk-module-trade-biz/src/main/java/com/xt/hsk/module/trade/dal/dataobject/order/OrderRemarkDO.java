package com.xt.hsk.module.trade.dal.dataobject.order;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 订单备注 DO
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@TableName("trade_order_remark")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderRemarkDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 订单ID
     */
    private Long tradeOrderId;

    /**
     * 是否是最新一条备注
     * 0-否 1-是
     */
    private Integer isLatest;

    /**
     * 订单备注
     */
    private String content;
} 