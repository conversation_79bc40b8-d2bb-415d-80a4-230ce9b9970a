package com.xt.hsk.module.trade.aop;

import com.xt.hsk.framework.common.util.servlet.ServletUtils;
import com.xt.hsk.framework.web.core.util.WebFrameworkUtils;
import com.xt.hsk.module.trade.annotation.OrderLog;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderDO;
import com.xt.hsk.module.trade.enums.OperatorTypeEnum;
import com.xt.hsk.module.trade.service.OrderLogService;
import com.xt.hsk.module.trade.service.OrderService;
import jakarta.annotation.Resource;
import java.lang.reflect.Method;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 订单日志切面
 * <p>
 * 负责拦截带有 {@link OrderLog} 注解的方法，自动记录订单操作日志。 支持从方法参数中获取订单信息，并在方法执行前后记录日志。 同时处理方法执行异常时的日志记录。
 *
 * <AUTHOR>
 * @since 2025/06/22
 */
@Aspect
@Component
@Slf4j
public class OrderLogAspect {

    @Resource
    private OrderLogService orderLogService;

    @Resource
    private OrderService orderService;

    // SpEL表达式解析器
    private final ExpressionParser expressionParser = new SpelExpressionParser();
    // 参数名发现器，用于获取方法参数名
    private final ParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();

    /**
     * 定义切点，拦截带有OrderLog注解的方法
     */
    @Pointcut("@annotation(com.xt.hsk.module.trade.annotation.OrderLog)")
    public void orderLogPointcut() {
    }

    /**
     * 环绕通知，在方法执行前后记录日志
     *
     * @param joinPoint 连接点
     * @return 方法执行结果
     * @throws Throwable 方法执行异常
     */
    @Around("orderLogPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        // 获取注解信息
        OrderLog orderLog = method.getAnnotation(OrderLog.class);

        // 创建SpEL上下文
        EvaluationContext context = createEvaluationContext(joinPoint);

        // 获取订单ID
        Long orderId = parseSpEL(orderLog.orderIdSpEL(), context, Long.class);
        if (orderId == null) {
            log.warn("订单ID解析失败，无法记录日志: {}", orderLog.orderIdSpEL());
            return joinPoint.proceed();
        }

        // 获取订单信息
        OrderDO order = orderService.getById(orderId);
        if (order == null) {
            log.warn("订单不存在，无法记录日志: {}", orderId);
            return joinPoint.proceed();
        }

        // 获取订单号
        String orderNo = order.getOrderNo();

        // 解析子订单ID
        Long orderItemId = null;
        if (StringUtils.hasText(orderLog.orderItemIdSpEL())) {
            orderItemId = parseSpEL(orderLog.orderItemIdSpEL(), context, Long.class);
        }

        // 解析操作前状态
        String beforeStatus = null;
        if (StringUtils.hasText(orderLog.beforeStatusSpEL())) {
            beforeStatus = parseSpEL(orderLog.beforeStatusSpEL(), context, String.class);
        } else {
            beforeStatus = String.valueOf(order.getOrderStatus());
        }

        // 执行原方法
        Object result = joinPoint.proceed();

        // 重新获取订单信息，获取最新状态
        order = orderService.getById(orderId);

        // 操作后状态
        String afterStatus = null;
        if (StringUtils.hasText(orderLog.afterStatusSpEL())) {
            afterStatus = parseSpEL(orderLog.afterStatusSpEL(), context, String.class);
        } else if (order != null) {
            afterStatus = String.valueOf(order.getOrderStatus());
        }

        // 解析操作人ID，只有在操作人类型是管理员ADMIN时才需要获取
        Long operatorId = null;
        if (orderLog.operatorType() == OperatorTypeEnum.ADMIN) {
            if (StringUtils.hasText(orderLog.operatorIdSpEL())) {
                operatorId = parseSpEL(orderLog.operatorIdSpEL(), context, Long.class);
            } else {
                // 尝试获取当前登录用户
                operatorId = WebFrameworkUtils.getLoginUserId();
            }

            // 操作人类型为管理员时，操作人ID不能为空
            if (operatorId == null) {
                log.warn("管理员操作但未能获取操作人ID，orderId: {}", orderId);
                // 默认设置为0，表示未知管理员
                operatorId = 0L;
            }
        }

        // 解析日志内容
        String notes = null;
        if (StringUtils.hasText(orderLog.spEL())) {
            notes = parseSpEL(orderLog.spEL(), context, String.class);
        }

        // 记录操作日志
        orderLogService.createOrderLog(
            orderId,
            orderNo,
            orderItemId,
            orderLog.actionType(),
            beforeStatus,
            afterStatus,
            operatorId,
            orderLog.operatorType(),
            notes,
            null,
            ServletUtils.getClientIP()
        );

        return result;
    }

    /**
     * 异常通知，记录操作失败的日志
     *
     * @param joinPoint 连接点
     * @param e         异常
     */
    @AfterThrowing(pointcut = "orderLogPointcut()", throwing = "e")
    public void afterThrowing(JoinPoint joinPoint, Exception e) {
        try {
            // 获取方法签名
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            // 获取注解信息
            OrderLog orderLog = method.getAnnotation(OrderLog.class);

            // 创建SpEL上下文
            EvaluationContext context = createEvaluationContext(joinPoint);

            // 获取订单ID
            Long orderId = parseSpEL(orderLog.orderIdSpEL(), context, Long.class);
            if (orderId == null) {
                log.warn("订单ID解析失败，无法记录异常日志: {}", orderLog.orderIdSpEL());
                return;
            }

            // 获取订单信息
            OrderDO order = orderService.getById(orderId);
            if (order == null) {
                log.warn("订单不存在，无法记录异常日志: {}", orderId);
                return;
            }

            // 获取订单号
            String orderNo = order.getOrderNo();

            // 解析子订单ID
            Long orderItemId = null;
            if (StringUtils.hasText(orderLog.orderItemIdSpEL())) {
                orderItemId = parseSpEL(orderLog.orderItemIdSpEL(), context, Long.class);
            }

            // 解析操作前状态
            String beforeStatus = null;
            if (StringUtils.hasText(orderLog.beforeStatusSpEL())) {
                beforeStatus = parseSpEL(orderLog.beforeStatusSpEL(), context, String.class);
            } else {
                beforeStatus = String.valueOf(order.getOrderStatus());
            }

            // 操作后状态与操作前状态相同（操作失败）
            String afterStatus = beforeStatus;

            // 解析操作人ID，只有在操作人类型是管理员ADMIN时才需要获取
            Long operatorId = null;
            if (orderLog.operatorType() == OperatorTypeEnum.ADMIN) {
                if (StringUtils.hasText(orderLog.operatorIdSpEL())) {
                    operatorId = parseSpEL(orderLog.operatorIdSpEL(), context, Long.class);
                } else {
                    // 尝试获取当前登录用户
                    operatorId = WebFrameworkUtils.getLoginUserId();
                }

                // 操作人类型为管理员时，操作人ID不能为空
                if (operatorId == null) {
                    log.warn("管理员操作但未能获取操作人ID，orderId: {}", orderId);
                    // 默认设置为0，表示未知管理员
                    operatorId = 0L;
                }
            }

            // 解析错误码
            String errorCode = null;
            if (StringUtils.hasText(orderLog.errorCodeSpEL())) {
                context.setVariable("exception", e);
                errorCode = parseSpEL(orderLog.errorCodeSpEL(), context, String.class);
            }
            if (errorCode == null) {
                errorCode = e.getClass().getSimpleName();
            }

            // 日志内容为异常信息
            String notes = "操作失败：" + e.getMessage();

            // 获取操作IP
            String ip = ServletUtils.getClientIP();

            // 记录操作失败日志
            orderLogService.createOrderLog(
                orderId,
                orderNo, // 使用之前保存的订单号，避免空指针
                orderItemId,
                orderLog.actionType(),
                beforeStatus,
                afterStatus,
                operatorId,
                orderLog.operatorType(),
                notes,
                errorCode,
                ip
            );
        } catch (Exception ex) {
            // 避免日志切面本身出现异常影响主流程
            log.error("记录订单操作异常日志时出错", ex);
        }
    }

    /**
     * 创建SpEL表达式评估上下文
     *
     * @param joinPoint 连接点
     * @return 评估上下文
     */
    private EvaluationContext createEvaluationContext(JoinPoint joinPoint) {
        StandardEvaluationContext context = new StandardEvaluationContext();

        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 获取方法参数名
        String[] paramNames = parameterNameDiscoverer.getParameterNames(method);
        if (paramNames != null) {
            Object[] args = joinPoint.getArgs();
            for (int i = 0; i < paramNames.length; i++) {
                context.setVariable(paramNames[i], args[i]);
            }
        }

        return context;
    }

    /**
     * 解析SpEL表达式
     *
     * @param spEL        SpEL表达式
     * @param context     评估上下文
     * @param targetClass 目标类型
     * @param <T>         目标类型
     * @return 解析结果
     */
    private <T> T parseSpEL(String spEL, EvaluationContext context, Class<T> targetClass) {
        if (!StringUtils.hasText(spEL)) {
            return null;
        }
        try {
            Expression expression = expressionParser.parseExpression(spEL);
            return expression.getValue(context, targetClass);
        } catch (Exception e) {
            log.warn("解析SpEL表达式出错: {}", spEL, e);
            return null;
        }
    }

} 