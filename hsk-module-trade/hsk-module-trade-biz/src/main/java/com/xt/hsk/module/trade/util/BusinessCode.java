package com.xt.hsk.module.trade.util;

import lombok.Getter;

/**
 * 订单号前缀业务代码
 *
 * <AUTHOR>
 * @since 2025/06/23
 */
@Getter
public enum BusinessCode {
    /**
     * 后台管理订单（赠送订单）
     */
    ADMIN_ORDER("AO", "后台管理订单", 1),

    /**
     * 用户自主下单（C端下单）
     */
    USER_ORDER("UO", "用户订单", 1),
    ;

    private final String code;
    private final String desc;
    private final int tableCount;

    BusinessCode(String code, String desc, int tableCount) {
        this.code = code;
        this.desc = desc;
        this.tableCount = tableCount;
    }

    public int tableCount() {
        return this.tableCount;
    }

    public String getCodeString() {
        return this.code;
    }

}
