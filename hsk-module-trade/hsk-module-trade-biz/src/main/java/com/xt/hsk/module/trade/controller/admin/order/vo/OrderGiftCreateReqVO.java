package com.xt.hsk.module.trade.controller.admin.order.vo;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

/**
 * 管理后台 - 赠送订单创建请求 VO
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Data
public class OrderGiftCreateReqVO {

    @NotNull(message = "用户ID不能为空")
    private List<Long> userId;


    /**
     * 赠送商品项
     */
    @Data
    public static class OrderGiftItemVO {

        @NotNull(message = "商品ID不能为空")
        private Long productId;

        @NotNull(message = "商品类型不能为空")
        private Integer productType;
    }
} 