package com.xt.hsk.module.trade.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.enums.IsEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderRemarkCreateReqVO;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderRemarkPageReqVO;
import com.xt.hsk.module.trade.convert.order.OrderRemarkConvert;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderRemarkDO;
import com.xt.hsk.module.trade.dal.mysql.order.OrderRemarkMapper;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 订单备注 Service 实现类
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Service
@Validated
@Slf4j
public class OrderRemarkServiceImpl extends ServiceImpl<OrderRemarkMapper, OrderRemarkDO> implements
    OrderRemarkService {

    @Resource
    private OrderRemarkMapper orderRemarkMapper;

    @Override
    public Long createOrderRemark(OrderRemarkCreateReqVO createReqVO) {
        // 将该订单之前的所有备注设置为非最新
        orderRemarkMapper.update(null, new LambdaUpdateWrapper<OrderRemarkDO>()
            .eq(OrderRemarkDO::getTradeOrderId, createReqVO.getTradeOrderId())
            .set(OrderRemarkDO::getIsLatest, IsEnum.NO.getCode()));

        // 插入新备注，并设置为最新
        OrderRemarkDO orderRemark = OrderRemarkConvert.INSTANCE.convert(createReqVO);
        orderRemark.setIsLatest(IsEnum.YES.getCode());
        orderRemarkMapper.insert(orderRemark);

        // 设置日志上下文变量
        LogRecordContext.putVariable("remarkId", orderRemark.getId());
        LogRecordContext.putVariable("remark", orderRemark);
        LogRecordContext.putVariable("orderId", createReqVO.getTradeOrderId().toString());

        // 返回
        return orderRemark.getId();
    }

    @Override
    public PageResult<OrderRemarkDO> getOrderRemarkListByOrderId(OrderRemarkPageReqVO reqVO) {
        return orderRemarkMapper.selectListByOrderId(reqVO);
    }

    @Override
    public Map<Long,String> getOrderLestRemark(List<Long> orderIds) {
        // 如果订单ID列表为空，直接返回空Map
        if (CollUtil.isEmpty(orderIds)) {
            return Map.of();
        }

        // 批量查询指定订单列表中的最新备注
        List<OrderRemarkDO> latestRemarks = this.lambdaQuery()
            .in(OrderRemarkDO::getTradeOrderId, orderIds)
            .eq(OrderRemarkDO::getIsLatest, 1).list();

        // 如果没有查到任何记录，返回空Map
        if (latestRemarks.isEmpty()) {

            return Map.of();
        }

        // 构建订单ID到备注内容的映射
        Map<Long, String> resultMap = new HashMap<>(latestRemarks.size());
        for (OrderRemarkDO remark : latestRemarks) {
            resultMap.put(remark.getTradeOrderId(), remark.getContent());
        }

        return resultMap;
    }
} 