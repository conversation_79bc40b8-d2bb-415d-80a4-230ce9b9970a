package com.xt.hsk.module.trade.validator;

import com.xt.hsk.module.edu.api.dto.EliteCourseRespDTO;
import com.xt.hsk.module.edu.api.elitecourse.EliteCourseApi;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 课程校验处理器
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Component
@Slf4j
public class CourseValidator extends AbstractGiftOrderValidator {

    @Resource
    private EliteCourseApi eliteCourseApi;

    @Override
    protected ValidateResult doValidate(List<Long> userIds, Long courseId) {
        log.info("[CourseValidator] 开始校验课程信息，课程ID：{}", courseId);

        if (courseId == null) {
            return ValidateResult.failure("课程ID不能为空");
        }

        // 调用教育模块API获取课程信息
        EliteCourseRespDTO course = eliteCourseApi.getById(courseId);

        if (course == null) {
            String errorMsg = String.format("未找到课程信息，课程ID：%s", courseId);
            log.warn("[CourseValidator] 课程校验失败: {}", errorMsg);
            return ValidateResult.failure(errorMsg);
        }

        // 课程价格是否都为空
        BigDecimal sellingPriceOt = course.getSellingPriceOt();
        BigDecimal sellingPriceCn = course.getSellingPriceCn();
        BigDecimal sellingPriceEn = course.getSellingPriceEn();
        if (sellingPriceOt == null && sellingPriceCn == null && sellingPriceEn == null) {
            String errorMsg = String.format("课程价格不能都为空，课程ID：%s", courseId);
            log.warn("[CourseValidator] 课程校验失败: {}", errorMsg);
            return ValidateResult.failure(errorMsg);
        }

        // 检查课程状态
        // if (course.getListingStatus() != null && course.getListingStatus() != 1) {
        //     String errorMsg = String.format("课程未上架，课程ID：%s，状态：%s", courseId, course.getListingStatus());
        //     log.warn("[CourseValidator] 课程校验失败: {}", errorMsg);
        //     return ValidateResult.failure(errorMsg);
        // }

        log.info("[CourseValidator] 课程校验通过，课程名称：{}", course.getCourseNameCn());
        return ValidateResult.success();
    }
} 