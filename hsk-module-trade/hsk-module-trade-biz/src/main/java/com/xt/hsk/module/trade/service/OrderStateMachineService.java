package com.xt.hsk.module.trade.service;

import com.xt.hsk.module.trade.enums.OrderStatusEnum;
import com.xt.hsk.module.trade.statemachine.OrderEvent;

/**
 * 订单状态机服务
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
public interface OrderStateMachineService {

    /**
     * 发送订单状态变更事件
     *
     * @param orderId 订单ID
     * @param event   事件
     * @return 是否成功
     */
    boolean sendEvent(Long orderId, OrderEvent event);

    /**
     * 获取当前订单状态
     *
     * @param orderId 订单ID
     * @return 订单状态
     */
    OrderStatusEnum getCurrentState(Long orderId);
} 