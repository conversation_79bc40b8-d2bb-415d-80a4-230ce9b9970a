package com.xt.hsk.module.trade.convert.order;

import com.xt.hsk.module.trade.controller.admin.order.vo.OrderRemarkCreateReqVO;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderRemarkRespVO;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderRemarkDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 订单备注 Convert
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Mapper
public interface OrderRemarkConvert {

    OrderRemarkConvert INSTANCE = Mappers.getMapper(OrderRemarkConvert.class);

    OrderRemarkDO convert(OrderRemarkCreateReqVO bean);

    OrderRemarkRespVO convert(OrderRemarkDO bean);

    List<OrderRemarkRespVO> convertList(List<OrderRemarkDO> list);

}