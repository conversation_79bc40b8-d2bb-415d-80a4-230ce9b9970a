package com.xt.hsk.module.trade.util;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 分布式ID生成器 用于生成全局唯一的ID，ID结构为：业务码 + 序列号 例如：AO1769649671860822016 (AO代表后台订单)
 * <p>
 * 业务码说明： - AO: 后台管理订单 (Admin Order) - UO: 用户订单 (User Order)
 *
 * <AUTHOR>
 */
@Getter
public class IdGenerator {

    /**
     * 默认的工作节点ID
     */
    private static final long DEFAULT_WORKER_ID = 1L;

    /**
     * 缓存的雪花算法实例，避免重复创建
     */
    private static final Snowflake SNOWFLAKE = IdUtil.getSnowflake(DEFAULT_WORKER_ID);

    /**
     * 业务码长度
     */
    private static final int BUSINESS_CODE_LENGTH = 2;

    /**
     * 业务标识码
     */
    private String businessCode;

    /**
     * 序列号
     */
    private String seq;

    /**
     * 私有构造函数，防止直接实例化
     */
    private IdGenerator() {
    }

    /**
     * 使用默认配置生成全局唯一ID
     *
     * @param businessCode 业务码枚举，用于标识不同的业务类型
     * @return 返回格式为 "业务码 + 序列号" 的唯一ID字符串，例如：AO1769649671860822016
     * @throws IllegalArgumentException 当businessCode为null时抛出
     */
    public static String generate(BusinessCode businessCode) {
        if (businessCode == null) {
            throw new IllegalArgumentException("businessCode cannot be null");
        }
        return businessCode.getCodeString() + SNOWFLAKE.nextId();
    }

    /**
     * 使用指定的工作节点ID生成全局唯一ID 适用于分布式环境下的多节点部署场景
     *
     * @param businessCode 业务码枚举，用于标识不同的业务类型
     * @param workerId     工作节点ID，用于分布式环境下的节点标识（0-31）
     * @return 返回格式为 "业务码 + 序列号" 的唯一ID字符串
     * @throws IllegalArgumentException 当businessCode为null或workerId超出范围时抛出
     */
    public static String generate(BusinessCode businessCode, long workerId) {
        if (businessCode == null) {
            throw new IllegalArgumentException("businessCode cannot be null");
        }
        if (workerId < 0 || workerId > 31) {
            throw new IllegalArgumentException("workerId must be between 0 and 31");
        }
        long id = IdUtil.getSnowflake(workerId).nextId();
        return businessCode.getCodeString() + id;
    }

    /**
     * 从ID字符串中提取业务码
     *
     * @param id 完整的ID字符串
     * @return 业务码字符串（前两位）
     * @throws IllegalArgumentException 当ID格式不正确时抛出
     */
    public static String getBusinessCode(String id) {
        if (StringUtils.isEmpty(id) || id.length() < BUSINESS_CODE_LENGTH) {
            throw new IllegalArgumentException(
                "Invalid IdGenerator format: IdGenerator must be at least " + BUSINESS_CODE_LENGTH
                    + " characters long");
        }
        return id.substring(0, BUSINESS_CODE_LENGTH);
    }

    /**
     * 从ID字符串中提取序列号
     *
     * @param id 完整的ID字符串
     * @return 序列号的Long值
     * @throws IllegalArgumentException 当ID格式不正确时抛出
     * @throws NumberFormatException    当序列号部分无法转换为Long类型时抛出
     */
    public static Long getSequence(String id) {
        if (StringUtils.isEmpty(id) || id.length() <= BUSINESS_CODE_LENGTH) {
            throw new IllegalArgumentException(
                "Invalid IdGenerator format: IdGenerator must be longer than "
                    + BUSINESS_CODE_LENGTH + " characters");
        }
        try {
            return Long.parseLong(id.substring(BUSINESS_CODE_LENGTH));
        } catch (NumberFormatException e) {
            throw new NumberFormatException("Invalid sequence number format in IdGenerator: " + id);
        }
    }

    /**
     * 将ID字符串解析为DistributeID对象
     *
     * @param id 完整的ID字符串
     * @return DistributeID对象
     * @throws IllegalArgumentException 当ID格式不正确时抛出
     */
    public static IdGenerator valueOf(String id) {
        if (StringUtils.isEmpty(id) || id.length() <= BUSINESS_CODE_LENGTH) {
            throw new IllegalArgumentException(
                "Invalid IdGenerator format: IdGenerator must be longer than "
                    + BUSINESS_CODE_LENGTH + " characters");
        }

        IdGenerator distributeIdGenerator = new IdGenerator();
        distributeIdGenerator.businessCode = getBusinessCode(id);
        distributeIdGenerator.seq = id.substring(BUSINESS_CODE_LENGTH);
        return distributeIdGenerator;
    }

    /**
     * 验证ID是否合法
     *
     * @param id 待验证的ID字符串
     * @return true:合法 false:不合法
     */
    public static boolean isValid(String id) {
        if (StringUtils.isEmpty(id) || id.length() <= BUSINESS_CODE_LENGTH) {
            return false;
        }
        try {
            String businessCode = getBusinessCode(id);
            Long sequence = getSequence(id);
            return businessCode != null && sequence != null;
        } catch (Exception e) {
            return false;
        }
    }

}