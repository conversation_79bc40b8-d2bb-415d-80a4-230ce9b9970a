package com.xt.hsk.module.trade.manager.admin.order;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.trade.controller.admin.order.vo.AdminOrderPageReqVO;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderAdminRespVO;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderDetailVO;
import com.xt.hsk.module.trade.convert.order.OrderConvert;
import com.xt.hsk.module.trade.convert.order.OrderItemConvert;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderDO;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderItemDO;
import com.xt.hsk.module.trade.enums.ErrorCodeConstants;
import com.xt.hsk.module.trade.service.OrderItemService;
import com.xt.hsk.module.trade.service.OrderService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 订单管理
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Slf4j
@Component
public class OrderAdminManager {

    @Resource
    private OrderService orderService;

    @Resource
    private OrderItemService orderItemService;

    /**
     * 获取订单页面
     *
     * @param pageReqVO 查询条件
     * @return 列表
     */
    public PageResult<OrderAdminRespVO> getOrderPage(@Valid AdminOrderPageReqVO pageReqVO) {
        return orderItemService.selectPageJoinOrderPage(pageReqVO);
    }

    /**
     * 订单详细信息
     * @param orderId 订单ID
     * @return 结果
     */
    public OrderDetailVO getOrderDetail(Long orderId) {
        OrderDO orderDO = orderService.getById(orderId);
        if (orderDO == null) {
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
        OrderDetailVO detailVO = OrderConvert.INSTANCE.convert(orderDO);

        // 子订单
        List<OrderItemDO> orderItemDOList = orderItemService.selectOrderItemByOrderId(orderId);

        List<OrderDetailVO.OrderItemVO> itemVOList = OrderItemConvert.INSTANCE.convert(orderItemDOList);

        // 设置有效期
        orderItemService.setPeriodValidity(itemVOList, OrderDetailVO.OrderItemVO::getProductId, OrderDetailVO.OrderItemVO::setPeriodValidity);

        detailVO.setOrderItemList(itemVOList);

        return detailVO;
    }
}
