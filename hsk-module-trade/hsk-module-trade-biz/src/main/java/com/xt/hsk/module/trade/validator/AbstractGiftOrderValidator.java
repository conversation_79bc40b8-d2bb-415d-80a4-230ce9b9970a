package com.xt.hsk.module.trade.validator;

import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 赠送订单校验抽象处理器
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Slf4j
public abstract class AbstractGiftOrderValidator {

    /**
     * 下一个处理器
     */
    @Getter
    @Setter
    private AbstractGiftOrderValidator nextValidator;

    /**
     * 校验方法
     *
     * @param userIds  用户ID列表
     * @param courseId 课程ID
     * @return 校验结果，包含是否通过和错误信息
     */
    public final ValidateResult validate(List<Long> userIds, Long courseId) {
        // 当前处理器的校验逻辑
        ValidateResult result = doValidate(userIds, courseId);

        // 如果当前校验未通过，直接返回结果
        if (!result.isSuccess()) {
            return result;
        }

        // 如果有下一个处理器，则继续校验
        if (nextValidator != null) {
            return nextValidator.validate(userIds, courseId);
        }

        // 所有校验都通过
        return ValidateResult.success();
    }

    /**
     * 子类实现具体的校验逻辑
     *
     * @param userIds  用户ID列表
     * @param courseId 课程ID
     * @return 校验结果
     */
    protected abstract ValidateResult doValidate(List<Long> userIds, Long courseId);

    /**
     * 校验结果类
     */
    @Getter
    public static class ValidateResult {

        /**
         * 是否校验通过
         */
        private final boolean success;

        /**
         * 错误信息
         */
        private final String errorMsg;

        private ValidateResult(boolean success, String errorMsg) {
            this.success = success;
            this.errorMsg = errorMsg;
        }

        /**
         * 创建成功结果
         */
        public static ValidateResult success() {
            return new ValidateResult(true, null);
        }

        /**
         * 创建失败结果
         */
        public static ValidateResult failure(String errorMsg) {
            return new ValidateResult(false, errorMsg);
        }
    }
} 