package com.xt.hsk.module.trade.bo;

import com.xt.hsk.module.trade.enums.OrderTypeEnum;
import com.xt.hsk.module.trade.enums.ProductTypeEnum;
import com.xt.hsk.module.trade.statemachine.OrderEvent;
import lombok.Data;

/**
 * 订单创建 DTO
 *
 * <AUTHOR>
 * @since 2025/06/23
 */
@Data
public class OrderCreateBO extends BaseOrderBO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品类型
     *
     * @see ProductTypeEnum
     */
    private Integer productType;


    @Override
    public OrderEvent getOrederEvent() {
        return OrderEvent.GIFT;
    }

    /**
     * 获取订单类型
     */
    public Integer getOrderType() {
        return OrderTypeEnum.NORMAL_ORDER.getCode();
    }
}
