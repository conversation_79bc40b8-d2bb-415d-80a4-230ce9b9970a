package com.xt.hsk.module.trade.controller.admin.order;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.translate.core.TranslateUtils;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderRemarkCreateReqVO;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderRemarkPageReqVO;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderRemarkRespVO;
import com.xt.hsk.module.trade.convert.order.OrderRemarkConvert;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderRemarkDO;
import com.xt.hsk.module.trade.service.OrderRemarkService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 管理后台 - 订单备注
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@RestController
@RequestMapping("/trade/order/remark")
@Validated
@Slf4j
public class OrderRemarkController {

    @Resource
    private OrderRemarkService orderRemarkService;

    /**
     * 创建订单备注
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('trade:order:update')")
    @LogRecord(type = LogRecordType.ORDER_REMARK_TYPE, subType = "创建订单备注",
        bizNo = "{{#remarkId}}",
        success = "创建订单备注：订单ID【{{#orderId}}】，备注内容：{{#remark.remark}}")
    public CommonResult<Long> createOrderRemark(
        @Valid @RequestBody OrderRemarkCreateReqVO createReqVO) {
        return success(orderRemarkService.createOrderRemark(createReqVO));
    }

    /**
     * 获取订单备注列表
     */
    @PostMapping("/list")
    @PreAuthorize("@ss.hasPermission('trade:order:query')")
    public CommonResult<PageResult<OrderRemarkRespVO>> getOrderRemarkList(
        @RequestBody @Valid OrderRemarkPageReqVO orderRemarkPageReqVO) {
        PageResult<OrderRemarkDO> remarkPageResult = orderRemarkService.getOrderRemarkListByOrderId(
            orderRemarkPageReqVO);
        List<OrderRemarkRespVO> orderRemarkRespVOList = OrderRemarkConvert.INSTANCE.convertList(
            remarkPageResult.getList());
        TranslateUtils.translate(orderRemarkRespVOList);
        return success(new PageResult<>(orderRemarkRespVOList, remarkPageResult.getTotal()));
    }


} 