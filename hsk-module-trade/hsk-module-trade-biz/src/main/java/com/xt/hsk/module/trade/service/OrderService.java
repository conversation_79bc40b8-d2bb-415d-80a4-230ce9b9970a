package com.xt.hsk.module.trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderPageReqVO;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderDO;
import jakarta.validation.Valid;

/**
 * 主订单 service 接口
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
public interface OrderService extends IService<OrderDO> {

    /**
     * 获取主订单分页
     *
     * @param pageReqVO 分页参数
     * @return 订单分页
     */
    PageResult<OrderDO> selectPage(@Valid OrderPageReqVO pageReqVO);

}
