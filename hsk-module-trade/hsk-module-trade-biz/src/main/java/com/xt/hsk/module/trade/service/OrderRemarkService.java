package com.xt.hsk.module.trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderRemarkCreateReqVO;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderRemarkPageReqVO;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderRemarkDO;
import java.util.List;
import java.util.Map;

/**
 * 订单备注 Service 接口
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
public interface OrderRemarkService extends IService<OrderRemarkDO> {

    /**
     * 创建订单备注
     *
     * @param createReqVO 创建信息
     * @return 备注编号
     */
    Long createOrderRemark(OrderRemarkCreateReqVO createReqVO);

    /**
     * 获得订单备注列表
     *
     * @return 订单备注列表
     */
    PageResult<OrderRemarkDO> getOrderRemarkListByOrderId(OrderRemarkPageReqVO reqVO);

    /**
     * 获取订单最新一条备注
     */
    Map<Long,String> getOrderLestRemark(List<Long> orderId);
} 