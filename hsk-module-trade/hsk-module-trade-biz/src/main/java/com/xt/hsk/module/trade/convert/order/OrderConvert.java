package com.xt.hsk.module.trade.convert.order;

import com.xt.hsk.module.trade.api.order.dto.GiftOrderRespDTO;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderDetailVO;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 订单转换
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Mapper
public interface OrderConvert {

    OrderConvert INSTANCE = Mappers.getMapper(OrderConvert.class);

    OrderDetailVO convert(OrderDO orderDO);

    /**
     * 创建赠送订单响应转换
     */
    GiftOrderRespDTO convertToGiftOrderResp(OrderDO orderDO);
}
