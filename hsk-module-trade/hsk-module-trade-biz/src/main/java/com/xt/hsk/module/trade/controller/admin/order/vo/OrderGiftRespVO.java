package com.xt.hsk.module.trade.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 订单赠送 Response VO
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Schema(description = "管理后台 - 订单赠送响应 VO")
@Data
public class OrderGiftRespVO {

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "赠送用户数量")
    private Integer userCount;

    @Schema(description = "是否成功")
    private Boolean success;
} 