package com.xt.hsk.module.trade.listener.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.xt.hsk.framework.common.enums.CurrencyEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.string.StrUtils;
import com.xt.hsk.framework.translate.core.TranslateUtils;
import com.xt.hsk.module.infra.listener.BaseEasyExcelExport;
import com.xt.hsk.module.trade.controller.admin.order.vo.AdminOrderPageReqVO;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderAdminRespVO;
import com.xt.hsk.module.trade.service.OrderItemService;
import com.xt.hsk.module.trade.service.OrderRemarkService;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 订单导出任务导出
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Slf4j
@Component
public class OrderExportTaskExport extends BaseEasyExcelExport<OrderAdminRespVO> {

    @Resource
    private OrderItemService orderItemService;

    @Resource
    private OrderRemarkService orderRemarkService;

    @Override
    protected List<List<String>> getExcelHead() {
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("订单号"));
        head.add(Collections.singletonList("用户ID"));
        head.add(Collections.singletonList("用户昵称"));
        head.add(Collections.singletonList("区号"));
        head.add(Collections.singletonList("手机号"));
        head.add(Collections.singletonList("商品类型"));
        head.add(Collections.singletonList("商品名称"));
        head.add(Collections.singletonList("有效期"));
        head.add(Collections.singletonList("支付币种"));
        head.add(Collections.singletonList("商品原价"));
        head.add(Collections.singletonList("实付金额"));
        head.add(Collections.singletonList("交易状态"));
        head.add(Collections.singletonList("订单来源"));
        head.add(Collections.singletonList("订单类型"));
        head.add(Collections.singletonList("支付方式"));
        head.add(Collections.singletonList("订单时间"));
        head.add(Collections.singletonList("订单备注"));
        return head;
    }

    @Override
    protected Long dataTotalCount(Map<String, Object> conditions) {
        // 使用配置好的 ObjectMapper 将 Map 转换为 TeacherPageReqVO 对象，忽略taskName字段
        AdminOrderPageReqVO dto = new AdminOrderPageReqVO();
        BeanUtil.copyProperties(conditions, dto);
        // 如果有选中的ID，返回选中的数量
        if (CollUtil.isNotEmpty(dto.getOrderItemIds())) {
            return (long) dto.getOrderItemIds().size();
        }
        return orderItemService.selectPageJoinOrderPageCount(dto);
    }

    @Override
    protected void buildDataList(List<List<String>> resultList, Map<String, Object> queryCondition,
        Long pageNo, Long pageSize) {

        AdminOrderPageReqVO dto = new AdminOrderPageReqVO();
        BeanUtil.copyProperties(queryCondition, dto);
        dto.setPageNo(Math.toIntExact(pageNo));
        dto.setPageSize(Math.toIntExact(pageSize));

        log.info("开始查询订单数据，页码：{}，页面大小：{}", pageNo, pageSize);

        PageResult<OrderAdminRespVO> voPage = orderItemService.selectPageJoinOrderPage(dto);
        log.info("查询到订单分页数据，总数：{}", voPage.getTotal());

        List<OrderAdminRespVO> pageList = voPage.getList();
        log.info("获取到订单列表，数量：{}",
            pageList != null ? pageList.size() : 0);

        if (CollUtil.isEmpty(pageList)) {
            log.info("订单列表为空，直接返回");
            return;
        }

        Map<Long, String> orderLestRemark = orderRemarkService.getOrderLestRemark(pageList.stream()
            .map(OrderAdminRespVO::getTradeOrderId)
            .toList());

        // 手动翻译
        TranslateUtils.translate(pageList);



        for (OrderAdminRespVO orderAdminRespVO : pageList) {
            List<String> row = new ArrayList<>();

            // 订单编号
            row.add(StrUtils.defaultIfNull(orderAdminRespVO.getOrderNo(), ""));

            // 用户ID
            row.add(StrUtils.defaultIfNull(orderAdminRespVO.getUserId(), ""));

            // 用户昵称
            row.add(StrUtils.defaultIfNull(orderAdminRespVO.getUserNickname(), ""));

            // 区号
            row.add(StrUtils.defaultIfNull(orderAdminRespVO.getUserCountryCode(), ""));

            // 手机号
            row.add(StrUtils.defaultIfNull(orderAdminRespVO.getUserMobile(), ""));

            // 商品类型
            row.add(StrUtils.defaultIfNull(orderAdminRespVO.getProductTypeDesc(), ""));

            // 商品名称
            row.add(StrUtils.defaultIfNull(orderAdminRespVO.getProductNameCn(), ""));
            // 有效期
            row.add(StrUtils.defaultIfNull(orderAdminRespVO.getPeriodValidity(), ""));

            // 支付币种
            // 币种名称 + (&币种)
            String currency =
                orderAdminRespVO.getCurrencyDesc() + "(" + orderAdminRespVO.getCurrency() + ")";
            row.add(currency);

            String currencySymbol = getCurrencySymbol(orderAdminRespVO.getCurrency());
            // 订单金额
            row.add(currencySymbol + " " +StrUtils.defaultIfNull(orderAdminRespVO.getTotalAmount(), ""));

            // 实际支付金额
            row.add(currencySymbol + " " +StrUtils.defaultIfNull(orderAdminRespVO.getPayAmount(), ""));

            // 订单状态
            row.add(orderAdminRespVO.getOrderStatusDesc());

            // 订单类型
            row.add(orderAdminRespVO.getOrderTypeDesc());

            // 订单来源
            row.add(orderAdminRespVO.getOrderSourceDesc());

            // 支付方式
            row.add(orderAdminRespVO.getPayChannelDesc());

            // 订单时间
            // 时间格式化
            row.add(DateUtil.format(orderAdminRespVO.getOrderTime(), DatePattern.NORM_DATETIME_PATTERN));

            // 订单备注
            row.add(orderLestRemark.get(orderAdminRespVO.getTradeOrderId()));

            resultList.add(row);
        }
        log.info("订单导出，当前页：{}，每页条数：{}，总条数：{}", pageNo, pageSize,
            voPage.getTotal());
    }

    /**
     * 根据货币获取货币单位符号
     */
    private String getCurrencySymbol(String currency) {
        return CurrencyEnum.getCurrencySymbolByCurrency(currency);
    }
}
