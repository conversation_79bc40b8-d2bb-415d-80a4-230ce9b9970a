package com.xt.hsk.module.trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.trade.api.order.dto.OrderAdminRespDTO;
import com.xt.hsk.module.trade.api.order.dto.UserOrderPageReqDTO;
import com.xt.hsk.module.trade.controller.admin.order.vo.AdminOrderPageReqVO;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderAdminRespVO;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderItemDO;
import jakarta.validation.Valid;
import java.util.List;
import java.util.function.Function;

/**
 * 子订单 Service 接口
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
public interface OrderItemService extends IService<OrderItemDO> {

    /**
     * 获取子订单分页
     *
     * @param pageReqVO 分页参数
     * @return 子订单分页
     */
    PageResult<OrderAdminRespVO> selectPageJoinOrderPage(@Valid AdminOrderPageReqVO pageReqVO);

    /**
     * 获取子订单分页总条目
     *
     * @param pageReqVO page req vo
     * @return 总条目
     */
    Long selectPageJoinOrderPageCount(@Valid AdminOrderPageReqVO pageReqVO);

    /**
     * 根据用户ID获取子订单
     */
    PageResult<OrderAdminRespDTO> selectPageOrderItemByUserId(
        UserOrderPageReqDTO userOrderPageReqDTO);

    /**
     * 根据订单ID获取子订单
     */
    List<OrderItemDO> selectOrderItemByOrderId(Long orderId);

    /**
     * 通用的设置有效期方法
     *
     * @param <T>                  泛型类型
     * @param list                 数据列表
     * @param productIdGetter      获取产品ID的函数
     * @param periodValiditySetter 设置有效期的函数
     */
    <T> void setPeriodValidity(List<T> list,
                               Function<T, Long> productIdGetter,
                               java.util.function.BiConsumer<T, String> periodValiditySetter);

}
