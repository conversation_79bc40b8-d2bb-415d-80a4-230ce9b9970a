package com.xt.hsk.module.trade.dal.mysql.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.xt.hsk.module.trade.api.order.dto.OrderAdminRespDTO;
import com.xt.hsk.module.trade.api.order.dto.UserOrderPageReqDTO;
import com.xt.hsk.module.trade.controller.admin.order.vo.AdminOrderPageReqVO;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderAdminRespVO;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderDO;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderItemDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 子订单 Mapper
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
@Mapper
public interface OrderItemMapper extends BaseMapperX<OrderItemDO> {


    /**
     * 查询条件构建包装器
     *
     * @param pageReqVO 页面要求 vo
     * @param isCount   是计数
     * @return {@code MPJLambdaWrapperX<OrderItemDO> }
     */
    default MPJLambdaWrapperX<OrderItemDO> buildWrapper(AdminOrderPageReqVO pageReqVO,
        boolean isCount) {

        MPJLambdaWrapperX<OrderItemDO> wrapper = new MPJLambdaWrapperX<OrderItemDO>()
            // left join trade_order on trade_order.id = trade_order_item.trade_order_id
            .leftJoin(OrderDO.class, OrderDO::getId, OrderItemDO::getTradeOrderId);

        if (isCount) {
            // 要执行的是count语句时 这里只要id即可 否则会count(字段名1,字段名2......)
            wrapper.select(OrderItemDO::getId);
        }else {
            // 需要两张表的所有字段
            wrapper.selectAs(OrderItemDO::getProductNameCn, "productName");
            wrapper.selectAs(OrderDO::getCurrency, "currency");
            wrapper.selectAll(OrderItemDO.class).selectAll(OrderDO.class);
        }

        // 导出用 如果ids 不为空，则只导出指定的
        if (CollUtil.isNotEmpty(pageReqVO.getOrderItemIds())) {
            wrapper.in(OrderItemDO::getId, pageReqVO.getOrderItemIds());
            return wrapper;
        }

        // 添加查询条件

        // 模糊查询需要去除前后空格
        if (CharSequenceUtil.isNotBlank(pageReqVO.getOrderNo())) {
            wrapper.like(OrderItemDO::getTradeOrderNo, pageReqVO.getOrderNo().trim());
        }
        if (CharSequenceUtil.isNotBlank(pageReqVO.getUserMobile())) {
            wrapper.like(OrderDO::getUserMobile, pageReqVO.getUserMobile().trim());
        }
        if (CharSequenceUtil.isNotBlank(pageReqVO.getProductName())) {
            wrapper.like(OrderItemDO::getProductNameCn, pageReqVO.getProductName().trim());
        }
        wrapper.eqIfPresent(OrderDO::getUserId, pageReqVO.getUserId());
        wrapper.eqIfPresent(OrderDO::getUserCountryCode, pageReqVO.getUserCountryCode());
        wrapper.eqIfPresent(OrderItemDO::getProductType, pageReqVO.getProductType());
        wrapper.eqIfPresent(OrderDO::getOrderStatus, pageReqVO.getOrderStatus());
        wrapper.eqIfPresent(OrderDO::getPayChannel, pageReqVO.getPayChannel());
        wrapper.eqIfPresent(OrderDO::getOrderSource, pageReqVO.getOrderSource());
        wrapper.eqIfPresent(OrderDO::getOrderType, pageReqVO.getOrderType());
        wrapper.eqIfPresent(OrderDO::getCurrency, pageReqVO.getCurrency());
        wrapper.betweenIfPresent(OrderDO::getOrderTime, pageReqVO.getOrderTime());
        wrapper.inIfPresent(OrderItemDO::getTradeOrderId, pageReqVO.getProductIdList());
        wrapper.orderByDesc(OrderDO::getId);

        return wrapper;
    }

    /**
     * 订单管理分页查询页面
     *
     * @param pageReqVO 订单条件
     * @return 结果
     */
    default PageResult<OrderAdminRespVO> selectPageJoinOrderPage(AdminOrderPageReqVO pageReqVO) {
        // 执行查询
        return selectJoinPage(pageReqVO, OrderAdminRespVO.class, buildWrapper(pageReqVO,false));
    }

    default PageResult<OrderAdminRespDTO> selectPageOrderItemByUserId(
        UserOrderPageReqDTO userOrderPageReqDTO) {
        MPJLambdaWrapperX<OrderItemDO> wrapper = new MPJLambdaWrapperX<OrderItemDO>()
            // left join trade_order on trade_order.id = trade_order_item.trade_order_id
            .leftJoin(OrderDO.class, OrderDO::getId, OrderItemDO::getTradeOrderId)
            // 别名需要前置
            .selectAs(OrderDO::getCurrency, "currency")
            .selectAs(OrderItemDO::getProductNameCn, "productName")
            .selectAs(OrderDO::getUpdateTime, "updateTime")
            .selectAs(OrderDO::getCancelTime, "createTime")
            // 需要两张表的所有字段
            .selectAll(OrderItemDO.class)
            .selectAll(OrderDO.class)
            // 筛选条件
            .eq(OrderItemDO::getUserId, userOrderPageReqDTO.getUserId())
            .orderByDesc(OrderDO::getId);

        // 执行查询
        return selectJoinPage(userOrderPageReqDTO, OrderAdminRespDTO.class, wrapper);
    }

    default Long selectPageJoinOrderPageCount(AdminOrderPageReqVO pageReqVO) {
        return this.selectCount(buildWrapper(pageReqVO,true));
    }

}
