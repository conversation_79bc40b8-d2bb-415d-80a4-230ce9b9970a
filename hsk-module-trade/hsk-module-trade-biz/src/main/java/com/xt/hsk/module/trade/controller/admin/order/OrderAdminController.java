package com.xt.hsk.module.trade.controller.admin.order;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.alibaba.fastjson.JSON;
import com.fhs.core.trans.anno.TransMethodResult;
import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.infra.api.export.ExportTaskApi;
import com.xt.hsk.module.infra.enums.export.ExportTaskTypeEnum;
import com.xt.hsk.module.trade.controller.admin.order.vo.AdminOrderPageReqVO;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderAdminRespVO;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderDetailVO;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderExportReqVO;
import com.xt.hsk.module.trade.manager.admin.order.OrderAdminManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 后台管理 - 订单接口
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@RestController
@RequestMapping("/trade/order")
@Validated
@Slf4j
public class OrderAdminController {

    @Resource
    private OrderAdminManager orderAdminManager;

    @Resource
    private ExportTaskApi exportTaskApi;

    /**
     * 获得订单列表分页
     */
    @PostMapping("/page")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('trade:order:query')")
    public CommonResult<PageResult<OrderAdminRespVO>> getInteractiveCoursePage(
        @RequestBody @Valid AdminOrderPageReqVO pageReqVO) {
        return success(orderAdminManager.getOrderPage(pageReqVO));
    }

    /**
     * 导出订单数据
     */
    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermission('trade:order:export')")
    public CommonResult<Long> exportInteractiveCourse(
        @RequestBody @Valid OrderExportReqVO exportReqVO) {
        Long taskId = exportTaskApi.createExportTask(exportReqVO.getTaskName(),
            ExportTaskTypeEnum.ORDER, JSON.toJSONString(exportReqVO));
        LogRecordContext.putVariable("taskId", taskId);
        return success(taskId);
    }

    /**
     * 订单详情
     *
     * @param orderId 订单ID
     */
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('trade:order:query')")
    @GetMapping("/getOrderDetail")
    public CommonResult<OrderDetailVO> getInteractiveCourse(@RequestParam("id") Long orderId) {
        return success(orderAdminManager.getOrderDetail(orderId));
    }
}
