package com.xt.hsk.module.trade.statemachine;

import com.xt.hsk.module.trade.dal.dataobject.order.OrderDO;
import com.xt.hsk.module.trade.enums.OrderStatusEnum;
import com.xt.hsk.module.trade.service.OrderService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.StateMachineContext;
import org.springframework.statemachine.StateMachinePersist;
import org.springframework.statemachine.persist.DefaultStateMachinePersister;
import org.springframework.statemachine.persist.StateMachinePersister;
import org.springframework.statemachine.support.DefaultStateMachineContext;

/**
 * 订单状态机持久化配置
 * <p>
 * 本类实现了Spring状态机的持久化机制，负责将状态机的状态保存到数据库，并在需要时从数据库还原状态机。 持久化机制是状态机框架的重要组成部分，具有以下主要作用：
 * <ul>
 *     <li><b>状态持久化</b>：将状态机的当前状态保存到数据库，确保系统重启后状态不丢失</li>
 *     <li><b>分布式支持</b>：在分布式环境中，不同节点可以通过持久化存储共享状态机状态</li>
 *     <li><b>事务一致性</b>：状态变更和业务操作在同一事务中执行，保证数据一致性</li>
 *     <li><b>状态恢复</b>：系统崩溃或重启后，可以从持久化存储中恢复状态机到上次的状态</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Configuration
public class OrderStateMachinePersister {

    @Resource
    private OrderService orderService;

    /**
     * 订单状态机持久化器
     * <p>
     * 创建并配置状态机持久化器，用于处理状态机状态的存储和恢复。 使用{@link DefaultStateMachinePersister}作为默认实现，并通过匿名内部类实现
     * {@link StateMachinePersist}接口来自定义读写操作。
     * <p>
     * 持久化器接收三个泛型参数：
     * <ul>
     *     <li>S - 状态类型，这里是{@link OrderStatusEnum}</li>
     *     <li>E - 事件类型，这里是{@link OrderEvent}</li>
     *     <li>T - 持久化标识符类型，这里是{@link Long}，代表订单ID</li>
     * </ul>
     *
     * @return 配置好的状态机持久化器
     */
    @Bean
    public StateMachinePersister<OrderStatusEnum, OrderEvent, Long> createOrderStateMachinePersister() {
        return new DefaultStateMachinePersister<>(
            new StateMachinePersist<OrderStatusEnum, OrderEvent, Long>() {
                /**
                 * 将状态机状态写入数据库
                 * <p>
                 * 当状态机状态发生变化时，此方法会被调用，负责将新状态保存到数据库。
                 * 具体实现是根据订单ID获取订单实体，然后更新其状态字段，并通过OrderService保存。
                 * <p>
                 * 注意：此方法应当在事务上下文中执行，以确保状态更新的原子性。
                 *
                 * @param context 状态机上下文，包含当前状态等信息
                 * @param orderId 订单ID，作为持久化的标识符
                 */
                @Override
                public void write(StateMachineContext<OrderStatusEnum, OrderEvent> context,
                    Long orderId) {
                    // 写入数据库
                    OrderDO order = orderService.getById(orderId);
                    if (order != null) {
                        order.setOrderStatus(context.getState().getCode());
                        orderService.updateById(order);
                    }
                }

                /**
                 * 从数据库读取状态机状态
                 * <p>
                 * 当需要恢复状态机状态时（如系统启动或处理特定订单时），此方法会被调用。
                 * 它从数据库读取订单的当前状态，并创建一个包含该状态的状态机上下文对象返回。
                 * <p>
                 * 如果找不到订单记录，则返回默认的初始状态（待支付）。
                 *
                 * @param orderId 订单ID，作为查询的标识符
                 * @return 包含订单当前状态的状态机上下文
                 */
                @Override
                public StateMachineContext<OrderStatusEnum, OrderEvent> read(Long orderId) {
                    // 从数据库读取状态
                    OrderDO order = orderService.getById(orderId);
                    if (order == null) {
                        return new DefaultStateMachineContext<>(OrderStatusEnum.WAITING_PAYMENT,
                            null, null, null);
                    }
                    OrderStatusEnum state = OrderStatusEnum.values()[order.getOrderStatus()];
                    return new DefaultStateMachineContext<>(state, null, null, null);
                }
            });
    }
} 