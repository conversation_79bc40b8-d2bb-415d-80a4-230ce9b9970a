package com.xt.hsk.module.trade.dal.mysql.order;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.trade.controller.admin.order.vo.OrderRemarkPageReqVO;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderRemarkDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 订单备注 Mapper
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Mapper
public interface OrderRemarkMapper extends BaseMapperX<OrderRemarkDO> {

    /**
     * 根据订单ID查询备注列表
     *
     * @return 备注列表
     */
    default PageResult<OrderRemarkDO> selectListByOrderId(OrderRemarkPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<OrderRemarkDO>()
            .eq(OrderRemarkDO::getTradeOrderId, reqVO.getTradeOrderId())
            .orderByDesc(OrderRemarkDO::getId));
    }

}