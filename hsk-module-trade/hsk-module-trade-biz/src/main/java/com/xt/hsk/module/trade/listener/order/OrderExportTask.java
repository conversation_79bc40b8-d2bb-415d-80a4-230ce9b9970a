package com.xt.hsk.module.trade.listener.order;

import com.alibaba.fastjson.JSON;
import com.xt.hsk.module.infra.listener.BaseExportTask;
import jakarta.annotation.Resource;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 订单导出任务
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Slf4j
@Component("orderExportTask")
public class OrderExportTask extends BaseExportTask<OrderExportTaskExport> {

    @Resource
    private OrderExportTaskExport orderExportTaskExport;

    @Override
    protected OrderExportTaskExport getExporter() {
        return orderExportTaskExport;
    }

    @Override
    protected String getFileName() {
        return "订单列表";
    }

    @Override
    protected Map<String, Object> buildQueryParams(String params) {
        try {
            return JSON.parseObject(params, Map.class);
        } catch (Exception e) {
            log.error("解析导出参数失败: {}", e.getMessage(), e);
            return Map.of();
        }
    }
}
