package com.xt.hsk.module.trade.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 订单备注 create req vo
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Schema(description = "管理后台 - 订单备注创建 Request VO")
@Data
public class OrderRemarkCreateReqVO {

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "订单编号不能为空")
    private Long tradeOrderId;

    @Schema(description = "备注内容", requiredMode = Schema.RequiredMode.REQUIRED, example = "用户要求尽快发货")
    @NotEmpty(message = "备注内容不能为空")
    private String content;
} 