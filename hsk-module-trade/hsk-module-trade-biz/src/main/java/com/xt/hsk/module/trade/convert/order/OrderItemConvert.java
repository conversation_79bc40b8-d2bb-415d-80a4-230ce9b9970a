package com.xt.hsk.module.trade.convert.order;

import com.xt.hsk.module.trade.controller.admin.order.vo.OrderDetailVO.OrderItemVO;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderItemDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 订单项转换
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Mapper
public interface OrderItemConvert {

    OrderItemConvert INSTANCE = Mappers.getMapper(OrderItemConvert.class);


    OrderItemVO convert(OrderItemDO bean);

    List<OrderItemVO> convert(List<OrderItemDO> bean);


}