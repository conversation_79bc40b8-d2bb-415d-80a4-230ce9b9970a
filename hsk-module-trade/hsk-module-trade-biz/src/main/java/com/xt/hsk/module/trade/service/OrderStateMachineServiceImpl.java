package com.xt.hsk.module.trade.service;

import com.xt.hsk.module.trade.annotation.OrderLog;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderDO;
import com.xt.hsk.module.trade.dal.mysql.order.OrderMapper;
import com.xt.hsk.module.trade.enums.OperatorTypeEnum;
import com.xt.hsk.module.trade.enums.OrderLogActionTypeEnum;
import com.xt.hsk.module.trade.enums.OrderStatusEnum;
import com.xt.hsk.module.trade.statemachine.OrderEvent;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.persist.StateMachinePersister;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 订单状态机服务实现
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Service
@Slf4j
public class OrderStateMachineServiceImpl implements OrderStateMachineService {

    @Resource
    private StateMachine<OrderStatusEnum, OrderEvent> orderStateMachine;

    @Resource(name = "createOrderStateMachinePersister")
    private StateMachinePersister<OrderStatusEnum, OrderEvent, Long> orderStateMachinePersister;

    @Resource
    private OrderService orderService;

    @Resource
    private OrderMapper orderMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @OrderLog(
        actionType = OrderLogActionTypeEnum.MODIFY,
        operatorType = OperatorTypeEnum.SYSTEM,
        spEL = "'订单状态更新: ' + #currentStatus.desc + ' -> ' + #newStatus.desc",
        beforeStatusSpEL = "#currentStatus.desc",
        afterStatusSpEL = "#newStatus.desc"
    )
    public boolean sendEvent(Long orderId, OrderEvent event) {
        try {
            // 查询订单当前状态
            OrderDO order = orderService.getById(orderId);
            if (order == null) {
                log.error("订单不存在，orderId: {}", orderId);
                return false;
            }

            // 构建消息
            Message<OrderEvent> message = MessageBuilder
                .withPayload(event)
                .setHeader("orderId", orderId)
                .build();

            // 加载状态机
            orderStateMachine.start();
            // 将订单状态同步到状态机
            OrderStatusEnum currentStatus = OrderStatusEnum.values()[order.getOrderStatus()];
            orderStateMachinePersister.restore(orderStateMachine, orderId);

            // 发送事件
            boolean result = orderStateMachine.sendEvent(message);
            if (!result) {
                log.error("订单状态机事件发送失败，orderId: {}, event: {}, currentStatus: {}",
                    orderId, event, currentStatus);
                return false;
            }

            // 获取新状态
            OrderStatusEnum newStatus = orderStateMachine.getState().getId();
            log.info("订单状态更新，orderId: {}, oldStatus: {}, newStatus: {}",
                orderId, currentStatus, newStatus);

            // 更新订单状态
            order.setOrderStatus(newStatus.getCode());
            order.setVersion(order.getVersion() + 1);
            boolean updateResult = orderService.updateById(order);

            // 持久化状态机
            orderStateMachinePersister.persist(orderStateMachine, orderId);
            return updateResult;
        } catch (Exception e) {
            log.error("订单状态变更异常，orderId: {}, event: {}", orderId, event, e);
            return false;
        } finally {
            orderStateMachine.stop();
        }
    }

    @Override
    public OrderStatusEnum getCurrentState(Long orderId) {
        OrderDO order = orderService.getById(orderId);
        if (order == null) {
            return null;
        }
        return OrderStatusEnum.values()[order.getOrderStatus()];
    }
} 