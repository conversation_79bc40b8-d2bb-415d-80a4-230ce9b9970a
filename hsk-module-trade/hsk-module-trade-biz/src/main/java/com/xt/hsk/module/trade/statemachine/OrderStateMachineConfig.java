package com.xt.hsk.module.trade.statemachine;

import com.xt.hsk.module.trade.enums.OrderStatusEnum;
import java.util.EnumSet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.config.EnableStateMachine;
import org.springframework.statemachine.config.EnumStateMachineConfigurerAdapter;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.listener.StateMachineListener;
import org.springframework.statemachine.listener.StateMachineListenerAdapter;
import org.springframework.statemachine.state.State;

/**
 * 订单状态机配置类
 * <p>
 * 本类实现了Spring状态机框架的核心配置，用于管理订单在其生命周期中的各种状态变迁。 使用状态机设计的主要优势：
 * <ul>
 *     <li><b>业务逻辑清晰化</b>：通过状态机明确定义了订单的所有可能状态和状态间的转换路径，避免了传统方式中复杂的if-else条件判断</li>
 *     <li><b>约束状态流转</b>：确保订单状态只能按照预定义的路径变化，避免了不合法的状态转换</li>
 *     <li><b>行为与状态解耦</b>：将状态转换时的行为(Action)与状态定义分离，使系统更加灵活</li>
 *     <li><b>可扩展性</b>：当需要添加新的订单状态或状态转换时，只需修改状态机配置，而无需修改核心业务代码</li>
 *     <li><b>异常处理</b>：状态机提供了统一的异常处理机制，使错误处理更加规范</li>
 * </ul>
 *
 * <h3>状态机整体设计</h3>
 * 状态机由三部分组成：状态(States)、事件(Events)和转换(Transitions)：
 * <p>
 * <b>1. 状态(States)</b>：使用{@link OrderStatusEnum}枚举定义，包括待支付、支付中、已完成、已关闭等
 * <p>
 * <b>2. 事件(Events)</b>：使用{@link OrderEvent}枚举定义，包括支付、支付成功、支付失败、取消等触发状态变更的事件
 * <p>
 * <b>3. 转换规则(Transitions)</b>：定义了在特定事件发生时，订单从一个状态转换到另一个状态的规则
 *
 * <h3>状态流转图</h3>
 * <pre>
 *                              ┌────────────────┐
 *                  ┌───────────│  已关闭(CLOSED) │
 *                  │           └────────────────┘
 *           CANCEL(取消)/
 *          TIMEOUT(超时)
 *                  │      PAY(支付)    ┌────────────────┐  PAY_SUCCESS(支付成功)  ┌──────────────────┐
 *  ┌─────────────────┐   ┌─────────────│  支付中(PAYING) │─────────────────────────│ 已完成(COMPLETED) │
 *  │ 待支付(WAITING_  │───┼────┤         └─────┬────────┘                        └────┬─────────────┘
 *  │    PAYMENT)     │   │    │               │                                      │
 *  └─────────────────┘   │    │               │ PAY_FAILED(支付失败)                 │ APPLY_REFUND(申请退款)
 *       │                │    │               │                                      │
 *       │                │    └───────────────┘                                      ▼
 *       │                │                                                  ┌─────────────────────┐
 *       │                │                                                  │ 退款申请中(REFUND_  │
 *       │                │                                                  │     APPLYING)       │
 *       │                │                                                  └──────┬──────────────┘
 *       │                │                                                         │
 *       │                │                                   ┌─────────────────────┼─────────────────────┐
 *       │                │                                   │                     │                     │
 *       │                │                                   ▼                     ▼                     ▼
 *       │    GIFT(赠送)  │                         ┌───────────────────┐  ┌────────────────────┐ ┌────────────────────┐
 *       └────────────────┼────────────────────────►│ 部分退款(PARTIAL_ │  │ 全部退款(FULL_    │ │ 退款失败(REFUND_   │
 *                        │                         │   REFUNDED)       │  │   REFUNDED)        │ │   FAILED)          │
 *                        │                         └──────┬────────────┘  └────────────────────┘ └────────────────────┘
 *                        │                                │                         ▲
 *                        │                                │ APPLY_REFUND(申请退款)  │
 *                        │                                │                         │
 *                        └────────────────────────────────┘              FULL_REFUND(全额退款)
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Configuration
@EnableStateMachine(name = "orderStateMachine")
@Slf4j
public class OrderStateMachineConfig extends
    EnumStateMachineConfigurerAdapter<OrderStatusEnum, OrderEvent> {

    /**
     * 配置状态机的状态
     * <p>
     * 此方法定义了状态机中所有可能的状态，并指定初始状态为"待支付"。 使用EnumSet.allOf确保了枚举中的所有状态都被纳入状态机的管理范围，
     * 当在OrderStatusEnum中新增状态时，无需在此处修改代码。
     *
     * @param states 状态配置器
     * @throws Exception 配置异常
     */
    @Override
    public void configure(StateMachineStateConfigurer<OrderStatusEnum, OrderEvent> states)
        throws Exception {
        states
            .withStates()
            .initial(OrderStatusEnum.WAITING_PAYMENT)  // 设置初始状态为"待支付"
            .states(EnumSet.allOf(OrderStatusEnum.class));  // 将所有枚举值作为状态
    }

    /**
     * 配置状态转换规则
     * <p>
     * 此方法定义了订单在不同状态之间转换的所有可能路径及触发条件。 每一个withExternal()方法调用定义了一条状态转换路径，包含： - source: 起始状态 - target:
     * 目标状态 - event: 触发该转换的事件
     * <p>
     * 通过这种声明式的配置，我们可以清晰地看到订单状态的所有可能流转， 并确保订单状态只能按照预定义的路径进行转换。
     * <p>
     * 例如： - 待支付状态的订单，只能在支付成功后变为已完成 - 已完成的订单，只能申请退款进入退款申请中状态 - 不允许从已关闭状态直接变为已完成状态
     *
     * @param transitions 转换规则配置器
     * @throws Exception 配置异常
     */
    @Override
    public void configure(StateMachineTransitionConfigurer<OrderStatusEnum, OrderEvent> transitions)
        throws Exception {
        transitions
            // 待支付 -> 支付中
            .withExternal()
            .source(OrderStatusEnum.WAITING_PAYMENT)
            .target(OrderStatusEnum.PAYING)
            .event(OrderEvent.PAY)
            .and()
            // 支付中 -> 已完成
            .withExternal()
            .source(OrderStatusEnum.PAYING)
            .target(OrderStatusEnum.COMPLETED)
            .event(OrderEvent.PAY_SUCCESS)
            .and()
            // 支付中 -> 待支付 (支付失败回退)
            .withExternal()
            .source(OrderStatusEnum.PAYING)
            .target(OrderStatusEnum.WAITING_PAYMENT)
            .event(OrderEvent.PAY_FAILED)
            .and()
            // 待支付 -> 已关闭 (用户取消)
            .withExternal()
            .source(OrderStatusEnum.WAITING_PAYMENT)
            .target(OrderStatusEnum.CLOSED)
            .event(OrderEvent.CANCEL)
            .and()
            // 待支付 -> 已关闭 (超时关闭)
            .withExternal()
            .source(OrderStatusEnum.WAITING_PAYMENT)
            .target(OrderStatusEnum.CLOSED)
            .event(OrderEvent.TIMEOUT)
            .and()
            // 直接从 待支付 -> 已完成 (赠送订单)
            .withExternal()
            .source(OrderStatusEnum.WAITING_PAYMENT)
            .target(OrderStatusEnum.COMPLETED)
            .event(OrderEvent.GIFT)
            .and()
            // 已完成 -> 退款申请中
            .withExternal()
            .source(OrderStatusEnum.COMPLETED)
            .target(OrderStatusEnum.REFUND_APPLYING)
            .event(OrderEvent.APPLY_REFUND)
            .and()
            // 退款申请中 -> 部分退款
            .withExternal()
            .source(OrderStatusEnum.REFUND_APPLYING)
            .target(OrderStatusEnum.PARTIAL_REFUNDED)
            .event(OrderEvent.PARTIAL_REFUND)
            .and()
            // 退款申请中 -> 全部退款
            .withExternal()
            .source(OrderStatusEnum.REFUND_APPLYING)
            .target(OrderStatusEnum.FULL_REFUNDED)
            .event(OrderEvent.FULL_REFUND)
            .and()
            // 退款申请中 -> 退款失败
            .withExternal()
            .source(OrderStatusEnum.REFUND_APPLYING)
            .target(OrderStatusEnum.REFUND_FAILED)
            .event(OrderEvent.REFUND_REJECT)
            .and()
            // 部分退款 -> 退款申请中 (继续申请退款)
            .withExternal()
            .source(OrderStatusEnum.PARTIAL_REFUNDED)
            .target(OrderStatusEnum.REFUND_APPLYING)
            .event(OrderEvent.APPLY_REFUND);
    }

    /**
     * 配置状态机的全局配置
     * <p>
     * 此方法设置状态机的全局特性，例如： - autoStartup: 设置状态机是否自动启动 - listener: 添加状态变更监听器，用于记录状态变更日志或处理特定事件
     *
     * @param config 配置器
     * @throws Exception 配置异常
     */
    @Override
    public void configure(StateMachineConfigurationConfigurer<OrderStatusEnum, OrderEvent> config)
        throws Exception {
        config
            .withConfiguration()
            .autoStartup(true)  // 自动启动状态机
            .listener(listener());  // 注册状态变更监听器
    }

    /**
     * 创建状态机监听器
     * <p>
     * 状态机监听器用于监听状态变化事件，可以在状态转换前后执行自定义逻辑。 在这里，我们主要用于记录状态变更的日志，便于问题排查和系统监控。
     * <p>
     * 该监听器重写了stateChanged方法，在状态发生变化时被调用，记录了状态变更的详细信息。 可以根据业务需要扩展更多监听方法，例如： - eventNotAccepted:
     * 当事件被拒绝时触发 - transition: 转换开始前触发 - transitionStarted: 转换开始时触发 - transitionEnded: 转换结束时触发
     *
     * @return 状态机监听器
     */
    @Bean
    public StateMachineListener<OrderStatusEnum, OrderEvent> listener() {
        return new StateMachineListenerAdapter<OrderStatusEnum, OrderEvent>() {
            @Override
            public void stateChanged(State<OrderStatusEnum, OrderEvent> from,
                State<OrderStatusEnum, OrderEvent> to) {
                if (from != null) {
                    log.info("订单状态从 {} 变更为 {}", from.getId(), to.getId());
                } else {
                    log.info("订单状态初始化为 {}", to.getId());
                }
            }
        };
    }
} 