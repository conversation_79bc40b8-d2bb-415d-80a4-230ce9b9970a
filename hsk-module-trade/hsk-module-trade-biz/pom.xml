<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xt.hsk</groupId>
    <artifactId>hsk-module-trade</artifactId>
    <version>${revision}</version>
  </parent>

  <artifactId>hsk-module-trade-biz</artifactId>

  <properties>
    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>


  <dependencies>
    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-spring-boot-starter-security</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-module-infra-api</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-module-system-biz</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-module-trade-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <!-- 添加edu模块依赖 -->
    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-module-edu-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <!-- 添加user模块依赖 -->
    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-module-user-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-module-user-biz</artifactId>
      <version>${revision}</version>
    </dependency>

    <!-- 业务组件 -->
    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-spring-boot-starter-biz-data-permission</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-spring-boot-starter-biz-tenant</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-spring-boot-starter-biz-ip</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-spring-boot-starter-protection</artifactId>
    </dependency>

    <!-- 添加Spring状态机依赖 -->
    <dependency>
      <groupId>org.springframework.statemachine</groupId>
      <artifactId>spring-statemachine-core</artifactId>
      <version>3.2.0</version>
    </dependency>
  </dependencies>
</project>