<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.xt.hsk</groupId>
        <artifactId>hsk-module-thirdparty</artifactId>
        <version>${revision}</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-module-thirdparty-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <!--		扣子-->
        <dependency>
            <groupId>com.coze</groupId>
            <artifactId>coze-api</artifactId>
        </dependency>

        <!--基础包-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <!-- DB 相关 -->
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-spring-boot-starter-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>javacv-platform</artifactId>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-spring-boot-starter-biz-ip</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-spring-boot-starter-protection</artifactId>
        </dependency>

    </dependencies>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>hsk-module-thirdparty-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        第三方服务 模块 Biz，实现业务逻辑
    </description>


</project>