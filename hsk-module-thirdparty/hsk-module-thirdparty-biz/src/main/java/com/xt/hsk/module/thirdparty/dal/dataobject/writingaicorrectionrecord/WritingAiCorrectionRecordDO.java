package com.xt.hsk.module.thirdparty.dal.dataobject.writingaicorrectionrecord;

import com.xt.hsk.framework.mybatis.core.dataobject.AppBaseDO;
import lombok.*;

import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;

/**
 * 写作AI批改记录 DO
 *
 * <AUTHOR>
 */
@TableName("t_writing_ai_correction_record")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WritingAiCorrectionRecordDO extends AppBaseDO {

    /**
     * AI批改记录表ID
     */
    @TableId
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 习题ID
     */
    private Long questionId;
    /**
     * 习题详情ID
     */
    private Long questionDetailId;
    /**
     * 题型ID
     */
    private Long questionTypeId;
    /**
     * 题目版本
     */
    private Integer questionVersion;
//    /**
//     * 发送给Coze的题目  图片描述和题干内容
//     */
//    private String sendContent;
//    /**
//     * 语言类型：zh-Hans-中文 en-英文 vi-越南语
//     */
//    private String language;
    /**
     * 作答记录ID
     */
    private Long recordId;
    /**
     * 用户答案数据记录ID
     */
    private Long answerDataId;
    /**
     * 任务状态：0-进行中 1-成功 2-失败(全部成功才是成功，有一个失败则任务失败)
     */
    private Integer status;
//    /**
//     * 批改类型：1-作文批改 2-文本润色
//     */
//    private Integer correctionType;
    /**
     * 总分
     */
    private Integer totalScore;
    /**
     * 得分
     */
    private Integer score;
//    /**
//     * 错误信息（失败时记录）
//     */
//    private String errorMessage;

}