package com.xt.hsk.module.thirdparty.controller.admin.aicorrectiontimes.vo;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AiCorrectionTimesRespVO {


    /**
     * 记录id
     */
    private Long id;


    /**
     * 用户ID
     */
    private Integer userId;


    /**
     * 业务类型 1.真题 2.模考 3.专项
     */
    private Integer bizType;


    /**
     * 业务ID
     */
    private Long bizId;


    /**
     * 调用时间
     */
    private LocalDateTime callTime;


    /**
     * 调用次数
     */
    private Integer callCount;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;


}