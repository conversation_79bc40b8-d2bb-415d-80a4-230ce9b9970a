package com.xt.hsk.module.thirdparty.config.zijie;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Coze 配置类，用于读取 yaml 中的 coze 配置
 */
@Component
@ConfigurationProperties(prefix = "zijie.coze.question")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuestionCozeConfig {

//    /**
//     * 默认的机器人id
//     */
//    private String publishedBotId;
//    /**
//     * 机器人列表
//     */
//    private List<Bot> botList;
    /**
     * 公钥
     */
    public String kid;
    /**
     * 应用id
     */
    public String iss;
    /**
     * 私钥
     */
    public String privateKey;

//    @Data
//    public static class Bot {
//        private Long questionType;
//        private Integer dimensionType;
//        private String botId;
//    }

}