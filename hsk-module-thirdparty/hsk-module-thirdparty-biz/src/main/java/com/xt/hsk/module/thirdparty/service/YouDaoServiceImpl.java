package com.xt.hsk.module.thirdparty.service;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.xt.hsk.module.infra.api.file.FileApi;
import com.xt.hsk.module.thirdparty.controller.app.sign.vo.SignReqVo;
import com.xt.hsk.module.thirdparty.controller.app.sign.vo.SignVo;
import com.xt.hsk.module.thirdparty.manage.sign.SignManage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.xt.hsk.module.thirdparty.api.YouDaoApi;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;

@Slf4j
@Service
public class YouDaoServiceImpl implements YouDaoService {

    @Resource
    private SignManage signManage;
    @Resource
    private FileApi fileApi;


    public String getInput(String input) {
        if (input == null) {
            return input;
        }
        int inputLen = input.length();
        if (inputLen <= 20) {
            return input;
        } else {
            return input.substring(0, 10) + inputLen + input.substring(inputLen - 10);
        }
    }

    @Override
    public String getAudioUrl(String text, String fileName) {
        // 参数校验
        if (text == null || text.isEmpty()) {
            throw new IllegalArgumentException("文本内容不能为空");
        }
        if (fileName == null || fileName.isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        /**
         * 字转音接口
         */
        String YOUDAO_TTS_URL = "https://openapi.youdao.com/ttsapi";
        HttpRequest httpRequest = HttpRequest.post(YOUDAO_TTS_URL);
        httpRequest.header("Content-Type", "application/x-www-form-urlencoded");
        httpRequest.form("q", text);
        httpRequest.form("angle", "0");
        httpRequest.form("concatLines", "0");
        httpRequest.form("imageType", "1");
        httpRequest.form("signType", "v3");

        SignReqVo reqVo = new SignReqVo();
        reqVo.setInput(getInput(text));
        reqVo.setSignType(3);
        SignVo sign = signManage.getSign(reqVo);
        httpRequest.form("sign", sign.getSign());
        httpRequest.form("curtime", sign.getCurtime());
        httpRequest.form("salt", sign.getSalt());
        httpRequest.form("appKey", sign.getAppKey());
        httpRequest.form("voiceName", sign.getExtra().get("voiceName"));
        HttpResponse execute = httpRequest.execute();

        try (InputStream audioStream = execute.bodyStream()) {
            if (audioStream == null) {
                throw new IOException("语音合成失败，返回音频流为空");
            }

            byte[] audioBytes = audioStream.readAllBytes();
            if (audioBytes.length == 0) {
                throw new IOException("语音合成失败，返回音频数据为空");
            }

            // 构建完整文件名（如果未提供后缀则添加）
            if (!fileName.endsWith(".mp3")) {
                fileName += ".mp3";
            }

            // 返回文件存储路径或 URL
            return fileApi.createFile(audioBytes, fileName);
        } catch (IOException e) {
            // 日志记录异常
            log.error("保存音频文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("语音合成文件保存失败", e);
        }
    }


}
