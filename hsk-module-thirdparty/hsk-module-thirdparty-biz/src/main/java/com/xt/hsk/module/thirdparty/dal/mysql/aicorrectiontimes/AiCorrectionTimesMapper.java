package com.xt.hsk.module.thirdparty.dal.mysql.aicorrectiontimes;

import java.util.*;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.module.thirdparty.dal.dataobject.aicorrectiontimes.AiCorrectionTimesDO;
import org.apache.ibatis.annotations.Mapper;
import com.xt.hsk.module.thirdparty.controller.admin.aicorrectiontimes.vo.*;

/**
 * ai批改次数记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AiCorrectionTimesMapper extends BaseMapperX<AiCorrectionTimesDO> {

    default PageResult<AiCorrectionTimesDO> selectPage(AiCorrectionTimesPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiCorrectionTimesDO>()
                .eqIfPresent(AiCorrectionTimesDO::getUserId, reqVO.getUserId())
                .eqIfPresent(AiCorrectionTimesDO::getBizType, reqVO.getBizType())
                .eqIfPresent(AiCorrectionTimesDO::getBizId, reqVO.getBizId())
                .betweenIfPresent(AiCorrectionTimesDO::getCallTime, reqVO.getCallTime())
                .eqIfPresent(AiCorrectionTimesDO::getCallCount, reqVO.getCallCount())
                .betweenIfPresent(AiCorrectionTimesDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AiCorrectionTimesDO::getId));
    }

    Integer getUserAiCorrectionTimes(Long userId, Date beginOfDay, Date endOfDay, Integer bizType);
}