package com.xt.hsk.module.thirdparty.controller.admin.aicorrectiontimes.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
public class AiCorrectionTimesSaveReqVO {

    /**
     * 记录id
     */
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Integer userId;

    /**
     * 业务类型 1.真题 2.模考 3.专项
     */
    @NotNull(message = "业务类型 1.真题 2.模考 3.专项不能为空")
    private Integer bizType;

    /**
     * 业务ID
     */
    @NotNull(message = "业务ID不能为空")
    private Long bizId;

    /**
     * 调用时间
     */
    @NotNull(message = "调用时间不能为空")
    private LocalDateTime callTime;

    /**
     * 调用次数
     */
    @NotNull(message = "调用次数不能为空")
    private Integer callCount;

}