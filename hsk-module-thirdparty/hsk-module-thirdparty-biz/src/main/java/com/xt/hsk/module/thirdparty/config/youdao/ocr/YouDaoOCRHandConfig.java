package com.xt.hsk.module.thirdparty.config.youdao.ocr;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 有道OCR手写识别配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "youdao.ocr.hand")
public class YouDaoOCRHandConfig {

    /**
     * 应用ID（对应YAML中的 app-id）
     */
    private String appKey;

    /**
     * 应用密钥（对应YAML中的 app-key）
     */
    private String appSecret;

}