package com.xt.hsk.module.thirdparty.controller.admin.speakingaicorrectionrecord.vo;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class SpeakingAiCorrectionRecordSaveReqVO {

    /**
     * 口语ai批改记录表id
     */
    private Long id;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空")
    private Long userId;

    /**
     * 习题id
     */
    @NotNull(message = "习题id不能为空")
    private Long questionId;

    /**
     * 题目版本
     */
    @NotNull(message = "题目版本不能为空")
    private Integer questionVersion;

    /**
     * 发送给第三方的题目
     */
    @NotEmpty(message = "发送给第三方的题目不能为空")
    private String sendContent;

    /**
     * 作答记录id
     */
    @NotNull(message = "作答记录id不能为空")
    private Long practiceRecordId;

    /**
     * 批改类型：1-音转字 2-口语评分
     */
    @NotNull(message = "批改类型：1-音转字 2-口语评分不能为空")
    private Integer correctionType;

    /**
     * 参考文本
     */
    private String refText;

    /**
     * 第三方返回结果 json
     */
    private String result;

    /**
     * 第三方返回的记录id
     */
    private Long recordId;

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 错误id
     */
    private Integer errId;

    /**
     * 错误信息（失败时记录）
     */
    private String errorMessage;

}