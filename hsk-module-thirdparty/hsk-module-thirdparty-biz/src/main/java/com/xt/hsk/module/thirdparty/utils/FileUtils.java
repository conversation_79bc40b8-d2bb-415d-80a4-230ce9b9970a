package com.xt.hsk.module.thirdparty.utils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

public class FileUtils {

    public static String setUrL(String input) throws UnsupportedEncodingException {
        String[] parts = input.split("/", -1); // -1 表示保留所有分割的结果
        String leftPart = String.join("/", java.util.Arrays.copyOfRange(parts, 0, parts.length - 1));
        String rightPart = parts[parts.length - 1];
        String encode = URLEncoder.encode(rightPart, "UTF-8");
        return leftPart + '/' + encode;
    }
}
