package com.xt.hsk.module.thirdparty.config.xunfei;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "xunfei")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class XunFeiConfig {
    /**
     * appid
     */
    private String appId;
    /**
     * accessKey
     */
    private String accessKey;
    /**
     * accessSecret
     */
    private String accessSecret;
    /**
     * 音频上传回调地址
     */
    private String callbackUrlBase;

}
