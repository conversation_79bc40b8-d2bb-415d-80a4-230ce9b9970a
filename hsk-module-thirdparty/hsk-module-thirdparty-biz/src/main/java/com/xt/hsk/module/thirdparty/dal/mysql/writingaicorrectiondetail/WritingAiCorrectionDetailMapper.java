package com.xt.hsk.module.thirdparty.dal.mysql.writingaicorrectiondetail;

import java.util.*;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.module.thirdparty.dal.dataobject.writingaicorrectiondetail.WritingAiCorrectionDetailDO;
import org.apache.ibatis.annotations.Mapper;
import com.xt.hsk.module.thirdparty.controller.admin.writingaicorrectiondetail.vo.*;

/**
 * 写作AI批改结果 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WritingAiCorrectionDetailMapper extends BaseMapperX<WritingAiCorrectionDetailDO> {

    default PageResult<WritingAiCorrectionDetailDO> selectPage(WritingAiCorrectionDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WritingAiCorrectionDetailDO>()
                .eqIfPresent(WritingAiCorrectionDetailDO::getRecordId, reqVO.getRecordId())
                .eqIfPresent(WritingAiCorrectionDetailDO::getDimensionType, reqVO.getDimensionType())
                .eqIfPresent(WritingAiCorrectionDetailDO::getScore, reqVO.getScore())
                .eqIfPresent(WritingAiCorrectionDetailDO::getStatus, reqVO.getStatus())
                .eqIfPresent(WritingAiCorrectionDetailDO::getTotalScore, reqVO.getTotalScore())
//                .eqIfPresent(WritingAiCorrectionDetailDO::getOriginalResult, reqVO.getOriginalResult())
                .betweenIfPresent(WritingAiCorrectionDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WritingAiCorrectionDetailDO::getId));
    }

}