package com.xt.hsk.module.thirdparty.controller.app.sign;

import cn.dev33.satoken.annotation.SaIgnore;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.thirdparty.controller.app.sign.vo.SignReqVo;
import com.xt.hsk.module.thirdparty.controller.app.sign.vo.SignVo;
import com.xt.hsk.module.thirdparty.dto.ShengTong.ShengTongCallDto;
import com.xt.hsk.module.thirdparty.dto.ShengTong.ShengTongRespDto;
import com.xt.hsk.module.thirdparty.manage.sign.SignManage;
import com.xt.hsk.module.thirdparty.service.ShengTongServiceImpl;
import com.xt.hsk.module.thirdparty.service.YouDaoServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;

/**
 * 三方接口获取签名
 */
@RestController
@RequestMapping("/third-party/sign")
public class SignController {
    @Resource
    private SignManage signManage;
    @Resource
    private YouDaoServiceImpl youDaoService;
    @Resource
    private ShengTongServiceImpl shengtongService;
    /**
     * 获取签名
     *
     * @param reqVo
     * @return
     */
    @PostMapping("/getSign")
    public CommonResult<SignVo> getSign(@RequestBody SignReqVo reqVo) {
        return CommonResult.success(signManage.getSign(reqVo));
    }

    @PostMapping("/testTTS")
    public CommonResult<String> testTTS() {
        return CommonResult.success(youDaoService.getAudioUrl("你好，最近发现有两个之前的标签都不见了\n" +
                "\n" +
                "一个是昨天发现的雅思活动——「72训练营」标签（现在我重新加标签并把之前的人标签都打回来了），一个是今天发现一个雅思平台入口——「剑20领资料」的标签不见了，之前打过标签的人、包括有个活码会自动打的这个标签，都不见了。\n" +
                "\n" +
                "我们这边只能查到什么时间对什么标签组进行了操作，但查不到删除的具体动作，这个可以查一下你们的日志看是怎么回事吗？我自己没有操作过，号借给过别人但不应该会删标签啊@云果果", "/2025070501/test1"));
    }

    @SaIgnore
    @PostMapping("/testShengtong")
    public ShengTongRespDto testShengtong() {
        ShengTongCallDto shengTongCallDto = new ShengTongCallDto();
        shengTongCallDto.setFile(new File("D:\\download\\compressed\\新录音.mp3"));
        shengTongCallDto.setRefText("微信可以不接收共同好友点赞提醒了");
        shengTongCallDto.setUserId(155L);
        shengTongCallDto.setQuestionId(1L);
        shengTongCallDto.setQuestionVersion(2);
        shengTongCallDto.setRecordId(3L);

        return shengtongService.oralEvaluation(shengTongCallDto);
    }
}
