package com.xt.hsk.module.thirdparty.service;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.lock.annotation.Lock4j;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.thirdparty.api.ShengTongApi;
import com.xt.hsk.module.thirdparty.config.shengtong.ShengtongConfig;
import com.xt.hsk.module.thirdparty.dal.dataobject.speakingaicorrectionrecord.SpeakingAiCorrectionRecordDO;
import com.xt.hsk.module.thirdparty.dto.ShengTong.ShengTongCallDto;
import com.xt.hsk.module.thirdparty.dto.ShengTong.ShengTongRespDto;
import com.xt.hsk.module.thirdparty.service.speakingaicorrectionrecord.SpeakingAiCorrectionRecordService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

import static com.xt.hsk.module.thirdparty.utils.FileUtils.setUrL;

@Slf4j
@Service
public class ShengTongServiceImpl implements ShengTongApi {
    /**
     * 汉字评测
     */
    private final String WORD_EVAL_CN = "word.eval.cn";
    /**
     * 中文句子评测
     */
    private final String SENT_EVAL_CN = "sent.eval.cn";
    /**
     * 中文段落评测
     */
    private final String PARA_EVAL_CN = "para.eval.cn";
    /**
     * 中文识别
     */
    private final String CN_ASR = "cn.asr";


    @Resource
    private ShengtongConfig shengtongConfig;
    @Resource
    private SpeakingAiCorrectionRecordService speakingAiCorrectionRecordService;

    @Override
    @Lock4j(name = "oralEvaluation", keys = {"#shengTongCallDto.userId", "#shengTongCallDto.questionId", "#shengTongCallDto.recordId"}, acquireTimeout = 0)
    public ShengTongRespDto oralEvaluation(ShengTongCallDto shengTongCallDto) {

        return getShengTongRespDto(shengTongCallDto, SENT_EVAL_CN, 2);
    }

    @Override
    @Lock4j(name = "chineseSoundToWord", keys = {"#shengTongCallDto.userId", "#shengTongCallDto.questionId"}, acquireTimeout = 0)
    public ShengTongRespDto chineseSoundToWord(ShengTongCallDto shengTongCallDto) {
        return getShengTongRespDto(shengTongCallDto, CN_ASR, 1);
    }

    /**
     * 口语评测
     *
     * @param voiceUrl 文件 url
     * @param refText  参考文本
     * @param type     评测类型
     * @param userId   用户id
     * @return
     */
    public String oralEvaluation1(String voiceUrl, String refText, String type, Long userId) {
        String url = shengtongConfig.getBaseUrl() + type;

        HttpRequest httpRequest = HttpRequest.post(url);
        String text = buildParam(userId, refText, type);
        httpRequest.header("Request-Index", "0");

        // 下载文件流
        InputStream inputStream = null;
        File file = null;
        try {
            String encode = setUrL(voiceUrl);
            URL fileUrl = new URL(encode);
            inputStream = fileUrl.openStream();
            file = File.createTempFile("temp", ".mp3");
            // 将输入流转换为文件
            try (FileOutputStream fos = new FileOutputStream(file)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
            }
            httpRequest.form("text", text);
            httpRequest.form("audio", file);
            HttpResponse execute = httpRequest.execute();

            return execute.body();
        } catch (Exception e) {
            log.error("下载音频文件失败", e);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (file != null) {
                    file.delete();
                }
            } catch (IOException e) {
                log.error("关闭输入流失败");
            }
        }

        return "";
    }

    private String buildParam(Long userId, String refText, String coreType) {
        //1.生成评分参数
        //sha1算法工具
        String appKey = shengtongConfig.getAppKey();
        String appSecret = shengtongConfig.getAppSecret();
        long timeReqMillis = System.currentTimeMillis();
        String connectSigStr = appKey + timeReqMillis + appSecret;
        String connectSig = SecureUtil.sha1(connectSigStr);

        long timeStartMillis = System.currentTimeMillis();
        String startSigStr = appKey + timeStartMillis + userId + appSecret;
        String startSig = SecureUtil.sha1(startSigStr);

        // tokenId 用随机字符串
        String tokenId = RandomUtil.randomString(32);
        //request param
        String params = "{"
                + "\"connect\":{"
                + "\"cmd\":\"connect\","
                + "\"param\":{"
                + "\"sdk\":{"
                + "\"protocol\":2,"
                + "\"version\":16777472,"
                + "\"source\":9"
                + "},"
                + "\"app\":{"
                + "\"applicationId\":\"" + appKey + "\","
                + "\"sig\":\"" + connectSig + "\","
                + "\"timestamp\":\"" + timeReqMillis + "\""
                + "}"
                + "}"
                + "},"
                + "\"start\":{"
                + "\"cmd\":\"start\","
                + "\"param\":{"
                + "\"app\":{"
                + "\"applicationId\":\"" + appKey + "\","
                + "\"timestamp\":\"" + timeStartMillis + "\","
                + "\"sig\":\"" + startSig + "\","
                + "\"userId\":\"" + userId + "\""
                + "},"
                + "\"audio\":{"
                + "\"sampleBytes\":2,"
                + "\"channel\":1,"
                + "\"sampleRate\":16000,"
                + "\"audioType\":\"mp3\""
                + "},"
                + "\"request\":{"
                + "\"tokenId\":\"" + tokenId + "\","
                + "\"refText\":\"" + refText + "\","
                + "\"coreType\":\"" + coreType + "\","
//                + "\"punctuate\":\"" + 1 + "\","//punctuate为0或不传时，识别文本字与字之间有空格；punctuate为1时，字与字之间无空格
//                + "\"getParam\":\"" + 1 + "\","//1-不返回参数，0-返回参数
                + "\"accent_dialect\":\"" + "non_native" + "\""//non_native：非母语 native：母语
                + "}"
                + "}"
                + "}"
                + "}";
        if (log.isDebugEnabled()) {
            log.debug("请求声通参数:{}", params);
        }
        return params;
    }


    /**
     * 调用声通
     *
     * @param shengTongCallDto
     * @param type
     * @return
     */
    private JSONObject callShengTong(ShengTongCallDto shengTongCallDto, String type) {
        Long userId = shengTongCallDto.getUserId();
        String refText = shengTongCallDto.getRefText();
        String url = shengtongConfig.getBaseUrl() + type;
        HttpRequest httpRequest = HttpRequest.post(url);
        String text = buildParam(userId, refText, type);
        httpRequest.header("Request-Index", "0");
        httpRequest.form("text", text);
        httpRequest.form("audio", shengTongCallDto.getFile());
        HttpResponse execute = httpRequest.execute();
        // 如果是网络问题，则重试一次
        if (execute.getStatus() != 200) {
            log.error("调用失败,重试一次:{} ", execute);
            execute = httpRequest.execute();
        }

        return JSONObject.parseObject(execute.body());
    }


    private ShengTongRespDto getShengTongRespDto(ShengTongCallDto shengTongCallDto, String type, Integer correctionType) {
        SpeakingAiCorrectionRecordDO recordDO = new SpeakingAiCorrectionRecordDO();
        recordDO.setQuestionId(shengTongCallDto.getQuestionId());
        recordDO.setQuestionVersion(shengTongCallDto.getQuestionVersion());
        recordDO.setUserId(shengTongCallDto.getUserId());
        recordDO.setSendContent(shengTongCallDto.getRefText());
        recordDO.setPracticeRecordId(shengTongCallDto.getRecordId());
        recordDO.setCorrectionType(correctionType);
        try {
            // 调用声通
            JSONObject result = callShengTong(shengTongCallDto, type);

            recordDO.setRecordId(result.getString("recordId"));
            if (result.containsKey("errId")) {
                recordDO.setResult(String.valueOf(result));
                recordDO.setErrId(result.getInteger("errId"));
                recordDO.setErrorMessage(result.getString("error"));
            }
            recordDO.setResult(result.getString("result"));
            if (result.getJSONObject("result").containsKey("overall")) {
                Integer totalScore = result.getJSONObject("result").getInteger("overall");
                recordDO.setTotalScore(totalScore);
            }
        } catch (Exception e) {
            log.error("调用声通失败 报错{}", e.getMessage(), e);
            recordDO.setErrId(1);
            recordDO.setErrorMessage("系统出错：" + e.getMessage());
        } finally {
            speakingAiCorrectionRecordService.save(recordDO);
        }
        return BeanUtils.toBean(recordDO, ShengTongRespDto.class);
    }

}
