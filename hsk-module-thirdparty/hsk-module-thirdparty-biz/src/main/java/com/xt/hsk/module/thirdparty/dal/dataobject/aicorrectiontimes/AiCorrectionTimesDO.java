package com.xt.hsk.module.thirdparty.dal.dataobject.aicorrectiontimes;

import com.xt.hsk.framework.mybatis.core.dataobject.AppBaseDO;
import lombok.*;

import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;

/**
 * ai批改次数记录 DO
 *
 * <AUTHOR>
 */
@TableName("t_ai_correction_times")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiCorrectionTimesDO extends AppBaseDO {

    /**
     * 记录id
     */
    @TableId
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 业务类型 1.真题 2.模考 3.专项
     */
    private Integer bizType;
    /**
     * 业务ID
     */
    private Long bizId;
    /**
     * 调用时间
     */
    private LocalDateTime callTime;
    /**
     * 调用次数
     */
    private Integer callCount;
    /**
     * 原始记录id
     */
    private Long originId;
    /**
     * 作答记录id
     */
    private Long recordId;

}