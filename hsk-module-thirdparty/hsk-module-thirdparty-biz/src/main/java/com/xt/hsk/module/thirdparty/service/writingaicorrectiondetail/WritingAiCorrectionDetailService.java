package com.xt.hsk.module.thirdparty.service.writingaicorrectiondetail;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;

import com.xt.hsk.module.thirdparty.controller.admin.writingaicorrectiondetail.vo.WritingAiCorrectionDetailPageReqVO;
import com.xt.hsk.module.thirdparty.dal.dataobject.writingaicorrectiondetail.WritingAiCorrectionDetailDO;
import jakarta.validation.Valid;

/**
 * 写作AI批改结果 Service 接口
 *
 * <AUTHOR>
 */
public interface WritingAiCorrectionDetailService extends IService<WritingAiCorrectionDetailDO> {
    PageResult<WritingAiCorrectionDetailDO> selectPage(@Valid WritingAiCorrectionDetailPageReqVO pageReqVO);

}