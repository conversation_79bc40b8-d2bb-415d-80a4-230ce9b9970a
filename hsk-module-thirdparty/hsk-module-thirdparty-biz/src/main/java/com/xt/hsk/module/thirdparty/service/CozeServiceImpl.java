package com.xt.hsk.module.thirdparty.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.coze.openapi.client.chat.CreateChatReq;
import com.coze.openapi.client.chat.CreateChatResp;
import com.coze.openapi.client.chat.RetrieveChatReq;
import com.coze.openapi.client.chat.RetrieveChatResp;
import com.coze.openapi.client.chat.model.Chat;
import com.coze.openapi.client.chat.model.ChatPoll;
import com.coze.openapi.client.chat.model.ChatStatus;
import com.coze.openapi.client.connversations.message.model.Message;
import com.coze.openapi.service.auth.JWTOAuth;
import com.coze.openapi.service.auth.JWTOAuthClient;
import com.coze.openapi.service.service.CozeAPI;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.xt.hsk.framework.common.enums.QuestionTypeEnum;
import com.xt.hsk.framework.common.exception.ServerException;
import com.xt.hsk.framework.common.exception.ServiceException;
import com.xt.hsk.framework.common.util.i18n.LanguageUtils;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.framework.redis.utils.RedisUtil;
import com.xt.hsk.module.thirdparty.api.CozeApi;
import com.xt.hsk.module.thirdparty.config.zijie.QuestionCozeConfig;
import com.xt.hsk.module.thirdparty.dal.dataobject.writingaicorrectiondetail.WritingAiCorrectionDetailDO;
import com.xt.hsk.module.thirdparty.dal.dataobject.writingaicorrectionrecord.WritingAiCorrectionRecordDO;
import com.xt.hsk.module.thirdparty.dto.coze.CozeCallDto;
import com.xt.hsk.module.thirdparty.dto.coze.CozeDataRespDto;
import com.xt.hsk.module.thirdparty.dto.coze.CozeRespDto;
import com.xt.hsk.module.thirdparty.enums.DimensionTypeEnum;
import com.xt.hsk.module.thirdparty.event.CozeEvent;
import com.xt.hsk.module.thirdparty.service.aicorrectiontimes.AiCorrectionTimesServiceImpl;
import com.xt.hsk.module.thirdparty.service.writingaicorrectiondetail.WritingAiCorrectionDetailService;
import com.xt.hsk.module.thirdparty.service.writingaicorrectionrecord.WritingAiCorrectionRecordService;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.net.MalformedURLException;
import java.net.URL;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xt.hsk.framework.common.constants.RedisKeyPrefix.COZE_TOKEN;


@Slf4j
@Component
public class CozeServiceImpl implements CozeApi {

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";
    private static final String BASE_URL = "https://api.coze.cn/v3/chat/";

    private static final String JWT_BASE_URL = "https://api.coze.cn/api/permission/oauth2/token";
    /**
     * 固定参数
     */
    private static final String GRANT_TYPE = "urn:ietf:params:oauth:grant-type:jwt-bearer";
    /**
     * 授权接口
     */
    private static final String AUTHORIZATION_ENDPOINT = "/api/permission/oauth2/token";
    /**
     * 最短回答长度
     */
    private static final int MIN_ANSWER_LENGTH = 10;
    /**
     * 看图造句最短回答长度
     */
    private static final int MIN_ANSWER_LENGTH_FOR_PICTURE_SENTENCE = 3;
    @Resource
    private QuestionCozeConfig cozeConfig;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private WritingAiCorrectionRecordService writingAiCorrectionRecordService;
    @Resource
    private WritingAiCorrectionDetailService writingAiCorrectionDetailService;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private AiCorrectionTimesServiceImpl aiCorrectionTimesService;

    public static ResponseData parseResponse(String text) {
        if (StringUtils.isBlank(text)) {
            return null;
        }
        return JSON.parseObject(text, ResponseData.class);
    }

    /**
     * 签署数据
     *
     * @param data             jwt前两个部分的字符产拼接
     * @param privateKeyString 私钥字符串
     * @return {@code byte[] }
     * @throws NoSuchAlgorithmException 没有这样的算法异常
     * @throws InvalidKeySpecException  无效密钥规范异常
     * @throws InvalidKeyException      无效密钥异常
     * @throws SignatureException       签名异常
     */
    public static byte[] signData(String data, String privateKeyString)
            throws NoSuchAlgorithmException, InvalidKeySpecException, InvalidKeyException, SignatureException {
        byte[] privateKeyBytes = Base64.getDecoder().decode(privateKeyString);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(keySpec);

        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(data.getBytes());
        return signature.sign();
    }

    private static String getContent(CozeAPI coze, CreateChatReq req) throws Exception {
        CreateChatResp chatResp = coze.chat().create(req);
        Chat chat = chatResp.getChat();
        // get chat id and conversationID
        String chatID = chat.getID();
        String conversationID = chat.getConversationID();

        /*
         * Step two, poll the result of chat
         * Assume the development allows at most one chat to run for 10 seconds. If it exceeds 10 seconds,
         * the chat will be cancelled.
         * And when the chat status is not completed, poll the status of the chat once every second.
         * After the chat is completed, retrieve all messages in the chat.
         * */
        long timeout = 10L;
        long start = System.currentTimeMillis() / 1000;
        while (ChatStatus.IN_PROGRESS.equals(chat.getStatus())) {
            try {
                // The API has a rate limit with 1 QPS.
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                e.printStackTrace();
                break;
            }

            if ((System.currentTimeMillis() / 1000) - start > timeout) {
                // The chat can be cancelled before its completed.
                //                System.out.println(coze.chat().cancel(CancelChatReq.of(conversationID, chatID)));
                break;
            }
            RetrieveChatResp resp = coze.chat().retrieve(RetrieveChatReq.of(conversationID, chatID));
            //            System.out.println(resp);
            chat = resp.getChat();
            if (ChatStatus.COMPLETED.equals(chat.getStatus())) {
                break;
            }
        }

        // The sdk provide an automatic polling method.
        ChatPoll chat2 = coze.chat().createAndPoll(req);
        String content = chat2.getMessages().get(0).getContent().trim();
        return content;
    }

    @Override
    public void questionWritingCorrection(CozeCallDto cozeCallDto) {
        LambdaQueryWrapper<WritingAiCorrectionRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WritingAiCorrectionRecordDO::getRecordId, cozeCallDto.getRecordId());
        queryWrapper.eq(WritingAiCorrectionRecordDO::getUserId, cozeCallDto.getUserId());
        WritingAiCorrectionRecordDO recordServiceOne = writingAiCorrectionRecordService.getOne(queryWrapper);
        if (recordServiceOne != null && recordServiceOne.getStatus() == 1) {// 如果已经成功了  就不要调了
            log.info("已经成功不用重复调用 请求参数：{}", JSON.toJSONString(cozeCallDto));
            aiCorrectionTimesService.returnAiCorrectionTimes(cozeCallDto.getAiCorrectionTimesId());
            return;
        }
        if (recordServiceOne != null && recordServiceOne.getStatus() == 0) {
            log.info("正在批改中不用重复调用 请求参数：{}", JSON.toJSONString(cozeCallDto));
            aiCorrectionTimesService.returnAiCorrectionTimes(cozeCallDto.getAiCorrectionTimesId());
            return;
        }
        try {
            Long writingAiCorrectionRecordId = null;
            WritingAiCorrectionRecordDO writingAiCorrectionRecordDO = new WritingAiCorrectionRecordDO();
            int status = 0;
            if (cozeCallDto.isNeedImageDescription() && (cozeCallDto.getImageDescription() == null || cozeCallDto.getImageDescription().isEmpty())) {
                status = 2;
                writingAiCorrectionRecordDO.setScore(0);
                writingAiCorrectionRecordDO.setTotalScore(getTotalScore(cozeCallDto));
            }


            writingAiCorrectionRecordDO.setQuestionId(cozeCallDto.getQuestionId());
            writingAiCorrectionRecordDO.setQuestionDetailId(cozeCallDto.getQuestionDetailId());
            writingAiCorrectionRecordDO.setQuestionVersion(cozeCallDto.getQuestionVersion());
            writingAiCorrectionRecordDO.setQuestionTypeId(cozeCallDto.getQuestionTypeId());
            writingAiCorrectionRecordDO.setUserId(cozeCallDto.getUserId());
            writingAiCorrectionRecordDO.setRecordId(cozeCallDto.getRecordId());
            writingAiCorrectionRecordDO.setAnswerDataId(cozeCallDto.getAnswerDataId());
            writingAiCorrectionRecordDO.setStatus(status);
            writingAiCorrectionRecordService.save(writingAiCorrectionRecordDO);
            writingAiCorrectionRecordId = writingAiCorrectionRecordDO.getId();

            if (recordServiceOne != null) {
                Long oldRecordId = recordServiceOne.getId();
                // 删除旧数据
                writingAiCorrectionRecordService.removeById(recordServiceOne);
                // 防止重复调用，将已有的数据复制一份
                LambdaQueryWrapper<WritingAiCorrectionDetailDO> queryWrapper1 = new LambdaQueryWrapper<>();
                queryWrapper1.eq(WritingAiCorrectionDetailDO::getRecordId, oldRecordId);

                List<WritingAiCorrectionDetailDO> list = writingAiCorrectionDetailService.list(queryWrapper1);
                if (list != null && !list.isEmpty()) {
                    for (WritingAiCorrectionDetailDO detailDO : list) {
                        detailDO.setRecordId(writingAiCorrectionRecordId);
                        detailDO.setId(null);
                    }
                    writingAiCorrectionDetailService.saveOrUpdateBatch(list);

                    LambdaUpdateWrapper<WritingAiCorrectionDetailDO> queryWrapper2 = new LambdaUpdateWrapper<>();
                    queryWrapper2.eq(WritingAiCorrectionDetailDO::getRecordId, oldRecordId);
                    // 删除旧数据
                    writingAiCorrectionDetailService.remove(queryWrapper2);
                }
            }
            if (status == 0) {
                // 异步调用这个方法
                Long finalWritingAiCorrectionRecordId = writingAiCorrectionRecordId;
                CompletableFuture.runAsync(() -> {
                    handleAiCorrectBefore2(finalWritingAiCorrectionRecordId, cozeCallDto);
                });
//                handleAiCorrectBefore(writingAiCorrectionRecordId, cozeCallDto);
            } else {
                log.error("ai批改失败 题目数据异常,不进行批改");
                aiCorrectionTimesService.returnAiCorrectionTimes(cozeCallDto.getAiCorrectionTimesId());
            }
        } catch (Exception e) {
            log.error("ai批改失败 报错：{}", e.getMessage(), e);
            aiCorrectionTimesService.returnAiCorrectionTimes(cozeCallDto.getAiCorrectionTimesId());
        }

    }

    @Async
    protected boolean handleAiCorrectBefore2(Long writingAiCorrectionRecordId, CozeCallDto cozeCallDto) {
        boolean flag = true;
        int userScore = 0;
        int count = 0;

        try {
            // 查询已有的批改数据
            LambdaQueryWrapper<WritingAiCorrectionDetailDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WritingAiCorrectionDetailDO::getRecordId, writingAiCorrectionRecordId);
            List<WritingAiCorrectionDetailDO> writingAiCorrectionDetailDOList = writingAiCorrectionDetailService.list(queryWrapper);

            Map<Integer, WritingAiCorrectionDetailDO> aiCorrectionDetailDOHashMap;
            if (writingAiCorrectionDetailDOList != null && !writingAiCorrectionDetailDOList.isEmpty()) {
                aiCorrectionDetailDOHashMap = writingAiCorrectionDetailDOList.stream()
                        .collect(Collectors.toMap(WritingAiCorrectionDetailDO::getDimensionType, Function.identity(), (t1, t2) -> t1));
            } else {
                aiCorrectionDetailDOHashMap = new HashMap<>();
            }

            String questionContent = cozeCallDto.getQuestionContent();
            String answerData = cozeCallDto.getAnswerData();
            // 只保留中文和标点
            // 正则说明：
            // - \\p{sc=Han} : 匹配所有中文字符（包括扩展字符集）
            // - \\p{P}      : 匹配所有Unicode标点符号（含中英文标点）
            // - [^...]      : 表示"非"这些字符
            // 整体含义：移除非中文字符且非标点符号的所有字符
            String regex = "[^\\p{sc=Han}~！@#￥%…&*（）‘’‐—–\\-+=【】「」、|：；“”《》，。？.→•●]";
            answerData = answerData.replaceAll(regex, "");
            // 获取需要处理的评分维度
            int botIndex = 1;
            if (QuestionTypeEnum.WRITING_ESSAY.getCode() == cozeCallDto.getQuestionTypeId() && cozeCallDto.getImageDescription() != null && !cozeCallDto.getImageDescription().isEmpty()) {
                botIndex = 2;
            }
            List<QuestionBotEnum> questionBotEnumList = QuestionBotEnum.getQuestionBotEnumList(cozeCallDto.getQuestionTypeId(), botIndex);

            // 并行处理每个评分维度
            List<CompletableFuture<DimensionResult>> futures = new ArrayList<>();

            for (QuestionBotEnum questionBotEnum : questionBotEnumList) {
                String finalAnswerData = answerData;
                CompletableFuture<DimensionResult> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        WritingAiCorrectionDetailDO writingAiCorrectionDetailDO = aiCorrectionDetailDOHashMap.get(questionBotEnum.getDimensionType());

                        // 如果已经成功，跳过
                        if (writingAiCorrectionDetailDO != null && writingAiCorrectionDetailDO.getStatus() == 1 && DimensionTypeEnum.getCommentsAndPolishingList().contains(questionBotEnum.dimensionType)) {
                            return new DimensionResult(true, 0, 0, questionBotEnum.dimensionType);
                        } else if (writingAiCorrectionDetailDO != null && writingAiCorrectionDetailDO.getStatus() == 1 && !DimensionTypeEnum.getCommentsAndPolishingList().contains(questionBotEnum.dimensionType)) {
                            return new DimensionResult(true, writingAiCorrectionDetailDO.getScore(), 1, questionBotEnum.dimensionType);
                        }

                        String content = "";
                        if (QuestionTypeEnum.WRITING_ESSAY.getCode() == cozeCallDto.getQuestionTypeId()) {
                            if (cozeCallDto.getImageDescription() != null && !cozeCallDto.getImageDescription().isEmpty()) {
                                content = "图片要素:\n" + cozeCallDto.getImageDescription() + "\n" + "回答:\n" + finalAnswerData;
                            } else {
                                content = "参考词:\n" + questionContent + "\n" + "回答:\n" + finalAnswerData;
                            }

                        } else if (QuestionTypeEnum.WRITING_PICTURE_SENTENCE.getCode() == cozeCallDto.getQuestionTypeId()) {
                            content = "参考词:\n" + questionContent + "\n" + "图片内容:\n" + cozeCallDto.getImageDescription() + "回答:\n" + finalAnswerData;
                        } else {
                            if (Objects.equals(questionBotEnum.dimensionType, DimensionTypeEnum.CONTENT_RELEVANCE.getCode())) {
                                content = "题目:\n" + questionContent + "\n" + "回答:\n" + finalAnswerData;
                            } else {
                                content = finalAnswerData;
                            }
                        }

                        // 根据维度类型处理逻辑
                        if (Objects.equals(questionBotEnum.dimensionType, DimensionTypeEnum.REFERENCE_WORD.getCode())) {
                            Integer handleRefWord = handleRefWord(writingAiCorrectionRecordId, cozeCallDto);
                            if (handleRefWord != -1) {
                                return new DimensionResult(true, handleRefWord, 1, questionBotEnum.dimensionType);
                            } else {
                                return new DimensionResult(true, 0, 1, questionBotEnum.dimensionType);
                            }
                        } else if (DimensionTypeEnum.getCommentsAndPolishingList().contains(questionBotEnum.dimensionType)) {
                            boolean result = handleCommentsAndPolishing(writingAiCorrectionRecordId, cozeCallDto, questionBotEnum, content, true, finalAnswerData);
                            return new DimensionResult(result, 0, 0, questionBotEnum.dimensionType);
                        } else if (Objects.equals(questionBotEnum.dimensionType, DimensionTypeEnum.LENGTH.getCode())) {
                            int score = handelAnswerLength(writingAiCorrectionRecordId, questionBotEnum, cozeCallDto.getAnswerData());
                            return new DimensionResult(score >= 0, score, 1, questionBotEnum.dimensionType);
                        } else {
                            // 调用AI评分
                            if (writingAiCorrectionDetailDO == null) {
                                WritingAiCorrectionDetailDO aiCorrectionDetailDO = new WritingAiCorrectionDetailDO();
                                aiCorrectionDetailDO.setRecordId(writingAiCorrectionRecordId);
                                aiCorrectionDetailDO.setDimensionType(questionBotEnum.dimensionType);
                                aiCorrectionDetailDO.setTotalScore(questionBotEnum.totalScore);
                                boolean processed = true;
                                if ((finalAnswerData.length() < MIN_ANSWER_LENGTH && QuestionTypeEnum.WRITING_SUMMARY.getCode() == cozeCallDto.getQuestionTypeId())
                                        || (QuestionTypeEnum.WRITING_PICTURE_SENTENCE.getCode() == cozeCallDto.getQuestionTypeId() && finalAnswerData.length() < MIN_ANSWER_LENGTH_FOR_PICTURE_SENTENCE)) {
                                    aiCorrectionDetailDO.setStatus(1);
                                    aiCorrectionDetailDO.setScore(0);
                                } else {
                                    processed = processAiCorrection(aiCorrectionDetailDO, content, cozeCallDto.getUserId(), questionBotEnum.botId);
                                    aiCorrectionDetailDO.setScore(Math.min(questionBotEnum.totalScore, aiCorrectionDetailDO.getScore()));
                                }
                                writingAiCorrectionDetailService.save(aiCorrectionDetailDO);
                                return new DimensionResult(processed, aiCorrectionDetailDO.getScore(), 1, questionBotEnum.dimensionType);
                            } else {
                                if (finalAnswerData.length() < MIN_ANSWER_LENGTH) {
                                    writingAiCorrectionDetailDO.setStatus(1);
                                    writingAiCorrectionDetailDO.setScore(0);
                                } else {
                                    boolean processed = processAiCorrection(writingAiCorrectionDetailDO, content, cozeCallDto.getUserId(), questionBotEnum.botId);
                                    writingAiCorrectionDetailDO.setScore(Math.min(questionBotEnum.totalScore, writingAiCorrectionDetailDO.getScore()));
                                    if (!processed) {
                                        return new DimensionResult(false, 0, 1, questionBotEnum.dimensionType);
                                    }
                                }
                                writingAiCorrectionDetailService.updateById(writingAiCorrectionDetailDO);
                                return new DimensionResult(true, writingAiCorrectionDetailDO.getScore(), 1, questionBotEnum.dimensionType);
                            }
                        }
                    } catch (Exception e) {
                        log.error("并行处理维度失败: {}", e.getMessage(), e);
                        return new DimensionResult(false, 0, 0, questionBotEnum.dimensionType);
                    }
                });
                futures.add(future);
            }

            // 等待所有任务完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allFutures.join(); // 阻塞直到所有任务完成

            // 收集结果
            List<DimensionResult> results = new ArrayList<>();
            for (CompletableFuture<DimensionResult> future : futures) {
                try {
                    results.add(future.get());
                } catch (Exception e) {
                    log.error("获取任务结果失败: {}", e.getMessage(), e);
                    results.add(new DimensionResult(false, 0, 0, -1));
                }
            }

            // 汇总结果
            for (DimensionResult result : results) {
                if (!result.success) {
                    flag = false;
                    log.error("ai批改失败, dimensionType {} 调用失败或者分项分记录失败", result.getDimensionType());
                }
                if (result.count > 0) {
                    userScore += result.score;
                    count += result.count;
                }
            }

            return flag;
        } catch (Exception e) {
            flag = false;
            log.error("ai批改失败 {}", e.getMessage(), e);
            return false;
        } finally {
            log.info("finally 处理逻辑");
            // 更新主表状态
            LambdaUpdateWrapper<WritingAiCorrectionRecordDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(WritingAiCorrectionRecordDO::getId, writingAiCorrectionRecordId);

            int totalScore = getTotalScore(cozeCallDto);

            if (!flag) {
                log.error("ai批改失败,存在bot 调用失败或者分项分记录失败");
                aiCorrectionTimesService.returnAiCorrectionTimes(cozeCallDto.getAiCorrectionTimesId());
                updateWrapper.set(WritingAiCorrectionRecordDO::getStatus, 2);
            } else {
                updateWrapper.set(WritingAiCorrectionRecordDO::getStatus, 1);
            }

            if (count > 0) {
                updateWrapper.set(WritingAiCorrectionRecordDO::getScore, Math.round((float) 1.0 * userScore / count));
            } else {
                updateWrapper.set(WritingAiCorrectionRecordDO::getScore, 0);
            }

            updateWrapper.set(WritingAiCorrectionRecordDO::getTotalScore, totalScore);
            writingAiCorrectionRecordService.update(updateWrapper);

            // 成功后发布事件 修改题目作答数据
            CozeEvent cozeEvent = new CozeEvent();
            cozeEvent.setWritingAiCorrectionRecordId(writingAiCorrectionRecordId);
            cozeEvent.setSuccess(flag);
            cozeEvent.setRecordId(cozeCallDto.getRecordId());
            applicationContext.publishEvent(cozeEvent);
        }
    }

    @Async
    protected boolean handleAiCorrectBefore(Long writingAiCorrectionRecordId, CozeCallDto cozeCallDto) {
        boolean flag = true;
        int userScore = 0;
        int count = 0;
        try {
            // 查询已有的批改数据
            LambdaQueryWrapper<WritingAiCorrectionDetailDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WritingAiCorrectionDetailDO::getRecordId, writingAiCorrectionRecordId);
            List<WritingAiCorrectionDetailDO> writingAiCorrectionDetailDOList = writingAiCorrectionDetailService.list(queryWrapper);
            // 成功标识

//            Fraction fraction = Fraction.getFraction(0, 1);
            String questionContent = cozeCallDto.getQuestionContent();
            String answerData = cozeCallDto.getAnswerData();
            // 只保留中文和标点
            // 正则说明：
            // - \\p{sc=Han} : 匹配所有中文字符（包括扩展字符集）
            // - \\p{P}      : 匹配所有Unicode标点符号（含中英文标点）
            // - [^...]      : 表示"非"这些字符
            // 整体含义：移除非中文字符且非标点符号的所有字符
            String regex = "[^\\p{sc=Han}~！@#￥%…&*（）‘’‐—–\\-+=【】「」、|：；“”《》，。？.→•●]";
            answerData = answerData.replaceAll(regex, "");

            // 获取需要处理的评分维度
            int botIndex = 1;
            if (QuestionTypeEnum.WRITING_ESSAY.getCode() == cozeCallDto.getQuestionTypeId() && cozeCallDto.getImageDescription() != null && !cozeCallDto.getImageDescription().isEmpty()) {
                botIndex = 2;
            }
            List<QuestionBotEnum> questionBotEnumList = QuestionBotEnum.getQuestionBotEnumList(cozeCallDto.getQuestionTypeId(), botIndex);

            Map<Integer, WritingAiCorrectionDetailDO> aiCorrectionDetailDOHashMap = new HashMap<>();
            if (writingAiCorrectionDetailDOList != null && !writingAiCorrectionDetailDOList.isEmpty()) {
                aiCorrectionDetailDOHashMap = writingAiCorrectionDetailDOList.stream().collect(Collectors.toMap(WritingAiCorrectionDetailDO::getDimensionType, Function.identity(), (t1, t2) -> t1));
            }

            for (QuestionBotEnum questionBotEnum : questionBotEnumList) {
                WritingAiCorrectionDetailDO writingAiCorrectionDetailDO = aiCorrectionDetailDOHashMap.get(questionBotEnum.getDimensionType());
                if (writingAiCorrectionDetailDO != null && writingAiCorrectionDetailDO.getStatus() == 1) {
                    // 已经成功了的科目就不处理了
                    continue;
                }
                String content = "";
                if (QuestionTypeEnum.WRITING_ESSAY.getCode() == cozeCallDto.getQuestionTypeId()) {
                    if (cozeCallDto.getImageDescription() != null && !cozeCallDto.getImageDescription().isEmpty()) {
                        content = "题目:\n" + cozeCallDto.getImageDescription() + "\n" + "回答:\n" + answerData;
                    } else {
                        content = "题目:\n" + questionContent + "\n" + "回答:\n" + answerData;
                    }

                } else {
                    if (Objects.equals(questionBotEnum.dimensionType, DimensionTypeEnum.CONTENT_RELEVANCE.getCode())) {
                        content = "题目:\n" + questionContent + "\n" + "回答:\n" + answerData;
                    } else {
                        content = answerData;
                    }
                }

                if (Objects.equals(questionBotEnum.dimensionType, DimensionTypeEnum.REFERENCE_WORD.getCode())) {
//                参考词使用
                    Integer handleRefWord = handleRefWord(writingAiCorrectionRecordId, cozeCallDto);
                    if (handleRefWord != -1) {
                        userScore += handleRefWord;
                        count++;
                    }
                } else if (DimensionTypeEnum.getCommentsAndPolishingList().contains(questionBotEnum.dimensionType)) {
                    flag = handleCommentsAndPolishing(writingAiCorrectionRecordId, cozeCallDto, questionBotEnum, content, flag, answerData);

                } else if (Objects.equals(questionBotEnum.dimensionType, DimensionTypeEnum.LENGTH.getCode())) {
                    userScore += handelAnswerLength(writingAiCorrectionRecordId, questionBotEnum, cozeCallDto.getAnswerData());
                    count++;
                } else if (Objects.equals(questionBotEnum.dimensionType, DimensionTypeEnum.CONTENT_RELEVANCE.getCode())
                        && QuestionTypeEnum.WRITING_ESSAY.getCode() == cozeCallDto.getQuestionTypeId()
                        && (cozeCallDto.getImageDescription() == null || cozeCallDto.getImageDescription().isEmpty())) {
                    // 短文写作 没有图片 没有 内容相关性 评分项
                } else {
                    if (writingAiCorrectionDetailDO == null) {
                        // 新增
                        WritingAiCorrectionDetailDO aiCorrectionDetailDO = new WritingAiCorrectionDetailDO();
                        aiCorrectionDetailDO.setRecordId(writingAiCorrectionRecordId);
                        aiCorrectionDetailDO.setDimensionType(questionBotEnum.dimensionType);
                        aiCorrectionDetailDO.setTotalScore(questionBotEnum.totalScore);
                        if (answerData.length() < MIN_ANSWER_LENGTH) {
                            aiCorrectionDetailDO.setStatus(1);
                            aiCorrectionDetailDO.setScore(0);
                        } else {
                            boolean processed = processAiCorrection(aiCorrectionDetailDO, content,
                                    cozeCallDto.getUserId(), questionBotEnum.botId);
                            aiCorrectionDetailDO.setScore(Math.min(questionBotEnum.totalScore, aiCorrectionDetailDO.getScore()));
                            if (!processed) {
                                flag = false;
                            }
                        }
                        writingAiCorrectionDetailService.save(aiCorrectionDetailDO);
                        userScore += aiCorrectionDetailDO.getScore();
                    } else {
                        if (answerData.length() < MIN_ANSWER_LENGTH) {
                            writingAiCorrectionDetailDO.setStatus(1);
                            writingAiCorrectionDetailDO.setScore(0);
                        } else {
                            boolean processed = processAiCorrection(writingAiCorrectionDetailDO, content,
                                    cozeCallDto.getUserId(), questionBotEnum.botId);
                            writingAiCorrectionDetailDO.setScore(Math.min(questionBotEnum.totalScore, writingAiCorrectionDetailDO.getScore()));

                            if (!processed) {
                                flag = false;
                            }
                        }
                        writingAiCorrectionDetailService.updateById(writingAiCorrectionDetailDO);
                        userScore += writingAiCorrectionDetailDO.getScore();
                    }
                    count++;
                }

            }

            return flag;
        } catch (Exception e) {
            flag = false;
            log.error("ai批改失败 {}", e.getMessage(), e);
            return false;
        } finally {
            log.info("finally 处理逻辑");
            // 更新主表状态
            LambdaUpdateWrapper<WritingAiCorrectionRecordDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(WritingAiCorrectionRecordDO::getId, writingAiCorrectionRecordId);

            // 所有分项分所占比例加起来 * 总分数
            // 不同题型总分不一样
            int totalScore = getTotalScore(cozeCallDto);

            if (!flag) {
                log.error("ai批改失败,存在bot 调用失败或者分项分记录失败");
                aiCorrectionTimesService.returnAiCorrectionTimes(cozeCallDto.getAiCorrectionTimesId());
                updateWrapper.set(WritingAiCorrectionRecordDO::getStatus, 2);
            } else {
                updateWrapper.set(WritingAiCorrectionRecordDO::getStatus, 1);
            }
            if (count > 0) {
                updateWrapper.set(WritingAiCorrectionRecordDO::getScore, Math.round((float) 1.0 * userScore / count));
            } else {
                updateWrapper.set(WritingAiCorrectionRecordDO::getScore, 0);
            }
            updateWrapper.set(WritingAiCorrectionRecordDO::getTotalScore, totalScore);
            writingAiCorrectionRecordService.update(updateWrapper);
            //         成功后发布事件 修改题目作答数据
            CozeEvent cozeEvent = new CozeEvent();
            cozeEvent.setWritingAiCorrectionRecordId(writingAiCorrectionRecordId);
            cozeEvent.setSuccess(flag);
            cozeEvent.setRecordId(cozeCallDto.getRecordId());
            cozeEvent.setBizType(cozeCallDto.getBizType());
            applicationContext.publishEvent(cozeEvent);
        }
    }

    private int getTotalScore(CozeCallDto cozeCallDto) {
        int totalScore = 0;
        if (QuestionTypeEnum.WRITING_PICTURE_SENTENCE.getCode() == cozeCallDto.getQuestionTypeId()) {
            totalScore = 8;
        } else if (QuestionTypeEnum.WRITING_ESSAY.getCode() == cozeCallDto.getQuestionTypeId()) {
            totalScore = 30;

        } else {
            totalScore = 100;
        }
        return totalScore;
    }

    private boolean handleCommentsAndPolishing(Long writingAiCorrectionRecordId, CozeCallDto cozeCallDto, QuestionBotEnum questionBotEnum, String content, boolean flag, String userAnswer) {
//        Integer dimensionType = questionBotEnum.dimensionType;
//        if (questionBotEnum.dimensionType == 9 || questionBotEnum.dimensionType == 10) {
//            dimensionType = 7;
//        }
//        String language = LanguageUtils.getLanguage();
//        // 只处理一种语言
//        if (Objects.equals(language, LanguageUtils.LANGUAGE_CN) && (questionBotEnum.dimensionType == 7 || questionBotEnum.dimensionType == 9)) {
//            return flag;
//        }
//        if (Objects.equals(language, LanguageUtils.LANGUAGE_EN) && (questionBotEnum.dimensionType == 7 || questionBotEnum.dimensionType == 10)) {
//            return flag;
//        }
//        if (Objects.equals(language, LanguageUtils.LANGUAGE_VI) && (questionBotEnum.dimensionType == 9 || questionBotEnum.dimensionType == 10)) {
//            return flag;
//        }
        // 一键润色 评语
        String callAiCorrection = "";
        String cnComment = "中文字数过少，可以再多写一些哦~";
        String enComment = "Your writing is too short. Try expanding on your ideas~";
        String viComment = "Bài viết còn hơi ngắn, bạn có thể viết dài thêm một chút được không~";
        if (userAnswer.length() < MIN_ANSWER_LENGTH && QuestionTypeEnum.WRITING_SUMMARY.getCode() == cozeCallDto.getQuestionTypeId()) {
            if (questionBotEnum.dimensionType == 7) {
                callAiCorrection = viComment;
            }
            if (questionBotEnum.dimensionType == 10) {
                callAiCorrection = cnComment;
            }
            if (questionBotEnum.dimensionType == 9) {
                callAiCorrection = enComment;
            }
            if (questionBotEnum.dimensionType == 8) {
                callAiCorrection = LanguageUtils.getLocalizedValue(cnComment, enComment, viComment);
            }
        } else if (userAnswer.length() < MIN_ANSWER_LENGTH_FOR_PICTURE_SENTENCE && QuestionTypeEnum.WRITING_PICTURE_SENTENCE.getCode() == cozeCallDto.getQuestionTypeId()) {
            if (questionBotEnum.dimensionType == 7) {
                callAiCorrection = viComment;
            }
            if (questionBotEnum.dimensionType == 10) {
                callAiCorrection = cnComment;
            }
            if (questionBotEnum.dimensionType == 9) {
                callAiCorrection = enComment;
            }
            if (questionBotEnum.dimensionType == 8) {
                callAiCorrection = LanguageUtils.getLocalizedValue(cnComment, enComment, viComment);
            }
        } else {
            callAiCorrection = callAiCorrection(content, cozeCallDto.getUserId(), questionBotEnum.botId);
        }
        int status = 2;
        if (callAiCorrection != null && !callAiCorrection.isEmpty()) {
            status = 1;
        } else {
            flag = false;
        }
        WritingAiCorrectionDetailDO writingAiCorrectionDetailDO = new WritingAiCorrectionDetailDO();
        writingAiCorrectionDetailDO.setRecordId(writingAiCorrectionRecordId);
        writingAiCorrectionDetailDO.setDimensionType(questionBotEnum.dimensionType);
        writingAiCorrectionDetailDO.setContent(callAiCorrection);
        writingAiCorrectionDetailDO.setStatus(status);
        writingAiCorrectionDetailService.save(writingAiCorrectionDetailDO);
        return flag;
    }


    private Integer handelAnswerLength(Long writingAiCorrectionRecordId, QuestionBotEnum questionBotEnum, String answerData) {
        int score = 0;
        if (QuestionTypeEnum.WRITING_SUMMARY.getCode() == questionBotEnum.questionType) {
            // 篇幅
            // 最终得分 = max( 0, 100 - 0.5 × |实际字数 - 400| )
            // - 公式说明：
            // - 实际字数：考生写作的完整字数（含标点）。
            // - |实际字数-400|：字数与基准值的绝对偏差。
            // - 扣分速率：每偏离1字扣0.5分（对称扣分，超长或不足均适用）。
            // - 保底规则：得分最低为0分（当字数差≥200时）。
            int wordCount = answerData.length();
            score = Math.max(0, 100 - (int) (0.5 * Math.abs(wordCount - 400)));


        } else if (QuestionTypeEnum.WRITING_ESSAY.getCode() == questionBotEnum.questionType) {
//            5 10字及以内
//            10 11-30字
//            15 31-50字
//            20 51-70字
//            25 71-75字
//            30 76字及以上
            int length = answerData.length();
            if (length <= 10) {
                score = 5;
            } else if (length <= 30) {
                score = 10;
            } else if (length <= 50) {
                score = 15;
            } else if (length <= 70) {
                score = 20;
            } else if (length <= 75) {
                score = 25;
            } else {
                score = 30;
            }
        }

        WritingAiCorrectionDetailDO writingAiCorrectionDetailDO = new WritingAiCorrectionDetailDO();
        writingAiCorrectionDetailDO.setRecordId(writingAiCorrectionRecordId);
        writingAiCorrectionDetailDO.setDimensionType(questionBotEnum.dimensionType);
        writingAiCorrectionDetailDO.setTotalScore(questionBotEnum.totalScore);
        writingAiCorrectionDetailDO.setScore(score);
        writingAiCorrectionDetailDO.setStatus(1);
        writingAiCorrectionDetailService.save(writingAiCorrectionDetailDO);
        return score;
    }

    private Integer handleRefWord(Long writingAiCorrectionRecordId, CozeCallDto cozeCallDto) {
        // 获取参考词
        String referenceWord = cozeCallDto.getQuestionContent();
        int totalScore = 0;
        int score = -1;
        if (QuestionTypeEnum.WRITING_PICTURE_SENTENCE.getCode() == cozeCallDto.getQuestionTypeId()) {
            totalScore = 8;
            // 使用了就是8分，不使用就是4分
            String answerData = cozeCallDto.getAnswerData();
            String[] split = referenceWord.split(" ");
            for (String word : split) {
                if (answerData.contains(word)) {
                    score = 8;
                    break;
                }
            }
            if (score == -1) {
                score = 4;
            }
        } else if (QuestionTypeEnum.WRITING_ESSAY.getCode() == cozeCallDto.getQuestionTypeId()
                && cozeCallDto.getImageDescription() != null && !cozeCallDto.getImageDescription().isEmpty()) {
            // 看图写短文  不会出现参考词 直接跳过
            log.info("看图写短文  不会出现参考词 直接跳过");
            return -1;
        } else {
            totalScore = 30;
            String answerData = cozeCallDto.getAnswerData();
            int used = 0;
            String[] split = referenceWord.split(" ");
            for (String word : split) {
                if (answerData.contains(word)) {
                    used++;
                }
            }
            score = Math.min(30, 5 * (used + 1));
        }
//        fraction = fraction.add();
        WritingAiCorrectionDetailDO writingAiCorrectionDetailDO = new WritingAiCorrectionDetailDO();
        writingAiCorrectionDetailDO.setRecordId(writingAiCorrectionRecordId);
        writingAiCorrectionDetailDO.setDimensionType(1);
        writingAiCorrectionDetailDO.setTotalScore(totalScore);
        writingAiCorrectionDetailDO.setScore(score);
        writingAiCorrectionDetailDO.setStatus(1);
        writingAiCorrectionDetailService.save(writingAiCorrectionDetailDO);
        return score;

    }

    /**
     * 执行AI评分调用与结果校验
     */
    private boolean processAiCorrection(WritingAiCorrectionDetailDO detailDO, String content,
                                        Long userId, String botId) {
        int score = -1;
        int status = 1;

        try {
            String contentRelatedness = callAiCorrection(content, userId, botId);

            // 尝试解析评分（最多两次）
            for (int attempt = 0; attempt < 2; attempt++) {
                try {
                    int parsedScore = Integer.parseInt(contentRelatedness);
                    if (parsedScore >= 0 && parsedScore <= 110) {
                        score = parsedScore;
                        break;
                    }
                } catch (NumberFormatException e) {
                    status = 2;
                    log.error("数据异常（尝试{}次）: {} botId:{}", attempt + 1, e.getMessage(), botId);
                }

                if (attempt == 0) {
                    log.error("数据异常（尝试{}次）: {} botId:{}", attempt + 1, contentRelatedness, botId);

                    contentRelatedness = callAiCorrection(content, userId, botId);
                }
            }

            if (score == -1) {
                status = 2;
                log.error("系统异常，扣子评分失败，参数，题目：{}，失败原因：分数不在预期，分数{}", content, score);
            }

        } catch (Exception e) {
            status = 2;
            log.error("系统异常，扣子评分失败，参数，题目：{}，失败原因：分数不在预期，分数{}", content, score);
            log.error("AI批改接口调用异常", e); // 记录完整异常堆栈
        } finally {
            detailDO.setScore(Math.max(0, score));
            detailDO.setStatus(status);

        }

        return status == 1;
    }

    /**
     * 处理看图造句
     * 1.参考词使用
     * 2.内容相关性
     * 3.错别字及词汇使用
     * 4.语法准确性与深广度
     *
     * @param writingAiCorrectionRecordId
     * @param cozeCallDto
     */
    private void handleWritingPictureSentence(Long writingAiCorrectionRecordId, CozeCallDto cozeCallDto) {
        // 看图造句
        String imageDescription = cozeCallDto.getImageDescription();
        // 参考词
        String sendContent = cozeCallDto.getQuestionContent();
        String[] refWords = sendContent.split(" ");
        // 用户回答
        String answerData = cozeCallDto.getAnswerData();
        //1.参考词使用
        int score = 4;
        for (String refWord : refWords) {
            if (answerData.contains(refWord)) {
                score = 8;
                break;
            }
        }
        WritingAiCorrectionDetailDO writingAiCorrectionDetailDO = new WritingAiCorrectionDetailDO();
        writingAiCorrectionDetailDO.setRecordId(writingAiCorrectionRecordId);
        writingAiCorrectionDetailDO.setDimensionType(1);
        writingAiCorrectionDetailDO.setScore(score);
        writingAiCorrectionDetailDO.setTotalScore(8);
        writingAiCorrectionDetailDO.setStatus(1);
        writingAiCorrectionDetailService.save(writingAiCorrectionDetailDO);
        // 2.内容相关性
//        String aiScore = callAiCorrection(cozeCallDto, 2);

        WritingAiCorrectionDetailDO writingAiCorrectionDetailDO2 = new WritingAiCorrectionDetailDO();
        writingAiCorrectionDetailDO.setRecordId(writingAiCorrectionRecordId);
        writingAiCorrectionDetailDO.setDimensionType(2);
//        if (aiScore ==  null) {
//            writingAiCorrectionDetailDO.setStatus(2);
////            writingAiCorrectionDetailDO.setScore(score);
//        }else {
//            writingAiCorrectionDetailDO.setStatus(1);
//            writingAiCorrectionDetailDO.setScore(Integer.parseInt(aiScore));
//        }

        writingAiCorrectionDetailDO.setTotalScore(8);

        writingAiCorrectionDetailService.save(writingAiCorrectionDetailDO);

    }

    @Override
    public CozeRespDto getQuestionAiCorrectionResult(CozeCallDto reqVO) {
        // 通过用户id 作答记录id查找ai批改记录
        LambdaQueryWrapper<WritingAiCorrectionRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WritingAiCorrectionRecordDO::getUserId, reqVO.getUserId())
                .eq(WritingAiCorrectionRecordDO::getRecordId, reqVO.getRecordId())
                .eq(WritingAiCorrectionRecordDO::getQuestionId, reqVO.getQuestionId())
                .eq(WritingAiCorrectionRecordDO::getQuestionDetailId, reqVO.getQuestionDetailId())
                .eq(WritingAiCorrectionRecordDO::getAnswerDataId, reqVO.getAnswerDataId());
        // 用户的一条作答数据应该只有一条批改记录，批改失败的数据成功后会被更新状态
        WritingAiCorrectionRecordDO writingAiCorrectionRecordDO = writingAiCorrectionRecordService.getOne(queryWrapper);
        if (writingAiCorrectionRecordDO == null) {
            return null;
        }
        CozeRespDto cozeRespDto = BeanUtils.toBean(writingAiCorrectionRecordDO, CozeRespDto.class);
//        if (writingAiCorrectionRecordDO.getStatus() != 1) {
//            return cozeRespDto;
//        }
        // 查找批改明细数据
        LambdaQueryWrapper<WritingAiCorrectionDetailDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WritingAiCorrectionDetailDO::getRecordId, writingAiCorrectionRecordDO.getId());
        List<WritingAiCorrectionDetailDO> writingAiCorrectionDetailDOList = writingAiCorrectionDetailService.list(lambdaQueryWrapper);
        List<CozeDataRespDto> cozeDataRespDtos = BeanUtils.toBean(writingAiCorrectionDetailDOList, CozeDataRespDto.class);
        List<CozeDataRespDto> finalDtos = new ArrayList<>();
        String language = LanguageUtils.getLanguage(); // 提前获取语言，避免重复调用
        // 使用Map映射语言与维度类型的转换规则
        Map<String, Integer> languageToDimensionMap = new HashMap<>();
        languageToDimensionMap.put(LanguageUtils.LANGUAGE_CN, 10);
        languageToDimensionMap.put(LanguageUtils.LANGUAGE_EN, 9);
        languageToDimensionMap.put(LanguageUtils.LANGUAGE_VI, 7);

        String cnComment = "中文字数过少，可以再多写一些哦~";
        String enComment = "Your writing is too short. Try expanding on your ideas~";
        String viComment = "Bài viết còn hơi ngắn, bạn có thể viết dài thêm một chút được không~";
        for (CozeDataRespDto cozeDataRespDto : cozeDataRespDtos) {
            if (cozeDataRespDto == null) {
                continue; // 空值处理
            }

            // 生成评语
            if (QuestionTypeEnum.WRITING_SUMMARY.getCode() == writingAiCorrectionRecordDO.getQuestionTypeId()) {
                // 缩写题的评语处理
                String commont = handleWritingSummaryComment(cozeDataRespDto.getDimensionType(), cozeDataRespDto.getScore());
                cozeDataRespDto.setComment(commont);
            } else if (QuestionTypeEnum.WRITING_PICTURE_SENTENCE.getCode() == writingAiCorrectionRecordDO.getQuestionTypeId()) {
                // 看图造句题的评语处理
                String commont = handleWritingPictureSentenceComment(cozeDataRespDto.getDimensionType(), cozeDataRespDto.getScore());
                cozeDataRespDto.setComment(commont);
            } else if (QuestionTypeEnum.WRITING_ESSAY.getCode() == writingAiCorrectionRecordDO.getQuestionTypeId()) {
                // 短文写作题的评语处理
                if (reqVO.isNeedImageDescription()) {
                    String commont = handleWritingEssayCommentHasImage(cozeDataRespDto.getDimensionType(), cozeDataRespDto.getScore());
                    cozeDataRespDto.setComment(commont);
                } else {
                    String commont = handleWritingEssayComment(cozeDataRespDto.getDimensionType(), cozeDataRespDto.getScore());
                    cozeDataRespDto.setComment(commont);
                }
            }


            Integer dimensionType = cozeDataRespDto.getDimensionType();
            DimensionTypeEnum typeEnum = DimensionTypeEnum.getByCode(dimensionType);
            if (typeEnum != null) {
                cozeDataRespDto.setDimensionTypeDesc(LanguageUtils.getLocalizedValue(typeEnum.getDesc(), typeEnum.getDescEn(), typeEnum.getDescVi()));
            }

            if (dimensionType == 8 && Objects.equals(cnComment, cozeDataRespDto.getContent())) {
                cozeDataRespDto.setContent(LanguageUtils.getLocalizedValue(cnComment, enComment, viComment));
            }
            if (dimensionType == 7 || dimensionType == 9 || dimensionType == 10) {
                // 判断是否需要转换维度类型
                if (languageToDimensionMap.containsKey(language) && Objects.equals(dimensionType, languageToDimensionMap.get(language))) {
                    cozeDataRespDto.setDimensionType(7);
                    finalDtos.add(cozeDataRespDto);
                }
            } else {
                finalDtos.add(cozeDataRespDto);
            }
        }

        finalDtos.sort(Comparator.comparing(CozeDataRespDto::getDimensionType));
        cozeRespDto.setCozeDataRespDtos(finalDtos);
        return cozeRespDto;
    }

    private String handleWritingEssayCommentHasImage(Integer dimensionType, Integer score) {
        if (Objects.equals(dimensionType, DimensionTypeEnum.REFERENCE_WORD.getCode())) {
            if (score <= 5) {
                String comment = "5分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "5分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội";
                String comment_en = "5分评语yingyu yingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 10) {
                String comment = "10分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "10分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "10分评语yingyu yingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 15) {
                String comment = "15分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "15分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "15分评语yingyu yingyuyingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 20) {
                String comment = "20分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "20分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "20分评语yingyu yingyuyingyuyingyuyingyuyingy";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 25) {
                String comment = "25分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "25分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "25分评语yingyu yingyuyingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 30) {
                String comment = "30分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "30分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "30分评语yingyu yingyuyingyuyingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }
        }
        if (Objects.equals(dimensionType, DimensionTypeEnum.WRONG_WORD_AND_WORD_USE.getCode())) {
            if (score >= 0 && score <= 11) {
                String comment = "1-11分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "1-11分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "1-11分评语yingyu yingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 12 && score <= 16) {
                String comment = "12-16分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "12-16分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "12-16分评语yingyu yingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 17 && score <= 21) {
                String comment = "17-21分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "17-21分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "17-21分评语yingyu yingyuyingyuyingyuyingyu}";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 22 && score <= 26) {
                String comment = "22-26分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "22-26分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "22-26分评语yingyu yingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 27 && score <= 30) {
                String comment = "27-30分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "27-30分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "27-30分评语yingyu yingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }

        } else if (Objects.equals(dimensionType, DimensionTypeEnum.GRAMMAR_ACCURACY_AND_BROADNESS.getCode())) {
            if (score >= 0 && score <= 11) {
                String comment = "1-11分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "1-11分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "1-11分评语yingyu yingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 12 && score <= 16) {
                String comment = "12-16分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "12-16分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "12-16分评语yingyu yingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 17 && score <= 21) {
                String comment = "17-21分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "17-21分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "17-21分评语yingyu yingyuyingyuyingyuyingyu}";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 22 && score <= 26) {
                String comment = "22-26分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "22-26分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "22-26分评语yingyu yingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 27 && score <= 30) {
                String comment = "27-30分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "27-30分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "27-30分评语yingyu yingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }
        } else if (Objects.equals(dimensionType, DimensionTypeEnum.CONTENT_RELEVANCE.getCode())) {
            if (score >= 0 && score <= 11) {
                String comment = "1-11分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "1-11分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "1-11分评语yingyu yingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 12 && score <= 16) {
                String comment = "12-16分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "12-16分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "12-16分评语yingyu yingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 17 && score <= 21) {
                String comment = "17-21分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "17-21分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "17-21分评语yingyu yingyuyingyuyingyuyingyu}";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 22 && score <= 26) {
                String comment = "22-26分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "22-26分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "22-26分评语yingyu yingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 27 && score <= 30) {
                String comment = "27-30分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "27-30分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "27-30分评语yingyu yingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }
        } else if (Objects.equals(dimensionType, DimensionTypeEnum.STATEMENT_AND_LOGIC_CONSISTENCY.getCode())) {
            if (score >= 0 && score <= 11) {
                String comment = "1-11分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "1-11分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "1-11分评语yingyu yingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 12 && score <= 16) {
                String comment = "12-16分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "12-16分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "12-16分评语yingyu yingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 17 && score <= 21) {
                String comment = "17-21分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "17-21分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "17-21分评语yingyu yingyuyingyuyingyuyingyu}";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 22 && score <= 26) {
                String comment = "22-26分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "22-26分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "22-26分评语yingyu yingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 27 && score <= 30) {
                String comment = "27-30分评语请重新修改，缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "27-30分评语Vui lòng sửa lại, nội dung thiếu tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                String comment_en = "27-30分评语yingyu yingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }
        } else if (Objects.equals(dimensionType, DimensionTypeEnum.LENGTH.getCode())) {
            if (score == 5) {
                String comment = "5分评语请重新修改，内容缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "5分评语Vui lòng sửa lại, nội dung thiếu thông tin quan trọng, vui lòng thêm thông tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                ;
                String comment_en = "5分评语yingyu yingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 10) {
                String comment = "10分评语请重新修改，内容缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "10分评语Vui lòng sửa lại, nội dung thiếu thông tin quan trọng, vui lòng thêm thông tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                ;
                String comment_en = "10分评语yingyu yingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyuyingyu";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 15) {
                String comment = "15分评语请重新修改，内容缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "15分评语Vui lòng sửa lại, nội dung thiếu%" +
                        " tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                ;
                String comment_en = "15分评语yingyu yingyuyingyuyingyuying";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 20) {
                String comment = "20分评语请重新修改，内容缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "20分评语Vui lòng sửa lại, nội dung thiếu%" +
                        " tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                ;
                String comment_en = "20分评语yingyu yingyuyingyuyingyuying";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 25) {
                String comment = "25分评语请重新修改，内容缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "25分评语Vui lòng sửa lại, nội dung thiếu%" +
                        " tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                ;
                String comment_en = "25分评语yingyu yingyuyingyuyingyuying";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 30) {
                String comment = "30分评语请重新修改，内容缺少必要信息，请补充内容，让内容更丰富，让内容更完整。";
                String comment_vi = "30分评语Vui lòng sửa lại, nội dung thiếu%" +
                        " tin quan trọng, vui lòng thêm tin, để nội dung được phong phú hơn, để nội dung được hoàn chỉnh hơn.";
                ;
                String comment_en = "30分评语yingyu yingyuyingyuyingyuying";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }
        }
        return null;
    }

    private String handleWritingEssayComment(Integer dimensionType, Integer score) {
        if (Objects.equals(dimensionType, DimensionTypeEnum.REFERENCE_WORD.getCode())) {
            if (score <= 5) {
                String comment = "未按要求使用参考词汇，请仔细阅读题目要求再动笔。";
                String comment_vi = "Bạn chưa dùng từ tham khảo đúng yêu cầu, hãy đọc kỹ hướng dẫn trước khi làm bài.";
                String comment_en = "Failed to use the reference words as required. Please read the question instructions carefully before starting.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 10) {
                String comment = "并未使用所有的参考词，建议重新检查一下词汇。";
                String comment_vi = "Bài còn thiếu từ tham khảo theo yêu cầu,  cần nâng cao độ chính xác khi sử dụng từ.";
                String comment_en = "You haven't used all the reference words suggested. It would be helpful to double-check your word choices.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 15) {
                String comment = "并未使用所有的参考词，建议重新检查词汇的运用。";
                String comment_vi = "Bài chưa sử dụng từ tham khảo đầy đủ, cần nâng cao độ chính xác khi sử dụng từ.";
                String comment_en = "You've missed some of the recommended reference words. I'd suggest taking another look at how you're using vocabulary throughout the text.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 20) {
                String comment = "参考词使用不够充分，建议仔细对照要求加强练习。";
                String comment_vi = "Việc sử dụng từ tham khảo chưa thật sự đầy đủ, bạn nên đối chiếu kỹ yêu cầu và luyện tập thêm nhé.";
                String comment_en = "The use of reference words is insufficient. It is recommended to carefully review the requirements and practice more.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 25) {
                String comment = "参考词运用得当，表达流畅自然，继续保持！";
                String comment_vi = "Từ kham khảo được sử dụng chính xác, cách diễn đạt trôi chảy tự nhiên, tiếp tục phát huy nhé!";
                String comment_en = "The reference words are well applied, and the expression is smooth and natural. Keep it up!";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 30) {
                String comment = "成功使用了参考词，表达清晰，能有效利用给定信息。";
                String comment_vi = "Vận dụng hiệu quả từ tham khảo, diễn đạt mạnh lạc, tận dụng tốt thông tin cho sẵn.";
                String comment_en = "The reference words are successfully used, with clear expression and effective utilization of the given information.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }
        }
        if (Objects.equals(dimensionType, DimensionTypeEnum.WRONG_WORD_AND_WORD_USE.getCode())) {
            if (score >= 0 && score <= 11) {
                String comment = "错别字明显，词汇使用存在问题，建议加强基础练习。";
                String comment_vi = "Xuất hiện nhiều lỗi chính tả, sử dụng từ còn tồn tại nhiều vấn đề, nên tăng cường luyện tập các bài tập cơ bản.";
                String comment_en = "There are obvious typos and issues with the vocabulary; it is recommended to strengthen basic practice.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 12 && score <= 16) {
                String comment = "错别字明显，词汇使用有误，建议加强积累与检查。";
                String comment_vi = "Xuất hiện nhiều lỗi chính tả, sử dụng từ chưa chính xác, nên tăng cường trau dồi thêm vốn từ và kiểm tra kỹ.";
                String comment_en = "There are some obvious typos and incorrect word, we may need to accumulate more vocabulary and review again.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 17 && score <= 21) {
                String comment = "词汇使用较基础，注意避免重复和用词不当，继续努力提升表达丰富度。";
                String comment_vi = "Sử dụng từ mức độ cơ bản, chú ý tránh lặp từ và sử dụng từ không phù hợp, tiếp tục cố gắng nâng cao khả năng diễn đạt phong phú.";
                String comment_en = "The vocabulary used is quite basic; try to avoid repetition and improper word choices, and keep working on enriching your expressions.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 22 && score <= 26) {
                String comment = "词汇运用较丰富，出现了个别错别字，需再细心些。";
                String comment_vi = "Sử dùng từ ngữ phong phú, xuất hiện một vài lỗi chính tả, cẩn thận hơn nhé.";
                String comment_en = "Your word choices are quite varied, which is good. Just be more careful with a few spelling mistakes here and there.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 27 && score <= 30) {
                String comment = "词汇运用精准丰富，表达流畅自然，展现出优秀的语言驾驭能力！";
                String comment_vi = "Sử dụng từ vựng chuẩn xác phong phú, diễn đạt trôi chảy tự nhiên, thể hiện khả năng sử dụng ngôn ngữ tuyệt vời!";
                String comment_en = "The vocabulary is precise and rich, with smooth and natural expression, demonstrating excellent command of the language!";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }

        } else if (Objects.equals(dimensionType, DimensionTypeEnum.GRAMMAR_ACCURACY_AND_BROADNESS.getCode())) {
            if (score >= 0 && score <= 11) {
                String comment = "语法错误明显，表达不够完整，建议多练习基础句式。";
                String comment_vi = "Còn nhiều lỗi ngữ pháp, biểu đạt chưa được hoàn thiện, nên luyện tập thêm các cấu trúc câu cơ bản.";
                String comment_en = "The grammar is clearly incorrect and the expression is incomplete. It's recommended to practice basic sentence structures more.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 12 && score <= 16) {
                String comment = "语法错误明显影响表达，建议多练习基本句式并注意语序。";
                String comment_vi = "Lỗi ngữ pháp ảnh hưởng rõ đến khả năng diễn đạt, cần luyện tập nhiều hơn các mẫu câu cơ bản và chú ý trật tự từ trong câu.";
                String comment_en = "Grammatical errors significantly affect expression. It is recommended to practice basic sentence structures more and pay attention to word order.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 17 && score <= 21) {
                String comment = "语法错误稍多，句式较单一，建议多练习复杂句型提升表达层次。";
                String comment_vi = "Nhiều lỗi ngữ pháp, câu văn đơn điệu, nên luyện thêm các mẫu câu phức để nâng cao khả năng diễn đạt.";
                String comment_en = "There are quite a few grammatical errors, and the sentence structures are somewhat repetitive. It is recommended to practice more complex sentence patterns to enhance the depth of expression.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 22 && score <= 26) {
                String comment = "语法运用较丰富，偶有小错不影响理解，可多练习复合句衔接。";
                String comment_vi = "Sử dụng đa dạng các cấu trúc ngữ pháp, còn một số lỗi nhỏ không ảnh hưởng đến ý nghĩa, có thể luyện tập kĩ năng nối các câu phức.";
                String comment_en = "Grammar is fairly varied with occasional minor errors that don't hinder comprehension. More practice with complex sentence structures would be beneficial.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 27 && score <= 30) {
                String comment = "语法运用娴熟，句式丰富多变，展现出扎实的语言功底，非常出色！";
                String comment_vi = "Sử dụng ngữ pháp thành thạo, đa dạng cấu trúc câu, cho thấy nền tảng ngôn ngữ vững chắc, thật xuất sắc!";
                String comment_en = "The grammar is skillfully applied with a rich variety of sentence structures, demonstrating a solid command of the language—truly outstanding!";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }
        } else if (Objects.equals(dimensionType, DimensionTypeEnum.STATEMENT_AND_LOGIC_CONSISTENCY.getCode())) {
            if (score >= 0 && score <= 11) {
                String comment = "语句碎片化严重，逻辑混乱，建议先理清思路再组织语言。";
                String comment_vi = "Câu văn rời rạc, trình bày thiếu logic, cần hệ thống lại ý tưởng trước khi diễn đạt.";
                String comment_en = "The sentences are highly fragmented and lack logical coherence. It is recommended to clarify your thoughts before organizing the language.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 12 && score <= 16) {
                String comment = "语句衔接不够自然，逻辑关系需更清晰，建议多练习段落连贯性。";
                String comment_vi = "Các câu văn liên kết chưa được tự nhiên, cần thể hiện rõ mối quan hệ logic giữa các ý, nên luyện tập cách viết mạch lạc giữa các đoạn văn.";
                String comment_en = "The sentence connections are not natural enough, and the logical relationships need to be clearer. It is recommended to practice paragraph coherence more.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 17 && score <= 21) {
                String comment = "语句衔接需更自然，逻辑关系可再清晰些，多练习会更好！";
                String comment_vi = "Cải thiện tính liên kết giữa các câu, thể hiện rõ hơn các mối quan hệ logic, luyện tập nhiều sẽ tiến bộ thôi!";
                String comment_en = "The sentence transitions could be more natural, and the logical connections clearer. More practice would help!";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 22 && score <= 26) {
                String comment = "语句衔接自然，逻辑清晰，若能丰富过渡方式会更出色。";
                String comment_vi = "Câu văn liền mạch tự nhiên, bố cục rõ ràng, nếu đa dạng được nhiều cách chuyển ý thì sẽ càng hay hơn.";
                String comment_en = "The sentences flow naturally with clear logic, and they would be better with more varied transitions.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 27 && score <= 30) {
                String comment = "语句自然流畅，逻辑严密连贯，展现出色的写作功底！";
                String comment_vi = "Câu văn tự nhiên, trôi chảy, logic chặt chẽ mạch lạc, thể hiện năng lực viết xuất sắc!";
                String comment_en = "The sentences are natural and fluent, with tight logic and coherence, showcasing excellent writing skills!";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }
        } else if (Objects.equals(dimensionType, DimensionTypeEnum.LENGTH.getCode())) {
            if (score == 5) {
                String comment = "篇幅过短，未能充分展开内容，建议增加细节描写。";
                String comment_vi = "Bài viết còn quá ngắn, chưa triển khai đầy đủ nội dung, cần bổ sung thêm các chi tiết miêu tả.";
                String comment_en = "The content is too brief and lacks sufficient elaboration; it is recommended to add more detailed descriptions.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 10) {
                String comment = "篇幅不足，建议充实内容，更完整地表达题目的主题。";
                String comment_vi = "Bài viết còn chưa đủ, cần bổ sung thêm nội dung, thể hiện được trọn vẹn chủ đề của đề bài.";
                String comment_en = "Insufficient length, it is recommended to enrich the content for a more complete expression of the topic's theme.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 15) {
                String comment = "篇幅略显不足，建议充实内容以更完整地表达主题。";
                String comment_vi = "Bài viết có phần hơi ngắn, nên bổ sung thêm nội dung để diễn đạt chủ đề được đầy đủ và trọn vẹn hơn.";
                String comment_en = "The length is slightly insufficient; it is recommended to enrich the content for a more complete expression of the topic.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 20) {
                String comment = "篇幅略显不足，继续充实内容以更完整地表达主题。";
                String comment_vi = "Bài viết có phần hơi ngắn, tiếp tục bổ sung thêm nội dung để diễn đạt chủ đề được đầy đủ và trọn vẹn hơn.";
                String comment_en = "The length is slightly insufficient; please expand the content to more fully express the topic.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 25) {
                String comment = "篇幅适中，内容充实，继续保持对字数的精准把控！";
                String comment_vi = "Bài viết độ dài hợp lý, nội dung đầy đủ, tiếp tục duy trì kiểm soát số lượng từ!";
                String comment_en = "Moderate length with substantial content—keep up the precise control of word count!";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 30) {
                String comment = "内容充实，篇幅恰到好处，继续保持！";
                String comment_vi = "Nội dung đầy đủ, độ dài chuẩn, tiếp tục phát huy nhé!";
                String comment_en = "The content is substantial and the length is just right—keep it up!";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }

        }
        return null;
    }

    private String handleWritingPictureSentenceComment(Integer dimensionType, Integer score) {
        if (Objects.equals(dimensionType, DimensionTypeEnum.REFERENCE_WORD.getCode())) {
            if (score == 4) {
                String comment = "未能正确使用给定的参考词，注意充分利用所提供的词汇。";
                String comment_vi = "Chưa sử dụng đúng các từ tham khảo đã cho, cần chú ý tận dụng đầy đủ các từ vựng được cung cấp";
                String comment_en = "Failed to correctly use the given reference words; be sure to make full use of the provided vocabulary.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 8) {
                String comment = "成功使用了参考词，表达清晰，能有效利用给定信息。";
                String comment_vi = "Đã sử dụng từ ngữ tham khảo một cách hiệu quả, diễn đạt rõ ràng và biết khai thác tốt thông tin được cung cấp";
                String comment_en = "Successfully used the reference words; expression is clear and the given information is effectively utilized.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }

        } else if (Objects.equals(dimensionType, DimensionTypeEnum.CONTENT_RELEVANCE.getCode())) {
            if (score <= 1) {
                String comment = "内容未涉及给定词汇或图片，表达明显偏离主题，需加强审题能力。";
                String comment_vi = "Nội dung không đề cập đến từ vựng hoặc hình ảnh được cung cấp, diễn đạt lạc đề, cần cải thiện khả năng đọc hiểu đề bài";
                String comment_en = "Content did not involve the given vocabulary or image; expression clearly deviates from the topic and comprehension of the prompt needs improvement.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score <= 5) {
                String comment = "内容与给定词汇或图片略有相关，但不够紧密，表达还需进一步贴近要求。";
                String comment_vi = "Nội dung có liên quan đến từ vựng hoặc hình ảnh được cung cấp, nhưng mối liên hệ chưa chặt chẽ, cách diễn đạt cần bám sát yêu cầu hơn nữa.";
                String comment_en = "Content is somewhat related to the given vocabulary or image, but not closely enough; expression needs to better align with the requirements.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score <= 8) {
                String comment = "内容紧密围绕参考词及图片展开，主题明确，继续保持！";
                String comment_vi = "Nội dung bám sát từ tham khảo và hình ảnh, chủ đề rõ ràng, cần tiếp tục phát huy!";
                String comment_en = "Content is closely centered around the reference words and image, with a clear theme—keep it up!";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }

        } else if (Objects.equals(dimensionType, DimensionTypeEnum.WRONG_WORD_AND_WORD_USE.getCode())) {
            if (score <= 1) {
                String comment = "错别字较多，严重影响表达效果，需特别注意词汇准确性。";
                String comment_vi = "Lỗi chính tả khá nhiều, ảnh hưởng nghiêm trọng đến hiệu quả diễn đạt, cần đặc biệt chú ý đến độ chính xác của từ ngữ";
                String comment_en = "Too many spelling mistakes, which severely affect the clarity of expression; special attention is needed for word accuracy.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 2) {
                String comment = "错别字数量偏多，表达效果明显受限，需强化基础书写能力。";
                String comment_vi = "Tương đối nhiều lỗi chính tả, làm giảm rõ rệt hiệu quả diễn đạt, cần rèn luyện thêm kỹ năng viết cơ bản.";
                String comment_en = "The number of spelling mistakes is relatively high, which significantly limits the effectiveness of the expression; basic writing skills need strengthening.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 3) {
                String comment = "错别字仍然存在，影响一定的阅读体验，建议仔细检查书写。";
                String comment_vi = "Vẫn còn xuất hiện lỗi chính tả, gây ảnh hưởng nhất định đến trải nghiệm đọc, nên kiểm tra kỹ lưỡng hơn khi viết.";
                String comment_en = "Spelling mistakes still exist, affecting the reading experience to some extent; careful proofreading is recommended.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 4) {
                String comment = "错别字明显，词汇使用偏简单基础，建议拓展词汇难度并检查书写。";
                String comment_vi = "Lỗi chính tả khá rõ, từ vựng sử dụng còn đơn giản, nên mở rộng vốn từ vựng với mức độ khó cao hơn và chú ý kiểm tra lại khi viết.";
                String comment_en = "Obvious spelling mistakes and overly simple vocabulary; consider expanding vocabulary difficulty and checking spelling.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 5) {
                String comment = "存在个别错别字，词汇较基础，表达尚可，需注意细节书写。";
                String comment_vi = "Có vài lỗi chính tả nhỏ, từ vựng còn đơn giản, cách diễn đạt tạm ổn, cần chú ý hơn đến các chi tiết khi viết.";
                String comment_en = "A few spelling mistakes, with rather basic vocabulary; expression is acceptable but attention to writing details is needed.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 6) {
                String comment = "存在个别错别字，词汇难度略有提升，但表达的准确性仍需加强。";
                String comment_vi = "Có vài lỗi chính tả nhỏ, từ vựng có phần nâng cao hơn nhưng cách diễn đạt vẫn cần cải thiện về độ chính xác.";
                String comment_en = "A few spelling mistakes, with slightly more advanced vocabulary; however, accuracy of expression still needs improvement.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 7) {
                String comment = "无错别字，但词汇使用较简单，建议进一步提升表达的深度。";
                String comment_vi = "Không có lỗi chính tả, nhưng từ vựng còn đơn giản, nên nâng cao độ sâu sắc trong cách diễn đạt.";
                String comment_en = "No spelling mistakes, but vocabulary use is relatively simple; suggest enhancing the depth of expression.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score == 8) {
                String comment = "表达流畅，无错别字，成功使用较高级词汇，继续保持！";
                String comment_vi = "Diễn đạt mạch lạc, không có lỗi chính tả, đã sử dụng các từ vựng nâng cao, tiếp tục phát huy nhé!";
                String comment_en = "Fluent expression with no spelling mistakes, successfully using more advanced vocabulary—keep up the good work!";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }
        } else if (Objects.equals(dimensionType, DimensionTypeEnum.GRAMMAR_ACCURACY_AND_BROADNESS.getCode())) {
            if (score >= 0 && score <= 3) {
                String comment = "语法错误较多，表达不完整或存在理解障碍，需加强语法训练。";
                String comment_vi = "Lỗi ngữ pháp khá nhiều, cách diễn đạt chưa hoàn chỉnh hoặc gây khó hiểu, cần luyện tập thêm về ngữ pháp";
                String comment_en = "Many grammar errors, resulting in incomplete expression or difficulty in comprehension; grammar training needs to be strengthened.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 4 && score <= 6) {
                String comment = "表达完整但句式简单，偶有语法错误，建议增加句式复杂性。";
                String comment_vi = "Diễn đạt đầy đủ nhưng câu văn còn đơn giản, thỉnh thoảng có lỗi ngữ pháp, nên sử dụng nhiều câu phức hơn.";
                String comment_en = "Expression is complete but sentence structures are simple, with occasional grammar mistakes; suggest increasing sentence complexity.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 7 && score <= 8) {
                String comment = "表达语法准确，句式丰富且有逻辑，整体表现优秀，继续保持！";
                String comment_vi = "Ngữ pháp chính xác, câu văn đa dạng và có logic, tổng thể rất tốt – hãy tiếp tục phát huy nhé!";
                String comment_en = "Grammatically accurate expression, with varied and logical sentence structures—excellent overall performance, keep it up!";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }
        }

        return "";
    }

    private String handleWritingSummaryComment(Integer dimensionType, Integer score) {
        // 内容相关性维度
        if (Objects.equals(dimensionType, DimensionTypeEnum.CONTENT_RELEVANCE.getCode())) {
            if (score >= 0 && score <= 19) {
                String comment = "内容偏离主旨，建议仔细对照原文重新梳理。";
                String comment_vi = "Nội dung lệch chủ đề, nên so sánh kỹ với nguyên văn để chỉnh sửa lại.";
                String comment_en = "The content deviates from the main topic. It is recommended to carefully review the original text again.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 20 && score <= 39) {
                String comment = "内容偏离原文明显，建议紧扣原文重点重新梳理。";
                String comment_vi = "Nội dung lệch nguyên văn rõ rệt, nên bám sát trọng điểm của nguyên văn để chỉnh sửa lại.";
                String comment_en = "The content significantly deviates from the original text; it is recommended to closely follow the key points of the original text.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 40 && score <= 59) {
                String comment = "原文内容遗漏明显，需更全面地把握原文要点。";
                String comment_vi = "Nội dung nguyên văn bị bỏ sót rõ ràng, cần nắm bắt toàn diện các điểm chính của nguyên văn hơn.";
                String comment_en = "The original content has obvious omissions and requires a more comprehensive grasp of the key points.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 60 && score <= 69) {
                String comment = "原文内容遗漏明显，逻辑稍显混乱，建议紧扣原文内容展开。";
                String comment_vi = "Nội dung nguyên văn bị bỏ sót rõ ràng, logic hơi lộn xộn, đề nghị bám sát nội dung nguyên văn để phát triển.";
                String comment_en = "The original content has obvious omissions and slightly confusing logic; it is recommended to stay closely aligned with the source material.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 70 && score <= 79) {
                String comment = "内容基本完整，但需注意关键细节的准确性和表述的严谨性。";
                String comment_vi = "Nội dung cơ bản đầy đủ, nhưng cần chú ý độ chính xác của chi tiết then chốt và sự nghiêm túc trong biểu đạt.";
                String comment_en = "The content is generally complete, but attention should be paid to the accuracy of key details and the rigor of expression.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 80 && score <= 89) {
                String comment = "对原文的内容把握准确，细节处理稍显简略，建议增加细节的描述。";
                String comment_vi = "Nắm bắt nội dung nguyên văn chính xác, chi tiết xử lý hơi đơn giản, đề nghị bổ sung thêm mô tả chi tiết.";
                String comment_en = "You accurately conveys the original content, though the details are somewhat simplified. It is recommended to enrich the description with more specifics.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 90 && score <= 100) {
                String comment = "缩写精准凝练，原文内容完整呈现，逻辑清晰，表现优异！";
                String comment_vi = "Tóm tắt chính xác cô đọng, nội dung nguyên văn thể hiện đầy đủ, logic rõ ràng, thể hiện xuất sắc!";
                String comment_en = "The passage is precise and concise, fully presenting the original content with clear logic and outstanding performance!";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }
        }
        // 语句与逻辑连贯性维度
        else if (Objects.equals(dimensionType, DimensionTypeEnum.STATEMENT_AND_LOGIC_CONSISTENCY.getCode())) {
            if (score >= 0 && score <= 19) {
                String comment = "语句杂乱无章，逻辑混乱不清，建议重新梳理内容脉络。";
                String comment_vi = "Câu văn rời rạc nghiêm trọng, logic rối loạn, đề nghị suy nghĩ rõ ràng rồi mới tổ chức ngôn ngữ.";
                String comment_en = "The sentences are disorganized and lack logical coherence; it is recommended to restructure the content for clarity.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 20 && score <= 39) {
                String comment = "语句碎片明显，逻辑主线模糊，建议加强句子衔接和整体连贯性。";
                String comment_vi = "Câu văn có nhiều đoạn rời rạc, mạch logic mơ hồ, đề nghị tăng cường kết nối câu và tính mạch lạc tổng thể.";
                String comment_en = "The sentence fragments are evident, and the logical thread is unclear. It is recommended to enhance sentence cohesion and overall coherence.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 40 && score <= 59) {
                String comment = "语句衔接不够自然，逻辑关系需更清晰，建议多练习连贯表达。";
                String comment_vi = "Câu văn liên kết chưa tự nhiên, quan hệ logic cần rõ ràng hơn, đề nghị luyện tập thêm cách diễn đạt mạch lạc.";
                String comment_en = "The sentence connections are not natural enough, and the logical relationships need to be clearer. More practice in coherent expression is recommended.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 60 && score <= 69) {
                String comment = "语句衔接不够自然，逻辑顺序需调整，建议多练习前后连贯的表达。";
                String comment_vi = "Câu văn liên kết chưa tự nhiên, trình tự logic cần điều chỉnh, đề nghị luyện tập thêm cách diễn đạt liền mạch trước sau.";
                String comment_en = "The transitions between sentences are not smooth enough, and the logical sequence needs adjustment. It is recommended to practice more on coherent expression.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 70 && score <= 79) {
                String comment = "语句衔接可以更自然，注意推理过程的完整性会更好。";
                String comment_vi = "Câu văn có thể liên kết tự nhiên hơn, chú ý quá trình suy luận đầy đủ sẽ tốt hơn.";
                String comment_en = "The sentence transitions could be more natural, and pay attention to the completeness of the reasoning process.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 80 && score <= 89) {
                String comment = "语句流畅自然，逻辑清晰连贯，个别地方可优化衔接方式。";
                String comment_vi = "Câu văn trôi chảy tự nhiên, logic rõ ràng liền mạch, vài chỗ có thể tối ưu thêm cách chuyển ý.";
                String comment_en = "The sentences are fluent and natural, with clear and coherent logic, though some transitions could be optimized for better flow.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 90 && score <= 100) {
                String comment = "语句如行云流水，逻辑严密自然，展现了出色的写作功底！";
                String comment_vi = "Câu văn mượt mà như nước chảy mây trôi, logic chặt chẽ tự nhiên, thể hiện kỹ năng viết xuất sắc!";
                String comment_en = "The sentences flow smoothly like flowing water, with tight logic and natural coherence, showcasing excellent writing skills!";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }
        }
        // 语法准确性与深广度维度
        else if (Objects.equals(dimensionType, DimensionTypeEnum.GRAMMAR_ACCURACY_AND_BROADNESS.getCode())) {
            if (score >= 0 && score <= 19) {
                String comment = "语法错误明显，严重影响表达效果，建议加强基础语法练习。";
                String comment_vi = "Lỗi ngữ pháp rõ ràng, ảnh hưởng nghiêm trọng đến hiệu quả diễn đạt, đề nghị tăng cường luyện tập ngữ pháp cơ bản";
                String comment_en = "The grammar errors are obvious and severely affect the expression. It is recommended to strengthen basic grammar practice.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 20 && score <= 39) {
                String comment = "语法错误明显，句子结构混乱，建议先练习完整句子的表达。";
                String comment_vi = "Lỗi ngữ pháp rõ ràng, cấu trúc câu hỗn loạn, đề nghị luyện tập cách diễn đạt câu hoàn chỉnh.";
                String comment_en = "The grammar is clearly incorrect and the sentence structure is chaotic. It's recommended to practice expressing complete sentences first.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 40 && score <= 59) {
                String comment = "语法错误明显，建议多练习句式和语法。";
                String comment_vi = "Lỗi ngữ pháp rõ ràng, đề nghị luyện tập thêm mẫu câu và ngữ pháp.";
                String comment_en = "There are obvious grammatical errors; more practice with sentence structures and grammar is recommended.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 60 && score <= 69) {
                String comment = "语法错误明显，建议多练习句式的运用。";
                String comment_vi = "Lỗi ngữ pháp rõ ràng, đề nghị luyện tập thêm cách sử dụng mẫu câu.";
                String comment_en = "There are obvious grammatical errors; more practice with sentence structures is recommended.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 70 && score <= 79) {
                String comment = "语法结构需更严谨，尝试运用更丰富的句式来表达。";
                String comment_vi = "Cấu trúc ngữ pháp cần chặt chẽ hơn, thử áp dụng mẫu câu phong phú hơn để diễn đạt.";
                String comment_en = "The grammatical structure needs to be more rigorous; try using a richer variety of sentence structures to express ideas.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 80 && score <= 89) {
                String comment = "语法运用正确，个别细节需稍加调整，整体表达流畅有力！";
                String comment_vi = "Ngữ pháp sử dụng chính xác, vài chi tiết cần điều chỉnh, tổng thể diễn đạt trôi chảy mạnh mẽ!";
                String comment_en = "Grammatically sound with minor refinements needed—overall a fluent and forceful expression.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 90 && score <= 100) {
                String comment = "语法精准，句式丰富凝练，展现出优秀的语言驾驭能力！";
                String comment_vi = "Ngữ pháp chính xác, mẫu câu phong phú, thể hiện khả năng điều khiển ngôn ngữ xuất sắc!";
                String comment_en = "With the accurate grammar, rich and concise sentence structures, it demonstrats excellent language mastery!";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }
        }
        // 错别字及词汇使用维度
        else if (Objects.equals(dimensionType, DimensionTypeEnum.WRONG_WORD_AND_WORD_USE.getCode())) {
            if (score >= 0 && score <= 19) {
                String comment = "错别字明显且词汇使用不当，需仔细检查并准确理解原文。";
                String comment_vi = "Lỗi chính tả rõ ràng và từ vựng dùng không đúng, cần kiểm tra kỹ và hiểu đúng nguyên văn.";
                String comment_en = "Obvious typos and inappropriate word usage require careful review and accurate understanding of the original text.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 20 && score <= 39) {
                String comment = "错别字明显，词汇使用有些不合适，需加强基础和用词准确性。";
                String comment_vi = "Lỗi chính tả rõ ràng, từ vựng sử dụng chưa phù hợp, cần tăng cường kiến thức cơ bản và độ chính xác trong dùng từ.";
                String comment_en = "There are obvious typos, and some word are inappropriate. It's necessary to strengthen the foundation and improve accuracy in the passage.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 40 && score <= 59) {
                String comment = "错别字明显，关键词语使用不当，需再次检查词汇。";
                String comment_vi = "Lỗi chính tả rõ ràng, từ khóa dùng chưa đúng, cần kiểm tra lại từ vựng.";
                String comment_en = "Noticeable typographical errors and improper use of key terminology require thorough vocabulary verification.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 60 && score <= 69) {
                String comment = "存在错别字，关键表述需更准确，建议再次核对词汇与检查细节。";
                String comment_vi = "Có lỗi chính tả, diễn đạt nội dung chính cần chính xác hơn, đề nghị kiểm tra lại từ vựng và chi tiết.";
                String comment_en = "There are noticeable typographical errors and inaccuracies in key expressions. A thorough review of terminology and details is strongly recommended.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 70 && score <= 79) {
                String comment = "词语需准确使用，搭配应得当，注意检查细节。";
                String comment_vi = "Từ ngữ cần sử dụng chính xác, phối hợp phải phù hợp, chú ý kiểm tra chi tiết.";
                String comment_en = "Terminology must be employed with precision, collocations should be appropriately structured, and meticulous attention to detail is required.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 80 && score <= 89) {
                String comment = "词汇使用较准确，个别错别字需注意，表达可更丰富些。";
                String comment_vi = "Từ vựng sử dụng khá chính xác, có xuất hiện vài lỗi chính tả, diễn đạt có thể phong phú thêm.";
                String comment_en = "Your word choices are mostly accurate - just watch for a few spelling errors. Try incorporating more varied expressions to enrich your writing.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score >= 90 && score <= 100) {
                String comment = "表达精准，词汇运用娴熟，缩写功力出色！";
                String comment_vi = "Diễn đạt chính xác, từ vựng dùng thành thạo, khả năng tóm lược xuất sắc!";
                String comment_en = "Precise expression, masterful vocabulary usage, and excellent writing skills!";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }
        }
        // 篇幅维度
        else if (Objects.equals(dimensionType, DimensionTypeEnum.LENGTH.getCode())) {
            // 篇幅维度根据具体情况返回相应评语
            if (score >= 0 && score <= 19) { // 内容过于简略
                String comment = "内容过于简略，建议充实细节以增强表达效果。";
                String comment_vi = "Nội dung quá đơn giản, nên bổ sung chi tiết để tăng hiệu quả diễn đạt.";
                String comment_en = "The content is too brief; it is recommended to enrich the details to enhance the expression.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score <= 39) { // 内容简洁但略显单薄
                String comment = "内容简洁但略显单薄，建议适当展开细节丰富表达。";
                String comment_vi = "Nội dung ngắn gọn nhưng hơi đơn giản, nên khai thêm chi tiết để làm phong phú cách diễn đạt.";
                String comment_en = "The content is concise but somewhat thin; it is recommended to expand on details to enrich the expression.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score <= 59) { // 篇幅把握精准
                String comment = "篇幅把握精准，内容紧凑得当，继续保持！";
                String comment_vi = "Độ dài được kiểm soát tốt, nội dung chặt chẽ hợp lý, tiếp tục phát huy!";
                String comment_en = "The length is well-controlled, the content is concise and appropriate—keep it up!";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score <= 79) { // 篇幅略长
                String comment = "篇幅略长，建议精简内容，突出重点会更出彩。";
                String comment_vi = "Độ dài hơi dài, đề nghị tinh giản nội dung, làm nổi bật trọng điểm sẽ nổi bật hơn.";
                String comment_en = "The content is a bit lengthy; it would be more impactful to streamline and highlight the key points.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            } else if (score <= 100) { // 篇幅偏长
                String comment = "内容表达完整但篇幅偏长，建议精简语句突出重点。";
                String comment_vi = "Nội dung trình bày đầy đủ nhưng độ dài hơi dài, nên tinh gọn câu văn để làm nổi bật ý chính.";
                String comment_en = "The content is complete but somewhat lengthy; it's recommended to streamline the sentences and highlight the key points.";
                return LanguageUtils.getLocalizedValue(comment, comment_en, comment_vi);
            }
        }

        // 默认返回空字符串（如果没有匹配的维度类型或分数范围）
        return "";
    }

    /**
     * @param content 需要发送的内容
     *                //     * @param questionTypeId 题型id
     * @param userId  用户id
     *                //     * @param dimensionType  评分维度类型：1-参考词使用 2-内容相关性 3-错别字及词汇使用 4-语法准确性与深广度 5-语句与逻辑连贯性 6-篇幅 7-整体评语 8-文本润色
     * @return
     */
    public String callAiCorrection(String content, Long userId, String botId) {
        try {

//            String token = getAccessToken();
            JWTOAuthClient oauth =
                    new JWTOAuthClient.JWTOAuthBuilder()
                            .clientID(cozeConfig.getIss())
                            .privateKey(cozeConfig.getPrivateKey())
                            .publicKey(cozeConfig.getKid())
                            .baseURL(BASE_URL)
                            .build();
            if (botId == null || botId.isEmpty()) {
                throw new ServerException(500, "未正确配置bot");
            }

            // Init the Coze client through the access_token.
            CozeAPI coze = new CozeAPI.Builder()
                    .baseURL(BASE_URL)
                    .auth(JWTOAuth.builder().jwtClient(oauth).build())
//                    .readTimeout(10000)
                    .build();

            CreateChatReq req = CreateChatReq.builder()
                    .botID(botId)
                    .userID(String.valueOf(userId))
                    .messages(Collections.singletonList(Message.buildUserQuestionText(content)))
                    .build();

            return getContent(coze, req);
        } catch (Exception e) {
            log.error("系统异常 扣子评分失败 botid：{} 参数{} 失败原因：{}", botId, content,
                    e.getMessage(), e);
            return null;
        }
    }

    private void errorLog(String questionStem, String answerData, int content) {
        log.error("系统异常，扣子评分失败，参数，题目：{}，回答：{}，失败原因：分数不在预期，分数{}", questionStem, answerData, content);
    }

    /**
     * 获取访问令牌 通过生成JWT 生成访问令牌 返回给客户端 客户端根据token调用扣子对话api
     *
     * @return {@code String }
     * @throws NoSuchAlgorithmException 没有这样算法例外
     * @throws InvalidKeySpecException  无效密钥规范异常
     * @throws SignatureException       签名异常
     * @throws InvalidKeyException      无效密钥异常
     * @throws MalformedURLException    格式错误 URLException
     */
    public String getAccessToken() {

        try {
            String redisToken = redisUtil.getString(COZE_TOKEN);
            if (!StrUtil.isEmpty(redisToken)) {
                return redisToken;
            }

            Map<String, Object> bodyMap = new HashMap<>(2);

            // 15 分钟后过期
            long expMillis = 60 * 15 * 1000;
            bodyMap.put("duration_seconds", expMillis);
            bodyMap.put("grant_type", GRANT_TYPE);
            URL url = new URL(BASE_URL + AUTHORIZATION_ENDPOINT);
            HttpRequest post = HttpRequest.post(url.toString())
                    .header("Authorization", TOKEN_PREFIX + generateJWT())
                    .body(JSON.toJSONString(bodyMap), "application/json");

            HttpResponse execute = post.execute();
            ResponseData responseData = parseResponse(execute.body());

            log.info("获取accessToken,请求参数:{},响应结果:{}", JSONObject.toJSONString(bodyMap),
                    JSONObject.toJSONString(responseData));

            if (responseData == null) {
                return null;
            }

            // 异常状态码不为空时就是扣子返回异常
            if (org.apache.commons.lang3.StringUtils.isNotBlank(responseData.getErrorCode())) {
                log.error("获取accessToken失败,错误码:{},错误信息:{}", responseData.getErrorCode(),
                        responseData.getErrorMessage());
                throw new ServerException(500, "获取accessToken失败:" + responseData.getErrorMessage());
            }
            String token = responseData.accessToken;

            redisUtil.set(COZE_TOKEN, token, Duration.ofMillis(expMillis));
            return token;
        } catch (NoSuchAlgorithmException e) {
            log.error("没有这样算法例外 {}", e.getMessage(), e);
            throw new ServiceException(500, "获取accessToken失败:" + e.getMessage());
        } catch (InvalidKeySpecException e) {
            log.error("无效密钥规范异常 {}", e.getMessage(), e);
            throw new ServiceException(500, "获取accessToken失败:" + e.getMessage());
        } catch (SignatureException e) {
            log.error("签名异常 {}", e.getMessage(), e);
            throw new ServiceException(500, "获取accessToken失败:" + e.getMessage());
        } catch (InvalidKeyException e) {
            log.error("无效密钥异常 {}", e.getMessage(), e);
            throw new ServiceException(500, "获取accessToken失败:" + e.getMessage());
        } catch (MalformedURLException e) {
            log.error("格式错误 URLException {}", e.getMessage(), e);
            throw new ServiceException(500, "获取accessToken失败:" + e.getMessage());
        } catch (Exception e) {
            log.error("系统异常 获取accessToken失败 错误信息：{}", e.getMessage(), e);
            throw new ServiceException(500, "获取accessToken失败:" + e.getMessage());
        }

    }

    /**
     * 生成 JWT
     *
     * @return {@code String } jwt字符串
     * @throws NoSuchAlgorithmException 没有这样算法例外
     * @throws InvalidKeySpecException  无效密钥规范异常
     * @throws InvalidKeyException      无效密钥异常
     * @throws SignatureException       签名异常
     */
    public String generateJWT()
            throws NoSuchAlgorithmException, InvalidKeySpecException, InvalidKeyException, SignatureException {

        // 设置Token的头部信息算法和Token的类型
        Map<String, String> headers = new HashMap<>();
        headers.put("alg", "RS256");
        headers.put("typ", "JWT");
        headers.put("kid", cozeConfig.getKid());

        String header = JSONObject.toJSONString(headers);
        // 设置 Payload 信息
        Map<String, Object> payloadMap = new HashMap<>();
        // OAuth 应用的 ID
        payloadMap.put("iss", cozeConfig.getIss());
        // 扣子 API 的 Endpoint
        payloadMap.put("aud", "api.coze.cn");
        // JWT 开始生效的时间，秒级时间戳
        DateUtil.currentSeconds();
        payloadMap.put("iat", DateUtil.currentSeconds());
        // JWT 过期时间，秒级时间戳
        payloadMap.put("exp", DateUtil.currentSeconds() + 86400);
        // 随机字符串，防止重放攻击
        payloadMap.put("jti", UUID.randomUUID().toString());

        String payload = JSONObject.toJSONString(payloadMap);

        byte[] headerBytes = header.getBytes();
        byte[] payloadBytes = payload.getBytes();

        String encodedHeader = Base64.getUrlEncoder().withoutPadding().encodeToString(headerBytes);
        String encodedPayload = Base64.getUrlEncoder().withoutPadding()
                .encodeToString(payloadBytes);

        String dataToSign = encodedHeader + "." + encodedPayload;

        byte[] signatureBytes = signData(dataToSign, cozeConfig.getPrivateKey());

        String encodedSignature = Base64.getUrlEncoder().withoutPadding()
                .encodeToString(signatureBytes);

        return encodedHeader + "." + encodedPayload + "." + encodedSignature;
    }

    enum QuestionBotEnum {

        // 缩写题
        QUESTION_BOT_1(QuestionTypeEnum.WRITING_SUMMARY.getCode(), DimensionTypeEnum.CONTENT_RELEVANCE.getCode(), "7525779850397909046", 100, 1),
        QUESTION_BOT_2(QuestionTypeEnum.WRITING_SUMMARY.getCode(), DimensionTypeEnum.WRONG_WORD_AND_WORD_USE.getCode(), "7526604697097535526", 100, 1),
        QUESTION_BOT_3(QuestionTypeEnum.WRITING_SUMMARY.getCode(), DimensionTypeEnum.GRAMMAR_ACCURACY_AND_BROADNESS.getCode(), "7526613020769124367", 100, 1),
        QUESTION_BOT_4(QuestionTypeEnum.WRITING_SUMMARY.getCode(), DimensionTypeEnum.STATEMENT_AND_LOGIC_CONSISTENCY.getCode(), "7526612731773468713", 100, 1),
        QUESTION_BOT_5(QuestionTypeEnum.WRITING_SUMMARY.getCode(), DimensionTypeEnum.LENGTH.getCode(), "", 100, 1),
        QUESTION_BOT_6(QuestionTypeEnum.WRITING_SUMMARY.getCode(), DimensionTypeEnum.OVERALL_COMMENT.getCode(), "7527150307441426471", null, 1),
        QUESTION_BOT_7(QuestionTypeEnum.WRITING_SUMMARY.getCode(), DimensionTypeEnum.TEXT_RUN_CORRECTION.getCode(), "7525765509762023467", null, 1),
        QUESTION_BOT_8(QuestionTypeEnum.WRITING_SUMMARY.getCode(), DimensionTypeEnum.OVERALL_COMMENT_EN.getCode(), "7527242444044894251", null, 1),
        QUESTION_BOT_9(QuestionTypeEnum.WRITING_SUMMARY.getCode(), DimensionTypeEnum.OVERALL_COMMENT_CN.getCode(), "7527260931551051810", null, 1),

        //短文写作题 给定短语写短文
        QUESTION_BOT_10(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.REFERENCE_WORD.getCode(), "", 30, 1),
        //        QUESTION_BOT_11(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.CONTENT_RELEVANCE.getCode(), "7525779850397909046", 30, 1),
        QUESTION_BOT_12(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.WRONG_WORD_AND_WORD_USE.getCode(), "7527561501415604233", 30, 1),
        QUESTION_BOT_13(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.GRAMMAR_ACCURACY_AND_BROADNESS.getCode(), "7527561788058386473", 30, 1),
        QUESTION_BOT_14(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.STATEMENT_AND_LOGIC_CONSISTENCY.getCode(), "7527562568064729114", 30, 1),
        QUESTION_BOT_15(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.LENGTH.getCode(), "", 30, 1),
        QUESTION_BOT_16(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.OVERALL_COMMENT.getCode(), "7527554530221899818", null, 1),
        QUESTION_BOT_17(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.TEXT_RUN_CORRECTION.getCode(), "7527560294051725358", null, 1),
        QUESTION_BOT_18(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.OVERALL_COMMENT_EN.getCode(), "7527554560802799635", null, 1),
        QUESTION_BOT_19(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.OVERALL_COMMENT_CN.getCode(), "7527560760457920552", null, 1),

        //短文写作题 给定图片写短文 - 第二套 (botIndex = 2)
//        QUESTION_BOT_30(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.REFERENCE_WORD.getCode(), "", 30, 2),
        QUESTION_BOT_31(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.CONTENT_RELEVANCE.getCode(), "7535394771649495092", 30, 2),
        QUESTION_BOT_32(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.WRONG_WORD_AND_WORD_USE.getCode(), "7535394066197168171", 30, 2),
        QUESTION_BOT_33(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.GRAMMAR_ACCURACY_AND_BROADNESS.getCode(), "7535393772012961811", 30, 2),
        QUESTION_BOT_34(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.STATEMENT_AND_LOGIC_CONSISTENCY.getCode(), "7535394256102604827", 30, 2),
        QUESTION_BOT_35(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.LENGTH.getCode(), "", 30, 2),
        QUESTION_BOT_36(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.OVERALL_COMMENT.getCode(), "7535393540797759542", null, 2),
        QUESTION_BOT_37(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.TEXT_RUN_CORRECTION.getCode(), "7535394949634801690", null, 2),
        QUESTION_BOT_38(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.OVERALL_COMMENT_EN.getCode(), "7535394387153387571", null, 2),
        QUESTION_BOT_39(QuestionTypeEnum.WRITING_ESSAY.getCode(), DimensionTypeEnum.OVERALL_COMMENT_CN.getCode(), "7535393787032895538", null, 2),

        //看图造句题
        QUESTION_BOT_20(QuestionTypeEnum.WRITING_PICTURE_SENTENCE.getCode(), DimensionTypeEnum.REFERENCE_WORD.getCode(), "", 8, 1),
        QUESTION_BOT_21(QuestionTypeEnum.WRITING_PICTURE_SENTENCE.getCode(), DimensionTypeEnum.CONTENT_RELEVANCE.getCode(), "7530843428201512970", 8, 1),
        QUESTION_BOT_22(QuestionTypeEnum.WRITING_PICTURE_SENTENCE.getCode(), DimensionTypeEnum.WRONG_WORD_AND_WORD_USE.getCode(), "7530842041350783015", 8, 1),
        QUESTION_BOT_23(QuestionTypeEnum.WRITING_PICTURE_SENTENCE.getCode(), DimensionTypeEnum.GRAMMAR_ACCURACY_AND_BROADNESS.getCode(), "7530841390508097545", 8, 1),
        QUESTION_BOT_26(QuestionTypeEnum.WRITING_PICTURE_SENTENCE.getCode(), DimensionTypeEnum.OVERALL_COMMENT.getCode(), "7530842112523780115", null, 1),
        QUESTION_BOT_27(QuestionTypeEnum.WRITING_PICTURE_SENTENCE.getCode(), DimensionTypeEnum.TEXT_RUN_CORRECTION.getCode(), "7530842546357059599", null, 1),
        QUESTION_BOT_28(QuestionTypeEnum.WRITING_PICTURE_SENTENCE.getCode(), DimensionTypeEnum.OVERALL_COMMENT_EN.getCode(), "7530842757263573044", null, 1),
        QUESTION_BOT_29(QuestionTypeEnum.WRITING_PICTURE_SENTENCE.getCode(), DimensionTypeEnum.OVERALL_COMMENT_CN.getCode(), "7530841239139762176", null, 1),
        ;

        private Long questionType;
        /**
         * 评分维度类型：1-参考词使用 2-内容相关性 3-错别字及词汇使用 4-语法准确性与深广度
         * 5-语句与逻辑连贯性 6-篇幅 7-整体评语-越南语 8-文本润色 9-整体评语-英语 10-整体评语-中文
         */
        private Integer dimensionType;
        private String botId;
        private Integer totalScore;

        /**
         * 同一个题型有多套bot
         */
        private Integer botIndex;

        QuestionBotEnum(Long questionType, Integer dimensionType, String botId, Integer totalScore, Integer botIndex) {
            this.questionType = questionType;
            this.dimensionType = dimensionType;
            this.botId = botId;
            this.totalScore = totalScore;
            this.botIndex = botIndex;
        }

        public static QuestionBotEnum getQuestionBotEnum(Long questionType, Integer dimensionType) {
            for (QuestionBotEnum value : QuestionBotEnum.values()) {
                if (value.questionType.equals(questionType) && value.dimensionType.equals(dimensionType)) {
                    return value;
                }
            }
            return null;
        }

        public static List<QuestionBotEnum> getQuestionBotEnumList(Long questionType, Integer botIndex) {
            List<QuestionBotEnum> list = new ArrayList<>();
            for (QuestionBotEnum value : QuestionBotEnum.values()) {
                if (value.questionType.equals(questionType) && value.botIndex.equals(botIndex)) {
                    list.add(value);
                }
            }
            return list;
        }

        public String getBotId() {
            return botId;
        }

        public Long getQuestionType() {
            return questionType;
        }

        public Integer getDimensionType() {
            return dimensionType;
        }

        public Integer getBotIndex() {
            return botIndex;
        }
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ResponseData {

        /**
         * 令牌
         */
        @JsonAlias("access_token")
        private String accessToken;
        /**
         * 过期时间
         */
        @JsonAlias("expires_in")
        private String expiresIn;
        /**
         * 令牌类型
         */
        @JsonAlias("token_type")
        private String tokenType;
        /**
         * 错误代码
         */
        @JsonAlias("error_code")
        private String errorCode;
        /**
         * 错误信息
         */
        @JsonAlias("error_message")
        private String errorMessage;
    }

    // 用于封装每个维度处理结果
    @Data
    private static class DimensionResult {
        boolean success;
        int score;
        int count;
        int dimensionType;

        public DimensionResult(boolean success, int score, int count, int dimensionType) {
            this.success = success;
            this.score = score;
            this.count = count;
            this.dimensionType = dimensionType;
        }
    }
}
