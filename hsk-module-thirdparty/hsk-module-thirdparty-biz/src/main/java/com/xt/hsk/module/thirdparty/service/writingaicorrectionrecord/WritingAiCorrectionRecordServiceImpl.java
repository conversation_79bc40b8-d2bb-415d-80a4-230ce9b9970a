package com.xt.hsk.module.thirdparty.service.writingaicorrectionrecord;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.thirdparty.api.WriteAiCorrection.WritingAiCorrectionRecordApi;
import com.xt.hsk.module.thirdparty.controller.admin.writingaicorrectionrecord.vo.WritingAiCorrectionRecordPageReqVO;
import com.xt.hsk.module.thirdparty.dal.dataobject.writingaicorrectiondetail.WritingAiCorrectionDetailDO;
import com.xt.hsk.module.thirdparty.dto.coze.WritingAiCorrectionDetailRespVO;
import com.xt.hsk.module.thirdparty.dto.coze.WritingAiCorrectionRecordRespVO;
import com.xt.hsk.module.thirdparty.service.writingaicorrectiondetail.WritingAiCorrectionDetailService;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.validation.Valid;


import com.xt.hsk.module.thirdparty.dal.dataobject.writingaicorrectionrecord.WritingAiCorrectionRecordDO;

import com.xt.hsk.module.thirdparty.dal.mysql.writingaicorrectionrecord.WritingAiCorrectionRecordMapper;

import java.util.Date;
import java.util.List;


/**
 * 写作AI批改记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WritingAiCorrectionRecordServiceImpl extends ServiceImpl<WritingAiCorrectionRecordMapper, WritingAiCorrectionRecordDO> implements WritingAiCorrectionRecordService, WritingAiCorrectionRecordApi {

    @Resource
    private WritingAiCorrectionRecordMapper writingAiCorrectionRecordMapper;
    @Resource
    private WritingAiCorrectionDetailService writingAiCorrectionDetailService;

    @Override
    public PageResult<WritingAiCorrectionRecordDO> selectPage(WritingAiCorrectionRecordPageReqVO pageReqVO) {

        return writingAiCorrectionRecordMapper.selectPage(pageReqVO);
    }

    @Override
    public Integer getUserWritingAiCorrectionCount(long userId, Date dateBegin, Date dateEnd) {

        return writingAiCorrectionRecordMapper.getUserWritingAiCorrectionCount(userId, dateBegin, dateEnd);
    }

    @Override
    public WritingAiCorrectionRecordRespVO getById(Long id) {
        WritingAiCorrectionRecordDO recordDO = writingAiCorrectionRecordMapper.selectById(id);
        return BeanUtils.toBean(recordDO, WritingAiCorrectionRecordRespVO.class);
//        return null;
    }

    @Override
    public List<WritingAiCorrectionDetailRespVO> getByRecordId(Long recordId) {
        LambdaQueryWrapper<WritingAiCorrectionDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WritingAiCorrectionDetailDO::getRecordId, recordId);
        List<WritingAiCorrectionDetailDO> writingAiCorrectionDetailDOList = writingAiCorrectionDetailService.list(queryWrapper);
        return BeanUtils.toBean(writingAiCorrectionDetailDOList, WritingAiCorrectionDetailRespVO.class);

    }
}