package com.xt.hsk.module.thirdparty.manage.aicorrection;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.thirdparty.controller.admin.writingaicorrectionrecord.vo.WritingAiCorrectionRecordPageReqVO;
import com.xt.hsk.module.thirdparty.controller.admin.writingaicorrectionrecord.vo.WritingAiCorrectionRecordSaveReqVO;
import com.xt.hsk.module.thirdparty.dal.dataobject.writingaicorrectionrecord.WritingAiCorrectionRecordDO;
import com.xt.hsk.module.thirdparty.service.writingaicorrectionrecord.WritingAiCorrectionRecordService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.DATA_NOT_EXIST;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;


/**
 * 写作AI批改记录 Manager
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WritingAiCorrectionRecordManager {

    @Resource
    private WritingAiCorrectionRecordService writingAiCorrectionRecordService;


    public Long createWritingAiCorrectionRecord(WritingAiCorrectionRecordSaveReqVO createReqVO) {
        // 插入
        WritingAiCorrectionRecordDO writingAiCorrectionRecord = BeanUtils.toBean(createReqVO, WritingAiCorrectionRecordDO.class);
        writingAiCorrectionRecordService.save(writingAiCorrectionRecord);

        // 返回
        return writingAiCorrectionRecord.getId();
    }


    public void updateWritingAiCorrectionRecord(WritingAiCorrectionRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateWritingAiCorrectionRecordExists(updateReqVO.getId());
        // 更新
        WritingAiCorrectionRecordDO updateObj = BeanUtils.toBean(updateReqVO, WritingAiCorrectionRecordDO.class);
        writingAiCorrectionRecordService.updateById(updateObj);
    }


    public void deleteWritingAiCorrectionRecord(Long id) {
        // 校验存在
        validateWritingAiCorrectionRecordExists(id);
        // 删除
        writingAiCorrectionRecordService.removeById(id);
    }

    private void validateWritingAiCorrectionRecordExists(Long id) {
        if (writingAiCorrectionRecordService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }


    public WritingAiCorrectionRecordDO getWritingAiCorrectionRecord(Long id) {
        return writingAiCorrectionRecordService.getById(id);
    }

    public PageResult<WritingAiCorrectionRecordDO> getWritingAiCorrectionRecordPage(@Valid WritingAiCorrectionRecordPageReqVO pageReqVO) {
        return writingAiCorrectionRecordService.selectPage(pageReqVO);
    }

}