package com.xt.hsk.module.thirdparty.service.writingaicorrectionrecord;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.thirdparty.controller.admin.writingaicorrectionrecord.vo.WritingAiCorrectionRecordPageReqVO;
import com.xt.hsk.module.thirdparty.dal.dataobject.writingaicorrectionrecord.WritingAiCorrectionRecordDO;
import com.xt.hsk.framework.common.pojo.PageResult;
import jakarta.validation.Valid;

/**
 * 写作AI批改记录 Service 接口
 *
 * <AUTHOR>
 */
public interface WritingAiCorrectionRecordService extends IService<WritingAiCorrectionRecordDO> {
    PageResult<WritingAiCorrectionRecordDO> selectPage(@Valid WritingAiCorrectionRecordPageReqVO pageReqVO);

}