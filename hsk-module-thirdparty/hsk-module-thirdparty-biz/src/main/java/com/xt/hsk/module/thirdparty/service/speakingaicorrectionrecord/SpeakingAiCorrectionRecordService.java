package com.xt.hsk.module.thirdparty.service.speakingaicorrectionrecord;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.thirdparty.controller.admin.speakingaicorrectionrecord.vo.SpeakingAiCorrectionRecordPageReqVO;
import com.xt.hsk.module.thirdparty.dal.dataobject.speakingaicorrectionrecord.SpeakingAiCorrectionRecordDO;
import com.xt.hsk.framework.common.pojo.PageResult;
import jakarta.validation.Valid;

/**
 * 口语ai批改记录 Service 接口
 *
 * <AUTHOR>
 */
public interface SpeakingAiCorrectionRecordService extends IService<SpeakingAiCorrectionRecordDO> {
    PageResult<SpeakingAiCorrectionRecordDO> selectPage(@Valid SpeakingAiCorrectionRecordPageReqVO pageReqVO);

}