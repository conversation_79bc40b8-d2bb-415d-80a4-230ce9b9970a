package com.xt.hsk.module.thirdparty.controller.admin.writingaicorrectionrecord.vo;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class WritingAiCorrectionRecordSaveReqVO {

    /**
     * AI批改记录表ID
     */
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 习题ID
     */
    @NotNull(message = "习题ID不能为空")
    private Long questionId;

    /**
     * 习题详情ID
     */
    private Long questionDetailId;

    /**
     * 题型ID
     */
    @NotNull(message = "题型ID不能为空")
    private Integer questionTypeId;

    /**
     * 题目版本
     */
    @NotNull(message = "题目版本不能为空")
    private Integer questionVersion;

    /**
     * 发送给Coze的题目
     */
    @NotEmpty(message = "发送给Coze的题目不能为空")
    private String sendContent;

    /**
     * 语言类型：zh-Hans-中文 en-英文 vi-越南语
     */
    @NotNull(message = "语言类型：zh-Hans-中文 en-英文 vi-越南语")
    private String language;

    /**
     * 作答记录ID
     */
    @NotNull(message = "作答记录ID不能为空")
    private Long recordId;

    /**
     * 批改类型：1-作文批改 2-文本润色
     */
    @NotNull(message = "批改类型：1-作文批改 2-文本润色不能为空")
    private Integer correctionType;

    /**
     * 总分（各分项汇总后存储）
     */
    private Integer totalScore;

    /**
     * 错误信息（失败时记录）
     */
    private String errorMessage;

}