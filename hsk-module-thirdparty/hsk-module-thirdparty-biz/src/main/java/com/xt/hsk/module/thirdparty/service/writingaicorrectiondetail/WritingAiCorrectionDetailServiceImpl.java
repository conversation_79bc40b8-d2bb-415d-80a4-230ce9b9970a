package com.xt.hsk.module.thirdparty.service.writingaicorrectiondetail;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.thirdparty.controller.admin.writingaicorrectiondetail.vo.WritingAiCorrectionDetailPageReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.validation.Valid;


import com.xt.hsk.module.thirdparty.dal.dataobject.writingaicorrectiondetail.WritingAiCorrectionDetailDO;

import com.xt.hsk.module.thirdparty.dal.mysql.writingaicorrectiondetail.WritingAiCorrectionDetailMapper;


/**
 * 写作AI批改结果 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WritingAiCorrectionDetailServiceImpl extends ServiceImpl<WritingAiCorrectionDetailMapper, WritingAiCorrectionDetailDO> implements WritingAiCorrectionDetailService {

    @Resource
    private WritingAiCorrectionDetailMapper writingAiCorrectionDetailMapper;

    @Override
    public PageResult<WritingAiCorrectionDetailDO> selectPage(WritingAiCorrectionDetailPageReqVO pageReqVO) {

        return writingAiCorrectionDetailMapper.selectPage(pageReqVO);
    }

}