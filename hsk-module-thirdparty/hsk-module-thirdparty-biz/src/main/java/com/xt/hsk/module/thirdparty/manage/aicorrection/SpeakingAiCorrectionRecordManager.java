package com.xt.hsk.module.thirdparty.manage.aicorrection;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.thirdparty.controller.admin.speakingaicorrectionrecord.vo.SpeakingAiCorrectionRecordPageReqVO;
import com.xt.hsk.module.thirdparty.controller.admin.speakingaicorrectionrecord.vo.SpeakingAiCorrectionRecordSaveReqVO;
import com.xt.hsk.module.thirdparty.dal.dataobject.speakingaicorrectionrecord.SpeakingAiCorrectionRecordDO;
import com.xt.hsk.module.thirdparty.service.speakingaicorrectionrecord.SpeakingAiCorrectionRecordService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.DATA_NOT_EXIST;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;


/**
 * 口语ai批改记录 Manager
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SpeakingAiCorrectionRecordManager {

    @Resource
    private SpeakingAiCorrectionRecordService speakingAiCorrectionRecordService;


    public Long createSpeakingAiCorrectionRecord(SpeakingAiCorrectionRecordSaveReqVO createReqVO) {
        // 插入
        SpeakingAiCorrectionRecordDO speakingAiCorrectionRecord = BeanUtils.toBean(createReqVO, SpeakingAiCorrectionRecordDO.class);
        speakingAiCorrectionRecordService.save(speakingAiCorrectionRecord);

        // 返回
        return speakingAiCorrectionRecord.getId();
    }


    public void updateSpeakingAiCorrectionRecord(SpeakingAiCorrectionRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateSpeakingAiCorrectionRecordExists(updateReqVO.getId());
        // 更新
        SpeakingAiCorrectionRecordDO updateObj = BeanUtils.toBean(updateReqVO, SpeakingAiCorrectionRecordDO.class);
        speakingAiCorrectionRecordService.updateById(updateObj);
    }


    public void deleteSpeakingAiCorrectionRecord(Long id) {
        // 校验存在
        validateSpeakingAiCorrectionRecordExists(id);
        // 删除
        speakingAiCorrectionRecordService.removeById(id);
    }

    private void validateSpeakingAiCorrectionRecordExists(Long id) {
        if (speakingAiCorrectionRecordService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }


    public SpeakingAiCorrectionRecordDO getSpeakingAiCorrectionRecord(Long id) {
        return speakingAiCorrectionRecordService.getById(id);
    }

    public PageResult<SpeakingAiCorrectionRecordDO> getSpeakingAiCorrectionRecordPage(@Valid SpeakingAiCorrectionRecordPageReqVO pageReqVO) {
        return speakingAiCorrectionRecordService.selectPage(pageReqVO);
    }

}