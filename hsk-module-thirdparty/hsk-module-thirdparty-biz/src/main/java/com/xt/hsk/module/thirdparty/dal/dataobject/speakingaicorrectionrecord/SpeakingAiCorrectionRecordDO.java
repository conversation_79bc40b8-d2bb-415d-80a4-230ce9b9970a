package com.xt.hsk.module.thirdparty.dal.dataobject.speakingaicorrectionrecord;

import com.xt.hsk.framework.mybatis.core.dataobject.AppBaseDO;
import lombok.*;

import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;

/**
 * 口语ai批改记录 DO
 *
 * <AUTHOR>
 */
@TableName("t_speaking_ai_correction_record")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpeakingAiCorrectionRecordDO extends AppBaseDO {

    /**
     * 口语ai批改记录表id
     */
    @TableId
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 习题id
     */
    private Long questionId;
    /**
     * 题目版本
     */
    private Integer questionVersion;
    /**
     * 发送给第三方的题目
     */
    private String sendContent;
    /**
     * 作答记录id
     */
    private Long practiceRecordId;
    /**
     * 批改类型：1-音转字 2-口语评分
     */
    private Integer correctionType;
    /**
     * 参考文本
     */
    private String refText;
    /**
     * 第三方返回结果 json
     */
    private String result;
    /**
     * 第三方返回的记录id
     */
    private String recordId;
    /**
     * 总分
     */
    private Integer totalScore;
    /**
     * 错误id
     */
    private Integer errId;
    /**
     * 错误信息（失败时记录）
     */
    private String errorMessage;

}