package com.xt.hsk.module.thirdparty.service.aicorrectiontimes;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.thirdparty.controller.admin.aicorrectiontimes.vo.AiCorrectionTimesPageReqVO;
import com.xt.hsk.module.thirdparty.dal.dataobject.aicorrectiontimes.AiCorrectionTimesDO;
import jakarta.validation.Valid;

/**
 * ai批改次数记录 Service 接口
 *
 * <AUTHOR>
 */
public interface AiCorrectionTimesService extends IService<AiCorrectionTimesDO> {
    PageResult<AiCorrectionTimesDO> selectPage(@Valid AiCorrectionTimesPageReqVO pageReqVO);

}