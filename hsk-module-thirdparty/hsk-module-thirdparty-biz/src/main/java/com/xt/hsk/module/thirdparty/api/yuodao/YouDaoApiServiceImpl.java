package com.xt.hsk.module.thirdparty.api.yuodao;

import com.xt.hsk.module.thirdparty.api.YouDaoApi;
import com.xt.hsk.module.thirdparty.service.YouDaoService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 有道 API 服务 IMPL
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@Service
public class YouDaoApiServiceImpl implements YouDaoApi {

    @Resource
    private YouDaoService youDaoService;

    /**
     * 获取音频url
     *
     * @param text     文本
     * @param fileName 文件名
     * @return 音频url
     */
    @Override
    public String getAudioUrl(String text, String fileName) {
        return youDaoService.getAudioUrl(text, fileName);
    }

}