package com.xt.hsk.module.thirdparty.manage.youdao.admin;

import cn.hutool.core.util.IdUtil;
import com.xt.hsk.module.thirdparty.controller.admin.youdao.vo.YouDaoTextToSpeechReqVO;
import com.xt.hsk.module.thirdparty.controller.admin.youdao.vo.YouDaoTextToSpeechRespVO;
import com.xt.hsk.module.thirdparty.service.YouDaoService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 有道 后台 manager
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@Component
public class YouDaoAdminManager {

    @Resource
    private YouDaoService youDaoService;

    /**
     * 文字转语音
     */
    public YouDaoTextToSpeechRespVO textToSpeech(@Valid YouDaoTextToSpeechReqVO reqVO) {
        String audioUrl = youDaoService.getAudioUrl(reqVO.getText(), IdUtil.simpleUUID());
        YouDaoTextToSpeechRespVO respVO = new YouDaoTextToSpeechRespVO();
        respVO.setAudioUrl(audioUrl);
        return respVO;
    }
}