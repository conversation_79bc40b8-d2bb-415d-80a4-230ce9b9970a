package com.xt.hsk.module.thirdparty.service.aicorrectiontimes;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.exception.ServerException;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.framework.redis.utils.RedisUtil;
import com.xt.hsk.module.infra.api.config.ConfigApi;
import com.xt.hsk.module.thirdparty.api.aicorrectiontimes.AiCorrectionTimesApi;
import com.xt.hsk.module.thirdparty.controller.admin.aicorrectiontimes.vo.AiCorrectionTimesPageReqVO;
import com.xt.hsk.module.thirdparty.dal.dataobject.aicorrectiontimes.AiCorrectionTimesDO;
import com.xt.hsk.module.thirdparty.dal.mysql.aicorrectiontimes.AiCorrectionTimesMapper;
import com.xt.hsk.module.thirdparty.dto.aiCorrection.AiCorrectionTimesSaveReqDto;
import com.xt.hsk.module.thirdparty.enums.AiCorrectBizTypeEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.Duration;
import java.util.Date;

import static com.xt.hsk.framework.common.constants.RedisKeyPrefix.WRITING_AI_CORRECTION_COUNT;


/**
 * ai批改次数记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AiCorrectionTimesServiceImpl extends ServiceImpl<AiCorrectionTimesMapper, AiCorrectionTimesDO> implements AiCorrectionTimesService, AiCorrectionTimesApi {

    //    public static final String USER_AI_CORRECTION_COUNT_KEY = "ai:correction:question:max";
    @Resource
    private AiCorrectionTimesMapper aiCorrectionTimesMapper;
    //    @Resource
//    private ConfigApi configApi;
    @Resource
    private RedisUtil redisUtil;

    @Resource
    private ConfigApi configApi;

    @Override
    public PageResult<AiCorrectionTimesDO> selectPage(AiCorrectionTimesPageReqVO pageReqVO) {

        return aiCorrectionTimesMapper.selectPage(pageReqVO);
    }

    @Override
    public Long createAiCorrectionTimes(AiCorrectionTimesSaveReqDto createReqVO) {

        if (createReqVO == null) {
            throw new ServerException(500, "参数不能为空");
        }
        if (createReqVO.getUserId() == null) {
            throw new ServerException(500, "用户ID不能为空");
        }
        if (createReqVO.getBizType() == null) {
            throw new ServerException(500, "业务类型不能为空");
        }
        if (createReqVO.getBizId() == null) {
            throw new ServerException(500, "业务ID不能为空");
        }
        if (createReqVO.getCallTime() == null) {
            throw new ServerException(500, "调用时间不能为空");
        }
        if (createReqVO.getCallCount() == null) {
            throw new ServerException(500, "调用次数不能为空");
        }

        AiCorrectionTimesDO timesDO = BeanUtils.toBean(createReqVO, AiCorrectionTimesDO.class);
        this.save(timesDO);
        if (timesDO.getBizType() == 1) {
            String key = String.format(WRITING_AI_CORRECTION_COUNT, DateUtil.format(timesDO.getCallTime(), "yyyyMMdd"), timesDO.getUserId());
            redisUtil.increment(key, timesDO.getCallCount());
        }
        return timesDO.getId();
    }

    @Override
    public Integer getUserAiCorrectionTimes(Long userId, Date date, Integer bizType, String useCountKey) {
        // 查询用户剩余次数
        String key = String.format(useCountKey, DateUtil.format(date, "yyyyMMdd"), userId);
        if (Boolean.TRUE.equals(redisUtil.exists(key))) {
            return redisUtil.getInteger(key);
        } else {
            DateTime beginOfDay = DateUtil.beginOfDay(date);
            DateTime endOfDay = DateUtil.endOfDay(date);
            Integer count = aiCorrectionTimesMapper.getUserAiCorrectionTimes(userId, beginOfDay, endOfDay, bizType);
            if (count == null) {
                count = 0;
            }
            redisUtil.set(key, count, Duration.ofDays(1));
            return count;
        }
    }

    @Override
    public void returnAiCorrectionTimes(Long id) {
        // 查询调用的记录
        AiCorrectionTimesDO timesDO = aiCorrectionTimesMapper.selectById(id);
        if (timesDO == null) {
            throw new ServerException(500, "调用记录不存在");
        }
        if (timesDO.getBizType() == 2) { // 模考不需要返还次数
            return;
        }

        if (timesDO.getBizType() == 1) {//真题返还
            // 查询返回的记录
            LambdaQueryWrapper<AiCorrectionTimesDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AiCorrectionTimesDO::getOriginId, id);
            long count = this.count(wrapper);
            if (count > 0) {
                throw new ServerException(500, "该记录已返还次数，不做处理");
            }
            timesDO.setCallCount(timesDO.getCallCount() * -1);
            timesDO.setOriginId(id);
            timesDO.setId(null);
            this.save(timesDO);


            String key = String.format(WRITING_AI_CORRECTION_COUNT, DateUtil.format(timesDO.getCallTime(), "yyyyMMdd"), timesDO.getUserId());
            redisUtil.increment(key, timesDO.getCallCount());
        }
    }

    /**
     * 获取用户剩余批改次数
     *
     * @param userId               用户ID
     * @param configKey            配置额度KEY
     * @param useCountKey          使用的次数KEY
     * @param date                 日期
     * @param aiCorrectBizTypeEnum AI批改业务类型
     * @return 剩余批改次数
     */
    @Override
    public int getUserRemainingCount(Long userId, String configKey, String useCountKey, Date date, AiCorrectBizTypeEnum aiCorrectBizTypeEnum) {
        String configValue = configApi.getConfigValueByKey(configKey);
        int maxCount = CharSequenceUtil.isBlank(configValue) ? 0 : Integer.parseInt(configValue);
        Integer useCount = this.getUserAiCorrectionTimes(userId, date, aiCorrectBizTypeEnum.getCode(), useCountKey);

        int remainingCount = maxCount - useCount;

        return Math.max(remainingCount, 0);
    }
}