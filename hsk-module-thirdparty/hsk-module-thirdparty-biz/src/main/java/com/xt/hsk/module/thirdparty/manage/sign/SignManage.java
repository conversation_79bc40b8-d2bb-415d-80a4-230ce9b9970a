package com.xt.hsk.module.thirdparty.manage.sign;

import cn.hutool.crypto.SecureUtil;
import com.xt.hsk.module.thirdparty.config.xunfei.XunFeiConfig;
import com.xt.hsk.module.thirdparty.config.youdao.ocr.YouDaoOCRHandConfig;
import com.xt.hsk.module.thirdparty.config.youdao.tts.YouDaoTTSConfig;
import com.xt.hsk.module.thirdparty.controller.app.sign.vo.SignReqVo;
import com.xt.hsk.module.thirdparty.controller.app.sign.vo.SignVo;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.springframework.stereotype.Service;

@Service
public class SignManage {
    @Resource
    private YouDaoOCRHandConfig youDaoOCRHandConfig;
    @Resource
    private YouDaoTTSConfig youDaoTTSConfig;
    @Resource
    private XunFeiConfig xunFeiConfig;

    /**
     * 获取签名
     *
     * @param reqVo
     * @return
     */
    public SignVo getSign(SignReqVo reqVo) {
        if (reqVo.getSignType() == 1) {
            // 1. 获取coze签名

        } else if (reqVo.getSignType() == 2) {
            // 2. 获取有道云手写签名
            return getYouDaoOCRHandSign(reqVo);
        } else if (reqVo.getSignType() == 3) {
            // 3. 有道tts(字转音)
            return getYouDaoTTS(reqVo);
        }

        return null;
    }

    private SignVo getYouDaoTTS(SignReqVo reqVo) {
        String appSecret = youDaoTTSConfig.getAppSecret();
        String appKey = youDaoTTSConfig.getAppKey();
        String input = reqVo.getInput();
        String salt = UUID.randomUUID().toString();
        String curtime = String.valueOf(System.currentTimeMillis() / 1000);
        String sign = SecureUtil.sha256().digestHex(appKey + input + salt + curtime + appSecret);


        Map<String, String> extra = new HashMap<>();
        extra.put("voiceName", youDaoTTSConfig.getVoiceName());
        return new SignVo()
                .setSign(sign)
                .setAppKey(appKey)
                .setSalt(salt)
                .setCurtime(curtime)
                .setInput(input)
                .setExtra(extra);
    }

    private SignVo getYouDaoOCRHandSign(SignReqVo reqVo) {
        String appSecret = youDaoOCRHandConfig.getAppSecret();
        String appKey = youDaoOCRHandConfig.getAppKey();
        String input = reqVo.getInput();
        String salt = UUID.randomUUID().toString();
        String curtime = String.valueOf(System.currentTimeMillis() / 1000);
        String sign = SecureUtil.sha256().digestHex(appKey + input + salt + curtime + appSecret);

        return new SignVo()
                .setSign(sign)
                .setAppKey(appKey)
                .setSalt(salt)
                .setCurtime(curtime)
                .setInput(input);
    }


}
