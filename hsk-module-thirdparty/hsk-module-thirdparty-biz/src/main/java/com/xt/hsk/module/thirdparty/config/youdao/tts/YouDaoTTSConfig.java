package com.xt.hsk.module.thirdparty.config.youdao.tts;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "youdao.tts")
public class YouDaoTTSConfig {

    /**
     * 应用ID（对应YAML中的 app-id）
     */
    private String appKey;

    /**
     * 应用密钥（对应YAML中的 app-key）
     */
    private String appSecret;
    /**
     * 发言人名字
     */
    private String voiceName;
}
