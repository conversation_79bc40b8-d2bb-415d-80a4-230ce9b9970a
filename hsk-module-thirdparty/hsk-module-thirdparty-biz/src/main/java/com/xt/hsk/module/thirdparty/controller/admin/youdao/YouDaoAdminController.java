package com.xt.hsk.module.thirdparty.controller.admin.youdao;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.thirdparty.controller.admin.youdao.vo.YouDaoTextToSpeechReqVO;
import com.xt.hsk.module.thirdparty.controller.admin.youdao.vo.YouDaoTextToSpeechRespVO;
import com.xt.hsk.module.thirdparty.manage.youdao.admin.YouDaoAdminManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 有道 后台 控制器
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@RestController
@RequestMapping("/third-party/you-dao")
public class YouDaoAdminController {

    @Resource
    private YouDaoAdminManager youDaoAdminManager;

    /**
     * 文字转语音
     */
    @PostMapping("/text-to-speech")
    public CommonResult<YouDaoTextToSpeechRespVO> textToSpeech(@Valid @RequestBody YouDaoTextToSpeechReqVO reqVO) {
        return CommonResult.success(youDaoAdminManager.textToSpeech(reqVO));
    }

}
