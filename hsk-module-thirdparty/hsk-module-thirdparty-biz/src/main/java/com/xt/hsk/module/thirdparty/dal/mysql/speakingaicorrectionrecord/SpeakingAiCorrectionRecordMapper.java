package com.xt.hsk.module.thirdparty.dal.mysql.speakingaicorrectionrecord;

import java.util.*;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.module.thirdparty.dal.dataobject.speakingaicorrectionrecord.SpeakingAiCorrectionRecordDO;
import org.apache.ibatis.annotations.Mapper;
import com.xt.hsk.module.thirdparty.controller.admin.speakingaicorrectionrecord.vo.*;

/**
 * 口语ai批改记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SpeakingAiCorrectionRecordMapper extends BaseMapperX<SpeakingAiCorrectionRecordDO> {

    default PageResult<SpeakingAiCorrectionRecordDO> selectPage(SpeakingAiCorrectionRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SpeakingAiCorrectionRecordDO>()
                .eqIfPresent(SpeakingAiCorrectionRecordDO::getUserId, reqVO.getUserId())
                .eqIfPresent(SpeakingAiCorrectionRecordDO::getQuestionId, reqVO.getQuestionId())
                .eqIfPresent(SpeakingAiCorrectionRecordDO::getQuestionVersion, reqVO.getQuestionVersion())
                .eqIfPresent(SpeakingAiCorrectionRecordDO::getSendContent, reqVO.getSendContent())
                .eqIfPresent(SpeakingAiCorrectionRecordDO::getPracticeRecordId, reqVO.getPracticeRecordId())
                .eqIfPresent(SpeakingAiCorrectionRecordDO::getCorrectionType, reqVO.getCorrectionType())
                .eqIfPresent(SpeakingAiCorrectionRecordDO::getRefText, reqVO.getRefText())
                .eqIfPresent(SpeakingAiCorrectionRecordDO::getResult, reqVO.getResult())
                .eqIfPresent(SpeakingAiCorrectionRecordDO::getRecordId, reqVO.getRecordId())
                .eqIfPresent(SpeakingAiCorrectionRecordDO::getTotalScore, reqVO.getTotalScore())
                .eqIfPresent(SpeakingAiCorrectionRecordDO::getErrId, reqVO.getErrId())
                .eqIfPresent(SpeakingAiCorrectionRecordDO::getErrorMessage, reqVO.getErrorMessage())
                .betweenIfPresent(SpeakingAiCorrectionRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SpeakingAiCorrectionRecordDO::getId));
    }

}