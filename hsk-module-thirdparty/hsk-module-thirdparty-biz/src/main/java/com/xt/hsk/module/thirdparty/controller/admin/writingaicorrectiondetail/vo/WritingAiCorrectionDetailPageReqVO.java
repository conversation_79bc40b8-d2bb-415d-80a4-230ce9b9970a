package com.xt.hsk.module.thirdparty.controller.admin.writingaicorrectiondetail.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WritingAiCorrectionDetailPageReqVO extends PageParam {

    /**
     * AI批改记录ID（关联主表）
     */
    private Long recordId;

    /**
     * 评分维度类型：1-参考词使用 2-内容相关性 3-错别字及词汇使用 4-语法准确性与深广度 5-语句与逻辑连贯性 6-篇幅 7-整体评语 8-文本润色
     *  (1和6走的后端逻辑)
     */
    private Integer dimensionType;

    /**
     * 分项分数
     */
    private Integer score;

    /**
     * 状态：0-未开始 1-成功 2-失败
     */
    private Integer status;

    /**
     * 总分（分项总分）
     */
    private Integer totalScore;

    /**
     * 原始结果（存储Coze的完整JSON响应）
     */
    private String originalResult;

    /**
     * 解析后结果（存储结构化数据，如评语/建议）
     */
    private String analysisResult;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}