package com.xt.hsk.module.thirdparty.service.aicorrectiontimes;

import com.xt.hsk.module.thirdparty.service.aicorrectiontimes.AiCorrectionTimesService;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.DATA_NOT_EXIST;

import jakarta.validation.Valid;

import java.util.*;

import com.xt.hsk.module.thirdparty.controller.admin.aicorrectiontimes.vo.*;
import com.xt.hsk.module.thirdparty.dal.dataobject.aicorrectiontimes.AiCorrectionTimesDO;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.pojo.PageParam;
import com.xt.hsk.framework.common.util.object.BeanUtils;


/**
 * ai批改次数记录 Manager
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AiCorrectionTimesManager {

    @Resource
    private AiCorrectionTimesService aiCorrectionTimesService;


    public Long createAiCorrectionTimes(AiCorrectionTimesSaveReqVO createReqVO) {
        // 插入
        AiCorrectionTimesDO aiCorrectionTimes = BeanUtils.toBean(createReqVO, AiCorrectionTimesDO.class);
        aiCorrectionTimesService.save(aiCorrectionTimes);

        // 返回
        return aiCorrectionTimes.getId();
    }


    public void updateAiCorrectionTimes(AiCorrectionTimesSaveReqVO updateReqVO) {
        // 校验存在
        validateAiCorrectionTimesExists(updateReqVO.getId());
        // 更新
        AiCorrectionTimesDO updateObj = BeanUtils.toBean(updateReqVO, AiCorrectionTimesDO.class);
        aiCorrectionTimesService.updateById(updateObj);
    }


    public void deleteAiCorrectionTimes(Long id) {
        // 校验存在
        validateAiCorrectionTimesExists(id);
        // 删除
        aiCorrectionTimesService.removeById(id);
    }

    private void validateAiCorrectionTimesExists(Long id) {
        if (aiCorrectionTimesService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }


    public AiCorrectionTimesDO getAiCorrectionTimes(Long id) {
        return aiCorrectionTimesService.getById(id);
    }

    public PageResult<AiCorrectionTimesDO> getAiCorrectionTimesPage(@Valid AiCorrectionTimesPageReqVO pageReqVO) {
        return aiCorrectionTimesService.selectPage(pageReqVO);
    }

}