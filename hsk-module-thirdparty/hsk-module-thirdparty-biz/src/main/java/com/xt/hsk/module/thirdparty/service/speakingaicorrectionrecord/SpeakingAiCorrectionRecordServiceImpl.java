package com.xt.hsk.module.thirdparty.service.speakingaicorrectionrecord;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.thirdparty.controller.admin.speakingaicorrectionrecord.vo.SpeakingAiCorrectionRecordPageReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.validation.Valid;


import com.xt.hsk.module.thirdparty.dal.dataobject.speakingaicorrectionrecord.SpeakingAiCorrectionRecordDO;

import com.xt.hsk.module.thirdparty.dal.mysql.speakingaicorrectionrecord.SpeakingAiCorrectionRecordMapper;


/**
 * 口语ai批改记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SpeakingAiCorrectionRecordServiceImpl extends ServiceImpl<SpeakingAiCorrectionRecordMapper, SpeakingAiCorrectionRecordDO> implements SpeakingAiCorrectionRecordService {

    @Resource
    private SpeakingAiCorrectionRecordMapper speakingAiCorrectionRecordMapper;

    @Override
    public PageResult<SpeakingAiCorrectionRecordDO> selectPage(SpeakingAiCorrectionRecordPageReqVO pageReqVO) {

        return speakingAiCorrectionRecordMapper.selectPage(pageReqVO);
    }

}