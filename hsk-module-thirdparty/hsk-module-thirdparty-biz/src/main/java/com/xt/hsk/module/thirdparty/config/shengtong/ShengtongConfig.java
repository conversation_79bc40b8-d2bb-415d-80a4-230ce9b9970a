package com.xt.hsk.module.thirdparty.config.shengtong;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "shengtong")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShengtongConfig {
    /**
     * accessKey
     */
    private String appKey;
    /**
     * accessSecret
     */
    private String appSecret;
    /**
     * 声通接口调用地址
     */
    private String baseUrl;
}
