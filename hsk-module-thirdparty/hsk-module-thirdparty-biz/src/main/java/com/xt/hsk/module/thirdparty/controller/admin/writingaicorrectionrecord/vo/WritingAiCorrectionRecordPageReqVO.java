package com.xt.hsk.module.thirdparty.controller.admin.writingaicorrectionrecord.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WritingAiCorrectionRecordPageReqVO extends PageParam {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 习题ID
     */
    private Long questionId;

    /**
     * 习题详情ID
     */
    private Long questionDetailId;

    /**
     * 题型ID
     */
    private Integer questionTypeId;

    /**
     * 题目版本
     */
    private Integer questionVersion;

    /**
     * 发送给Coze的题目
     */
    private String sendContent;

    /**
     * 语言类型：zh-Hans-中文 en-英文 vi-越南语
     */
    private String language;

    /**
     * 作答记录ID
     */
    private Long recordId;

    /**
     * 批改类型：1-作文批改 2-文本润色
     */
    private Integer correctionType;

    /**
     * 总分（各分项汇总后存储）
     */
    private Integer totalScore;

    /**
     * 错误信息（失败时记录）
     */
    private String errorMessage;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}