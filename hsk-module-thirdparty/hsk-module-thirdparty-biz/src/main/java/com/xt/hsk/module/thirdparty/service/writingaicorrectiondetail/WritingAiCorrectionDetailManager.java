package com.xt.hsk.module.thirdparty.service.writingaicorrectiondetail;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.thirdparty.controller.admin.writingaicorrectiondetail.vo.WritingAiCorrectionDetailPageReqVO;
import com.xt.hsk.module.thirdparty.controller.admin.writingaicorrectiondetail.vo.WritingAiCorrectionDetailSaveReqVO;
import com.xt.hsk.module.thirdparty.dal.dataobject.writingaicorrectiondetail.WritingAiCorrectionDetailDO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.DATA_NOT_EXIST;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;


/**
 * 写作AI批改结果 Manager
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WritingAiCorrectionDetailManager {

    @Resource
    private WritingAiCorrectionDetailService writingAiCorrectionDetailService;


    public Long createWritingAiCorrectionDetail(WritingAiCorrectionDetailSaveReqVO createReqVO) {
        // 插入
        WritingAiCorrectionDetailDO writingAiCorrectionDetail = BeanUtils.toBean(createReqVO, WritingAiCorrectionDetailDO.class);
        writingAiCorrectionDetailService.save(writingAiCorrectionDetail);

        // 返回
        return writingAiCorrectionDetail.getId();
    }


    public void updateWritingAiCorrectionDetail(WritingAiCorrectionDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateWritingAiCorrectionDetailExists(updateReqVO.getId());
        // 更新
        WritingAiCorrectionDetailDO updateObj = BeanUtils.toBean(updateReqVO, WritingAiCorrectionDetailDO.class);
        writingAiCorrectionDetailService.updateById(updateObj);
    }


    public void deleteWritingAiCorrectionDetail(Long id) {
        // 校验存在
        validateWritingAiCorrectionDetailExists(id);
        // 删除
        writingAiCorrectionDetailService.removeById(id);
    }

    private void validateWritingAiCorrectionDetailExists(Long id) {
        if (writingAiCorrectionDetailService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }


    public WritingAiCorrectionDetailDO getWritingAiCorrectionDetail(Long id) {
        return writingAiCorrectionDetailService.getById(id);
    }

    public PageResult<WritingAiCorrectionDetailDO> getWritingAiCorrectionDetailPage(@Valid WritingAiCorrectionDetailPageReqVO pageReqVO) {
        return writingAiCorrectionDetailService.selectPage(pageReqVO);
    }

}