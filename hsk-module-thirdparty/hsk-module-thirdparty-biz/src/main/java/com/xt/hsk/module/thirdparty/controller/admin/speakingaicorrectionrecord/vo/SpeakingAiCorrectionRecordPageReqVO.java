package com.xt.hsk.module.thirdparty.controller.admin.speakingaicorrectionrecord.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpeakingAiCorrectionRecordPageReqVO extends PageParam {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 习题id
     */
    private Long questionId;

    /**
     * 题目版本
     */
    private Integer questionVersion;

    /**
     * 发送给第三方的题目
     */
    private String sendContent;

    /**
     * 作答记录id
     */
    private Long practiceRecordId;

    /**
     * 批改类型：1-音转字 2-口语评分
     */
    private Integer correctionType;

    /**
     * 参考文本
     */
    private String refText;

    /**
     * 第三方返回结果 json
     */
    private String result;

    /**
     * 第三方返回的记录id
     */
    private Long recordId;

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 错误id
     */
    private Integer errId;

    /**
     * 错误信息（失败时记录）
     */
    private String errorMessage;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}