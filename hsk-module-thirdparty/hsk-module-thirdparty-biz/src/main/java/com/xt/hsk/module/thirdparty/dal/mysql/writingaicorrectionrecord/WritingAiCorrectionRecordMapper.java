package com.xt.hsk.module.thirdparty.dal.mysql.writingaicorrectionrecord;

import java.util.*;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.module.thirdparty.dal.dataobject.writingaicorrectionrecord.WritingAiCorrectionRecordDO;
import org.apache.ibatis.annotations.Mapper;
import com.xt.hsk.module.thirdparty.controller.admin.writingaicorrectionrecord.vo.*;

/**
 * 写作AI批改记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WritingAiCorrectionRecordMapper extends BaseMapperX<WritingAiCorrectionRecordDO> {

    default PageResult<WritingAiCorrectionRecordDO> selectPage(WritingAiCorrectionRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WritingAiCorrectionRecordDO>()
                .eqIfPresent(WritingAiCorrectionRecordDO::getUserId, reqVO.getUserId())
                .eqIfPresent(WritingAiCorrectionRecordDO::getQuestionId, reqVO.getQuestionId())
                .eqIfPresent(WritingAiCorrectionRecordDO::getQuestionDetailId, reqVO.getQuestionDetailId())
                .eqIfPresent(WritingAiCorrectionRecordDO::getQuestionTypeId, reqVO.getQuestionTypeId())
                .eqIfPresent(WritingAiCorrectionRecordDO::getQuestionVersion, reqVO.getQuestionVersion())
//                .eqIfPresent(WritingAiCorrectionRecordDO::getSendContent, reqVO.getSendContent())
//                .eqIfPresent(WritingAiCorrectionRecordDO::getLanguage, reqVO.getLanguage())
                .eqIfPresent(WritingAiCorrectionRecordDO::getRecordId, reqVO.getRecordId())
//                .eqIfPresent(WritingAiCorrectionRecordDO::getCorrectionType, reqVO.getCorrectionType())
                .eqIfPresent(WritingAiCorrectionRecordDO::getTotalScore, reqVO.getTotalScore())
//                .eqIfPresent(WritingAiCorrectionRecordDO::getErrorMessage, reqVO.getErrorMessage())
                .betweenIfPresent(WritingAiCorrectionRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WritingAiCorrectionRecordDO::getId));
    }

    Integer getUserWritingAiCorrectionCount(long userId, Date dateBegin, Date dateEnd);
}