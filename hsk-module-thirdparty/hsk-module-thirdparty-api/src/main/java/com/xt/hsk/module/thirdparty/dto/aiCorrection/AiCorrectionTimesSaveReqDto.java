package com.xt.hsk.module.thirdparty.dto.aiCorrection;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class AiCorrectionTimesSaveReqDto {

    /**
     * 记录id
     */
    private Long id;

    /**
     * 用户ID
     */
//    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 业务类型 1.真题 2.模考 3.专项
     */
//    @NotNull(message = "业务类型 1.真题 2.模考 3.专项不能为空")
    private Integer bizType;

    /**
     * 业务ID
     */
//    @NotNull(message = "业务ID不能为空")
    private Long bizId;

    /**
     * 调用时间
     */
//    @NotNull(message = "调用时间不能为空")
    private Date callTime;

    /**
     * 调用次数
     */
//    @NotNull(message = "调用次数不能为空")
    private Integer callCount;
    /**
     * 作答记录id
     */
    private Long recordId;

}