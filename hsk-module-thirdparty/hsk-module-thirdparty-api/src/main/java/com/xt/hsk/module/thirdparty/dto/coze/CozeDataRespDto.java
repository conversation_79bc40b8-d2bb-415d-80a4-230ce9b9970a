package com.xt.hsk.module.thirdparty.dto.coze;

import lombok.Data;

/**
 * coze 返回数据
 */
@Data
public class CozeDataRespDto implements java.io.Serializable {
    /**
     * AI批改结果表ID
     */
    private Long id;
    /**
     * AI批改记录ID（关联主表）
     */
    private Long recordId;
    /**
     * 评分维度类型：1-参考词使用 2-内容相关性 3-错别字及词汇使用 4-语法准确性与深广度 5-语句与逻辑连贯性 6-篇幅 7-整体评语 8-文本润色
     *  (1和6走的后端逻辑)
     */
    private Integer dimensionType;
    /**
     * 评分维度类型描述
     */
    private String dimensionTypeDesc;
    /**
     * 分项分数
     */
    private Integer score;
    /**
     * 状态：0-未开始 1-成功 2-失败
     */
    private Integer status;
    /**
     * 总分（分项总分）
     */
    private Integer totalScore;
    /**
     * 返回文本
     */
    private String content;
    /**
     * 评语
     */
    private String comment;
}
