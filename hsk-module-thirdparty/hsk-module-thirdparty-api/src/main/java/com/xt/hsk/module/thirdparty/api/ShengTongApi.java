package com.xt.hsk.module.thirdparty.api;

import com.xt.hsk.module.thirdparty.dto.ShengTong.ShengTongCallDto;
import com.xt.hsk.module.thirdparty.dto.ShengTong.ShengTongRespDto;

/**
 * 声通api
 */
public interface ShengTongApi {
    /**
     * 口语评分
     *
     * @param shengTongCallDto 调用声通接口参数
     * @return
     */
    ShengTongRespDto oralEvaluation(ShengTongCallDto shengTongCallDto);

    /**
     * 中文音转字
     *
     * @param shengTongCallDto 调用声通接口参数
     * @return
     */
    ShengTongRespDto chineseSoundToWord(ShengTongCallDto shengTongCallDto);
}
