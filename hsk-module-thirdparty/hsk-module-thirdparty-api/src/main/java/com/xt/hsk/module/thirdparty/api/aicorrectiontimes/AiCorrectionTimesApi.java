package com.xt.hsk.module.thirdparty.api.aicorrectiontimes;

import com.xt.hsk.module.thirdparty.dto.aiCorrection.AiCorrectionTimesSaveReqDto;
import com.xt.hsk.module.thirdparty.enums.AiCorrectBizTypeEnum;

import java.util.Date;

public interface AiCorrectionTimesApi {
    /**
     * 保存用户调用次数
     *
     * @param createReqVO
     * @return
     */
    Long createAiCorrectionTimes(AiCorrectionTimesSaveReqDto createReqVO);

    /**
     * 获取用户今日调用次数
     */
    Integer getUserAiCorrectionTimes(Long userId, Date date, Integer bizType, String useCountKey);

    /**
     * 返还用户调用次数 根据调用的id 进行返还
     *
     * @param id 调用次数id
     */
    void returnAiCorrectionTimes(Long id);

    /**
     * 获取用户剩余批改次数
     *
     * @param userId               用户ID
     * @param configKey            配置额度KEY
     * @param useCountKey          使用的次数KEY
     * @param date                 日期
     * @param aiCorrectBizTypeEnum AI批改业务类型
     * @return 剩余批改次数
     */
    int getUserRemainingCount(Long userId, String configKey, String useCountKey, Date date, AiCorrectBizTypeEnum aiCorrectBizTypeEnum);
}
