package com.xt.hsk.module.thirdparty.api;

import com.xt.hsk.module.thirdparty.dto.coze.CozeCallDto;
import com.xt.hsk.module.thirdparty.dto.coze.CozeRespDto;

public interface CozeApi {

    /**
     * 真题写作批改
     *
     * @param cozeCallDto 调用参数
     * @return
     */
    void questionWritingCorrection(CozeCallDto cozeCallDto);

    /**
     * 获取Ai批改结果
     */
    CozeRespDto getQuestionAiCorrectionResult(CozeCallDto reqVO);
}
