package com.xt.hsk.module.thirdparty.dto.coze;

import lombok.Data;

import java.util.Date;

/**
 * coze 调用参数
 */
@Data
public class CozeCallDto implements java.io.Serializable {
    /**
     * 题目内容
     */
    private String questionContent;
    /**
     * 用户作答内容
     */
    private String answerData;
    /**
     * 题目类型
     */
    private Long questionTypeId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 记录id
     */
    private Long recordId;
    /**
     * 用户答案数据记录ID
     */
    private Long answerDataId;
    /**
     * 图片信息
     */
    private String imageDescription;
    /**
     * 图片信息
     */
    private boolean needImageDescription = false;
    /**
     * 题目id
     */
    private Long questionId;
    /**
     * 题目详情id
     */
    private Long questionDetailId;
    /**
     * 题目版本
     */
    private Integer questionVersion;
    /**
     * 是否需要一键润色
     */
    private Boolean oneClickPolishing = false;
    /**
     * ai批改时间
     */
    private Date aiCorrectionDate;
    /**
     * ai批改次数记录id
     */
    private Long aiCorrectionTimesId;
    /**
     * 业务类型 1.真题 2.模考 3.专项
     */
    private Integer bizType;

}
