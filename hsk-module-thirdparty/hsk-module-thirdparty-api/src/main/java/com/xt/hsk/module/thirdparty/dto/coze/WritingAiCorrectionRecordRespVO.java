package com.xt.hsk.module.thirdparty.dto.coze;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class WritingAiCorrectionRecordRespVO {


    /**
     * AI批改记录表ID
     */
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 习题ID
     */
    private Long questionId;
    /**
     * 习题详情ID
     */
    private Long questionDetailId;
    /**
     * 题型ID
     */
    private Long questionTypeId;
    /**
     * 题目版本
     */
    private Integer questionVersion;
    /**
     * 发送给Coze的题目  图片描述和题干内容
     */
    private String sendContent;
    /**
     * 语言类型：zh-Hans-中文 en-英文 vi-越南语
     */
    private String language;
    /**
     * 作答记录ID
     */
    private Long recordId;
    /**
     * 用户答案数据记录ID
     */
    private Long answerDataId;
    /**
     * 任务状态：0-进行中 1-成功 2-失败(全部成功才是成功，有一个失败则任务失败)
     */
    private Integer status;
//    /**
//     * 批改类型：1-作文批改 2-文本润色
//     */
//    private Integer correctionType;
    /**
     * 总分（各分项汇总后存储）
     */
    private Integer totalScore;
    /**
     * 得分
     */
    private Integer score;
//    /**
//     * 错误信息（失败时记录）
//     */
//    private String errorMessage;

}