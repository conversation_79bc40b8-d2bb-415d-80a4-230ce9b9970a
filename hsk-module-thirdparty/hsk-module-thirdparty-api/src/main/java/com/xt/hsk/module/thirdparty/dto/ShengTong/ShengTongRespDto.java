package com.xt.hsk.module.thirdparty.dto.ShengTong;

import lombok.Data;

/**
 * 声通评估返回结果
 */
@Data
public class ShengTongRespDto implements java.io.Serializable {
    /**
     * 口语ai批改记录表id
     */
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 习题id
     */
    private Long questionId;
    /**
     * 题目版本
     */
    private Integer questionVersion;
    /**
     * 发送给第三方的题目
     */
    private String sendContent;
    /**
     * 作答记录id
     */
    private Long practiceRecordId;
    /**
     * 批改类型：1-音转字 2-口语评分
     */
    private Integer correctionType;
    /**
     * 参考文本
     */
    private String refText;
    /**
     * 第三方返回结果 json
     */
    private String result;
    /**
     * 第三方返回的记录id
     */
    private String recordId;
    /**
     * 总分
     */
    private Integer totalScore;
    /**
     * 错误id
     */
    private Integer errId;
    /**
     * 错误信息（失败时记录）
     */
    private String errorMessage;
}
