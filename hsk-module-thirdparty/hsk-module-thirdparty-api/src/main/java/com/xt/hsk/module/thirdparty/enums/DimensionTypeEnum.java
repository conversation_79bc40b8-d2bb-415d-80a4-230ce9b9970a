package com.xt.hsk.module.thirdparty.enums;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;


public enum DimensionTypeEnum {
    //    评分维度类型：1-参考词使用 2-内容相关性 3-错别字及词汇使用 4-语法准确性与深广度 5-语句与逻辑连贯性 6-篇幅 7-整体评语 8-文本润色
    REFERENCE_WORD(1, "参考词使用"),
    CONTENT_RELEVANCE(2, "内容相关性"),
    WRONG_WORD_AND_WORD_USE(3, "错别字及词汇使用"),
    GRAMMAR_ACCURACY_AND_BROADNESS(4, "语法准确性与深广度"),
    STATEMENT_AND_LOGIC_CONSISTENCY(5, "语句与逻辑连贯性"),
    LENGTH(6, "篇幅"),
    OVERALL_COMMENT(7, "整体评语"),
    TEXT_RUN_CORRECTION(8, "文本润色"),
    OVERALL_COMMENT_EN(9, "整体评语"),
    OVERALL_COMMENT_CN(10, "整体评语"),
    ;
    private Integer code;
    private String desc;

    DimensionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        for (DimensionTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getCode() {
        return code;
    }

    public static List<Integer> getCommentsAndPolishingList() {
        List<Integer> integers = new ArrayList<>();
        integers.add(OVERALL_COMMENT.getCode());
        integers.add(TEXT_RUN_CORRECTION.getCode());
        integers.add(OVERALL_COMMENT_EN.getCode());
        integers.add(OVERALL_COMMENT_CN.getCode());
        return integers;
    }
}
