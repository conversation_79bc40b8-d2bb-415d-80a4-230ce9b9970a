package com.xt.hsk.module.thirdparty.dto.coze;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class WritingAiCorrectionDetailRespVO {

    /**
     * AI批改结果表ID
     */
    private Long id;
    /**
     * AI批改记录ID（关联主表）
     */
    private Long recordId;
    /**
     * 评分维度类型：1-参考词使用 2-内容相关性 3-错别字及词汇使用 4-语法准确性与深广度 5-语句与逻辑连贯性 6-篇幅 7-整体评语 8-文本润色
     *  (1和6走的后端逻辑)
     */
    private Integer dimensionType;
    /**
     * 分项分数
     */
    private Integer score;
    /**
     * 状态：0-未开始 1-成功 2-失败
     */
    private Integer status;
    /**
     * 总分（分项总分）
     */
    private Integer totalScore;
    /**
     * 返回文本
     */
    private String content;
//    /**
//     * 原始结果（存储Coze的完整JSON响应）
//     */
//    private String originalResult;
//    /**
//     * 解析后结果（存储结构化数据，如评语/建议）
//     */
//    private String analysisResult;

    /**
     * 解析后结果（存储结构化数据，如评语/建议）
     */
//    private String analysisResult;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;


}