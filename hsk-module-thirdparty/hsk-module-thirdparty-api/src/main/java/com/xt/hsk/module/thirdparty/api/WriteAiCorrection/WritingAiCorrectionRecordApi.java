package com.xt.hsk.module.thirdparty.api.WriteAiCorrection;

import com.xt.hsk.module.thirdparty.dto.coze.WritingAiCorrectionDetailRespVO;
import com.xt.hsk.module.thirdparty.dto.coze.WritingAiCorrectionRecordRespVO;

import java.util.Date;
import java.util.List;

/**
 * 获取用户写作批改记录
 */
public interface WritingAiCorrectionRecordApi {

    /**
     * 查询用户当日已使用的写作批改次数
     *
     * @param userId    用户id
     * @param dateBegin 查询开始时间
     * @param dateEnd   查询结束时间
     * @return
     */
    Integer getUserWritingAiCorrectionCount(long userId, Date dateBegin, Date dateEnd);


    /**
     * 获取用户写作批改记录
     */
    WritingAiCorrectionRecordRespVO getById(Long id);

    /**
     * 创建用户写作批改数据
     */
    List<WritingAiCorrectionDetailRespVO> getByRecordId(Long recordId);

}
