package com.xt.hsk.module.thirdparty.enums;

/**
 * ai批改业务类型枚举
 */
public enum AiCorrectBizTypeEnum {
    //    1.真题 2.模考 3.专项
    REAL_EXAM(1, "真题练习"),
    MOCK_EXAM(2, "模考"),
    SPECIAL_EXERCISE(3, "专项");
    private Integer code;
    private String desc;

    AiCorrectBizTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
