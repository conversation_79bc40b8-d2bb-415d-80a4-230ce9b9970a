package com.xt.hsk.module.marketing.enums.banner;

/**
 * Marketing 模块操作日志枚举常量 目的：统一管理操作日志相关常量
 *
 * <AUTHOR>
 */
public interface LogRecordConstants {

    // ======================= BANNER 管理 =======================

    String BANNER_TYPE = "营销 Banner";
    String BANNER_CREATE_SUB_TYPE = "创建Banner";
    String BANNER_CREATE_SUCCESS = "创建了Banner【{{#banner.bannerName}}】";
    String BANNER_UPDATE_SUB_TYPE = "更新Banner";
    String BANNER_UPDATE_SUCCESS = "更新了Banner【{{#banner.bannerName}}】";
    String BANNER_DELETE_SUB_TYPE = "删除Banner";
    String BANNER_DELETE_SUCCESS = "删除了Banner【{{#banner.bannerName}}】";
    String BANNER_DISPLAY_SUB_TYPE = "修改Banner展示状态";
    String BANNER_DISPLAY_SUCCESS = "修改了Banner【{{#banner.bannerName}}】的展示状态为【{{#displayDesc}}】";

    // ======================= 路由配置 管理 =======================

    String ROUTE_CONFIG_TYPE = "路由配置";
    String ROUTE_CONFIG_CREATE_SUB_TYPE = "创建路由配置";
    String ROUTE_CONFIG_CREATE_SUCCESS = "创建了路由配置【{{#route.pathName}}】";
    String ROUTE_CONFIG_UPDATE_SUB_TYPE = "更新路由配置";
    String ROUTE_CONFIG_UPDATE_SUCCESS = "更新了路由配置【{{#route.pathName}}】";
    String ROUTE_CONFIG_DELETE_SUB_TYPE = "删除路由配置";
    String ROUTE_CONFIG_DELETE_SUCCESS = "删除了路由配置【{{#route.pathName}}】";
    String ROUTE_CONFIG_SHOW_SUB_TYPE = "修改路由显示状态";
    String ROUTE_CONFIG_SHOW_SUCCESS = "修改了路由配置【{{#route.pathName}}】的显示状态为【{{#showDesc}}】";

} 