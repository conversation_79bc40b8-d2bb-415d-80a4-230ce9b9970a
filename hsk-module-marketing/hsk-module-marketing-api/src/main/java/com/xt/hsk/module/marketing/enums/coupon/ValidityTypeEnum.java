package com.xt.hsk.module.marketing.enums.coupon;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.Getter;

/**
 * 有效期类型枚举
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
public enum ValidityTypeEnum implements BasicEnum<Integer> {
    /**
     * 有效期类型枚举
     */
    FIXED_DAYS(1, "固定天数"),
    FIXED_TIME(2, "固定时间段"),
    ;

    public final Integer code;
    public final String desc;

    ValidityTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据类型码获取对应的描述信息
     *
     * @param code 类型码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (ValidityTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据类型码获取对应的枚举实例
     *
     * @param code 类型码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static ValidityTypeEnum getByCode(Integer code) {
        for (ValidityTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
} 