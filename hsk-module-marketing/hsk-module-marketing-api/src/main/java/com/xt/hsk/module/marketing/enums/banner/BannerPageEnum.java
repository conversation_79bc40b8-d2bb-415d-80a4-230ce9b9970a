package com.xt.hsk.module.marketing.enums.banner;

import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Banner页面枚举
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
@Getter
@AllArgsConstructor
public enum BannerPageEnum implements BasicEnum<Integer>, ArrayValuable<Integer> {

    /**
     * 练习
     */
    PRACTICE(1, "练习"),
    /**
     * 题型练习列表页
     */
    PRACTICE_TYPE_LIST(2, "题型练习列表页"),
    /**
     * 课程
     */
    COURSE(3, "课程"),
    /**
     * 我的
     */
    MY(4, "我的");

    /**
     * 页面类型
     */
    private final Integer code;
    /**
     * 页面描述
     */
    private final String desc;

    private static final Integer[] ARRAYS = Arrays.stream(values()).map(BannerPageEnum::getCode)
        .toArray(Integer[]::new);


    @Override
    public Integer[] array() {
        return ARRAYS;
    }
} 