package com.xt.hsk.module.marketing.enums.coupon;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.Getter;

/**
 * 优惠券模板状态枚举
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
public enum CouponTemplateStatusEnum implements BasicEnum<Integer> {
    /**
     * 优惠券模板状态枚举
     */
    UNPUBLISHED(1, "未发放"),
    TAKING(2, "领取中"),
    FINISHED(3, "已结束"),
    ;

    public final Integer code;
    public final String desc;

    CouponTemplateStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据状态码获取对应的描述信息
     *
     * @param code 状态码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (CouponTemplateStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取对应的枚举实例
     *
     * @param code 状态码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static CouponTemplateStatusEnum getByCode(Integer code) {
        for (CouponTemplateStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
} 