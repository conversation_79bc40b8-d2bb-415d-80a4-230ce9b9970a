package com.xt.hsk.module.marketing.enums.coupon;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.Getter;

/**
 * 优惠券状态枚举
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
public enum CouponStatusEnum implements BasicEnum<Integer> {
    /**
     * 优惠券状态枚举
     */
    UNUSED(1, "未使用"),
    USED(2, "已使用"),
    EXPIRED(3, "已过期"),
    ;

    public final Integer code;
    public final String desc;

    CouponStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据状态码获取对应的描述信息
     *
     * @param code 状态码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (CouponStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取对应的枚举实例
     *
     * @param code 状态码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static CouponStatusEnum getByCode(Integer code) {
        for (CouponStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
} 