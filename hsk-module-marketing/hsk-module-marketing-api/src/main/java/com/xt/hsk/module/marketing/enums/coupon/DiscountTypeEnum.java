package com.xt.hsk.module.marketing.enums.coupon;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.Getter;

/**
 * 折扣类型枚举
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
public enum DiscountTypeEnum implements BasicEnum<Integer> {
    /**
     * 折扣类型枚举
     */
    DIRECT(1, "无门槛"),
    THRESHOLD(2, "满减"),
    ;

    public final Integer code;
    public final String desc;

    DiscountTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据类型码获取对应的描述信息
     *
     * @param code 类型码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (DiscountTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据类型码获取对应的枚举实例
     *
     * @param code 类型码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static DiscountTypeEnum getByCode(Integer code) {
        for (DiscountTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
} 