package com.xt.hsk.module.marketing.enums.banner;

import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Banner点击动作类型枚举
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
@Getter
@AllArgsConstructor
public enum BannerActionTypeEnum implements BasicEnum<Integer>, ArrayValuable<Integer> {

    /**
     * 虚拟定义的值
     */
    NONE(1, "无动作"),
    SHOW_IMAGE(2, "显示图片"),
    EXTERNAL_LINK(3, "外部链接"),
    APP_ROUTE(4, "APP内页面"),
    POPUP(5, "打开弹窗");

    /**
     * 动作类型
     */
    private final Integer code;
    /**
     * 动作描述
     */
    private final String desc;

    private static final Integer[] ARRAYS = Arrays.stream(values())
        .map(BannerActionTypeEnum::getCode).toArray(Integer[]::new);


    @Override
    public Integer[] array() {
        return ARRAYS;
    }
} 