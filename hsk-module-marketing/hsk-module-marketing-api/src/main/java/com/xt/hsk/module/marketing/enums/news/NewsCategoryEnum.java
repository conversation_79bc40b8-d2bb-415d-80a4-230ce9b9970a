package com.xt.hsk.module.marketing.enums.news;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资讯分类枚举
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@Getter
@AllArgsConstructor
public enum NewsCategoryEnum implements BasicEnum<Integer>, VO, ArrayValuable<Integer> {
    /**
     * 考试资讯
     */
    EXAM(1, "考试资讯"),
    /**
     * 备考指南
     */
    REVIEW(2, "备考指南");

    private static final Integer[] ARRAYS = Arrays.stream(values()).map(NewsCategoryEnum::getCode).toArray(Integer[]::new);
    private final Integer code;
    private final String desc;


    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}
