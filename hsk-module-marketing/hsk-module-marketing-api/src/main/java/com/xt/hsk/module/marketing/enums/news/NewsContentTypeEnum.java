package com.xt.hsk.module.marketing.enums.news;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * news 内容类型枚举
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@Getter
@AllArgsConstructor
public enum NewsContentTypeEnum implements BasicEnum<Integer>, VO, ArrayValuable<Integer> {

    /**
     * 富文本
     */
    RICH_TEXT(1, "富文本"),
    /**
     * 图片
     */
    IMAGE(2, "图片"),
    ;

    private static final Integer[] ARRAYS = Arrays.stream(values()).map(NewsContentTypeEnum::getCode).toArray(Integer[]::new);
    private final Integer code;
    private final String desc;


    @Override
    public Integer[] array() {
        return ARRAYS;
    }

}
