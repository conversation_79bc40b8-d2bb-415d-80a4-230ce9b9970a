package com.xt.hsk.module.marketing.enums.news;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资讯展示状态
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@Getter
@AllArgsConstructor
public enum NewsShowStatus implements BasicEnum<Integer>, VO, ArrayValuable<Integer> {
    /**
     * 展示
     */
    SHOW(1, "展示"),
    /**
     * 定时展示
     */
    SHOW_SCHEDULED(2, "定时展示"),
    /**
     * 不展示
     */
    NOT_SHOW(0, "不展示");

    private final Integer code;
    private final String desc;
    private static final Integer[] ARRAYS = Arrays.stream(values())
        .map(NewsShowStatus::getCode).toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}
