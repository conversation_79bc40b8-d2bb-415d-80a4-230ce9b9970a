package com.xt.hsk.module.marketing.enums;

/**
 * 日志记录常量
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
public interface LogRecordConstants {

    // ======================= 资讯 =======================
    // 资讯
    String NEWS_TYPE = "资讯";
    String NEWS_CREATE_SUB_TYPE = "创建资讯";
    String NEWS_UPDATE_SUB_TYPE = "更新资讯";
    String NEWS_DELETE_SUB_TYPE = "删除资讯";
    String NEWS_CREATE_SUCCESS = "创建了资讯【{{#news.nameCn}}】";
    String NEWS_UPDATE_SUCCESS = "更新了资讯【{{#news.nameCn}}】: {_DIFF{#updateReqVO}}";
    String NEWS_DELETE_SUCCESS = "删除了资讯【{{#news.nameCn}}】";
    String NEWS_UPDATE_STATUS_SUCCESS = "修改了资讯【{{#news.nameCn}}】的状态为【{{#showStatus}}】";

    // 资讯内容
    String NEWS_CONTENT_TYPE = "资讯内容";
    String NEWS_CONTENT_UPDATE_SUB_TYPE = "更新资讯内容";
    String NEWS_CONTENT_DELETE_SUB_TYPE = "删除资讯内容";
    String NEWS_CONTENT_UPDATE_SUCCESS = "更新了资讯【{{#news.nameCn}}】的资讯内容: {_DIFF{#updateReqVO}}";
    String NEWS_CONTENT_DELETE_SUCCESS = "删除了资讯【{{#news.nameCn}}】的资讯内容【{{#id}}】";
    String NEWS_CONTENT_CREATE_SUCCESS = "创建了资讯【{{#news.nameCn}}】的资讯内容【{{#newsContent.contentCn}}】";

}
