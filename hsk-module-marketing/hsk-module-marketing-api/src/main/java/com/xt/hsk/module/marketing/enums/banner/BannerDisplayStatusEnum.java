package com.xt.hsk.module.marketing.enums.banner;

import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Banner展示状态枚举
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Getter
@AllArgsConstructor
public enum BannerDisplayStatusEnum implements BasicEnum<Integer>, ArrayValuable<Integer> {

    /**
     * 立即显示
     */
    SHOW_NOW(1, "立即展示"),
    /**
     * 定时展示
     */
    SHOW_SCHEDULED(2, "定时展示"),
    /**
     * 隐藏
     */
    HIDDEN(3, "隐藏");

    /**
     * 状态类型
     */
    private final Integer code;
    /**
     * 状态描述
     */
    private final String desc;

    private static final Integer[] ARRAYS = Arrays.stream(values())
        .map(BannerDisplayStatusEnum::getCode).toArray(Integer[]::new);


    @Override
    public Integer[] array() {
        return ARRAYS;
    }
} 