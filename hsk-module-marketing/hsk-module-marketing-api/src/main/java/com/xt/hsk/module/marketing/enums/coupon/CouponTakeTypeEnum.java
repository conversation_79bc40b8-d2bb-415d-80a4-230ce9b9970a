package com.xt.hsk.module.marketing.enums.coupon;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.Getter;

/**
 * 优惠券领取类型枚举
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
public enum CouponTakeTypeEnum implements BasicEnum<Integer> {
    /**
     * 优惠券领取类型枚举
     */
    TAKE(1, "领取"),
    GIFT(2, "赠送"),
    ;

    public final Integer code;
    public final String desc;

    CouponTakeTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据类型码获取对应的描述信息
     *
     * @param code 类型码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (CouponTakeTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据类型码获取对应的枚举实例
     *
     * @param code 类型码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static CouponTakeTypeEnum getByCode(Integer code) {
        for (CouponTakeTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
} 