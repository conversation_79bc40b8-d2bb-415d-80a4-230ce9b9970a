package com.xt.hsk.module.marketing.enums.banner;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Banner位置枚举
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
@Getter
@AllArgsConstructor
public enum BannerPositionEnum implements BasicEnum<Integer>, VO, ArrayValuable<Integer> {

    /**
     * 顶部
     */
    TOP(1, "顶部"),
    /**
     * 中部
     */
    MIDDLE(2, "中部"),
    /**
     * 底部
     */
    BOTTOM(3, "底部");

    /**
     * 位置类型
     */
    private final Integer code;
    /**
     * 位置描述
     */
    private final String desc;

    private static final Integer[] ARRAYS = Arrays.stream(values()).map(BannerPositionEnum::getCode)
        .toArray(Integer[]::new);


    @Override
    public Integer[] array() {
        return ARRAYS;
    }
} 