package com.xt.hsk.module.marketing.enums;


import com.xt.hsk.framework.common.exception.ErrorCode;
import com.xt.hsk.framework.common.exception.enums.ServiceErrorCodeRange;

/**
 * 错误代码常量 模块 marketing 营销模块错误码区间 [1-031-001-000 ~ 1-035-000-000)] 营销系统，优惠券业务 使用 031 业务模块编码
 * 优惠券模块，错误码从 001 开始
 *
 * <AUTHOR>
 * @see ServiceErrorCodeRange yudao设计说明
 * @since 2025/06/18
 */
public interface ErrorCodeConstants {

    /**
     * 优惠券模板模块错误代码
     **/
    ErrorCode COUPON_TEMPLATE_NOT_EXISTS = new ErrorCode(1_031_001_001, "优惠券模板不存在");
    ErrorCode COUPON_TEMPLATE_NAME_EXISTS = new ErrorCode(1_031_001_002, "优惠券名称已存在");
    ErrorCode COUPON_TEMPLATE_CODE_EXISTS = new ErrorCode(1_031_001_003, "优惠券码已存在");
    ErrorCode COUPON_TEMPLATE_STATUS_ERROR = new ErrorCode(1_031_001_004, "优惠券状态不正确");

    /**
     * 优惠券模块错误代码
     **/
    ErrorCode COUPON_NOT_EXISTS = new ErrorCode(1_031_002_001, "优惠券不存在");
    ErrorCode COUPON_ALREADY_USED = new ErrorCode(1_031_002_002, "优惠券已被使用");
    ErrorCode COUPON_EXPIRED = new ErrorCode(1_031_002_003, "优惠券已过期");
    ErrorCode COUPON_REVOKED = new ErrorCode(1_031_002_004, "优惠券已被撤销");
    ErrorCode COUPON_TAKE_LIMIT_EXCEEDED = new ErrorCode(1_031_002_005, "超过领取限制");
    ErrorCode COUPON_TOTAL_COUNT_EXCEEDED = new ErrorCode(1_031_002_006, "优惠券已发放完毕");

    /**
     * 优惠券记录模块错误代码
     **/
    ErrorCode COUPON_RECORD_NOT_EXISTS = new ErrorCode(1_031_003_001, "优惠券记录不存在");

    /**
     * banner模块错误代码
     */
    ErrorCode BANNER_NOT_EXISTS = new ErrorCode(1_032_001_001, "Banner不存在");

    /**
     * 资讯模块错误代码
     */
    ErrorCode NEWS_NOT_EXISTS = new ErrorCode(1_033_001_001, "资讯不存在");
    ErrorCode NEWS_SHOW_TIME_ERROR = new ErrorCode(1_033_001_002, "资讯展示时间错误");
    ErrorCode NEWS_FEATURED_COUNT_LIMIT = new ErrorCode(1_033_001_003, "精选资讯数量达到上限");

} 