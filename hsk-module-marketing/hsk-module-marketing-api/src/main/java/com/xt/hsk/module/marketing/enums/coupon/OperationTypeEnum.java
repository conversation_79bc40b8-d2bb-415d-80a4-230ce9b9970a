package com.xt.hsk.module.marketing.enums.coupon;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.Getter;

/**
 * 操作类型枚举
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
public enum OperationTypeEnum implements BasicEnum<Integer> {
    /**
     * 操作类型枚举
     */
    TAKE(1, "领取"),
    USE(2, "使用"),
    EXPIRE(3, "过期"),
    REVOKE(4, "撤销"),
    GIFT(5, "赠送"),
    REFUND(6, "退款返还"),
    ;

    public final Integer code;
    public final String desc;

    OperationTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据类型码获取对应的描述信息
     *
     * @param code 类型码
     * @return 对应的描述信息，如果找不到则返回null
     */
    public static String getDescByCode(Integer code) {
        for (OperationTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据类型码获取对应的枚举实例
     *
     * @param code 类型码
     * @return 对应的枚举实例，如果找不到则返回null
     */
    public static OperationTypeEnum getByCode(Integer code) {
        for (OperationTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
} 