package com.xt.hsk.module.marketing.manager.coupon;

import cn.dev33.satoken.stp.StpUtil;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.util.i18n.LanguageUtils;
import com.xt.hsk.module.marketing.controller.app.coupon.vo.CouponListReqVO;
import com.xt.hsk.module.marketing.controller.app.coupon.vo.CouponVO;
import com.xt.hsk.module.marketing.convert.coupon.CouponConvert;
import com.xt.hsk.module.marketing.dal.dataobject.coupon.CouponDO;
import com.xt.hsk.module.marketing.dal.dataobject.coupon.CouponTemplateDO;
import com.xt.hsk.module.marketing.enums.coupon.CouponTakeTypeEnum;
import com.xt.hsk.module.marketing.service.coupon.CouponService;
import com.xt.hsk.module.marketing.service.coupon.CouponTemplateService;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 优惠券app
 *
 * <AUTHOR>
 * @since 2025/06/06
 */
@Slf4j
@Component
public class AppCouponManager {

    @Resource
    private CouponTemplateService couponTemplateService;

    @Resource
    private CouponService couponService;

    /**
     * 根据精品课程查询优惠券列表
     * 展示本人商品可使用且未使用的优惠券，区分已领取和待领取两种状态
     *
     * @param reqVO req vo
     * @return 优惠券列表
     */
    public CommonResult<List<CouponVO>> getCouponListByEliteCourseId(CouponListReqVO reqVO) {
        Long userId = StpUtil.isLogin() ? StpUtil.getLoginIdAsLong() : null;
        Long courseId = reqVO.getCourseId();

        List<CouponVO> result = new ArrayList<>();

        // 1. 获取用户已拥有的优惠券（已领取状态）
        if (userId != null) {
            List<CouponDO> userCoupons = couponService.getUserAvailableCoupons(userId, courseId);
            List<CouponVO> userCouponVOs = CouponConvert.INSTANCE.convertList(userCoupons);
            // 设置为已领取状态
            // 1-已领取
            // 优惠券表中只保存了模板ID，没有优惠券详情,这里需要查询模板详情
            Map<Long, CouponTemplateDO> templateDOMap = couponTemplateService.lambdaQuery()
                .in(CouponTemplateDO::getId,
                    userCoupons.stream().map(CouponDO::getCouponTemplateId).toList()).list()
                .stream().collect(Collectors.toMap(CouponTemplateDO::getId, Function.identity()));
            userCouponVOs.forEach(coupon -> {
                CouponTemplateDO couponTemplateDO = templateDOMap.get(coupon.getCouponTemplateId());
                if (couponTemplateDO != null) {
                    coupon.setCouponName(LanguageUtils.getLocalizedValue(
                        couponTemplateDO.getCouponNameCn(),
                        couponTemplateDO.getCouponNameEn(),
                        couponTemplateDO.getCouponNameOt()));

                    coupon.setDiscountType(couponTemplateDO.getDiscountType());
                    coupon.setDiscountPrice(LanguageUtils.getLocalizedValue(
                        couponTemplateDO.getDiscountPriceCn(),
                        couponTemplateDO.getDiscountPriceEn(),
                        couponTemplateDO.getDiscountPriceOt()));
                    coupon.setThresholdPrice(LanguageUtils.getLocalizedValue(
                        couponTemplateDO.getThresholdPriceCn(),
                        couponTemplateDO.getThresholdPriceEn(),
                        couponTemplateDO.getThresholdPriceOt()));
                    coupon.setReducePrice(LanguageUtils.getLocalizedValue(
                        couponTemplateDO.getReducePriceCn(),
                        couponTemplateDO.getReducePriceEn(),
                        couponTemplateDO.getReducePriceOt()));
                    coupon.setTotalCount(couponTemplateDO.getTotalCount());
                    coupon.setValidityType(couponTemplateDO.getValidityType());
                    coupon.setValidityDays(couponTemplateDO.getValidityDays());
                    coupon.setValidStartTime(couponTemplateDO.getValidStartTime());
                    coupon.setValidEndTime(couponTemplateDO.getValidEndTime());
                    coupon.setTakeType(couponTemplateDO.getTakeType());
                    coupon.setProductScope(couponTemplateDO.getProductScope());
                }
            });

            userCouponVOs.forEach(coupon -> coupon.setTakeStatus(1));
            result.addAll(userCouponVOs);
        }

        // 2. 获取可领取的优惠券模板（待领取状态）
        // 只查询类型为"领取"的模板
        List<CouponTemplateDO> availableTemplates = couponTemplateService.getAvailableCouponTemplates(
            userId, courseId);

        // 过滤出领取类型的模板（PRD要求：后台类型=领取or赠送）
        List<CouponTemplateDO> takeTypeTemplates = availableTemplates.stream()
            .filter(template -> CouponTakeTypeEnum.TAKE.code.equals(template.getTakeType()))
            .toList();

        if (!takeTypeTemplates.isEmpty()) {
            List<CouponVO> templateCouponVOs = CouponConvert.INSTANCE.convertTemplateList(
                takeTypeTemplates);
            // 设置为待领取状态
            // 2-待领取
            templateCouponVOs.forEach(coupon -> coupon.setTakeStatus(2));
            result.addAll(templateCouponVOs);
        }

        // 3. 按金额从大到小排序，金额一致时按到期时间由近到远排序
        result.sort(
            Comparator.comparing(this::getCouponAmount).reversed()
                .thenComparing(CouponVO::getValidEndTime)
        );

        return CommonResult.success(result);
    }

    /**
     * 获取优惠券金额
     *
     * @param coupon 优惠券
     * @return 金额
     */
    private BigDecimal getCouponAmount(CouponVO coupon) {
        if (coupon.getDiscountType() == 1) {
            // 无门槛优惠券
            return coupon.getDiscountPrice();
        } else if (coupon.getDiscountType() == 2) {
            // 满减优惠券
            return coupon.getReducePrice();
        }
        return BigDecimal.ZERO;
    }

    /**
     * 领取优惠券 优惠券模板ID
     *
     * @param userId           用户 ID
     * @param couponTemplateId 优惠券模板 ID
     */
    public Long takeCoupon(Long userId, Long couponTemplateId) {
        return couponService.takeCoupon(userId, couponTemplateId);
    }
}
