package com.xt.hsk.module.marketing.convert.coupon;

import com.xt.hsk.framework.common.util.i18n.LanguageUtils;
import com.xt.hsk.module.marketing.controller.app.coupon.vo.CouponVO;
import com.xt.hsk.module.marketing.dal.dataobject.coupon.CouponDO;
import com.xt.hsk.module.marketing.dal.dataobject.coupon.CouponTemplateDO;
import java.util.List;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * 优惠券 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CouponConvert {

    CouponConvert INSTANCE = Mappers.getMapper(CouponConvert.class);

    /**
     * 将优惠券DO转换为VO
     * 默认已领取状态
     * @param couponDO 优惠券DO
     * @return 优惠券VO
     */
    @Mapping(target = "takeStatus", constant = "1")

    CouponVO convert(CouponDO couponDO);

    /**
     * 将优惠券DO列表转换为VO列表
     *
     * @param couponDOList 优惠券DO列表
     * @return 优惠券VO列表
     */
    List<CouponVO> convertList(List<CouponDO> couponDOList);

    /**
     * 将优惠券模板DO转换为优惠券VO（用于展示待领取的优惠券）
     * 默认未使用状态
     * @param templateDO 优惠券模板DO
     * @return 优惠券VO
     */
    @Mapping(target = "takeStatus", constant = "2") // 待领取状态
    @Mapping(target = "status", constant = "1")
    CouponVO convertTemplate(CouponTemplateDO templateDO);

    /**
     * 将优惠券模板DO列表转换为优惠券VO列表
     *
     * @param templateDOList 优惠券模板DO列表
     * @return 优惠券VO列表
     */
    List<CouponVO> convertTemplateList(List<CouponTemplateDO> templateDOList);

    @AfterMapping
    default void fillLocalizedFields(CouponTemplateDO bean, @MappingTarget CouponVO respVO) {
        // 这段代码会在基本映射完成后执行
        // 可以访问源对象bean的所有属性
        // 可以修改目标对象respVO的属性
        respVO.setCouponName(LanguageUtils.getLocalizedValue(
            bean.getCouponNameCn(),
            bean.getCouponNameEn(),
            bean.getCouponNameOt()));
        respVO.setDiscountPrice(LanguageUtils.getLocalizedValue(
            bean.getDiscountPriceCn(),
            bean.getDiscountPriceEn(),
            bean.getDiscountPriceOt()));

        respVO.setThresholdPrice(LanguageUtils.getLocalizedValue(
            bean.getThresholdPriceCn(),
            bean.getThresholdPriceEn(),
            bean.getThresholdPriceOt()));

        respVO.setReducePrice(LanguageUtils.getLocalizedValue(
            bean.getReducePriceCn(),
            bean.getReducePriceEn(),
            bean.getReducePriceOt()));
    }
} 