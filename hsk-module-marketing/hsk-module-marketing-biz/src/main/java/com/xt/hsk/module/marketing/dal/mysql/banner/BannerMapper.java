package com.xt.hsk.module.marketing.dal.mysql.banner;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.marketing.controller.admin.banner.vo.BannerPageReqVO;
import com.xt.hsk.module.marketing.dal.dataobject.banner.BannerDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * Banner Mapper
 */
@Mapper
public interface BannerMapper extends BaseMapperX<BannerDO> {

    /**
     * 分页查询
     *
     * @param reqVO 查询条件
     * @return 结果列表
     */
    default PageResult<BannerDO> selectPage(BannerPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BannerDO>()
            .likeIfPresent(BannerDO::getBannerName, reqVO.getBannerName())
            .eqIfPresent(BannerDO::getPage, reqVO.getPage())
            .eqIfPresent(BannerDO::getPosition, reqVO.getPosition())
            .eqIfPresent(BannerDO::getDisplayStatus, reqVO.getDisplayStatus())
            .orderByDesc(BannerDO::getCreateTime)
            .orderByDesc(BannerDO::getDisplayTime));
    }
} 