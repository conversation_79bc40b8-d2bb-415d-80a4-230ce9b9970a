package com.xt.hsk.module.marketing.dal.dataobject.coupon;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import com.xt.hsk.module.marketing.enums.coupon.CouponStatusEnum;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 优惠券 DO
 *
 * <AUTHOR>
 */
@TableName(value = "marketing_coupon", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CouponDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 优惠券模板ID
     */
    private Long couponTemplateId;

    /**
     * 优惠券状态 1-未使用 2-已使用 3-已过期
     * @see CouponStatusEnum
     */
    private Integer status;

    /**
     * 优惠券名称-中文
     */
    private String couponNameCn;

    /**
     * 优惠券名称-英文
     */
    private String couponNameEn;

    /**
     * 优惠券名称-其他
     */
    private String couponNameOt;

    /**
     * 优惠券码
     */
    private String couponCode;

    /**
     * 领取类型 1-领取 2-赠送
     */
    private Integer takeType;

    /**
     * 撤销状态 0-未撤销 1-已撤销
     */
    private Integer revokeStatus;

    /**
     * 生效开始时间
     */
    private LocalDateTime startTime;

    /**
     * 生效结束时间
     */
    private LocalDateTime endTime;

    /**
     * 实际使用时涉及的商品ID
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> usedProductIds;

    /**
     * 使用此优惠券的订单ID
     */
    private Long orderId;

    /**
     * 使用此优惠券的订单编号
     */
    private String orderNumber;

    /**
     * 使用时间
     */
    private LocalDateTime useTime;

    /**
     * 赠送人ID
     */
    private Long giftUserId;
} 