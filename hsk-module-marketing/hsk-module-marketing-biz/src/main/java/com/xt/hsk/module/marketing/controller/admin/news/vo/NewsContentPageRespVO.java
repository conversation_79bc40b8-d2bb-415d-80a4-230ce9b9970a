package com.xt.hsk.module.marketing.controller.admin.news.vo;

import com.xt.hsk.module.marketing.enums.news.NewsContentTypeEnum;
import lombok.Data;

/**
 * 资讯内容管理响应
 *
 * <AUTHOR>
 * @since 2025/07/01
 */
@Data
public class NewsContentPageRespVO {

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 资讯ID
     */
    private Long newsId;
    /**
     * 内容类型(1-文本/图片/视频 2-图片ALT)
     * @see NewsContentTypeEnum
     */
    private Integer contentType;
    /**
     * 内容_cn
     */
    private String contentCn;
    /**
     * 内容_en
     */
    private String contentEn;
    /**
     * 内容_ot
     */
    private String contentOt;
}
