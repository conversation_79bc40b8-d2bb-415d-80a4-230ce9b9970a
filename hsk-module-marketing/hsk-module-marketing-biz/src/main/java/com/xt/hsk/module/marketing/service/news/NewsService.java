package com.xt.hsk.module.marketing.service.news;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsPageReqVO;
import com.xt.hsk.module.marketing.dal.dataobject.news.NewsDO;
import jakarta.validation.Valid;

/**
 * 资讯 Service 接口
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
public interface NewsService extends IService<NewsDO> {

    /**
     * 分页查询资讯列表
     *
     * @param pageReqVO 分页参数
     * @return {@code PageResult<NewsDO> }
     */
    PageResult<NewsDO> selectPage(@Valid NewsPageReqVO pageReqVO);

}
