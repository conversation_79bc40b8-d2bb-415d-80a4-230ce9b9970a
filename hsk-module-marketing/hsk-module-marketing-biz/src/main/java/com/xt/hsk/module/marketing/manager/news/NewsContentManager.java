package com.xt.hsk.module.marketing.manager.news;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.marketing.enums.ErrorCodeConstants.NEWS_NOT_EXISTS;

import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsContentPageReqVO;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsContentPageRespVO;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsContentSaveOrUpdateVO;
import com.xt.hsk.module.marketing.convert.news.NewsContentConvert;
import com.xt.hsk.module.marketing.dal.dataobject.news.NewsContentDO;
import com.xt.hsk.module.marketing.dal.dataobject.news.NewsDO;
import com.xt.hsk.module.marketing.service.news.NewsContentService;
import com.xt.hsk.module.marketing.service.news.NewsService;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class NewsContentManager {

    @Resource
    private NewsService newsService;

    @Resource
    private NewsContentService newsContentService;

    private NewsDO validateNewsExists(Long id) {
        NewsDO newsDO = newsService.getById(id);
        if (newsDO == null) {
            throw exception(NEWS_NOT_EXISTS);
        }
        return newsDO;
    }

    /**
     * 获取资讯内容页面
     */
    public PageResult<NewsContentPageRespVO> getNewsContentPage(NewsContentPageReqVO reqVO) {
        PageResult<NewsContentDO> pageResult = newsContentService.selectPage(reqVO);
        List<NewsContentPageRespVO> newsContentPageRespVOList = NewsContentConvert.INSTANCE.convertList(pageResult.getList());
        return new PageResult<>(newsContentPageRespVOList, pageResult.getTotal());
    }

    /**
     * 删除资讯内容
     */
    public void deleteContent(Long id) {
        NewsContentDO contentDO = newsContentService.getById(id);
        if (contentDO == null) {
            return;
        }
        NewsDO newsDO = validateNewsExists(contentDO.getNewsId());
        newsContentService.removeById(id);

        LogRecordContext.putVariable("news", newsDO);
    }

    /**
     * 创建或更新资讯内容
     */
    public void createOrUpdateContent(NewsContentSaveOrUpdateVO reqVO, Long newsId) {
        NewsDO newsDO = validateNewsExists(newsId);

        NewsContentDO contentDO = NewsContentConvert.INSTANCE.convert(reqVO);
        newsContentService.saveOrUpdate(contentDO);

        LogRecordContext.putVariable("news", newsDO);
    }
}
