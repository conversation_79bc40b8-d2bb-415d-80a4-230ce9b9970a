package com.xt.hsk.module.marketing.dal.dataobject.banner;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 页面路由配置实体类
 *
 * <AUTHOR>
 * @since 2025/07/07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("marketing_route_config")
public class RouteConfigDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 页面名称
     */
    private String pathName;

    /**
     * iOS页面路径
     */
    private String iosPath;

    /**
     * Android页面路径
     */
    private String androidPath;

    /**
     * H5页面路径
     */
    private String h5Path;

    /**
     * 是否显示：0-隐藏 1-显示
     */
    private Integer isShow;
} 