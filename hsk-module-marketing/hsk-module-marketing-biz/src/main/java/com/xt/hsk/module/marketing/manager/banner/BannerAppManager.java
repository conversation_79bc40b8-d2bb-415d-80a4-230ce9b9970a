package com.xt.hsk.module.marketing.manager.banner;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xt.hsk.module.marketing.controller.app.banner.vo.AppBannerPageListRespVO;
import com.xt.hsk.module.marketing.controller.app.banner.vo.AppBannerRespVO;
import com.xt.hsk.module.marketing.convert.banner.BannerConvert;
import com.xt.hsk.module.marketing.dal.dataobject.banner.BannerDO;
import com.xt.hsk.module.marketing.enums.banner.BannerDisplayStatusEnum;
import com.xt.hsk.module.marketing.service.banner.BannerService;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * Banner APP应用管理 Manager，负责App端业务逻辑编排
 */
@Service
@Validated
public class BannerAppManager {

    @Resource
    private BannerService bannerService;

    /**
     * 获取指定页面的Banner列表
     *
     * @param page     页面类型
     * @param position 位置类型
     * @return Banner列表
     */
    public List<AppBannerRespVO> getBannerList(Integer page, Integer position) {
        LambdaQueryWrapper<BannerDO> queryWrapper = Wrappers.lambdaQuery(BannerDO.class)
            .eq(BannerDO::getPage, page)
            .eq(position != null, BannerDO::getPosition, position)
            .eq(BannerDO::getDisplayStatus, BannerDisplayStatusEnum.SHOW_NOW.getCode())
            .orderByAsc(BannerDO::getSort)
            .orderByDesc(BannerDO::getDisplayTime);

        List<BannerDO> bannerList = bannerService.list(queryWrapper);

        // 转换成App端VO
        return BannerConvert.INSTANCE.convertList2App(bannerList);
    }

    /**
     * 批量获取多个页面的Banner列表
     *
     * @param pageList 页面类型列表
     * @param position 位置类型
     * @return 按页面分组的Banner列表
     */
    public AppBannerPageListRespVO getBannerPageList(List<Integer> pageList, Integer position) {
        LambdaQueryWrapper<BannerDO> queryWrapper = Wrappers.lambdaQuery(BannerDO.class)
            .in(BannerDO::getPage, pageList)
            .eq(position != null, BannerDO::getPosition, position)
            .eq(BannerDO::getDisplayStatus, BannerDisplayStatusEnum.SHOW_NOW.getCode())
            .orderByAsc(BannerDO::getSort)
            .orderByDesc(BannerDO::getDisplayTime);

        List<BannerDO> bannerList = bannerService.list(queryWrapper);

        // 将Banner列表按页面类型分组
        Map<Integer, List<BannerDO>> bannerMap = bannerList.stream()
            .collect(Collectors.groupingBy(BannerDO::getPage));

        // 转换成响应VO
        AppBannerPageListRespVO respVO = new AppBannerPageListRespVO();
        Map<Integer, List<AppBannerRespVO>> resultMap = bannerMap.entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> BannerConvert.INSTANCE.convertList2App(entry.getValue())
            ));
        respVO.setBannerMap(resultMap);

        return respVO;
    }


} 