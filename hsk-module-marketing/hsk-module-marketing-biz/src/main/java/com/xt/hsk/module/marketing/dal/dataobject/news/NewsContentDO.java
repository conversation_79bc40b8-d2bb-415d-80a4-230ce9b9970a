package com.xt.hsk.module.marketing.dal.dataobject.news;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 资讯内容 DO
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@TableName("marketing_news_content")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NewsContentDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 资讯ID
     */
    private Long newsId;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 内容类型(1-文本/图片/视频 2-图片ALT)
     */
    private Integer contentType;
    /**
     * 内容_cn
     */
    private String contentCn;
    /**
     * 内容_en
     */
    private String contentEn;
    /**
     * 内容_ot
     */
    private String contentOt;
    /**
     * 上次翻译时间
     */
    private LocalDateTime lastTranslateTime;

}
