package com.xt.hsk.module.marketing.convert.route;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.marketing.controller.admin.route.vo.RouteConfigCreateReqVO;
import com.xt.hsk.module.marketing.controller.admin.route.vo.RouteConfigRespVO;
import com.xt.hsk.module.marketing.controller.admin.route.vo.RouteConfigUpdateReqVO;
import com.xt.hsk.module.marketing.dal.dataobject.banner.RouteConfigDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 路由配置转换器
 */
@Mapper
public interface RouteConfigConvert {

    RouteConfigConvert INSTANCE = Mappers.getMapper(RouteConfigConvert.class);

    /**
     * RouteConfigCreateReqVO 转换为 RouteConfigDO
     */
    RouteConfigDO convert(RouteConfigCreateReqVO bean);

    /**
     * RouteConfigUpdateReqVO 转换为 RouteConfigDO
     */
    RouteConfigDO convert(RouteConfigUpdateReqVO bean);

    /**
     * RouteConfigDO 转换为 RouteConfigRespVO
     */
    RouteConfigRespVO convert(RouteConfigDO bean);

    /**
     * RouteConfigDO列表 转换为 RouteConfigRespVO列表
     */
    List<RouteConfigRespVO> convertList(List<RouteConfigDO> list);

    /**
     * RouteConfigDO分页 转换为 RouteConfigRespVO分页
     */
    PageResult<RouteConfigRespVO> convertPage(PageResult<RouteConfigDO> page);
} 