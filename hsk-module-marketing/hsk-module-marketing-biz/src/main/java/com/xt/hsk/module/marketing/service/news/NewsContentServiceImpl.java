package com.xt.hsk.module.marketing.service.news;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsContentPageReqVO;
import com.xt.hsk.module.marketing.dal.dataobject.news.NewsContentDO;
import com.xt.hsk.module.marketing.dal.mysql.news.NewsContentMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 资讯内容 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class NewsContentServiceImpl extends ServiceImpl<NewsContentMapper, NewsContentDO> implements NewsContentService {

    @Resource
    private NewsContentMapper newsContentMapper;

    @Override
    public PageResult<NewsContentDO> selectPage(NewsContentPageReqVO pageReqVO) {
        return newsContentMapper.selectPage(pageReqVO);
    }

    @Override
    public void removeByNewsId(Long newsId) {
        log.info("删除资讯内容，newsId:{}", newsId);
        newsContentMapper.delete(new UpdateWrapper<NewsContentDO>().eq("news_id", newsId));
    }
}
