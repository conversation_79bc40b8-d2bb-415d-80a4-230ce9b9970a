package com.xt.hsk.module.marketing.dal.dataobject.coupon;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 优惠券模板 DO
 *
 * <AUTHOR>
 */
@TableName(value = "marketing_coupon_template", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CouponTemplateDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 优惠券名称-中文
     */
    private String couponNameCn;

    /**
     * 优惠券名称-英文
     */
    private String couponNameEn;

    /**
     * 优惠券名称-其他
     */
    private String couponNameOt;

    /**
     * 优惠券类型 1-领取 2-赠送
     */
    private Integer takeType;

    /**
     * 优惠券状态 1-未发放 2-领取中 3-已结束
     */
    private Integer status;

    /**
     * 优惠券码
     */
    private String couponCode;

    /**
     * 使用商品范围 1-精品课
     */
    private Integer productScope;

    /**
     * 生效的商品ID范围，空值或包含-1表示所有指定商品类型的商品都能够使用
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> productIds;

    /**
     * 折扣类型 1-无门槛 2-满减
     */
    private Integer discountType;

    /**
     * 无门槛优惠金额-中文，当discount_type=1时必填
     */
    private BigDecimal discountPriceCn;

    /**
     * 无门槛优惠金额-英文，当discount_type=1时必填
     */
    private BigDecimal discountPriceEn;

    /**
     * 无门槛优惠金额-其他，当discount_type=1时必填
     */
    private BigDecimal discountPriceOt;

    /**
     * 满减条件-中文，当discount_type=2时必填
     */
    private BigDecimal thresholdPriceCn;

    /**
     * 满减条件-英文，当discount_type=2时必填
     */
    private BigDecimal thresholdPriceEn;

    /**
     * 满减条件-其他，当discount_type=2时必填
     */
    private BigDecimal thresholdPriceOt;

    /**
     * 满减值-中文，当discount_type=2时必填
     */
    private BigDecimal reducePriceCn;

    /**
     * 满减值-英文，当discount_type=2时必填
     */
    private BigDecimal reducePriceEn;

    /**
     * 满减值-其他，当discount_type=2时必填
     */
    private BigDecimal reducePriceOt;

    /**
     * 发放数量
     */
    private Integer totalCount;

    /**
     * 每人限领数量
     */
    private Integer takeLimitCount;

    /**
     * 有效期类型 1-固定天数 2-固定时间段
     */
    private Integer validityType;

    /**
     * 有效期天数，当validity_type=1时必填
     */
    private Integer validityDays;

    /**
     * 有效时间-开始时间，当validity_type=2时必填
     */
    private LocalDateTime validStartTime;

    /**
     * 有效时间-结束时间，当validity_type=2时必填
     */
    private LocalDateTime validEndTime;

} 