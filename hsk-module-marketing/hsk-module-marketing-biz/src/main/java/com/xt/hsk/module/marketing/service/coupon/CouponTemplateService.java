package com.xt.hsk.module.marketing.service.coupon;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.marketing.dal.dataobject.coupon.CouponTemplateDO;
import java.util.List;

/**
 * 优惠券模板 Service 接口
 *
 * <AUTHOR>
 */
public interface CouponTemplateService extends IService<CouponTemplateDO> {

    /**
     * 获取指定商品可用的优惠券模板列表（待领取的优惠券模板）
     *
     * @param userId   用户ID，用于判断是否超过领取限制
     * @param courseId 商品ID
     * @return 优惠券模板列表
     */
    List<CouponTemplateDO> getAvailableCouponTemplates(Long userId, Long courseId);
} 