package com.xt.hsk.module.marketing.controller.admin.news.vo;

import com.xt.hsk.framework.common.validation.InEnum;
import com.xt.hsk.module.marketing.enums.news.NewsCategoryEnum;
import com.xt.hsk.module.marketing.enums.news.NewsShowStatus;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 新增资讯VO
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@Data
public class NewsSaveReqVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 资讯名称
     */
    @NotEmpty(message = "资讯名称不能为空")
    @Size(max = 30, message = "资讯名称长度不能超过30个字符")
    private String nameCn;

    /**
     * 资讯封面
     */
    @NotBlank(message = "请选择资讯封面")
    private String coverUrl;

    /**
     * 分类(1-考试资讯 2-备考指南)
     * @see NewsCategoryEnum
     */
    @InEnum(value = NewsCategoryEnum.class,message = "分类必须是 {value} 范围内")
    @NotNull(message = "请选择资讯分类")
    private Integer category;

    /**
     * 新闻来源
     */
    @Size(max = 10, message = "新闻来源长度不能超过10个字符")
    private String sourceCn;

    /**
     * 展示状态(1-展示 2-定时展示 3-隐藏)
     * @see NewsShowStatus
     */
    @InEnum(value = NewsShowStatus.class,message = "展示状态必须是 {value} 范围内")
    @NotNull(message = "请选择展示状态")
    private Integer showStatus;

    /**
     * 展示时间
     */
    private LocalDateTime showTime;

    /**
     * 是否精选资讯(0-否 1-是)
     */
    @NotNull(message = "是否精选资讯(0-否 1-是)不能为空")
    private Integer isFeatured;

    /**
     * 资讯内容列表
     */
    private List<NewsContentSaveReqVO> contentList;
}