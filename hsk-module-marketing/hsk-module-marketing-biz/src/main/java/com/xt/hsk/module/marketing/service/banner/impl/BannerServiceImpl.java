package com.xt.hsk.module.marketing.service.banner.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.marketing.controller.admin.banner.vo.BannerPageReqVO;
import com.xt.hsk.module.marketing.dal.dataobject.banner.BannerDO;
import com.xt.hsk.module.marketing.dal.mysql.banner.BannerMapper;
import com.xt.hsk.module.marketing.enums.banner.BannerDisplayStatusEnum;
import com.xt.hsk.module.marketing.service.banner.BannerService;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * Banner 服务实现类
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Slf4j
@Service
@Validated
public class BannerServiceImpl extends ServiceImpl<BannerMapper, BannerDO> implements
    BannerService {

    @Resource
    private BannerMapper bannerMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer jobUpperBanner() {
        log.info("执行Banner定时上架定时任务");

        // 更新定时显示的Banner 状态为定时展示 上架时间不能为空
        LambdaUpdateWrapper<BannerDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(BannerDO::getDisplayStatus, BannerDisplayStatusEnum.SHOW_NOW.getCode())
            .eq(BannerDO::getDisplayStatus, BannerDisplayStatusEnum.SHOW_SCHEDULED.getCode())
            .isNotNull(BannerDO::getDisplayTime)
            .le(BannerDO::getDisplayTime, LocalDateTime.now());
        return bannerMapper.update(updateWrapper);
    }

    @Override
    public PageResult<BannerDO> selectPage(BannerPageReqVO pageReqVO) {
        return bannerMapper.selectPage(pageReqVO);
    }
}