package com.xt.hsk.module.marketing.dal.dataobject.news;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import com.xt.hsk.module.marketing.enums.news.NewsShowStatus;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 资讯 DO
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@TableName("marketing_news")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NewsDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 资讯名称 Cn
     */
    private String nameCn;
    // /**
    //  * 资讯名称 EN
    //  */
    // private String nameEn;
    // /**
    //  * 资讯名称 OT
    //  */
    // private String nameOt;
    /**
     * 资讯封面
     */
    private String coverUrl;
    /**
     * 分类(1-考试资讯 2-备考指南)
     *
     */
    private Integer category;
    /**
     * 新闻来源
     */
    private String sourceCn;
    /**
     * 展示状态(1-展示 2-定时展示 3-隐藏)
     * @see NewsShowStatus
     */
    private Integer showStatus;
    /**
     * 展示时间
     */
    private LocalDateTime showTime;
    /**
     * 是否精选资讯(0-否 1-是)
     */
    private Integer isFeatured;
    /**
     * 展示点赞数
     */
    private Integer displayLikeCount;
    /**
     * 实际点赞数
     */
    private Integer actualLikeCount;
    /**
     * 展示分享数
     */
    private Integer displayShareCount;
    /**
     * 实际分享数
     */
    private Integer actualShareCount;
}
