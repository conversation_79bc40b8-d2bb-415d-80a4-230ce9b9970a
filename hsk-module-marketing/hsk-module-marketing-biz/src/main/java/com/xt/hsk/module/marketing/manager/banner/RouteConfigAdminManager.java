package com.xt.hsk.module.marketing.manager.banner;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.BAD_REQUEST;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;

import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.marketing.controller.admin.route.vo.RouteConfigCreateReqVO;
import com.xt.hsk.module.marketing.controller.admin.route.vo.RouteConfigPageReqVO;
import com.xt.hsk.module.marketing.controller.admin.route.vo.RouteConfigUpdateReqVO;
import com.xt.hsk.module.marketing.convert.route.RouteConfigConvert;
import com.xt.hsk.module.marketing.dal.dataobject.banner.RouteConfigDO;
import com.xt.hsk.module.marketing.dal.mysql.banner.RouteConfigMapper;
import com.xt.hsk.module.marketing.service.route.RouteConfigService;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 路由配置后台管理 Manager，负责业务逻辑编排
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@Service
@Validated
public class RouteConfigAdminManager {

    @Resource
    private RouteConfigService routeConfigService;

    @Resource
    private RouteConfigMapper routeConfigMapper;

    /**
     * 创建路由配置
     *
     * @param createReqVO 创建信息
     * @return 路由配置ID
     */
    public Long createRouteConfig(RouteConfigCreateReqVO createReqVO) {
        // 插入
        RouteConfigDO routeConfig = RouteConfigConvert.INSTANCE.convert(createReqVO);
        routeConfigService.save(routeConfig);

        // 添加操作日志上下文
        LogRecordContext.putVariable("route", routeConfig);

        // 返回
        return routeConfig.getId();
    }

    /**
     * 更新路由配置
     *
     * @param updateReqVO 更新信息
     */
    public void updateRouteConfig(RouteConfigUpdateReqVO updateReqVO) {
        // 校验存在
        validateRouteConfigExists(updateReqVO.getId());

        // 更新
        RouteConfigDO updateObj = RouteConfigConvert.INSTANCE.convert(updateReqVO);
        routeConfigService.updateById(updateObj);

        // 添加操作日志上下文
        RouteConfigDO newRouteConfig = routeConfigService.getById(updateReqVO.getId());
        LogRecordContext.putVariable("route", newRouteConfig);
    }

    /**
     * 删除路由配置
     *
     * @param id 路由配置ID
     */
    public void deleteRouteConfig(Long id) {
        // 校验存在
        RouteConfigDO routeConfig = validateRouteConfigExists(id);

        // 添加操作日志上下文
        LogRecordContext.putVariable("route", routeConfig);

        // 删除
        routeConfigService.removeById(id);
    }

    /**
     * 获得路由配置
     *
     * @param id 路由配置ID
     * @return 路由配置
     */
    public RouteConfigDO getRouteConfig(Long id) {
        return routeConfigService.getById(id);
    }

    /**
     * 分页获取路由配置列表
     *
     * @param pageReqVO 分页查询
     * @return 路由配置分页
     */
    public PageResult<RouteConfigDO> getRouteConfigPage(RouteConfigPageReqVO pageReqVO) {
        // 分页查询
        List<RouteConfigDO> list = routeConfigMapper.selectPage(pageReqVO);
        // 获得总数
        Long total = routeConfigMapper.selectCount();
        // 返回
        return new PageResult<>(list, total);
    }

    private RouteConfigDO validateRouteConfigExists(Long id) {
        RouteConfigDO routeConfig = routeConfigService.getById(id);
        if (routeConfig == null) {
            throw exception(BAD_REQUEST, "路由配置不存在");
        }
        return routeConfig;
    }
} 