package com.xt.hsk.module.marketing.dal.mysql.news;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsPageReqVO;
import com.xt.hsk.module.marketing.dal.dataobject.news.NewsDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 资讯 Mapper
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@Mapper
public interface NewsMapper extends BaseMapperX<NewsDO> {

    default PageResult<NewsDO> selectPage(NewsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<NewsDO>()
            .likeIfPresent(NewsDO::getNameCn, reqVO.getName())
            .eqIfPresent(NewsDO::getCoverUrl, reqVO.getCoverUrl())
            .eqIfPresent(NewsDO::getCategory, reqVO.getCategory())
            .eqIfPresent(NewsDO::getSourceCn, reqVO.getSource())
            .eqIfPresent(NewsDO::getShowStatus, reqVO.getShowStatus())
            .betweenIfPresent(NewsDO::getShowTime, reqVO.getShowTime())
            .eqIfPresent(NewsDO::getIsFeatured, reqVO.getIsFeatured())
            .eqIfPresent(NewsDO::getDisplayLikeCount, reqVO.getDisplayLikeCount())
            .eqIfPresent(NewsDO::getActualLikeCount, reqVO.getActualLikeCount())
            .eqIfPresent(NewsDO::getDisplayShareCount, reqVO.getDisplayShareCount())
            .eqIfPresent(NewsDO::getActualShareCount, reqVO.getActualShareCount())
            .betweenIfPresent(NewsDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(NewsDO::getId));
    }

}
