package com.xt.hsk.module.marketing.dal.dataobject.coupon;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 优惠券记录 DO
 *
 * <AUTHOR>
 */
@TableName("marketing_coupon_record")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CouponRecordDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 优惠券模板ID
     */
    private Long couponTemplateId;

    /**
     * 操作类型 1-领取 2-使用 3-过期 4-撤销 5-赠送 6-退款返还
     */
    private Integer operationType;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * 操作前状态
     */
    private Integer previousStatus;

    /**
     * 操作后状态
     */
    private Integer currentStatus;

    /**
     * 关联订单ID
     */
    private Long orderId;

    /**
     * 关联订单编号
     */
    private String orderNumber;

    /**
     * 关联退款单ID
     */
    private Long refundId;

    /**
     * 关联退款单编号
     */
    private String refundNumber;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作IP
     */
    private String operationIp;

    /**
     * 操作备注
     */
    private String operationNote;
} 