package com.xt.hsk.module.marketing.job;

import com.baomidou.lock.annotation.Lock4j;
import com.xt.hsk.framework.quartz.core.handler.JobHandler;
import com.xt.hsk.module.marketing.service.banner.BannerService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * banner 定时任务
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Slf4j
@Component
public class BannerJob implements JobHandler {

    @Resource
    private BannerService bannerService;

    @Override
    @Lock4j(name = "bannerRelease")
    public String execute(String param) throws Exception {
        // 执行任务
        Integer updateCount = bannerService.jobUpperBanner();
        // 返回结果 记录更新成功的数量
        return String.format("更新%s条banner数据", updateCount);
    }
}
