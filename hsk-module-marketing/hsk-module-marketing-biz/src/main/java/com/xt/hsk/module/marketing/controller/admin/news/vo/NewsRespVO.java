package com.xt.hsk.module.marketing.controller.admin.news.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.xt.hsk.module.marketing.enums.news.NewsCategoryEnum;
import com.xt.hsk.module.marketing.enums.news.NewsShowStatus;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 资讯 Response VO
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@Data
public class NewsRespVO implements VO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 资讯名称
     */
    private String name;

    /**
     * 资讯封面
     */
    private String coverUrl;

    /**
     * 分类(1-考试资讯 2-备考指南)
     * @see NewsCategoryEnum
     */
    private Integer category;
    /**
     * 资讯分类描述
     */
    private String categoryDesc;

    /**
     * 新闻来源
     */
    private String source;

    /**
     * 展示状态(1-展示 2-隐藏 3-待展示)
     * @see NewsShowStatus
     */
    private Integer showStatus;

    /**
     * 展示状态描述
     */
    private String showStatusDesc;

    /**
     * 展示时间
     */
    private LocalDateTime showTime;

    /**
     * 是否精选资讯(0-否 1-是)
     */
    private Integer isFeatured;

    /**
     * 展示点赞数
     */
    private Integer displayLikeCount;

    /**
     * 实际点赞数
     */
    private Integer actualLikeCount;

    /**
     * 展示分享数
     */
    private Integer displayShareCount;

    /**
     * 实际分享数
     */
    private Integer actualShareCount;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    @Trans(type = TransType.SIMPLE, targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO", fields = "nickname", ref = "creatorName")
    private String creator;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 最近更新人ID
     */
    @Trans(type = TransType.SIMPLE, targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO", fields = "nickname", ref = "updaterName")
    private String updater;

    /**
     * 更新人名称
     */
    private String updaterName;


}
