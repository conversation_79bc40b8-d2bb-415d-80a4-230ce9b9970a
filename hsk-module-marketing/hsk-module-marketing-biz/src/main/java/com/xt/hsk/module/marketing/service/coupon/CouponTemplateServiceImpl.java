package com.xt.hsk.module.marketing.service.coupon;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.module.marketing.dal.dataobject.coupon.CouponDO;
import com.xt.hsk.module.marketing.dal.dataobject.coupon.CouponTemplateDO;
import com.xt.hsk.module.marketing.dal.mysql.coupon.CouponMapper;
import com.xt.hsk.module.marketing.dal.mysql.coupon.CouponTemplateMapper;
import com.xt.hsk.module.marketing.enums.coupon.CouponTakeTypeEnum;
import com.xt.hsk.module.marketing.enums.coupon.CouponTemplateStatusEnum;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 优惠券模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CouponTemplateServiceImpl extends
    ServiceImpl<CouponTemplateMapper, CouponTemplateDO> implements CouponTemplateService {

    @Resource
    private CouponTemplateMapper couponTemplateMapper;

    @Resource
    private CouponMapper couponMapper;

    @Override
    public List<CouponTemplateDO> getAvailableCouponTemplates(Long userId, Long courseId) {

        // 查询所有可领取的优惠券模板
        LambdaQueryWrapper<CouponTemplateDO> queryWrapper = new LambdaQueryWrapper<CouponTemplateDO>()
            // 领取类型
            .eq(CouponTemplateDO::getTakeType, CouponTakeTypeEnum.TAKE.code)
            // 领取中状态
            .eq(CouponTemplateDO::getStatus, CouponTemplateStatusEnum.TAKING.code)
            // 已开始
            .le(CouponTemplateDO::getValidStartTime, LocalDateTime.now())
            // 未结束
            .ge(CouponTemplateDO::getValidEndTime, LocalDateTime.now());

        // 如果有课程ID，筛选可用于该课程的优惠券模板
        if (courseId != null) {
            queryWrapper.and(wrapper ->
                wrapper.isNull(CouponTemplateDO::getProductIds)
                    .or()
                    .apply("JSON_CONTAINS(product_ids, '-1')")
                    .or()
                    .apply("JSON_CONTAINS(product_ids, '" + courseId + "')")
            );
        }

        List<CouponTemplateDO> templates = couponTemplateMapper.selectList(queryWrapper);
        if (templates.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取所有有领取限制的模板ID
        List<Long> templateIdsWithLimit = templates.stream()
            .filter(template -> template.getTakeLimitCount() != null
                && template.getTakeLimitCount() > 0)
            .map(CouponTemplateDO::getId)
            .toList();

        // 如果没有需要检查领取限制的模板，直接返回所有模板
        if (templateIdsWithLimit.isEmpty()) {
            return templates;
        }

        // 批量查询用户已领取的各模板的优惠券数量
        if (userId == null) {
            return templates;
        }
        Map<Long, Long> templateIdToCountMap = getUserCouponCountMap(userId, templateIdsWithLimit);

        // 在内存中过滤超过领取限制的优惠券模板
        return templates.stream()
            .filter(template -> {
                // 无领取限制的直接通过
                if (template.getTakeLimitCount() == null || template.getTakeLimitCount() <= 0) {
                    return true;
                }
                // 获取已领取数量，不存在则为0
                Long count = templateIdToCountMap.getOrDefault(template.getId(), 0L);
                // 未超过限制才返回
                return count < template.getTakeLimitCount();
            })
            .toList();
    }

    /**
     * 批量查询用户已领取的各模板的优惠券数量
     *
     * @param userId      用户ID
     * @param templateIds 模板ID列表
     * @return 模板ID到已领取数量的映射
     */
    private Map<Long, Long> getUserCouponCountMap(Long userId, List<Long> templateIds) {
        if (templateIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 批量查询用户已领取的所有优惠券
        List<CouponDO> userCoupons = couponMapper.selectList(new LambdaQueryWrapper<CouponDO>()
            .eq(CouponDO::getUserId, userId)
            .in(CouponDO::getCouponTemplateId, templateIds));

        // 统计每个模板的领取数量
        return userCoupons.stream()
            .collect(Collectors.groupingBy(
                CouponDO::getCouponTemplateId,
                Collectors.counting()
            ));
    }
} 