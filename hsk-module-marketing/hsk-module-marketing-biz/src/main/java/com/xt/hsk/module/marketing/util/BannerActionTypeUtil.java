package com.xt.hsk.module.marketing.util;

import com.xt.hsk.module.marketing.dal.dataobject.banner.BannerDO;
import com.xt.hsk.module.marketing.enums.banner.BannerActionTypeEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * Banner动作类型工具类 用于动态计算Banner的动作类型
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
public class BannerActionTypeUtil {

    /**
     * 根据Banner数据计算动作类型
     *
     * @param banner BannerDO对象
     * @return 动作类型枚举值
     */
    public static Integer calculateActionType(BannerDO banner) {
        if (banner == null) {
            return BannerActionTypeEnum.NONE.getCode();
        }

        // 判断显示图片
        if (StringUtils.isNotBlank(banner.getJumpImageUrl())) {
            return BannerActionTypeEnum.SHOW_IMAGE.getCode();
        }

        // 判断外部链接
        if (StringUtils.isNotBlank(banner.getJumpLinkUrl())) {
            return BannerActionTypeEnum.EXTERNAL_LINK.getCode();
        }

        // 判断APP内页面
        if (banner.getJumpRouteId() != null) {
            return BannerActionTypeEnum.APP_ROUTE.getCode();
        }

        // 判断打开弹窗
        if (StringUtils.isNotBlank(banner.getJumpPopup())) {
            return BannerActionTypeEnum.POPUP.getCode();
        }

        // 默认为无动作
        return BannerActionTypeEnum.NONE.getCode();
    }
} 