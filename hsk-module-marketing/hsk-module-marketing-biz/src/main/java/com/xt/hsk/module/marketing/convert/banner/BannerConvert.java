package com.xt.hsk.module.marketing.convert.banner;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.marketing.controller.admin.banner.vo.BannerCreateReqVO;
import com.xt.hsk.module.marketing.controller.admin.banner.vo.BannerRespVO;
import com.xt.hsk.module.marketing.controller.admin.banner.vo.BannerUpdateReqVO;
import com.xt.hsk.module.marketing.controller.app.banner.vo.AppBannerRespVO;
import com.xt.hsk.module.marketing.dal.dataobject.banner.BannerDO;
import com.xt.hsk.module.marketing.util.BannerActionTypeUtil;
import java.util.List;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * Banner 转换器
 */
@Mapper
public interface BannerConvert {

    BannerConvert INSTANCE = Mappers.getMapper(BannerConvert.class);

    /**
     * BannerCreateReqVO 转换为 BannerDO
     */
    BannerDO convert(BannerCreateReqVO bean);

    /**
     * BannerUpdateReqVO 转换为 BannerDO
     */
    BannerDO convert(BannerUpdateReqVO bean);

    /**
     * BannerDO 转换为 BannerRespVO
     */
    BannerRespVO convert(BannerDO bean);

    /**
     * BannerDO列表 转换为 BannerRespVO列表
     */
    List<BannerRespVO> convertList(List<BannerDO> list);

    /**
     * BannerDO分页 转换为 BannerRespVO分页
     */
    PageResult<BannerRespVO> convertPage(PageResult<BannerDO> page);

    /**
     * BannerDO 转换为 AppBannerRespVO
     */
    AppBannerRespVO convert2App(BannerDO bean);

    /**
     * 在转换完成后，设置动态计算的actionType
     */
    @AfterMapping
    default void setActionType(BannerDO bean, @MappingTarget AppBannerRespVO result) {
        if (bean != null && result != null) {
            // 动态计算actionType
            result.setActionType(BannerActionTypeUtil.calculateActionType(bean));
        }
    }

    /**
     * BannerDO列表 转换为 AppBannerRespVO列表
     */
    List<AppBannerRespVO> convertList2App(List<BannerDO> list);
} 