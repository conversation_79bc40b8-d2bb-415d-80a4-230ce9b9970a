package com.xt.hsk.module.marketing.convert.news;

import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsContentPageRespVO;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsContentSaveOrUpdateVO;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsContentSaveReqVO;
import com.xt.hsk.module.marketing.dal.dataobject.news.NewsContentDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 资讯内容转换
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@Mapper
public interface NewsContentConvert {

    NewsContentConvert INSTANCE = Mappers.getMapper(NewsContentConvert.class);

    /**
     * 创建资讯内容
     */
    NewsContentDO convert(NewsContentSaveReqVO bean);
    /**
     * 更新或者创建资讯内容
     */
    NewsContentDO convert(NewsContentSaveOrUpdateVO saveOrUpdateVO);
    /**
     * 批量创建资讯内容
     */
    List<NewsContentDO> convert(List<NewsContentSaveReqVO> bean);
    /**
     * 获取资讯内容分页
     */
    List<NewsContentPageRespVO> convertList(List<NewsContentDO> list);
}
