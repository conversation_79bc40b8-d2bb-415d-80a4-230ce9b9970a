package com.xt.hsk.module.marketing.dal.dataobject.banner;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * Banner实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "marketing_banner", autoResultMap = true)
public class BannerDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * Banner名称
     */
    private String bannerName;

    /**
     * 页面 1-练习 2-题型练习列表页 3-课程 4-我的
     */
    private Integer page;

    /**
     * 所属位置 1-顶部 2中部 3-底部
     */
    private Integer position;

    /**
     * 轮播图地址
     */
    private String imageUrl;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 点击显示图片地址
     */
    private String jumpImageUrl;

    /**
     * 外部链接地址
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String jumpLinkUrl;

    /**
     * 关联的页面路由ID
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer jumpRouteId;

    /**
     * 路由参数值，例如：{"course_id": 12345}
     */
    @TableField(typeHandler = JacksonTypeHandler.class,updateStrategy = FieldStrategy.ALWAYS)
    private Object jumpRouteParams;

    /**
     * 弹窗内容
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String jumpPopup;

    /**
     * 展示状态：1-立即展示, 2-定时展示, 3-隐藏
     */
    private Integer displayStatus;

    /**
     * 展示时间 display_status = 2必填
     */
    private LocalDateTime displayTime;
} 