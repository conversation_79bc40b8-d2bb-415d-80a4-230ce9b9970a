package com.xt.hsk.module.marketing.service.banner;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.marketing.controller.admin.banner.vo.BannerPageReqVO;
import com.xt.hsk.module.marketing.dal.dataobject.banner.BannerDO;

/**
 * Banner 服务接口
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
public interface BannerService extends IService<BannerDO> {

    /**
     * 定时任务上架banner
     */
    Integer jobUpperBanner();

    /**
     * 分页查询
     *
     * @param pageReqVO 查询条件
     * @return 结果
     */
    PageResult<BannerDO> selectPage(BannerPageReqVO pageReqVO);
}