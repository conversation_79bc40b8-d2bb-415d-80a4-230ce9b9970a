package com.xt.hsk.module.marketing.controller.admin.news.vo;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import com.xt.hsk.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NewsPageReqVO extends PageParam {

    /**
     * 资讯名称
     */
    private String name;

    /**
     * 资讯封面
     */
    private String coverUrl;

    /**
     * 分类(1-考试资讯 2-备考指南)
     */
    private Integer category;

    /**
     * 新闻来源
     */
    private String source;

    /**
     * 展示状态(1-展示 2-隐藏 3-待展示)
     */
    private Integer showStatus;

    /**
     * 展示时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] showTime;

    /**
     * 是否精选资讯(0-否 1-是)
     */
    private Integer isFeatured;

    /**
     * 展示点赞数
     */
    private Integer displayLikeCount;

    /**
     * 实际点赞数
     */
    private Integer actualLikeCount;

    /**
     * 展示分享数
     */
    private Integer displayShareCount;

    /**
     * 实际分享数
     */
    private Integer actualShareCount;

    /**
     * 排序权重
     */
    private Integer sortWeight;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
