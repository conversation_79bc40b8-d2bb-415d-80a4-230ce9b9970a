package com.xt.hsk.module.marketing.service.coupon;

import static com.xt.hsk.module.marketing.enums.ErrorCodeConstants.COUPON_TAKE_LIMIT_EXCEEDED;
import static com.xt.hsk.module.marketing.enums.ErrorCodeConstants.COUPON_TEMPLATE_NOT_EXISTS;
import static com.xt.hsk.module.marketing.enums.ErrorCodeConstants.COUPON_TOTAL_COUNT_EXCEEDED;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.exception.ServiceException;
import com.xt.hsk.module.marketing.dal.dataobject.coupon.CouponDO;
import com.xt.hsk.module.marketing.dal.dataobject.coupon.CouponRecordDO;
import com.xt.hsk.module.marketing.dal.dataobject.coupon.CouponTemplateDO;
import com.xt.hsk.module.marketing.dal.mysql.coupon.CouponMapper;
import com.xt.hsk.module.marketing.dal.mysql.coupon.CouponRecordMapper;
import com.xt.hsk.module.marketing.dal.mysql.coupon.CouponTemplateMapper;
import com.xt.hsk.module.marketing.enums.coupon.CouponStatusEnum;
import com.xt.hsk.module.marketing.enums.coupon.CouponTakeTypeEnum;
import com.xt.hsk.module.marketing.enums.coupon.OperationTypeEnum;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 优惠券 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CouponServiceImpl extends
    ServiceImpl<CouponMapper, CouponDO> implements CouponService {

    @Resource
    private CouponTemplateMapper couponTemplateMapper;

    @Resource
    private CouponRecordMapper couponRecordMapper;

    @Override
    public List<CouponDO> getUserAvailableCoupons(Long userId, Long courseId) {
        if (userId == null) {
            return Collections.emptyList();
        }

        // 查询用户未使用的优惠券
        LambdaQueryWrapper<CouponDO> queryWrapper = new LambdaQueryWrapper<CouponDO>()
            .eq(CouponDO::getUserId, userId)
            .eq(CouponDO::getStatus, CouponStatusEnum.UNUSED.getCode())
            // 未撤销
            .eq(CouponDO::getRevokeStatus, 0)
            .le(CouponDO::getStartTime, LocalDateTime.now())
            .ge(CouponDO::getEndTime, LocalDateTime.now());

        // 如果有课程ID，筛选可用于该课程的优惠券
        if (courseId != null) {
            queryWrapper.and(wrapper ->
                wrapper.isNull(CouponDO::getUsedProductIds)
                    .or()
                    .apply("JSON_CONTAINS(used_product_ids, '\"-1\"')")
                    .or()
                    .apply("JSON_CONTAINS(used_product_ids, '\"" + courseId + "\"')")
            );
        }

        return baseMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long takeCoupon(Long userId, Long couponTemplateId) {
        // 1. 校验优惠券模板是否存在
        CouponTemplateDO template = couponTemplateMapper.selectById(couponTemplateId);
        if (template == null) {
            throw new ServiceException(COUPON_TEMPLATE_NOT_EXISTS);
        }

        // 2. 校验优惠券是否已发放完毕
        if (template.getTotalCount() <= countByCouponTemplateId(couponTemplateId)) {
            throw new ServiceException(COUPON_TOTAL_COUNT_EXCEEDED);
        }

        // 3. 校验用户是否超出领取限制
        if (template.getTakeLimitCount() != null && template.getTakeLimitCount() > 0) {
            long count = lambdaQuery()
                .eq(CouponDO::getUserId, userId)
                .eq(CouponDO::getCouponTemplateId, couponTemplateId)
                .count();
            if (count >= template.getTakeLimitCount()) {
                throw new ServiceException(COUPON_TAKE_LIMIT_EXCEEDED);
            }
        }

        // 4. 创建优惠券
        CouponDO coupon = createCoupon(userId, template);

        // 5. 创建领取记录
        createCouponRecord(coupon, OperationTypeEnum.TAKE.code);

        return coupon.getId();
    }

    /**
     * 统计优惠券模板下发放的优惠券数量
     *
     * @param couponTemplateId 优惠券模板ID
     * @return 优惠券数量
     */
    private long countByCouponTemplateId(Long couponTemplateId) {
        return lambdaQuery()
            .eq(CouponDO::getCouponTemplateId, couponTemplateId)
            .count();
    }

    /**
     * 创建优惠券
     *
     * @param userId   用户ID
     * @param template 优惠券模板
     * @return 创建的优惠券
     */
    private CouponDO createCoupon(Long userId, CouponTemplateDO template) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = now;
        LocalDateTime endTime;

        // 计算有效期
        if (template.getValidityType() == 1) {
            // 固定天数
            endTime = now.plusDays(template.getValidityDays());
        } else {
            // 固定时间段
            startTime = template.getValidStartTime();
            endTime = template.getValidEndTime();
        }

        // 创建优惠券
        CouponDO coupon = new CouponDO();
        coupon.setUserId(userId);
        coupon.setCouponTemplateId(template.getId());
        coupon.setStatus(CouponStatusEnum.UNUSED.code);
        coupon.setCouponNameCn(template.getCouponNameCn());
        coupon.setCouponNameEn(template.getCouponNameEn());
        coupon.setCouponNameOt(template.getCouponNameOt());
        coupon.setCouponCode(template.getCouponCode());
        coupon.setTakeType(CouponTakeTypeEnum.TAKE.code);
        coupon.setRevokeStatus(0);
        coupon.setStartTime(startTime);
        coupon.setEndTime(endTime);

        baseMapper.insert(coupon);
        return coupon;
    }

    /**
     * 创建优惠券操作记录
     *
     * @param coupon        优惠券
     * @param operationType 操作类型
     */
    private void createCouponRecord(CouponDO coupon, Integer operationType) {
        CouponRecordDO record = new CouponRecordDO();
        record.setCouponId(coupon.getId());
        record.setUserId(coupon.getUserId());
        record.setCouponTemplateId(coupon.getCouponTemplateId());
        record.setOperationType(operationType);
        record.setOperationTime(LocalDateTime.now());
        // 新领取的优惠券没有之前的状态
        record.setPreviousStatus(null);
        record.setCurrentStatus(coupon.getStatus());
        couponRecordMapper.insert(record);
    }
} 