package com.xt.hsk.module.marketing.controller.admin.news.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 资讯内容分页查询类
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NewsContentPageReqVO extends PageParam {
    /**
     * 资讯ID
     */
    private Long newsId;
}
