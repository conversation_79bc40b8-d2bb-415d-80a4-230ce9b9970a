package com.xt.hsk.module.marketing.controller.app.banner.vo;

import com.xt.hsk.module.marketing.enums.banner.BannerActionTypeEnum;
import lombok.Data;

/**
 * App Banner 响应VO，用于返回给App端的数据
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
@Data
public class AppBannerRespVO {

    /**
     * Banner编号
     */
    private Long id;

    /**
     * Banner名称
     */
    private String bannerName;

    /**
     * 轮播图地址
     */
    private String imageUrl;

    /**
     * 点击动作类型 - 见BannerActionTypeEnum枚举 
     * 1-无动作（无跳转）
     * 2-显示图片（查看大图）
     * 3-外部链接（跳转H5页面）
     * 4-APP内页面（跳转APP内部页面）
     * 5-打开弹窗（显示弹窗内容）
     * <p>
     * 该字段值是根据填写的跳转字段动态计算的，方便APP端直接判断处理动作
     * 此字段不保存在数据库中，仅在查询时计算返回
     * @see BannerActionTypeEnum
     */
    private Integer actionType;

    /**
     * 点击显示图片地址 当actionType=2时有值
     */
    private String jumpImageUrl;

    /**
     * 外部链接地址 当actionType=3时有值
     */
    private String jumpLinkUrl;

    /**
     * 关联的页面路由ID 当actionType=4时有值
     */
    private Integer jumpRouteId;

    /**
     * 路由参数值 当actionType=4时有值
     */
    private Object jumpRouteParams;

    /**
     * 弹窗内容 当actionType=5时有值
     */
    private String jumpPopup;
} 