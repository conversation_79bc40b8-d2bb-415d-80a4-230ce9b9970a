package com.xt.hsk.module.marketing.dal.mysql.news;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsContentPageReqVO;
import com.xt.hsk.module.marketing.dal.dataobject.news.NewsContentDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 资讯内容 Mapper
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@Mapper
public interface NewsContentMapper extends BaseMapperX<NewsContentDO> {

    default PageResult<NewsContentDO> selectPage(NewsContentPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<NewsContentDO>()
            .eqIfPresent(NewsContentDO::getNewsId, reqVO.getNewsId())
            .orderByAsc(NewsContentDO::getId));
    }

}
