package com.xt.hsk.module.marketing.controller.app.coupon;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.StpUtil;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.marketing.controller.app.coupon.vo.CouponListReqVO;
import com.xt.hsk.module.marketing.controller.app.coupon.vo.CouponVO;
import com.xt.hsk.module.marketing.controller.app.coupon.vo.TakeCouponReqVO;
import com.xt.hsk.module.marketing.manager.coupon.AppCouponManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * APP - 优惠券
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/marketing/coupon")
@Validated
@Slf4j
public class AppCouponController {

    @Resource
    private AppCouponManager appCouponManager;
    
    /**
     * 根据精品课查询优惠券列表
     */
    @SaIgnore
    @PostMapping("/v1/getCouponListByEliteCourseId")
    public CommonResult<List<CouponVO>> getCouponListByEliteCourseId(
        @RequestBody @Valid CouponListReqVO reqVO) {
        return appCouponManager.getCouponListByEliteCourseId(reqVO);
    }

    /**
     * 领取优惠券
     */
    @PostMapping("/v1/take")
    public CommonResult<Long> takeCoupon(@Valid @RequestBody TakeCouponReqVO reqVO) {
        return CommonResult.success(
            appCouponManager.takeCoupon(StpUtil.getLoginIdAsLong(), reqVO.getCouponTemplateId()));
    }
}