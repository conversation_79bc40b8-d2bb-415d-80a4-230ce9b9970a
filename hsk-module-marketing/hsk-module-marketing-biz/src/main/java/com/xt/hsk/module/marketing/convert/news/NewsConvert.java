package com.xt.hsk.module.marketing.convert.news;

import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsRespVO;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsSaveReqVO;
import com.xt.hsk.module.marketing.dal.dataobject.news.NewsDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 资讯转换
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@Mapper
public interface NewsConvert {

    NewsConvert INSTANCE = Mappers.getMapper(NewsConvert.class);
    /**
     * 新增参数转DO
     */
    NewsDO convert(NewsSaveReqVO bean);

    /**
     * DO转响应结果
     */
    NewsRespVO convert(NewsDO bean);

    /**
     * DO转响应结果 列表
     */
    List<NewsRespVO> convertList(List<NewsDO> list);
}
