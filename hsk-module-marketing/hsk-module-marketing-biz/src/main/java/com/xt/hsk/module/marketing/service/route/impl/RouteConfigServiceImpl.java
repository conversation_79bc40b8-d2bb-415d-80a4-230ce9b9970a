package com.xt.hsk.module.marketing.service.route.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.module.marketing.dal.dataobject.banner.RouteConfigDO;
import com.xt.hsk.module.marketing.dal.mysql.banner.RouteConfigMapper;
import com.xt.hsk.module.marketing.service.route.RouteConfigService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 路由配置 Service 实现类
 */
@Service
@Validated
public class RouteConfigServiceImpl extends ServiceImpl<RouteConfigMapper, RouteConfigDO> implements
    RouteConfigService {

} 