package com.xt.hsk.module.marketing.controller.admin.news.vo;

import com.xt.hsk.framework.common.validation.InEnum;
import com.xt.hsk.module.marketing.enums.news.NewsContentTypeEnum;
import lombok.Data;

/**
 * 资讯内容保存参数
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@Data
public class NewsContentSaveReqVO {

    /**
     * 内容类型  1：富文本 2：图片
     */
    @InEnum(value = NewsContentTypeEnum.class,message = "内容类型必须是 {value} 范围内")
    private Integer contentType;
    /**
     * 内容_cn
     */
    private String contentCn;
    /**
     * 内容_en
     */
    private String contentEn;
    /**
     * 内容_ot
     */
    private String contentOt;
}
