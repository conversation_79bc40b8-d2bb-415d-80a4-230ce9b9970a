package com.xt.hsk.module.marketing.manager.banner;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.marketing.enums.ErrorCodeConstants.BANNER_NOT_EXISTS;

import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.marketing.controller.admin.banner.vo.BannerBaseVO;
import com.xt.hsk.module.marketing.controller.admin.banner.vo.BannerCreateReqVO;
import com.xt.hsk.module.marketing.controller.admin.banner.vo.BannerPageReqVO;
import com.xt.hsk.module.marketing.controller.admin.banner.vo.BannerRespVO;
import com.xt.hsk.module.marketing.controller.admin.banner.vo.BannerUpdateReqVO;
import com.xt.hsk.module.marketing.convert.banner.BannerConvert;
import com.xt.hsk.module.marketing.dal.dataobject.banner.BannerDO;
import com.xt.hsk.module.marketing.enums.banner.BannerDisplayStatusEnum;
import com.xt.hsk.module.marketing.service.banner.BannerService;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * Banner 后台管理 Manager，负责业务逻辑编排
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
@Service
@Validated
public class BannerAdminManager {

    @Resource
    private BannerService bannerService;


    /**
     * 创建Banner
     * 新增时默认排序为1，不可为负数、0
     * 在相同页面+位置下，如果输入的banner序号和之前的序号重复，则小于该序号的序号不变，后续的序号各+1
     *
     * @param createReqVO 创建信息
     * @return Banner编号
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createBanner(BannerCreateReqVO createReqVO) {
        // 插入
        BannerDO banner = BannerConvert.INSTANCE.convert(createReqVO);
        
        // 设置默认排序为1，如果未设置或者小于等于0
        Integer sort = banner.getSort();
        if (sort == null || sort <= 0) {
            sort = 1;
            banner.setSort(sort);
        }
        
        // 处理排序逻辑：如果序号重复，则大于等于该序号的都要加1
        adjustSortForCreate(banner.getPage(), banner.getPosition(), sort);

        // 发布时间规则
        banner.setDisplayTime(getBannerDisplayTime(createReqVO));
        
        bannerService.save(banner);

        // 添加操作日志上下文
        LogRecordContext.putVariable("banner", banner);

        // 返回
        return banner.getId();
    }

    /**
     * 根据展示状态变更规则获取Banner的展示时间
     *
     * @param oldBanner 原始Banner对象
     * @param updateReqVO 更新请求
     * @return 最终的展示时间
     */
    private LocalDateTime getBannerDisplayTimeForUpdate(BannerDO oldBanner, BannerUpdateReqVO updateReqVO) {
        Integer oldStatus = oldBanner.getDisplayStatus();
        Integer newStatus = updateReqVO.getDisplayStatus();

        // 如果状态没有变更，保持原有发布时间
        if (oldStatus.equals(newStatus)) {
            return oldBanner.getDisplayTime();
        }

        // 状态变更情况处理
        // 1. 从【立即发布】变为【定时发布】：使用设定的Banner发布时间
        if (BannerDisplayStatusEnum.SHOW_NOW.getCode().equals(oldStatus)
            && BannerDisplayStatusEnum.SHOW_SCHEDULED.getCode().equals(newStatus)) {
            return updateReqVO.getDisplayTime();
        }

        // 2. 从【暂不发布】变为【定时发布】：使用设定的Banner发布时间
        if (BannerDisplayStatusEnum.HIDDEN.getCode().equals(oldStatus)
            && BannerDisplayStatusEnum.SHOW_SCHEDULED.getCode().equals(newStatus)) {
            return updateReqVO.getDisplayTime();
        }

        // 3. 其他所有状态变更情况：保持原有发布时间不变
        return oldBanner.getDisplayTime();
    }


    private LocalDateTime getBannerDisplayTime(BannerBaseVO createReqVO) {
        if (BannerDisplayStatusEnum.SHOW_NOW.getCode().equals(createReqVO.getDisplayStatus())) {
            return LocalDateTime.now();
        }
        if (BannerDisplayStatusEnum.SHOW_SCHEDULED.getCode().equals(createReqVO.getDisplayStatus())) {
            return createReqVO.getDisplayTime();
        }
        if (BannerDisplayStatusEnum.HIDDEN.getCode().equals(createReqVO.getDisplayStatus())) {
            return null;
        }
        return null;
    }

    /**
     * 更新Banner
     * 点击显示banner状态 只能选择一项 点击显示的突破 点击跳转外部链接 内部链接 弹窗 只能选择其一 另外几个改成null
     * 编辑修改banner序号规则：
     * - 如果将排序值从x改到y (y < x)，则排序值在[y, x-1]范围的Banner排序值都+1
     * - 如果将排序值从y改到x (y < x)，则排序值在[y+1, x]范围的Banner排序值都-1
     *
     * @param updateReqVO 更新信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBanner(BannerUpdateReqVO updateReqVO) {
        // 校验存在
        BannerDO oldBanner = validateBannerExists2(updateReqVO.getId());

        // 更新
        BannerDO updateObj = BannerConvert.INSTANCE.convert(updateReqVO);
        
        // 处理跳转类型，确保只有一个跳转类型生效，其他置为null
        handleBannerJumpType(updateReqVO, updateObj);
        
        // 处理排序逻辑
        Integer newSort = updateObj.getSort();
        Integer oldSort = oldBanner.getSort();
        
        // 只有当排序值变化时才需要调整其他Banner的排序
        if (newSort != null && !newSort.equals(oldSort)) {
            // 确保排序值不为负数或0
            if (newSort <= 0) {
                newSort = 1;
                updateObj.setSort(newSort);
            }
            
            // 调整排序
            adjustSortForUpdate(oldBanner.getPage(), oldBanner.getPosition(), oldSort, newSort);
        }

        // 发布时间规则
        updateObj.setDisplayTime(getBannerDisplayTimeForUpdate(oldBanner, updateReqVO));
        updateObj.setUpdateTime(LocalDateTime.now());
        bannerService.updateById(updateObj);

        // 添加操作日志上下文
        BannerDO newBanner = bannerService.getById(updateReqVO.getId());
        LogRecordContext.putVariable("banner", newBanner);
    }

    /**
     * 处理Banner跳转类型，确保只有一个跳转类型生效
     * 
     * @param updateReqVO 前端传入的更新请求
     * @param updateObj 待更新的实体对象
     */
    private void handleBannerJumpType(BannerUpdateReqVO updateReqVO, BannerDO updateObj) {
        // 先重置所有跳转相关字段为null
        resetAllJumpFields(updateObj);
        
        // 保留一种跳转类型，按优先级判断
        if (StringUtils.isNotEmpty(updateReqVO.getJumpImageUrl())) {
            // 1. 点击显示图片
            updateObj.setJumpImageUrl(updateReqVO.getJumpImageUrl());
        } else if (StringUtils.isNotEmpty(updateReqVO.getJumpLinkUrl())) {
            // 2. 外部链接
            updateObj.setJumpLinkUrl(updateReqVO.getJumpLinkUrl());
        } else if (updateReqVO.getJumpRouteId() != null) {
            // 3. 内部链接（同时保留路由参数）
            updateObj.setJumpRouteId(updateReqVO.getJumpRouteId());
            updateObj.setJumpRouteParams(updateReqVO.getJumpRouteParams());
        } else if (StringUtils.isNotEmpty(updateReqVO.getJumpPopup())) {
            // 4. 弹窗
            updateObj.setJumpPopup(updateReqVO.getJumpPopup());
        }
        // 如果都没有提供，则所有字段保持为null
    }
    
    /**
     * 重置所有跳转相关字段为null
     */
    private void resetAllJumpFields(BannerDO banner) {
        banner.setJumpImageUrl(null);
        banner.setJumpLinkUrl(null);
        banner.setJumpRouteId(null);
        banner.setJumpRouteParams(null);
        banner.setJumpPopup(null);
    }

    /**
     * 删除Banner
     * 删除序号=x的banner，则序号<x的banner序号不变，序号>x的banner序号-1
     *
     * @param id Banner编号
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBanner(Long id) {
        // 校验存在
        BannerDO banner = validateBannerExists2(id);

        // 添加操作日志上下文
        LogRecordContext.putVariable("banner", banner);
        
        // 获取当前banner的排序值
        Integer sort = banner.getSort();
        Integer page = banner.getPage();
        Integer position = banner.getPosition();
        
        // 删除
        bannerService.removeById(id);
        
        // 调整其他banner的排序
        adjustSortForDelete(page, position, sort);
    }

    /**
     * 获得Banner
     *
     * @param id Banner编号
     * @return Banner
     */
    public BannerDO getBanner(Long id) {
        return bannerService.getById(id);
    }

    /**
     * 分页获取Banner列表
     *
     * @param pageReqVO 分页查询
     * @return Banner分页
     */
    public PageResult<BannerRespVO> getBannerPage(BannerPageReqVO pageReqVO) {
        PageResult<BannerDO> pageResult = bannerService.selectPage(pageReqVO);
        return BannerConvert.INSTANCE.convertPage(pageResult);
    }

    /**
     * 修改Banner的上下架状态
     *
     * @param id      Banner编号
     * @param display 是否展示
     */
    public void updateBannerDisplay(Long id, boolean display) {
        // 校验存在
        BannerDO banner = validateBannerExists2(id);

        // 更新状态
        BannerDO updateObj = new BannerDO();
        updateObj.setId(id);
        String displayDesc;
        if (display) {
            // 展示时，修改状态为"立即展示"，刷新发布时间
            updateObj.setDisplayStatus(BannerDisplayStatusEnum.SHOW_NOW.getCode());
            updateObj.setDisplayTime(LocalDateTime.now());
            displayDesc = BannerDisplayStatusEnum.SHOW_NOW.getDesc();
        } else {
            // 隐藏时，修改状态为"隐藏"
            updateObj.setDisplayStatus(BannerDisplayStatusEnum.HIDDEN.getCode());
            displayDesc = BannerDisplayStatusEnum.HIDDEN.getDesc();
        }
        updateObj.setUpdateTime(LocalDateTime.now());
        bannerService.updateById(updateObj);

        // 添加操作日志上下文
        LogRecordContext.putVariable("banner", banner);
        LogRecordContext.putVariable("displayDesc", displayDesc);
    }

    /**
     * 新增Banner时调整排序
     * 如果相同页面+位置下，输入的banner序号和之前的序号重复，则小于该序号的序号不变，后续的序号各+1
     * 
     * @param page 页面
     * @param position 位置
     * @param sort 排序值
     */
    private void adjustSortForCreate(Integer page, Integer position, Integer sort) {
        // 将大于等于该排序值的记录的排序值都+1
        bannerService.lambdaUpdate()
            .setSql("sort = sort + 1")
            .eq(BannerDO::getPage, page)
            .eq(BannerDO::getPosition, position)
            .ge(BannerDO::getSort, sort)
            .update();
    }

    /**
     * 更新Banner时调整排序
     * 如果将排序值从x改到y (y < x)，则排序值在[y, x-1]范围的Banner排序值都+1
     * 如果将排序值从y改到x (y < x)，则排序值在[y+1, x]范围的Banner排序值都-1
     * 
     * @param page 页面
     * @param position 位置
     * @param oldSort 旧排序值
     * @param newSort 新排序值
     */
    private void adjustSortForUpdate(Integer page, Integer position, Integer oldSort, Integer newSort) {
        // 不需要调整
        if (oldSort.equals(newSort)) {
            return;
        }
        
        if (newSort < oldSort) {
            // 向前移动：将[newSort, oldSort-1]范围内的banner序号+1
            bannerService.lambdaUpdate()
                .setSql("sort = sort + 1")
                .eq(BannerDO::getPage, page)
                .eq(BannerDO::getPosition, position)
                .between(BannerDO::getSort, newSort, oldSort - 1)
                .update();
        } else {
            // 向后移动：将[oldSort+1, newSort]范围内的banner序号-1
            bannerService.lambdaUpdate()
                .setSql("sort = sort - 1")
                .eq(BannerDO::getPage, page)
                .eq(BannerDO::getPosition, position)
                .between(BannerDO::getSort, oldSort + 1, newSort)
                .update();
        }
    }
    
    /**
     * 删除Banner时调整排序
     * 删除序号=x的banner，则序号<x的banner序号不变，序号>x的banner序号-1
     * 
     * @param page 页面
     * @param position 位置
     * @param sort 被删除的排序值
     */
    private void adjustSortForDelete(Integer page, Integer position, Integer sort) {
        // 将大于该排序值的记录的排序值都-1
        bannerService.lambdaUpdate()
            .setSql("sort = sort - 1")
            .eq(BannerDO::getPage, page)
            .eq(BannerDO::getPosition, position)
            .gt(BannerDO::getSort, sort)
            .update();
    }

    private BannerDO validateBannerExists2(Long id) {
        BannerDO banner = bannerService.getById(id);
        if (banner == null) {
            throw exception(BANNER_NOT_EXISTS);
        }
        return banner;
    }
} 