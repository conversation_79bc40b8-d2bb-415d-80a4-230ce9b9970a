package com.xt.hsk.module.marketing.controller.app.coupon.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 优惠券 VO
 *
 * <AUTHOR>
 * @since 2025/06/06
 */
@Data
public class CouponVO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 优惠券模板ID
     */
    private Long couponTemplateId;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 折扣类型 1-无门槛 2-满减
     */
    private Integer discountType;

    /**
     * 无门槛优惠金额-中文，当discount_type=1时必填
     */
    private BigDecimal discountPrice;

    /**
     * 满减条件-中文，当discount_type=2时必填
     */
    private BigDecimal thresholdPrice;

    /**
     * 满减值-中文，当discount_type=2时必填
     */
    private BigDecimal reducePrice;

    /**
     * 发放数量
     */
    private Integer totalCount;

    /**
     * 有效期类型
     */
    private Integer validityType;

    /**
     * 有效期天数
     */
    private Integer validityDays;

    /**
     * 有效时间-开始时间
     */
    private LocalDateTime validStartTime;

    /**
     * 有效时间-结束时间
     */
    private LocalDateTime validEndTime;

    /**
     * 领取类型 1-领取 2-赠送
     */
    private Integer takeType;

    /**
     * 领取状态 1-已领取 2-待领取
     */
    private Integer takeStatus;

    /**
     * 使用商品范围 1-精品课
     */
    private Integer productScope;

    /**
     * 优惠券状态 1-未使用 2-已使用 3-已过期
     */
    private Integer status;

}
