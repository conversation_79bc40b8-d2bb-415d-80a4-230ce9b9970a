package com.xt.hsk.module.marketing.controller.app.banner.vo;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import lombok.Data;

/**
 * App Banner 页面列表请求 VO
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
@Data
public class AppBannerPageListReqVO {

    /**
     * 页面类型列表 - 见BannerPageEnum枚举 1-练习 2-题型练习列表页 3-课程 4-我的
     */
    @NotEmpty(message = "页面类型列表不能为空")
    private List<Integer> pageList;

    /**
     * 位置类型 - 见BannerPositionEnum枚举 1-顶部 2-中部 3-底部
     */
    private Integer position;
} 