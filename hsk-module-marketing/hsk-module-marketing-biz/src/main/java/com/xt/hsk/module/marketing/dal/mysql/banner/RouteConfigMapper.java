package com.xt.hsk.module.marketing.dal.mysql.banner;

import com.xt.hsk.framework.mybatis.core.mapper.BaseMapperX;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.marketing.controller.admin.route.vo.RouteConfigPageReqVO;
import com.xt.hsk.module.marketing.dal.dataobject.banner.RouteConfigDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 路由配置 Mapper
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
@Mapper
public interface RouteConfigMapper extends BaseMapperX<RouteConfigDO> {

    /**
     * 分页查询路由配置
     *
     * @param reqVO 查询条件
     * @return 路由配置列表
     */
    default List<RouteConfigDO> selectPage(RouteConfigPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<RouteConfigDO>()
            .likeIfPresent(RouteConfigDO::getPathName, reqVO.getPathName())
            .eqIfPresent(RouteConfigDO::getIsShow, reqVO.getIsShow())
            .likeIfPresent(RouteConfigDO::getIosPath, reqVO.getIosPath())
            .likeIfPresent(RouteConfigDO::getAndroidPath, reqVO.getAndroidPath())
            .likeIfPresent(RouteConfigDO::getH5Path, reqVO.getH5Path())
            .orderByDesc(RouteConfigDO::getCreateTime));
    }
} 