package com.xt.hsk.module.marketing.service.news;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsPageReqVO;
import com.xt.hsk.module.marketing.dal.dataobject.news.NewsDO;
import com.xt.hsk.module.marketing.dal.mysql.news.NewsMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 资讯 Service 实现类
 *
 * <AUTHOR> 2025/06/27
 */
@Service
@Validated
public class NewsServiceImpl extends ServiceImpl<NewsMapper, NewsDO> implements NewsService {

    @Resource
    private NewsMapper newsMapper;

    @Override
    public PageResult<NewsDO> selectPage(NewsPageReqVO pageReqVO) {

        return newsMapper.selectPage(pageReqVO);
    }

}