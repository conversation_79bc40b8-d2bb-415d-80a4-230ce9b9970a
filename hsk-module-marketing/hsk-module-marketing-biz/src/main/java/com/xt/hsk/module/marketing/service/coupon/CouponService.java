package com.xt.hsk.module.marketing.service.coupon;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.marketing.dal.dataobject.coupon.CouponDO;
import java.util.List;

/**
 * 优惠券 Service 接口
 *
 * <AUTHOR>
 */
public interface CouponService extends IService<CouponDO> {

    /**
     * 获取用户在指定课程可用的优惠券列表
     *
     * @param userId   用户ID
     * @param courseId 课程ID
     * @return 优惠券列表
     */
    List<CouponDO> getUserAvailableCoupons(Long userId, Long courseId);

    /**
     * 领取优惠券
     * 校验逻辑：
     * 1. 优惠券模板是否存在
     * 2. 优惠券模板是否可用
     * 3. 优惠券是否已领取完毕
     * 4. 用户是否已经领取过该类型的优惠券
     * 5. 优惠券是否已经过期
     *
     * @param userId           用户ID
     * @param couponTemplateId 优惠券模板ID
     * @return 领取的优惠券ID
     */
    Long takeCoupon(Long userId, Long couponTemplateId);
}