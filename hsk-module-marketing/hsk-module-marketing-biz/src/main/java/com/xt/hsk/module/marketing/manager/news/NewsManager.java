package com.xt.hsk.module.marketing.manager.news;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.marketing.enums.ErrorCodeConstants.NEWS_FEATURED_COUNT_LIMIT;
import static com.xt.hsk.module.marketing.enums.ErrorCodeConstants.NEWS_NOT_EXISTS;
import static com.xt.hsk.module.marketing.enums.ErrorCodeConstants.NEWS_SHOW_TIME_ERROR;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.enums.IsEnum;
import com.xt.hsk.framework.common.enums.IsShowEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.infra.api.config.ConfigApi;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsPageReqVO;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsRespVO;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsSaveReqVO;
import com.xt.hsk.module.marketing.convert.news.NewsContentConvert;
import com.xt.hsk.module.marketing.convert.news.NewsConvert;
import com.xt.hsk.module.marketing.dal.dataobject.news.NewsContentDO;
import com.xt.hsk.module.marketing.dal.dataobject.news.NewsDO;
import com.xt.hsk.module.marketing.enums.news.NewsShowStatus;
import com.xt.hsk.module.marketing.service.news.NewsContentService;
import com.xt.hsk.module.marketing.service.news.NewsService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 资讯 Manager
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@Component
public class NewsManager {


    static String NEWS_FEATURED_COUNT_LIMIT_KEY = "news.featured.count.limit";

    @Resource
    private NewsService newsService;

    @Resource
    private NewsContentService newsContentService;

    @Resource
    private ConfigApi configApi;

    private NewsDO validateNewsExists(Long id) {
        NewsDO newsDO = newsService.getById(id);
        if (newsDO == null) {
            throw exception(NEWS_NOT_EXISTS);
        }
        return newsDO;
    }

    /**
     * 检查是否可以添加为精选资讯
     */
    public void checkFeaturedNews(NewsDO newsDO) {

        if (newsDO == null) {
            return;
        }

        if (IsEnum.NO.getCode().equals(newsDO.getIsFeatured())) {
            return;
        }

        // 如果要修改为精选资讯 检查上限 如果是修改时 检查现有的精选资讯中包不包含这个资讯
        List<NewsDO> newsList = getFeaturedNewsList();

        String valueByKey = configApi.getConfigValueByKey(NEWS_FEATURED_COUNT_LIMIT_KEY);
        if (CharSequenceUtil.isBlank(valueByKey)) {
            return;
        }

        // 字符串转为数字
        int limit = Integer.parseInt(valueByKey);

        Long newsId = newsDO.getId();
        // 表示新增时就要创建为精选资讯 需要检查是否超出上限
        if (newsId == null) {
            if (newsList.size() >= limit) {
                throw exception(NEWS_FEATURED_COUNT_LIMIT);
            }
        } else {
            // 修改时 检查现有的精选资讯中包不包含这个资讯
            List<Long> newsIdList = newsList.stream().map(NewsDO::getId).toList();
            if (newsIdList.contains(newsId)) {
                return;
            }
            if (newsIdList.size() >= limit) {
                throw exception(NEWS_FEATURED_COUNT_LIMIT);
            }
        }
    }


    /**
     * 查询精选资讯列表
     */
    public List<NewsDO> getFeaturedNewsList() {
        return newsService.lambdaQuery()
            .eq(NewsDO::getIsFeatured, IsShowEnum.SHOW.getCode())
            .select(NewsDO::getId)
            .list();
    }

    /**
     * 删除资讯
     *
     * @param id 资讯编号
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteNews(Long id) {
        NewsDO newsDO = validateNewsExists(id);
        newsService.removeById(id);
        newsContentService.removeByNewsId(id);
        // 记录操作日志上下文
        LogRecordContext.putVariable("news", newsDO);
    }


    /**
     * 创建资讯
     * @param createReqVO 创建参数
     * @return 资讯编号
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createNews(@Valid NewsSaveReqVO createReqVO) {
        // 检查参数
        checkParam(createReqVO);

        // 创建资讯
        NewsDO newsDO = NewsConvert.INSTANCE.convert(createReqVO);

        // 检查能否设置为精选资讯
        checkFeaturedNews(newsDO);

        // 保存资讯
        newsService.save(newsDO);

        // 创建资讯内容并绑定资讯ID
        List<NewsContentDO> contentDOList = NewsContentConvert.INSTANCE.convert(
            createReqVO.getContentList());
        if (CollUtil.isNotEmpty(contentDOList)) {
            contentDOList.forEach(c -> c.setNewsId(newsDO.getId()));
            newsContentService.saveBatch(contentDOList);
        }

        // 记录操作日志上下文
        LogRecordContext.putVariable("news", newsDO);
        return newsDO.getId();
    }

    /**
     * 检查参数
     */
    public void checkParam(NewsSaveReqVO reqVO) {

        // 如果是定时展示, 则展示时间不能为空
        if (NewsShowStatus.SHOW_SCHEDULED.getCode().equals(reqVO.getShowStatus())
            && reqVO.getShowTime() == null) {
            throw exception(NEWS_SHOW_TIME_ERROR, "展示时间不能为空");
        }

        // 如果是定时展示, 则展示时间不能小于当前时间
        if (NewsShowStatus.SHOW_SCHEDULED.getCode().equals(reqVO.getShowStatus())
            && reqVO.getShowTime().isBefore(LocalDateTime.now())) {
            throw exception(NEWS_SHOW_TIME_ERROR,"展示时间不能小于当前时间");
        }
    }


    /**
     * 更新资讯
     * @param updateReqVO 更新参数
     */
    public void updateNews(@Valid NewsSaveReqVO updateReqVO) {

        // 校验存在
        validateNewsExists(updateReqVO.getId());

        // 检查参数
        checkParam(updateReqVO);

        // 更新
        NewsDO newsDO = NewsConvert.INSTANCE.convert(updateReqVO);

        // 检查能否设置为精选资讯
        checkFeaturedNews(newsDO);

        // 更新
        newsService.updateById(newsDO);

        // 记录操作日志上下文
        LogRecordContext.putVariable("news", newsDO);
    }


    /**
     * 获取资讯
     *
     * @param id 资讯ID
     * @return {@code NewsDO }
     */
    public NewsDO getNews(Long id) {
        return validateNewsExists(id);
    }

    /**
     * 分页查询资讯页面
     */
    public PageResult<NewsRespVO> getNewsPage(@Valid NewsPageReqVO pageReqVO) {
        PageResult<NewsDO> pageResult = newsService.selectPage(pageReqVO);
        List<NewsRespVO> newsRespVOList = NewsConvert.INSTANCE.convertList(pageResult.getList());
        return new PageResult<>(newsRespVOList, pageResult.getTotal());
    }

}
