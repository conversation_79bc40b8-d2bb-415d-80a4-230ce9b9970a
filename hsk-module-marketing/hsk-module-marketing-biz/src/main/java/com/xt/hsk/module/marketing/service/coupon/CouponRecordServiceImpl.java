package com.xt.hsk.module.marketing.service.coupon;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.module.marketing.dal.dataobject.coupon.CouponRecordDO;
import com.xt.hsk.module.marketing.dal.mysql.coupon.CouponRecordMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 优惠券记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CouponRecordServiceImpl extends
    ServiceImpl<CouponRecordMapper, CouponRecordDO> implements CouponRecordService {

    @Resource
    private CouponRecordMapper couponRecordMapper;

} 