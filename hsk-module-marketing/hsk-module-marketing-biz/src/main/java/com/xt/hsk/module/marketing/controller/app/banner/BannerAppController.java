package com.xt.hsk.module.marketing.controller.app.banner;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import cn.dev33.satoken.annotation.SaIgnore;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.marketing.controller.app.banner.vo.AppBannerPageListReqVO;
import com.xt.hsk.module.marketing.controller.app.banner.vo.AppBannerPageListRespVO;
import com.xt.hsk.module.marketing.controller.app.banner.vo.AppBannerRespVO;
import com.xt.hsk.module.marketing.manager.banner.BannerAppManager;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * App - Banner 控制器
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
@RestController
@RequestMapping("/marketing/banner/")
@Validated
@SaIgnore
public class BannerAppController {

    @Resource
    private BannerAppManager bannerAppManager;

    /**
     * 获取指定页面的Banner列表
     *
     * @param page     页面类型
     * @param position 位置类型
     * @return Banner列表
     */
    @GetMapping("/list")
    public CommonResult<List<AppBannerRespVO>> getBannerList(
        @RequestParam(value = "page") Integer page,
        @RequestParam(value = "position", required = false) Integer position) {
        return success(bannerAppManager.getBannerList(page, position));
    }

    /**
     * 批量获取多个页面的Banner列表
     *
     * @param reqVO 请求参数，包含页面类型列表和位置类型
     * @return 按页面分组的Banner列表
     */
    @PostMapping("/page-list")
    public CommonResult<AppBannerPageListRespVO> getBannerPageList(
        @RequestBody @Validated AppBannerPageListReqVO reqVO) {
        return success(
            bannerAppManager.getBannerPageList(reqVO.getPageList(), reqVO.getPosition()));
    }
} 