package com.xt.hsk.module.marketing.service.news;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsContentPageReqVO;
import com.xt.hsk.module.marketing.dal.dataobject.news.NewsContentDO;
import jakarta.validation.Valid;

/**
 * 资讯内容 Service 接口
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
public interface NewsContentService extends IService<NewsContentDO> {

    /**
     * 分页查询
     *
     * @param pageReqVO 分页查询参数
     * @return {@code PageResult<NewsContentDO> }
     */
    PageResult<NewsContentDO> selectPage(@Valid NewsContentPageReqVO pageReqVO);

    /**
     * 根据资讯ID删除
     */
    void removeByNewsId(Long newsId);
}
